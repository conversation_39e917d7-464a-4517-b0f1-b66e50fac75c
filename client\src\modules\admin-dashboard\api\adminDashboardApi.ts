/**
 * API layer for admin dashboard data fetching
 */

import { apiClient } from '@/lib/api'
import type {
  AdminDashboardApiResponse,
  AdminDashboardData,
  AdminDashboardMetrics,
  AuditLogEntry,
  AuditLogsApiParams,
  ComponentLibraryStats,
  ProjectOversightSummary,
  ProjectsApiParams,
  QuickAdminAction,
  SecurityAlert,
  SecurityAlertsApiParams,
  SystemConfiguration,
  UserManagementSummary,
  UsersApiParams,
} from '../types'

/**
 * Fetch complete admin dashboard data
 */
export async function fetchAdminDashboardData(): Promise<AdminDashboardData> {
  try {
    const response = await apiClient.get<AdminDashboardApiResponse>('/api/v1/admin/dashboard')

    if (response.data.status === 'error') {
      throw new Error(response.data.message || 'Failed to fetch admin dashboard data')
    }

    return response.data.data
  } catch (error) {
    console.error('Error fetching admin dashboard data:', error)
    throw error
  }
}

/**
 * Fetch system metrics for admin dashboard
 */
export async function fetchSystemMetrics(): Promise<AdminDashboardMetrics> {
  try {
    const response = await apiClient.get<{ data: AdminDashboardMetrics }>('/api/v1/admin/metrics')
    return response.data.data
  } catch (error) {
    console.error('Error fetching system metrics:', error)
    throw error
  }
}

/**
 * Fetch user management summaries
 */
export async function fetchUserSummaries(
  params?: UsersApiParams
): Promise<UserManagementSummary[]> {
  try {
    const queryParams = new URLSearchParams()

    if (params?.limit) queryParams.append('limit', params.limit.toString())
    if (params?.offset) queryParams.append('offset', params.offset.toString())
    if (params?.role) queryParams.append('role', params.role)
    if (params?.isActive !== undefined) queryParams.append('is_active', params.isActive.toString())
    if (params?.sortBy) queryParams.append('sort_by', params.sortBy)
    if (params?.sortOrder) queryParams.append('sort_order', params.sortOrder)
    if (params?.search) queryParams.append('search', params.search)

    const url = `/api/v1/admin/users${queryParams.toString() ? `?${queryParams.toString()}` : ''}`
    const response = await apiClient.get<{ data: UserManagementSummary[] }>(url)

    return response.data.data
  } catch (error) {
    console.error('Error fetching user summaries:', error)
    throw error
  }
}

/**
 * Fetch component library statistics
 */
export async function fetchComponentLibraryStats(): Promise<ComponentLibraryStats> {
  try {
    const response = await apiClient.get<{ data: ComponentLibraryStats }>(
      '/api/v1/admin/components/stats'
    )
    return response.data.data
  } catch (error) {
    console.error('Error fetching component library stats:', error)
    throw error
  }
}

/**
 * Fetch project oversight summaries
 */
export async function fetchProjectSummaries(
  params?: ProjectsApiParams
): Promise<ProjectOversightSummary[]> {
  try {
    const queryParams = new URLSearchParams()

    if (params?.limit) queryParams.append('limit', params.limit.toString())
    if (params?.offset) queryParams.append('offset', params.offset.toString())
    if (params?.status) queryParams.append('status', params.status)
    if (params?.type) queryParams.append('type', params.type)
    if (params?.priority) queryParams.append('priority', params.priority)
    if (params?.ownerId) queryParams.append('owner_id', params.ownerId)
    if (params?.sortBy) queryParams.append('sort_by', params.sortBy)
    if (params?.sortOrder) queryParams.append('sort_order', params.sortOrder)
    if (params?.search) queryParams.append('search', params.search)

    const url = `/api/v1/admin/projects${queryParams.toString() ? `?${queryParams.toString()}` : ''}`
    const response = await apiClient.get<{ data: ProjectOversightSummary[] }>(url)

    return response.data.data
  } catch (error) {
    console.error('Error fetching project summaries:', error)
    throw error
  }
}

/**
 * Fetch audit logs
 */
export async function fetchAuditLogs(params?: AuditLogsApiParams): Promise<AuditLogEntry[]> {
  try {
    const queryParams = new URLSearchParams()

    if (params?.limit) queryParams.append('limit', params.limit.toString())
    if (params?.offset) queryParams.append('offset', params.offset.toString())
    if (params?.startDate) queryParams.append('start_date', params.startDate)
    if (params?.endDate) queryParams.append('end_date', params.endDate)
    if (params?.userId) queryParams.append('user_id', params.userId)
    if (params?.action) queryParams.append('action', params.action)
    if (params?.resource) queryParams.append('resource', params.resource)
    if (params?.success !== undefined) queryParams.append('success', params.success.toString())
    if (params?.sortBy) queryParams.append('sort_by', params.sortBy)
    if (params?.sortOrder) queryParams.append('sort_order', params.sortOrder)

    const url = `/api/v1/admin/audit-logs${queryParams.toString() ? `?${queryParams.toString()}` : ''}`
    const response = await apiClient.get<{ data: AuditLogEntry[] }>(url)

    return response.data.data
  } catch (error) {
    console.error('Error fetching audit logs:', error)
    throw error
  }
}

/**
 * Fetch security alerts
 */
export async function fetchSecurityAlerts(
  params?: SecurityAlertsApiParams
): Promise<SecurityAlert[]> {
  try {
    const queryParams = new URLSearchParams()

    if (params?.limit) queryParams.append('limit', params.limit.toString())
    if (params?.offset) queryParams.append('offset', params.offset.toString())
    if (params?.type) queryParams.append('type', params.type)
    if (params?.severity) queryParams.append('severity', params.severity)
    if (params?.resolved !== undefined) queryParams.append('resolved', params.resolved.toString())
    if (params?.startDate) queryParams.append('start_date', params.startDate)
    if (params?.endDate) queryParams.append('end_date', params.endDate)
    if (params?.sortBy) queryParams.append('sort_by', params.sortBy)
    if (params?.sortOrder) queryParams.append('sort_order', params.sortOrder)

    const url = `/api/v1/admin/security-alerts${queryParams.toString() ? `?${queryParams.toString()}` : ''}`
    const response = await apiClient.get<{ data: SecurityAlert[] }>(url)

    return response.data.data
  } catch (error) {
    console.error('Error fetching security alerts:', error)
    throw error
  }
}

/**
 * Fetch system configuration
 */
export async function fetchSystemConfiguration(): Promise<SystemConfiguration[]> {
  try {
    const response = await apiClient.get<{ data: SystemConfiguration[] }>('/api/v1/admin/config')
    return response.data.data
  } catch (error) {
    console.error('Error fetching system configuration:', error)
    throw error
  }
}

/**
 * Fetch quick admin actions
 */
export async function fetchQuickAdminActions(): Promise<QuickAdminAction[]> {
  try {
    const response = await apiClient.get<{ data: QuickAdminAction[] }>(
      '/api/v1/admin/quick-actions'
    )
    return response.data.data
  } catch (error) {
    console.error('Error fetching quick admin actions:', error)
    throw error
  }
}

/**
 * Update user status (activate/deactivate)
 */
export async function updateUserStatus(userId: string, isActive: boolean): Promise<void> {
  try {
    await apiClient.patch(`/api/v1/admin/users/${userId}/status`, { is_active: isActive })
  } catch (error) {
    console.error('Error updating user status:', error)
    throw error
  }
}

/**
 * Update user role
 */
export async function updateUserRole(userId: string, role: string): Promise<void> {
  try {
    await apiClient.patch(`/api/v1/admin/users/${userId}/role`, { role })
  } catch (error) {
    console.error('Error updating user role:', error)
    throw error
  }
}

/**
 * Resolve security alert
 */
export async function resolveSecurityAlert(alertId: string, resolvedBy: string): Promise<void> {
  try {
    await apiClient.patch(`/api/v1/admin/security-alerts/${alertId}/resolve`, {
      resolved_by: resolvedBy,
    })
  } catch (error) {
    console.error('Error resolving security alert:', error)
    throw error
  }
}

/**
 * Update system configuration
 */
export async function updateSystemConfiguration(
  configId: string,
  value: string,
  modifiedBy: string
): Promise<void> {
  try {
    await apiClient.patch(`/api/v1/admin/config/${configId}`, {
      value,
      modified_by: modifiedBy,
    })
  } catch (error) {
    console.error('Error updating system configuration:', error)
    throw error
  }
}

/**
 * Execute quick admin action
 */
export async function executeQuickAdminAction(
  actionId: string,
  params?: Record<string, any>
): Promise<void> {
  try {
    await apiClient.post(`/api/v1/admin/quick-actions/${actionId}/execute`, params || {})
  } catch (error) {
    console.error('Error executing quick admin action:', error)
    throw error
  }
}

/**
 * Export audit logs
 */
export async function exportAuditLogs(params?: AuditLogsApiParams): Promise<Blob> {
  try {
    const queryParams = new URLSearchParams()

    if (params?.startDate) queryParams.append('start_date', params.startDate)
    if (params?.endDate) queryParams.append('end_date', params.endDate)
    if (params?.userId) queryParams.append('user_id', params.userId)
    if (params?.action) queryParams.append('action', params.action)
    if (params?.resource) queryParams.append('resource', params.resource)
    if (params?.success !== undefined) queryParams.append('success', params.success.toString())

    const url = `/api/v1/admin/audit-logs/export${queryParams.toString() ? `?${queryParams.toString()}` : ''}`
    const response = await apiClient.get(url, { responseType: 'blob' })

    return response.data
  } catch (error) {
    console.error('Error exporting audit logs:', error)
    throw error
  }
}

/**
 * Bulk user operations
 */
export async function bulkUserOperation(
  operation: 'activate' | 'deactivate' | 'delete' | 'change_role',
  userIds: string[],
  params?: { role?: string }
): Promise<void> {
  try {
    await apiClient.post('/api/v1/admin/users/bulk', {
      operation,
      user_ids: userIds,
      ...params,
    })
  } catch (error) {
    console.error('Error performing bulk user operation:', error)
    throw error
  }
}

/**
 * Get system health status
 */
export async function getSystemHealth(): Promise<{
  status: 'healthy' | 'warning' | 'critical'
  checks: Array<{
    name: string
    status: 'pass' | 'fail' | 'warn'
    message: string
    timestamp: string
  }>
}> {
  try {
    const response = await apiClient.get('/api/v1/admin/health')
    return response.data
  } catch (error) {
    console.error('Error fetching system health:', error)
    throw error
  }
}
