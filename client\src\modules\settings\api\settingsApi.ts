/**
 * API client methods for the Settings module
 * Ultimate Electrical Designer - Settings & User Preferences
 */

import { apiClient } from '@/lib/api/client'
import type {
  UserPreferences,
  UserPreferencesUpdate,
  SettingsExport,
  SettingsImport,
  SettingsApiResponse,
} from '../types'
import { API_ENDPOINTS } from '../constants'
import {
  validateUserPreferences,
  validateSettingsExport,
  safeValidateUserPreferences,
  safeValidateSettingsExport,
} from '../schemas/settingsSchemas'

/**
 * Settings API client class
 */
export class SettingsApi {
  /**
   * Get current user preferences
   */
  async getUserPreferences(): Promise<SettingsApiResponse<UserPreferences>> {
    try {
      const response = await apiClient.get(API_ENDPOINTS.PREFERENCES)

      // Validate response data
      const validationResult = safeValidateUserPreferences(response.data)
      if (!validationResult.success) {
        return {
          success: false,
          error: {
            message: 'Invalid preferences data received from server',
            code: 'VALIDATION_ERROR',
            details: validationResult.error.issues,
          },
        }
      }

      return {
        success: true,
        data: validationResult.data,
      }
    } catch (error: any) {
      return {
        success: false,
        error: {
          message: error.response?.data?.detail || error.message || 'Failed to fetch preferences',
          code: error.response?.data?.code || 'FETCH_ERROR',
          details: error.response?.data,
        },
      }
    }
  }

  /**
   * Update current user preferences
   */
  async updateUserPreferences(
    preferences: UserPreferencesUpdate
  ): Promise<SettingsApiResponse<UserPreferences>> {
    try {
      const response = await apiClient.put(API_ENDPOINTS.PREFERENCES, preferences)

      // Validate response data
      const validationResult = safeValidateUserPreferences(response.data)
      if (!validationResult.success) {
        return {
          success: false,
          error: {
            message: 'Invalid preferences data received from server',
            code: 'VALIDATION_ERROR',
            details: validationResult.error.issues,
          },
        }
      }

      return {
        success: true,
        data: validationResult.data,
      }
    } catch (error: any) {
      return {
        success: false,
        error: {
          message: error.response?.data?.detail || error.message || 'Failed to update preferences',
          code: error.response?.data?.code || 'UPDATE_ERROR',
          details: error.response?.data,
        },
      }
    }
  }

  /**
   * Reset user preferences to defaults
   */
  async resetUserPreferences(): Promise<SettingsApiResponse<{ message: string }>> {
    try {
      const response = await apiClient.delete(API_ENDPOINTS.RESET)

      return {
        success: true,
        data: response.data,
      }
    } catch (error: any) {
      return {
        success: false,
        error: {
          message: error.response?.data?.detail || error.message || 'Failed to reset preferences',
          code: error.response?.data?.code || 'RESET_ERROR',
          details: error.response?.data,
        },
      }
    }
  }

  /**
   * Export user preferences
   */
  async exportUserPreferences(): Promise<SettingsApiResponse<SettingsExport>> {
    try {
      const response = await apiClient.post(API_ENDPOINTS.EXPORT)

      // Validate response data
      const validationResult = safeValidateSettingsExport(response.data)
      if (!validationResult.success) {
        return {
          success: false,
          error: {
            message: 'Invalid export data received from server',
            code: 'VALIDATION_ERROR',
            details: validationResult.error.issues,
          },
        }
      }

      return {
        success: true,
        data: validationResult.data,
      }
    } catch (error: any) {
      return {
        success: false,
        error: {
          message: error.response?.data?.detail || error.message || 'Failed to export preferences',
          code: error.response?.data?.code || 'EXPORT_ERROR',
          details: error.response?.data,
        },
      }
    }
  }

  /**
   * Import user preferences
   */
  async importUserPreferences(
    importData: SettingsImport
  ): Promise<SettingsApiResponse<UserPreferences>> {
    try {
      const response = await apiClient.post(API_ENDPOINTS.IMPORT, importData)

      // Validate response data
      const validationResult = safeValidateUserPreferences(response.data)
      if (!validationResult.success) {
        return {
          success: false,
          error: {
            message: 'Invalid preferences data received from server',
            code: 'VALIDATION_ERROR',
            details: validationResult.error.issues,
          },
        }
      }

      return {
        success: true,
        data: validationResult.data,
      }
    } catch (error: any) {
      return {
        success: false,
        error: {
          message: error.response?.data?.detail || error.message || 'Failed to import preferences',
          code: error.response?.data?.code || 'IMPORT_ERROR',
          details: error.response?.data,
        },
      }
    }
  }

  /**
   * Validate import file content
   */
  async validateImportFile(file: File): Promise<SettingsApiResponse<SettingsImport>> {
    try {
      const text = await file.text()
      const data = JSON.parse(text)

      // Basic validation of import structure
      if (!data.preferences) {
        return {
          success: false,
          error: {
            message: 'Invalid import file: missing preferences data',
            code: 'INVALID_FORMAT',
          },
        }
      }

      return {
        success: true,
        data: data as SettingsImport,
      }
    } catch (error: any) {
      return {
        success: false,
        error: {
          message: error.message || 'Failed to validate import file',
          code: 'VALIDATION_ERROR',
        },
      }
    }
  }

  /**
   * Download export file
   */
  downloadExportFile(exportData: SettingsExport, filename?: string): void {
    const blob = new Blob([JSON.stringify(exportData, null, 2)], {
      type: 'application/json',
    })

    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = filename || `ued-settings-${new Date().toISOString().split('T')[0]}.json`

    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)

    URL.revokeObjectURL(url)
  }

  /**
   * Check if preferences have been modified
   */
  comparePreferences(current: UserPreferences, updated: UserPreferencesUpdate): boolean {
    const keys = Object.keys(updated) as (keyof UserPreferencesUpdate)[]

    return keys.some((key) => {
      if (key in current) {
        return current[key as keyof UserPreferences] !== updated[key]
      }
      return false
    })
  }

  /**
   * Get preferences diff for display
   */
  getPreferencesDiff(
    current: UserPreferences,
    updated: UserPreferencesUpdate
  ): Record<string, { from: any; to: any }> {
    const diff: Record<string, { from: any; to: any }> = {}
    const keys = Object.keys(updated) as (keyof UserPreferencesUpdate)[]

    keys.forEach((key) => {
      if (key in current && current[key as keyof UserPreferences] !== updated[key]) {
        diff[key] = {
          from: current[key as keyof UserPreferences],
          to: updated[key],
        }
      }
    })

    return diff
  }

  /**
   * Merge preferences with defaults
   */
  mergeWithDefaults(
    preferences: Partial<UserPreferences>,
    defaults: UserPreferences
  ): UserPreferences {
    return {
      ...defaults,
      ...preferences,
    }
  }

  /**
   * Sanitize preferences for export
   */
  sanitizeForExport(preferences: UserPreferences): Partial<UserPreferences> {
    const { id, user_id, created_at, updated_at, ...sanitized } = preferences
    return sanitized
  }

  /**
   * Validate preferences against business rules
   */
  validateBusinessRules(preferences: UserPreferencesUpdate): {
    isValid: boolean
    errors: string[]
  } {
    const errors: string[] = []

    // Auto save interval validation
    if (preferences.auto_save_interval !== undefined) {
      if (preferences.auto_save_interval < 30) {
        errors.push('Auto save interval must be at least 30 seconds')
      }
      if (preferences.auto_save_interval > 3600) {
        errors.push('Auto save interval cannot exceed 1 hour')
      }
    }

    // Calculation precision validation
    if (preferences.calculation_precision !== undefined) {
      if (preferences.calculation_precision < 0) {
        errors.push('Calculation precision cannot be negative')
      }
      if (preferences.calculation_precision > 6) {
        errors.push('Calculation precision cannot exceed 6 decimal places')
      }
    }

    // Language validation
    if (preferences.language !== undefined) {
      const supportedLanguages = ['en', 'es', 'fr', 'de']
      if (!supportedLanguages.includes(preferences.language)) {
        errors.push('Unsupported language selected')
      }
    }

    // Theme validation
    if (preferences.theme !== undefined) {
      const supportedThemes = ['light', 'dark', 'system']
      if (!supportedThemes.includes(preferences.theme)) {
        errors.push('Unsupported theme selected')
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
    }
  }
}

// Create singleton instance
export const settingsApi = new SettingsApi()

// Export individual methods for convenience
export const {
  getUserPreferences,
  updateUserPreferences,
  resetUserPreferences,
  exportUserPreferences,
  importUserPreferences,
  validateImportFile,
  downloadExportFile,
  comparePreferences,
  getPreferencesDiff,
  mergeWithDefaults,
  sanitizeForExport,
  validateBusinessRules,
} = settingsApi
