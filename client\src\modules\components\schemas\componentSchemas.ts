/**
 * Component Zod Validation Schemas
 * Comprehensive client-side validation for component management
 */

import { z } from 'zod'

// Base component dimensions schema
export const ComponentDimensionsSchema = z
  .object({
    length: z.number().positive().optional(),
    width: z.number().positive().optional(),
    height: z.number().positive().optional(),
    diameter: z.number().positive().optional(),
    unit: z.enum(['mm', 'cm', 'm', 'in', 'ft']).default('mm'),
  })
  .refine((data) => data.length || data.width || data.height || data.diameter, {
    message: 'At least one dimension must be provided',
  })

// Component specifications schema (flexible)
export const ComponentSpecificationsSchema = z
  .record(z.string().min(1), z.union([z.string(), z.number(), z.boolean()]))
  .optional()

// Base component schema with common fields
export const ComponentBaseSchema = z.object({
  name: z
    .string()
    .min(2, 'Name must be at least 2 characters')
    .max(200, 'Name must not exceed 200 characters')
    .trim(),

  manufacturer: z
    .string()
    .min(2, 'Manufacturer must be at least 2 characters')
    .max(100, 'Manufacturer must not exceed 100 characters')
    .trim(),

  model_number: z
    .string()
    .min(1, 'Model number is required')
    .max(100, 'Model number must not exceed 100 characters')
    .trim(),

  description: z
    .string()
    .max(1000, 'Description must not exceed 1000 characters')
    .trim()
    .optional(),

  component_type: z
    .string()
    .min(1, 'Component type is required')
    .max(50, 'Component type must not exceed 50 characters'),

  category: z
    .string()
    .min(1, 'Category is required')
    .max(50, 'Category must not exceed 50 characters'),

  specifications: ComponentSpecificationsSchema,

  unit_price: z
    .number()
    .nonnegative('Price must be non-negative')
    .multipleOf(0.01, 'Price must have at most 2 decimal places')
    .optional(),

  currency: z
    .string()
    .length(3, 'Currency must be a 3-letter ISO code')
    .regex(/^[A-Z]{3}$/, 'Currency must be uppercase letters')
    .default('EUR'),

  supplier: z.string().max(100, 'Supplier name must not exceed 100 characters').trim().optional(),

  part_number: z.string().max(100, 'Part number must not exceed 100 characters').trim().optional(),

  weight_kg: z.number().nonnegative('Weight must be non-negative').optional(),

  dimensions: ComponentDimensionsSchema.optional(),

  is_active: z.boolean().default(true),
  is_preferred: z.boolean().default(false),

  stock_status: z
    .enum(['available', 'limited', 'out_of_stock', 'discontinued', 'on_order'])
    .default('available'),

  version: z.string().default('1.0'),

  metadata: z.record(z.string(), z.any()).optional(),
})

// Component creation schema
export const ComponentCreateSchema = ComponentBaseSchema.extend({
  // All fields from base schema are required for creation
  name: ComponentBaseSchema.shape.name,
  manufacturer: ComponentBaseSchema.shape.manufacturer,
  model_number: ComponentBaseSchema.shape.model_number,
  component_type: ComponentBaseSchema.shape.component_type,
  category: ComponentBaseSchema.shape.category,
})

// Component update schema (all fields optional except id)
export const ComponentUpdateSchema = ComponentBaseSchema.partial().extend({
  id: z.number().positive(),
})

// Component read schema (includes server-generated fields)
export const ComponentReadSchema = ComponentBaseSchema.extend({
  id: z.number().positive(),
  created_at: z.string().datetime(),
  updated_at: z.string().datetime(),
  created_by: z.number().positive().optional(),
  updated_by: z.number().positive().optional(),

  // Computed fields
  display_name: z.string().optional(),
  full_name: z.string().optional(),

  // Relationships
  category_id: z.number().positive().optional(),
  component_type_id: z.number().positive().optional(),
})

// Component summary schema (for lists and dropdowns)
export const ComponentSummarySchema = z.object({
  id: z.number().positive(),
  name: z.string(),
  manufacturer: z.string(),
  model_number: z.string(),
  display_name: z.string().optional(),
  component_type: z.string(),
  category: z.string(),
  unit_price: z.number().optional(),
  currency: z.string().default('EUR'),
  is_active: z.boolean(),
  is_preferred: z.boolean(),
})

// Bulk operations schemas
export const ComponentBulkCreateSchema = z.object({
  components: z.array(ComponentCreateSchema).min(1, 'At least one component required'),
  validate_only: z.boolean().default(false),
  skip_duplicates: z.boolean().default(true),
})

export const ComponentBulkUpdateSchema = z.object({
  component_ids: z.array(z.number().positive()).min(1, 'At least one component ID required'),
  updates: ComponentBaseSchema.partial(),
  validate_only: z.boolean().default(false),
})

export const ComponentBulkDeleteSchema = z.object({
  component_ids: z.array(z.number().positive()).min(1, 'At least one component ID required'),
  permanent: z.boolean().default(false),
})

// Validation result schema
export const ComponentValidationResultSchema = z.object({
  component_id: z.number().positive().optional(),
  is_valid: z.boolean(),
  errors: z.array(
    z.object({
      field: z.string(),
      message: z.string(),
      code: z.string().optional(),
    })
  ),
  warnings: z
    .array(
      z.object({
        field: z.string(),
        message: z.string(),
        code: z.string().optional(),
      })
    )
    .optional(),
})

// Component statistics schema
export const ComponentStatsSchema = z.object({
  total_components: z.number().nonnegative(),
  active_components: z.number().nonnegative(),
  inactive_components: z.number().nonnegative(),
  preferred_components: z.number().nonnegative(),
  by_category: z.record(z.string(), z.number().nonnegative()),
  by_manufacturer: z.record(z.string(), z.number().nonnegative()),
  by_type: z.record(z.string(), z.number().nonnegative()),
  price_range: z.object({
    min: z.number().optional(),
    max: z.number().optional(),
    average: z.number().optional(),
  }),
  last_updated: z.string().datetime(),
})

// Paginated response schema
export const ComponentPaginatedResponseSchema = z.object({
  items: z.array(ComponentReadSchema),
  total: z.number().nonnegative(),
  page: z.number().positive(),
  size: z.number().positive(),
  pages: z.number().positive(),
  has_next: z.boolean(),
  has_prev: z.boolean(),
})

// Export type definitions
export type ComponentDimensions = z.infer<typeof ComponentDimensionsSchema>
export type ComponentSpecifications = z.infer<typeof ComponentSpecificationsSchema>
export type ComponentBase = z.infer<typeof ComponentBaseSchema>
export type ComponentCreate = z.infer<typeof ComponentCreateSchema>
export type ComponentUpdate = z.infer<typeof ComponentUpdateSchema>
export type ComponentRead = z.infer<typeof ComponentReadSchema>
export type ComponentSummary = z.infer<typeof ComponentSummarySchema>
export type ComponentBulkCreate = z.infer<typeof ComponentBulkCreateSchema>
export type ComponentBulkUpdate = z.infer<typeof ComponentBulkUpdateSchema>
export type ComponentBulkDelete = z.infer<typeof ComponentBulkDeleteSchema>
export type ComponentValidationResult = z.infer<typeof ComponentValidationResultSchema>
export type ComponentStats = z.infer<typeof ComponentStatsSchema>
export type ComponentPaginatedResponse = z.infer<typeof ComponentPaginatedResponseSchema>

// Validation helper functions
export const validateComponent = (data: unknown): ComponentRead => {
  return ComponentReadSchema.parse(data)
}

export const validateComponentCreate = (data: unknown): ComponentCreate => {
  return ComponentCreateSchema.parse(data)
}

export const validateComponentUpdate = (data: unknown): ComponentUpdate => {
  return ComponentUpdateSchema.parse(data)
}

export const validateComponentBulkCreate = (data: unknown): ComponentBulkCreate => {
  return ComponentBulkCreateSchema.parse(data)
}

// Safe validation functions that return results instead of throwing
export const safeValidateComponent = (data: unknown) => {
  return ComponentReadSchema.safeParse(data)
}

export const safeValidateComponentCreate = (data: unknown) => {
  return ComponentCreateSchema.safeParse(data)
}

export const safeValidateComponentUpdate = (data: unknown) => {
  return ComponentUpdateSchema.safeParse(data)
}
