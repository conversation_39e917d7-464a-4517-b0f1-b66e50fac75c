/**
 * E2E tests for RBAC and Audit Trail workflows
 * Tests the complete user journey for role management and audit viewing
 */

import { test, expect, Page } from '@playwright/test'

// Test data
const testRole = {
  name: 'Test Manager',
  description: 'Test role for E2E testing',
  permissions: '["read", "write", "manage"]',
  priority: 75,
  notes: 'Created during E2E testing',
}

const testUser = {
  name: 'Test User',
  email: '<EMAIL>',
  password: 'TestPass123!',
}

// Helper functions
async function loginAsAdmin(page: Page) {
  await page.goto('/login')
  await page.fill('[data-testid="username"]', 'admin')
  await page.fill('[data-testid="password"]', 'admin123')
  await page.click('[data-testid="login-button"]')
  await page.waitForURL('/dashboard')
}

async function navigateToRoleManagement(page: Page) {
  await page.click('[data-testid="admin-menu"]')
  await page.click('[data-testid="role-management-link"]')
  await page.waitForURL('/admin/roles')
}

async function navigateToAuditTrail(page: Page) {
  await page.click('[data-testid="admin-menu"]')
  await page.click('[data-testid="audit-trail-link"]')
  await page.waitForURL('/admin/audit')
}

test.describe('RBAC and Audit Trail E2E Workflow', () => {
  test.beforeEach(async ({ page }) => {
    // Setup: Login as admin
    await loginAsAdmin(page)
  })

  test('should complete full role management workflow', async ({ page }) => {
    // Navigate to role management
    await navigateToRoleManagement(page)

    // Verify role management page loads
    await expect(page.locator('[data-testid="role-management-title"]')).toContainText('Role Management')

    // Check if existing roles are displayed
    await expect(page.locator('[data-testid="roles-table"]')).toBeVisible()
    await expect(page.locator('[data-testid="role-item"]')).toHaveCount(3) // Admin, Editor, Viewer

    // Create a new role
    await page.click('[data-testid="create-role-button"]')
    await expect(page.locator('[data-testid="create-role-dialog"]')).toBeVisible()

    // Fill in role details
    await page.fill('[data-testid="role-name-input"]', testRole.name)
    await page.fill('[data-testid="role-description-input"]', testRole.description)
    await page.fill('[data-testid="role-permissions-input"]', testRole.permissions)
    await page.fill('[data-testid="role-priority-input"]', testRole.priority.toString())
    await page.fill('[data-testid="role-notes-input"]', testRole.notes)

    // Submit the form
    await page.click('[data-testid="create-role-submit"]')

    // Verify role was created
    await expect(page.locator('[data-testid="success-toast"]')).toContainText('Role Created')
    await expect(page.locator('[data-testid="roles-table"]')).toContainText(testRole.name)

    // Verify audit log was created
    await navigateToAuditTrail(page)
    await expect(page.locator('[data-testid="activity-logs-table"]')).toContainText('CREATE')
    await expect(page.locator('[data-testid="activity-logs-table"]')).toContainText('Role')
  })

  test('should update existing role and track changes', async ({ page }) => {
    await navigateToRoleManagement(page)

    // Find and edit the Editor role
    await page.click('[data-testid="role-item"]:has-text("Editor") [data-testid="edit-role-button"]')
    await expect(page.locator('[data-testid="edit-role-dialog"]')).toBeVisible()

    // Update role description
    const newDescription = 'Updated content editor role'
    await page.fill('[data-testid="role-description-input"]', newDescription)

    // Submit the update
    await page.click('[data-testid="update-role-submit"]')

    // Verify update was successful
    await expect(page.locator('[data-testid="success-toast"]')).toContainText('Role Updated')
    await expect(page.locator('[data-testid="roles-table"]')).toContainText(newDescription)

    // Check audit trail for update
    await navigateToAuditTrail(page)
    await page.click('[data-testid="audit-trails-tab"]')
    
    await expect(page.locator('[data-testid="audit-trails-table"]')).toContainText('UPDATE')
    await expect(page.locator('[data-testid="audit-trails-table"]')).toContainText('UserRole')
    await expect(page.locator('[data-testid="audit-trails-table"]')).toContainText('description')
  })

  test('should assign roles to users and track assignments', async ({ page }) => {
    // Navigate to user management
    await page.click('[data-testid="admin-menu"]')
    await page.click('[data-testid="user-management-link"]')
    await page.waitForURL('/admin/users')

    // Create a test user first
    await page.click('[data-testid="create-user-button"]')
    await page.fill('[data-testid="user-name-input"]', testUser.name)
    await page.fill('[data-testid="user-email-input"]', testUser.email)
    await page.fill('[data-testid="user-password-input"]', testUser.password)
    await page.click('[data-testid="create-user-submit"]')

    // Verify user was created
    await expect(page.locator('[data-testid="success-toast"]')).toContainText('User Created')

    // Assign role to user
    await page.click('[data-testid="user-item"]:has-text("Test User") [data-testid="manage-roles-button"]')
    await expect(page.locator('[data-testid="user-roles-dialog"]')).toBeVisible()

    // Assign Editor role
    await page.click('[data-testid="available-roles"] [data-testid="role-item"]:has-text("Editor")')
    await page.click('[data-testid="assign-role-button"]')

    // Verify assignment
    await expect(page.locator('[data-testid="assigned-roles"]')).toContainText('Editor')
    await expect(page.locator('[data-testid="success-toast"]')).toContainText('Role Assigned')

    // Check audit trail for assignment
    await navigateToAuditTrail(page)
    await expect(page.locator('[data-testid="activity-logs-table"]')).toContainText('ROLE_ASSIGNMENT')
    await expect(page.locator('[data-testid="activity-logs-table"]')).toContainText('Test User')
  })

  test('should view and filter audit logs', async ({ page }) => {
    await navigateToAuditTrail(page)

    // Verify audit trail interface loads
    await expect(page.locator('[data-testid="audit-trail-title"]')).toContainText('Activity Logs')
    await expect(page.locator('[data-testid="activity-logs-table"]')).toBeVisible()

    // Test search functionality
    await page.fill('[data-testid="search-input"]', 'CREATE')
    await page.waitForTimeout(1000) // Wait for search to filter

    // Verify search results
    const rows = page.locator('[data-testid="activity-log-row"]')
    await expect(rows).toHaveCount(1)
    await expect(rows.first()).toContainText('CREATE')

    // Clear search
    await page.fill('[data-testid="search-input"]', '')
    await page.waitForTimeout(1000)

    // Test severity filter
    await page.click('[data-testid="severity-filter"]')
    await page.click('[data-testid="severity-option-INFO"]')
    await page.waitForTimeout(1000)

    // Verify filter results
    await expect(page.locator('[data-testid="activity-log-row"]')).toHaveCountGreaterThan(0)

    // Test category filter
    await page.click('[data-testid="category-filter"]')
    await page.click('[data-testid="category-option-AUTHENTICATION"]')
    await page.waitForTimeout(1000)

    // Verify authentication logs are shown
    await expect(page.locator('[data-testid="activity-logs-table"]')).toContainText('AUTHENTICATION')
  })

  test('should view record history in audit trail', async ({ page }) => {
    await navigateToAuditTrail(page)

    // Switch to audit trails tab
    await page.click('[data-testid="audit-trails-tab"]')
    await expect(page.locator('[data-testid="audit-trails-table"]')).toBeVisible()

    // Click on a record to view history
    await page.click('[data-testid="audit-trail-row"]:first-child [data-testid="view-history-button"]')
    await expect(page.locator('[data-testid="record-history-dialog"]')).toBeVisible()

    // Verify history dialog content
    await expect(page.locator('[data-testid="record-history-title"]')).toContainText('Record History')
    await expect(page.locator('[data-testid="history-timeline"]')).toBeVisible()

    // Check timeline tab
    await expect(page.locator('[data-testid="timeline-tab"]')).toBeVisible()
    await expect(page.locator('[data-testid="history-changes"]')).toBeVisible()

    // Check summary tab
    await page.click('[data-testid="summary-tab"]')
    await expect(page.locator('[data-testid="change-summary"]')).toBeVisible()

    // Close dialog
    await page.click('[data-testid="close-dialog-button"]')
    await expect(page.locator('[data-testid="record-history-dialog"]')).not.toBeVisible()
  })

  test('should handle security events and alerts', async ({ page }) => {
    // Simulate a security event by attempting unauthorized access
    await page.click('[data-testid="user-menu"]')
    await page.click('[data-testid="logout-button"]')
    await page.waitForURL('/login')

    // Attempt login with wrong credentials
    await page.fill('[data-testid="username"]', 'admin')
    await page.fill('[data-testid="password"]', 'wrongpassword')
    await page.click('[data-testid="login-button"]')

    // Verify failed login message
    await expect(page.locator('[data-testid="error-message"]')).toContainText('Invalid credentials')

    // Login with correct credentials
    await page.fill('[data-testid="password"]', 'admin123')
    await page.click('[data-testid="login-button"]')
    await page.waitForURL('/dashboard')

    // Check security events in audit trail
    await navigateToAuditTrail(page)
    await page.click('[data-testid="security-events-filter"]')
    await page.waitForTimeout(1000)

    // Verify security events are logged
    await expect(page.locator('[data-testid="activity-logs-table"]')).toContainText('FAILED_LOGIN')
    await expect(page.locator('[data-testid="activity-logs-table"]')).toContainText('LOGIN')

    // Check security badge
    await expect(page.locator('[data-testid="security-badge"]')).toBeVisible()
  })

  test('should handle role hierarchy visualization', async ({ page }) => {
    await navigateToRoleManagement(page)

    // Switch to hierarchy tab
    await page.click('[data-testid="hierarchy-tab"]')
    await expect(page.locator('[data-testid="role-hierarchy"]')).toBeVisible()

    // Verify hierarchy structure
    await expect(page.locator('[data-testid="hierarchy-title"]')).toContainText('Role Hierarchy')
    await expect(page.locator('[data-testid="hierarchy-tree"]')).toBeVisible()

    // Check parent-child relationships
    await expect(page.locator('[data-testid="role-node"]:has-text("Admin")')).toBeVisible()
    await expect(page.locator('[data-testid="role-node"]:has-text("Editor")')).toBeVisible()
    await expect(page.locator('[data-testid="role-node"]:has-text("Viewer")')).toBeVisible()

    // Verify priority indicators
    await expect(page.locator('[data-testid="priority-badge"]')).toHaveCountGreaterThan(0)
  })

  test('should handle error scenarios gracefully', async ({ page }) => {
    // Test network error handling
    await page.route('**/api/v1/roles**', route => route.abort())
    
    await navigateToRoleManagement(page)

    // Verify error handling
    await expect(page.locator('[data-testid="error-message"]')).toContainText('Error Loading Roles')
    await expect(page.locator('[data-testid="retry-button"]')).toBeVisible()

    // Test retry functionality
    await page.unroute('**/api/v1/roles**')
    await page.click('[data-testid="retry-button"]')
    
    // Verify recovery
    await expect(page.locator('[data-testid="roles-table"]')).toBeVisible()
  })

  test('should support pagination in audit logs', async ({ page }) => {
    await navigateToAuditTrail(page)

    // Verify pagination controls
    await expect(page.locator('[data-testid="pagination-info"]')).toBeVisible()
    await expect(page.locator('[data-testid="pagination-controls"]')).toBeVisible()

    // Test page navigation
    const nextButton = page.locator('[data-testid="next-page-button"]')
    if (await nextButton.isEnabled()) {
      await nextButton.click()
      await expect(page.locator('[data-testid="current-page"]')).toContainText('2')
    }

    // Test page size change
    await page.click('[data-testid="page-size-select"]')
    await page.click('[data-testid="page-size-option-50"]')
    await page.waitForTimeout(1000)

    // Verify page size change
    await expect(page.locator('[data-testid="activity-log-row"]')).toHaveCountLessThanOrEqual(50)
  })

  test('should handle real-time updates in audit trail', async ({ page }) => {
    await navigateToAuditTrail(page)

    // Enable auto-refresh
    await page.click('[data-testid="auto-refresh-toggle"]')
    await expect(page.locator('[data-testid="auto-refresh-indicator"]')).toBeVisible()

    // Perform an action that should generate logs
    await navigateToRoleManagement(page)
    await page.click('[data-testid="create-role-button"]')
    await page.fill('[data-testid="role-name-input"]', 'Real-time Test Role')
    await page.click('[data-testid="create-role-submit"]')

    // Go back to audit trail
    await navigateToAuditTrail(page)

    // Verify new log appears (with auto-refresh)
    await expect(page.locator('[data-testid="activity-logs-table"]')).toContainText('Real-time Test Role')
  })

  test.afterEach(async ({ page }) => {
    // Cleanup: Remove test data
    try {
      await navigateToRoleManagement(page)
      
      // Delete test roles if they exist
      const testRoleRow = page.locator('[data-testid="role-item"]:has-text("Test Manager")')
      if (await testRoleRow.isVisible()) {
        await testRoleRow.locator('[data-testid="delete-role-button"]').click()
        await page.click('[data-testid="confirm-delete"]')
      }
    } catch (error) {
      console.log('Cleanup failed:', error)
    }
  })
})