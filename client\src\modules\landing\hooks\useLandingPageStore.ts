/**
 * Zustand store for landing page client state
 */

import { create } from 'zustand'

interface LandingPageState {
  // UI state
  isHeroVisible: boolean
  isFeaturesVisible: boolean
  activeFeature: string | null

  // Interaction state
  hoveredFeature: string | null
  scrollProgress: number

  // Actions
  setHeroVisible: (visible: boolean) => void
  setFeaturesVisible: (visible: boolean) => void
  setActiveFeature: (featureId: string | null) => void
  setHoveredFeature: (featureId: string | null) => void
  setScrollProgress: (progress: number) => void
  resetState: () => void
}

export const useLandingPageStore = create<LandingPageState>((set) => ({
  // Initial state
  isHeroVisible: false,
  isFeaturesVisible: false,
  activeFeature: null,
  hoveredFeature: null,
  scrollProgress: 0,

  // Actions
  setHeroVisible: (visible) => set({ isHeroVisible: visible }),
  setFeaturesVisible: (visible) => set({ isFeaturesVisible: visible }),
  setActiveFeature: (featureId) => set({ activeFeature: featureId }),
  setHoveredFeature: (featureId) => set({ hoveredFeature: featureId }),
  setScrollProgress: (progress) => set({ scrollProgress: progress }),
  resetState: () =>
    set({
      isHeroVisible: false,
      isFeaturesVisible: false,
      activeFeature: null,
      hoveredFeature: null,
      scrollProgress: 0,
    }),
}))
