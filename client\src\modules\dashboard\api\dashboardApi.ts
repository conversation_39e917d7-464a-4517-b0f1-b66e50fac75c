/**
 * API functions for dashboard data
 */

import { apiClient } from '@/lib/api/client'
import type {
  DashboardData,
  DashboardMetrics,
  ProjectSummary,
  RecentCalculation,
  ProjectsApiParams,
  CalculationsApiParams,
  MetricsApiParams,
  DashboardApiResponse,
} from '../types'

/**
 * Fetch complete dashboard data
 */
export async function fetchDashboardData(): Promise<DashboardData> {
  try {
    const response = await apiClient.get<DashboardApiResponse>('/dashboard')
    return response.data
  } catch (error) {
    console.error('Failed to fetch dashboard data:', error)
    throw error
  }
}

/**
 * Fetch dashboard metrics
 */
export async function fetchDashboardMetrics(params?: MetricsApiParams): Promise<DashboardMetrics> {
  try {
    const response = await apiClient.get<DashboardMetrics>('/dashboard/metrics', { params })
    return response
  } catch (error) {
    console.error('Failed to fetch dashboard metrics:', error)
    throw error
  }
}

/**
 * Fetch projects for dashboard
 */
export async function fetchDashboardProjects(
  params?: ProjectsApiParams
): Promise<ProjectSummary[]> {
  try {
    const response = await apiClient.get<ProjectSummary[]>('/dashboard/projects', { params })
    return response
  } catch (error) {
    console.error('Failed to fetch dashboard projects:', error)
    throw error
  }
}

/**
 * Fetch recent calculations for dashboard
 */
export async function fetchDashboardCalculations(
  params?: CalculationsApiParams
): Promise<RecentCalculation[]> {
  try {
    const response = await apiClient.get<RecentCalculation[]>('/dashboard/calculations', { params })
    return response
  } catch (error) {
    console.error('Failed to fetch dashboard calculations:', error)
    throw error
  }
}

/**
 * Create a new project
 */
export async function createProject(
  projectData: Omit<ProjectSummary, 'id' | 'createdAt' | 'lastModified'>
): Promise<ProjectSummary> {
  try {
    const response = await apiClient.post<ProjectSummary>('/projects', projectData)
    return response
  } catch (error) {
    console.error('Failed to create project:', error)
    throw error
  }
}

/**
 * Update a project
 */
export async function updateProject(
  id: string,
  updates: Partial<ProjectSummary>
): Promise<ProjectSummary> {
  try {
    const response = await apiClient.put<ProjectSummary>(`/projects/${id}`, updates)
    return response
  } catch (error) {
    console.error('Failed to update project:', error)
    throw error
  }
}

/**
 * Delete a project
 */
export async function deleteProject(id: string): Promise<void> {
  try {
    await apiClient.delete(`/projects/${id}`)
  } catch (error) {
    console.error('Failed to delete project:', error)
    throw error
  }
}
