/**
 * Component Management Store
 * Zustand store for managing component UI state
 */

import { create } from 'zustand'
import { devtools, persist } from 'zustand/middleware'
import type {
  BulkOperationState,
  ComponentDisplayOptions,
  ComponentFilterState,
  ComponentListState,
  ComponentSearchState,
} from '../types'

interface ComponentStore {
  // List state
  listState: ComponentListState
  setListState: (state: Partial<ComponentListState>) => void
  updateFilters: (filters: Partial<ComponentFilterState>) => void
  clearFilters: () => void

  // Search state
  searchState: ComponentSearchState
  setSearchState: (state: Partial<ComponentSearchState>) => void
  addToSearchHistory: (query: string) => void
  clearSearchHistory: () => void

  // Bulk operations
  bulkState: BulkOperationState
  setBulkState: (state: Partial<BulkOperationState>) => void
  selectComponent: (id: number) => void
  deselectComponent: (id: number) => void
  selectAll: (ids: number[]) => void
  clearSelection: () => void

  // Display options
  displayOptions: ComponentDisplayOptions
  setDisplayOptions: (options: Partial<ComponentDisplayOptions>) => void
  toggleDisplayOption: (option: keyof ComponentDisplayOptions) => void

  // UI state
  sidebarOpen: boolean
  setSidebarOpen: (open: boolean) => void

  // Computed properties
  getActiveFilterCount: () => number
  getSelectedCount: () => number
  isComponentSelected: (id: number) => boolean

  // Reset all state
  reset: () => void
}

const defaultFilters: ComponentFilterState = {
  search_term: '',
  category: null,
  component_type: null,
  manufacturer: '',
  is_preferred: null,
  is_active: null,
  min_price: null,
  max_price: null,
  currency: 'EUR',
  stock_status: '',
}

const defaultListState: ComponentListState = {
  filters: defaultFilters,
  sortBy: 'name',
  sortOrder: 'asc',
  page: 1,
  pageSize: 20,
  viewMode: 'grid',
  selectedComponents: [],
}

const defaultSearchState: ComponentSearchState = {
  query: '',
  field: 'name',
  isSearching: false,
  isAdvancedMode: false,
  searchHistory: [],
  recentSearches: [],
  savedSearches: [],
  suggestions: [],
}

const defaultBulkState: BulkOperationState = {
  selectedIds: [],
  operation: null,
  isProcessing: false,
  progress: 0,
  results: [],
  errors: [],
}

const defaultDisplayOptions: ComponentDisplayOptions = {
  showImages: true,
  showSpecifications: true,
  showPricing: true,
  showPrices: true,
  showAvailability: true,
  compactMode: false,
}

export const useComponentStore = create<ComponentStore>()(
  devtools(
    persist(
      (set, get) => ({
        // List state
        listState: defaultListState,
        setListState: (state) =>
          set((prev) => ({
            listState: { ...prev.listState, ...state },
          })),

        updateFilters: (filters) =>
          set((prev) => ({
            listState: {
              ...prev.listState,
              filters: { ...prev.listState.filters, ...filters },
              page: 1, // Reset to first page when filters change
            },
          })),

        clearFilters: () =>
          set((prev) => ({
            listState: {
              ...prev.listState,
              filters: defaultFilters,
              page: 1,
            },
          })),

        // Search state
        searchState: defaultSearchState,
        setSearchState: (state) =>
          set((prev) => ({
            searchState: { ...prev.searchState, ...state },
          })),

        addToSearchHistory: (query) =>
          set((prev) => {
            const newEntry = { query, timestamp: new Date().toISOString() }
            const history = prev.searchState.searchHistory.filter((h) => h.query !== query)
            const recentSearches = prev.searchState.recentSearches.filter((h) => h.query !== query)
            return {
              searchState: {
                ...prev.searchState,
                searchHistory: [newEntry, ...history].slice(0, 10), // Keep last 10
                recentSearches: [newEntry, ...recentSearches].slice(0, 10), // Keep last 10
              },
            }
          }),

        clearSearchHistory: () =>
          set((prev) => ({
            searchState: {
              ...prev.searchState,
              searchHistory: [],
              recentSearches: [],
            },
          })),

        // Bulk operations
        bulkState: defaultBulkState,
        setBulkState: (state) =>
          set((prev) => ({
            bulkState: { ...prev.bulkState, ...state },
          })),

        selectComponent: (id) =>
          set((prev) => {
            // Validate ID - must be a positive number
            if (typeof id !== 'number' || id <= 0 || !Number.isInteger(id)) {
              return prev // Don't update state for invalid IDs
            }

            const selectedIds = prev.bulkState.selectedIds.includes(id)
              ? prev.bulkState.selectedIds
              : [...prev.bulkState.selectedIds, id]

            return {
              bulkState: { ...prev.bulkState, selectedIds },
              listState: { ...prev.listState, selectedComponents: selectedIds },
            }
          }),

        deselectComponent: (id) =>
          set((prev) => {
            const selectedIds = prev.bulkState.selectedIds.filter((selectedId) => selectedId !== id)

            return {
              bulkState: { ...prev.bulkState, selectedIds },
              listState: { ...prev.listState, selectedComponents: selectedIds },
            }
          }),

        selectAll: (ids) =>
          set((prev) => {
            // Filter out invalid IDs
            const validIds = ids.filter(
              (id) => typeof id === 'number' && id > 0 && Number.isInteger(id)
            )

            return {
              bulkState: { ...prev.bulkState, selectedIds: validIds },
              listState: { ...prev.listState, selectedComponents: validIds },
            }
          }),

        clearSelection: () =>
          set((prev) => ({
            bulkState: { ...prev.bulkState, selectedIds: [] },
            listState: { ...prev.listState, selectedComponents: [] },
          })),

        // Display options
        displayOptions: defaultDisplayOptions,
        setDisplayOptions: (options) =>
          set((prev) => ({
            displayOptions: { ...prev.displayOptions, ...options },
          })),

        toggleDisplayOption: (option) =>
          set((prev) => ({
            displayOptions: {
              ...prev.displayOptions,
              [option]: !prev.displayOptions[option],
            },
          })),

        // UI state
        sidebarOpen: true,
        setSidebarOpen: (open) => set({ sidebarOpen: open }),

        // Computed properties
        getActiveFilterCount: () => {
          const filters = get().listState.filters
          const defaults = defaultFilters
          return Object.entries(filters).filter(([key, value]) => {
            const defaultValue = defaults[key as keyof ComponentFilterState]
            return value !== null && value !== undefined && value !== '' && value !== defaultValue
          }).length
        },

        getSelectedCount: () => {
          return get().bulkState.selectedIds.length
        },

        isComponentSelected: (id) => {
          return get().bulkState.selectedIds.includes(id)
        },

        // Reset
        reset: () =>
          set({
            listState: defaultListState,
            searchState: defaultSearchState,
            bulkState: defaultBulkState,
            displayOptions: defaultDisplayOptions,
            sidebarOpen: true,
          }),
      }),
      {
        name: 'component-store',
        partialize: (state) => ({
          listState: {
            filters: state.listState.filters,
            sortBy: state.listState.sortBy,
            sortOrder: state.listState.sortOrder,
            pageSize: state.listState.pageSize,
            viewMode: state.listState.viewMode,
          },
          searchState: {
            searchHistory: state.searchState.searchHistory,
            savedSearches: state.searchState.savedSearches,
          },
          displayOptions: state.displayOptions,
          sidebarOpen: state.sidebarOpen,
        }),
      }
    ),
    { name: 'component-store' }
  )
)

// Selector hooks for better performance
export const useComponentFilters = () => useComponentStore((state) => state.listState.filters)
export const useComponentViewMode = () => useComponentStore((state) => state.listState.viewMode)
export const useComponentSelection = () => useComponentStore((state) => state.bulkState.selectedIds)
export const useComponentDisplayOptions = () => useComponentStore((state) => state.displayOptions)
