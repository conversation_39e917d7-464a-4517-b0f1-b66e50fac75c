/**
 * @vitest-environment jsdom
 */

import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { render, screen } from '@testing-library/react'
import { beforeEach, describe, expect, it, vi } from 'vitest'
import { LandingPage } from '../../components/LandingPage'

// Mock all the child components
vi.mock('@/components/common/Header', () => ({
  Header: () => <header data-testid="header">Header</header>,
}))

vi.mock('@/components/common/Footer', () => ({
  Footer: () => <footer data-testid="footer">Footer</footer>,
}))

vi.mock('../../components/HeroSection', () => ({
  HeroSection: ({ hero }: any) => <section data-testid="hero-section">Hero: {hero.title}</section>,
}))

vi.mock('../../components/FeaturesSection', () => ({
  FeaturesSection: ({ features }: any) => (
    <section data-testid="features-section">Features: {features.length}</section>
  ),
}))

vi.mock('../../components/TrustIndicators', () => ({
  TrustIndicators: ({ indicators }: any) => (
    <section data-testid="trust-indicators">Trust: {indicators.length}</section>
  ),
}))

vi.mock('../../components/CTASection', () => ({
  CTASection: ({ cta }: any) => <section data-testid="cta-section">CTA: {cta.title}</section>,
}))

// Mock dynamic imports
vi.mock('next/dynamic', () => ({
  default: (importFn: any, options: any) => {
    const Component = importFn().then(
      (mod: any) => mod.default || mod.FeaturesSection || mod.TrustIndicators || mod.CTASection
    )
    return (props: any) => {
      if (importFn.toString().includes('FeaturesSection')) {
        return (
          <section data-testid="features-section">Features: {props.features?.length || 0}</section>
        )
      }
      if (importFn.toString().includes('TrustIndicators')) {
        return (
          <section data-testid="trust-indicators">Trust: {props.indicators?.length || 0}</section>
        )
      }
      if (importFn.toString().includes('CTASection')) {
        return <section data-testid="cta-section">CTA: {props.cta?.title || 'Default'}</section>
      }
      return <div>Loading...</div>
    }
  },
}))

// Mock the landing page data hook
vi.mock('../../hooks/useLandingPageData', () => ({
  useLandingPageData: vi.fn(),
}))

// Mock the landing page store
vi.mock('../../hooks/useLandingPageStore', () => ({
  useLandingPageStore: () => ({
    resetState: vi.fn(),
  }),
}))

// Mock Intersection Observer
const mockIntersectionObserver = vi.fn()
mockIntersectionObserver.mockReturnValue({
  observe: () => null,
  unobserve: () => null,
  disconnect: () => null,
})
window.IntersectionObserver = mockIntersectionObserver

const createTestQueryClient = () =>
  new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
      },
    },
  })

const renderWithProviders = (component: React.ReactElement) => {
  const queryClient = createTestQueryClient()
  return render(<QueryClientProvider client={queryClient}>{component}</QueryClientProvider>)
}

const mockLandingPageData = {
  hero: {
    badge: { icon: 'check', text: 'Test Badge' },
    title: 'Test Title',
    subtitle: 'Test Subtitle',
    description: 'Test Description',
    backgroundPattern: true,
    floatingElements: true,
  },
  features: [
    {
      id: '1',
      title: 'Feature 1',
      description: 'Desc 1',
      icon: 'icon1',
      color: 'primary' as const,
    },
    {
      id: '2',
      title: 'Feature 2',
      description: 'Desc 2',
      icon: 'icon2',
      color: 'secondary' as const,
    },
  ],
  trustIndicators: [
    { id: '1', icon: 'icon1', label: 'Label 1', value: 'Value 1' },
    { id: '2', icon: 'icon2', label: 'Label 2', value: 'Value 2' },
  ],
  cta: {
    title: 'Test CTA Title',
    subtitle: 'Test CTA Subtitle',
    description: 'Test CTA Description',
    primaryAction: { label: 'Primary', href: '/primary' },
    secondaryAction: { label: 'Secondary', href: '/secondary' },
  },
}

const createMockQueryResult = (overrides = {}) => ({
  data: undefined,
  isLoading: false,
  error: null,
  isError: false,
  isPending: false,
  isLoadingError: false,
  isRefetchError: false,
  isSuccess: false,
  isStale: false,
  isFetching: false,
  isFetched: false,
  isPaused: false,
  isPlaceholderData: false,
  isRefetching: false,
  failureCount: 0,
  failureReason: null,
  errorUpdateCount: 0,
  dataUpdatedAt: 0,
  errorUpdatedAt: 0,
  fetchStatus: 'idle' as const,
  status: 'pending' as const,
  refetch: vi.fn(),
  remove: vi.fn(),
  promise: Promise.resolve(),
  ...overrides,
})

describe('LandingPage', () => {
  let mockUseLandingPageData: any

  beforeEach(async () => {
    vi.clearAllMocks()
    const landingModule = await import('../../hooks/useLandingPageData')
    mockUseLandingPageData = vi.mocked(landingModule.useLandingPageData)
  })

  it('renders all main sections when data is loaded', () => {
    mockUseLandingPageData.mockReturnValue(
      createMockQueryResult({
        data: mockLandingPageData,
        isLoading: false,
        isSuccess: true,
        status: 'success',
      })
    )

    renderWithProviders(<LandingPage />)

    expect(screen.getByTestId('header')).toBeInTheDocument()
    expect(screen.getByTestId('hero-section')).toBeInTheDocument()
    expect(screen.getByTestId('features-section')).toBeInTheDocument()
    expect(screen.getByTestId('trust-indicators')).toBeInTheDocument()
    expect(screen.getByTestId('cta-section')).toBeInTheDocument()
    expect(screen.getByTestId('footer')).toBeInTheDocument()
  })

  it('shows loading state when data is loading', () => {
    mockUseLandingPageData.mockReturnValue(
      createMockQueryResult({
        isLoading: true,
        status: 'pending',
      })
    )

    renderWithProviders(<LandingPage />)

    expect(screen.getByText('Loading...')).toBeInTheDocument()
    expect(screen.queryByTestId('hero-section')).not.toBeInTheDocument()
  })

  it('renders with default data when fetch fails', () => {
    mockUseLandingPageData.mockReturnValue(
      createMockQueryResult({
        error: new Error('Fetch failed'),
        isError: true,
        status: 'error',
      })
    )

    renderWithProviders(<LandingPage />)

    // Should still render sections with default data
    expect(screen.getByTestId('hero-section')).toBeInTheDocument()
    expect(screen.getByTestId('features-section')).toBeInTheDocument()
  })

  it('uses provided data prop when available', () => {
    mockUseLandingPageData.mockReturnValue(createMockQueryResult())

    const customData = {
      ...mockLandingPageData,
      hero: {
        ...mockLandingPageData.hero,
        title: 'Custom Title',
      },
    }

    renderWithProviders(<LandingPage data={customData} />)

    expect(screen.getByText('Hero: Custom Title')).toBeInTheDocument()
  })

  it('applies custom className when provided', () => {
    mockUseLandingPageData.mockReturnValue(
      createMockQueryResult({
        data: mockLandingPageData,
        isSuccess: true,
        status: 'success',
      })
    )

    const { container } = renderWithProviders(<LandingPage className="custom-landing-class" />)

    const mainDiv = container.querySelector('.custom-landing-class')
    expect(mainDiv).toBeInTheDocument()
  })

  it('includes structured data for SEO', () => {
    mockUseLandingPageData.mockReturnValue(
      createMockQueryResult({
        data: mockLandingPageData,
        isSuccess: true,
        status: 'success',
      })
    )

    const { container } = renderWithProviders(<LandingPage />)

    const structuredDataScript = container.querySelector('script[type="application/ld+json"]')
    expect(structuredDataScript).toBeInTheDocument()

    if (structuredDataScript) {
      const structuredData = JSON.parse(structuredDataScript.textContent || '{}')
      expect(structuredData['@context']).toBe('https://schema.org')
      expect(structuredData['@type']).toBe('SoftwareApplication')
      expect(structuredData.name).toBe('Ultimate Electrical Designer')
    }
  })

  it('has proper semantic HTML structure', () => {
    mockUseLandingPageData.mockReturnValue(
      createMockQueryResult({
        data: mockLandingPageData,
        isSuccess: true,
        status: 'success',
      })
    )

    renderWithProviders(<LandingPage />)

    expect(screen.getByRole('main')).toBeInTheDocument()
    expect(screen.getByTestId('header')).toBeInTheDocument()
    expect(screen.getByTestId('footer')).toBeInTheDocument()
  })

  it('includes aria-label attributes for sections', () => {
    mockUseLandingPageData.mockReturnValue(
      createMockQueryResult({
        data: mockLandingPageData,
        isSuccess: true,
        status: 'success',
      })
    )

    const { container } = renderWithProviders(<LandingPage />)

    // Check that sections have proper aria-labels (these would be on the actual section elements)
    const sections = container.querySelectorAll('section')
    expect(sections.length).toBeGreaterThan(0)
  })

  it('handles partial data gracefully', () => {
    mockUseLandingPageData.mockReturnValue(
      createMockQueryResult({
        data: {
          hero: mockLandingPageData.hero,
          features: [],
          trustIndicators: [],
          cta: mockLandingPageData.cta,
        },
        isSuccess: true,
        status: 'success',
      })
    )

    renderWithProviders(<LandingPage />)

    expect(screen.getByTestId('hero-section')).toBeInTheDocument()
    expect(screen.getByTestId('features-section')).toBeInTheDocument()
    expect(screen.getByText('Features: 0')).toBeInTheDocument()
    expect(screen.getByText('Trust: 0')).toBeInTheDocument()
  })
})
