/**
 * RegisterForm Component Tests
 * Comprehensive tests for the registration form component
 */

import { useAuth } from '@/hooks/useAuth'
import { act } from '@testing-library/react'
import { renderWithProviders, screen, userEvent, waitFor } from '@/test/utils'
import { beforeEach, describe, expect, it, vi } from 'vitest'
import { RegisterForm } from '../RegisterForm'

// Mock the useAuth hook
vi.mock('@/hooks/useAuth')

const mockUseAuth = vi.mocked(useAuth)

describe('RegisterForm Component', () => {
  const mockRegister = vi.fn()
  const mockOnSuccess = vi.fn()

  beforeEach(() => {
    vi.clearAllMocks()
    mockUseAuth.mockReturnValue({
      register: mockRegister,
      isRegisterPending: false,
      registerError: null,
      login: vi.fn(),
      isLoading: false,
      loginError: null,
      user: null,
      token: null,
      isAuthenticated: false,
      logout: vi.fn(),
      hasRole: vi.fn(),
      isAdmin: vi.fn(),
      requireAuth: vi.fn(),
      requireAdmin: vi.fn(),
      logoutError: null,
      isLoginPending: false,
      isLogoutPending: false,
    })
  })

  it('renders registration form fields', () => {
    renderWithProviders(<RegisterForm />)

    expect(screen.getByLabelText(/username/i)).toBeInTheDocument()
    expect(screen.getByLabelText(/email address/i)).toBeInTheDocument()
    expect(screen.getByLabelText(/^password$/i)).toBeInTheDocument()
    expect(screen.getByLabelText(/confirm password/i)).toBeInTheDocument()
    expect(screen.getByRole('button', { name: /create your account/i })).toBeInTheDocument()
  })

  it('validates required fields', async () => {
    const user = userEvent.setup()
    renderWithProviders(<RegisterForm />)

    const submitButton = screen.getByRole('button', { name: /create your account/i })

    // Submit the form with empty fields
    await act(async () => {
      await user.click(submitButton)
    })

    // Wait for validation errors to appear
    await waitFor(() => {
      expect(screen.getByText(/username must be at least 3 characters/i)).toBeInTheDocument()
    })

    await waitFor(() => {
      expect(screen.getByText(/please enter a valid email address/i)).toBeInTheDocument()
    })

    await waitFor(() => {
      expect(screen.getByText(/password must be at least 8 characters/i)).toBeInTheDocument()
    })

    expect(mockRegister).not.toHaveBeenCalled()
  })

  it('validates username format', async () => {
    const user = userEvent.setup()
    renderWithProviders(<RegisterForm />)

    const usernameInput = screen.getByLabelText(/username/i)
    const submitButton = screen.getByRole('button', { name: /create your account/i })

    await act(async () => {
      await user.type(usernameInput, 'ab') // Too short
      await user.click(submitButton)
    })

    await waitFor(() => {
      expect(screen.getByText(/username must be at least 3 characters/i)).toBeInTheDocument()
    })

    // Clear and try invalid characters
    await act(async () => {
      await user.clear(usernameInput)
      await user.type(usernameInput, 'user@name') // Invalid characters
      await user.click(submitButton)
    })

    await waitFor(() => {
      expect(
        screen.getByText(/username can only contain letters, numbers, underscores, and hyphens/i)
      ).toBeInTheDocument()
    })
  })

  it('validates email format', async () => {
    const user = userEvent.setup()
    renderWithProviders(<RegisterForm />)

    const emailInput = screen.getByLabelText(/email address/i)
    const submitButton = screen.getByRole('button', { name: /create your account/i })

    await act(async () => {
      await user.type(emailInput, 'invalid-email')
      await user.click(submitButton)
    })

    await waitFor(() => {
      expect(screen.getByText(/please enter a valid email address/i)).toBeInTheDocument()
    })
  })

  it('validates password strength requirements', async () => {
    const user = userEvent.setup()
    renderWithProviders(<RegisterForm />)

    const passwordInput = screen.getByLabelText(/^password$/i)
    const submitButton = screen.getByRole('button', { name: /create your account/i })

    // Test weak password
    await act(async () => {
      await user.type(passwordInput, 'password') // No uppercase, numbers, or special chars
      await user.click(submitButton)
    })

    await waitFor(() => {
      expect(
        screen.getByText(/password must contain at least one uppercase letter/i)
      ).toBeInTheDocument()
    })
  })

  it('validates password confirmation', async () => {
    const user = userEvent.setup()
    renderWithProviders(<RegisterForm />)

    const passwordInput = screen.getByLabelText(/^password$/i)
    const confirmPasswordInput = screen.getByLabelText(/confirm password/i)
    const submitButton = screen.getByRole('button', { name: /create your account/i })

    await act(async () => {
      await user.type(passwordInput, 'TestPassword123!')
      await user.type(confirmPasswordInput, 'DifferentPassword123!')
      await user.click(submitButton)
    })

    await waitFor(() => {
      expect(screen.getByText(/passwords do not match/i)).toBeInTheDocument()
    })
  })

  it('submits form with valid data', async () => {
    const user = userEvent.setup()
    mockRegister.mockResolvedValue({ id: 1, name: 'testuser', email: '<EMAIL>' })

    renderWithProviders(<RegisterForm onSuccess={mockOnSuccess} />)

    const usernameInput = screen.getByLabelText(/username/i)
    const emailInput = screen.getByLabelText(/email address/i)
    const passwordInput = screen.getByLabelText(/^password$/i)
    const confirmPasswordInput = screen.getByLabelText(/confirm password/i)
    const submitButton = screen.getByRole('button', { name: /create your account/i })

    await act(async () => {
      await user.type(usernameInput, 'testuser')
      await user.type(emailInput, '<EMAIL>')
      await user.type(passwordInput, 'TestPassword123!')
      await user.type(confirmPasswordInput, 'TestPassword123!')
      await user.click(submitButton)
    })

    await waitFor(() => {
      expect(mockRegister).toHaveBeenCalledWith({
        name: 'testuser',
        email: '<EMAIL>',
        password: 'TestPassword123!',
      })
    })

    expect(mockOnSuccess).toHaveBeenCalled()
  })

  it('displays loading state', () => {
    mockUseAuth.mockReturnValue({
      ...mockUseAuth(),
      isRegisterPending: true,
    })

    renderWithProviders(<RegisterForm />)

    expect(screen.getByText(/creating your account/i)).toBeInTheDocument()
    expect(screen.getByRole('button', { name: /creating your account/i })).toBeDisabled()
  })

  it('displays registration error', () => {
    const error = new Error('User already exists')
    mockUseAuth.mockReturnValue({
      ...mockUseAuth(),
      registerError: error,
    })

    renderWithProviders(<RegisterForm />)

    expect(screen.getByText(/registration failed/i)).toBeInTheDocument()
    expect(screen.getByText(/user already exists/i)).toBeInTheDocument()
  })

  it('shows password strength indicator', async () => {
    const user = userEvent.setup()
    renderWithProviders(<RegisterForm />)

    const passwordInput = screen.getByLabelText(/^password$/i)

    await act(async () => {
      await user.type(passwordInput, 'TestPassword123!')
    })

    // Password strength indicator should be visible
    await waitFor(() => {
      expect(screen.getByText(/password strength/i)).toBeInTheDocument()
    })
  })

  it('shows password match indicator', async () => {
    const user = userEvent.setup()
    renderWithProviders(<RegisterForm />)

    const passwordInput = screen.getByLabelText(/^password$/i)
    const confirmPasswordInput = screen.getByLabelText(/confirm password/i)

    await act(async () => {
      await user.type(passwordInput, 'TestPassword123!')
      await user.type(confirmPasswordInput, 'TestPassword123!')
    })

    // Password match indicator should show success
    await waitFor(() => {
      expect(screen.getByText(/passwords match/i)).toBeInTheDocument()
    })
  })

  it('clears field errors when user starts typing', async () => {
    const user = userEvent.setup()
    renderWithProviders(<RegisterForm />)

    const submitButton = screen.getByRole('button', { name: /create your account/i })

    // Submit to show errors
    await act(async () => {
      await user.click(submitButton)
    })

    // Wait for validation errors to appear
    await waitFor(() => {
      expect(screen.getByText(/username must be at least 3 characters/i)).toBeInTheDocument()
    })

    // Start typing in username field
    const usernameInput = screen.getByLabelText(/username/i)
    await act(async () => {
      await user.type(usernameInput, 'test')
    })

    // Username error should be cleared
    await waitFor(() => {
      expect(screen.queryByText(/username must be at least 3 characters/i)).not.toBeInTheDocument()
    })
  })

  it('shows terms and privacy policy links', () => {
    renderWithProviders(<RegisterForm />)

    expect(screen.getByText(/terms of service/i)).toBeInTheDocument()
    expect(screen.getByText(/privacy policy/i)).toBeInTheDocument()
  })

  it('shows security information', () => {
    renderWithProviders(<RegisterForm />)

    expect(screen.getByText(/enterprise-grade security/i)).toBeInTheDocument()
  })

  it('has proper accessibility attributes', () => {
    renderWithProviders(<RegisterForm />)

    const usernameInput = screen.getByLabelText(/username/i)
    const emailInput = screen.getByLabelText(/email address/i)
    const passwordInput = screen.getByLabelText(/^password$/i)
    const confirmPasswordInput = screen.getByLabelText(/confirm password/i)

    expect(usernameInput).toHaveAttribute('autoComplete', 'username')
    expect(emailInput).toHaveAttribute('autoComplete', 'email')
    expect(passwordInput).toHaveAttribute('autoComplete', 'new-password')
    expect(confirmPasswordInput).toHaveAttribute('autoComplete', 'new-password')
  })

  it('shows helper text for fields', () => {
    renderWithProviders(<RegisterForm />)

    expect(
      screen.getByText(/3-50 characters, letters, numbers, underscores, and hyphens only/i)
    ).toBeInTheDocument()
    expect(
      screen.getByText(/we'll use this to send you important account information/i)
    ).toBeInTheDocument()
  })
})
