/**
 * Utility functions for the dashboard domain
 */

import type {
  DashboardData,
  DashboardMetrics,
  ProjectSummary,
  RecentCalculation,
  QuickAction,
  ActivityItem,
  DashboardPreferences,
  DashboardWidget,
} from './types'

/**
 * Default dashboard data
 */
export const defaultDashboardData: DashboardData = {
  metrics: {
    totalProjects: 0,
    activeProjects: 0,
    completedCalculations: 0,
    recentActivity: 0,
    systemUptime: '99.9%',
    lastLogin: new Date().toISOString(),
  },
  projects: [],
  recentCalculations: [],
  quickActions: [
    {
      id: 'heat-tracing',
      title: 'Heat Tracing Design',
      description: 'Design and calculate heat tracing systems for industrial applications',
      icon: 'zap',
      color: 'primary',
      href: '/heat-tracing',
      category: 'calculation',
      isEnabled: true,
      requiresAuth: true,
    },
    {
      id: 'load-calculations',
      title: 'Load Calculations',
      description: 'Perform electrical load analysis and sizing calculations',
      icon: 'calculator',
      color: 'secondary',
      href: '/load-calculations',
      category: 'calculation',
      isEnabled: true,
      requiresAuth: true,
    },
    {
      id: 'cable-sizing',
      title: 'Cable Sizing',
      description: 'Calculate proper cable sizes for electrical installations',
      icon: 'cable',
      color: 'accent',
      href: '/cable-sizing',
      category: 'calculation',
      isEnabled: true,
      requiresAuth: true,
    },
    {
      id: 'new-project',
      title: 'New Project',
      description: 'Create a new electrical design project',
      icon: 'plus-circle',
      color: 'dark',
      href: '/projects/new',
      category: 'project',
      isEnabled: true,
      requiresAuth: true,
    },
    {
      id: 'project-management',
      title: 'Project Management',
      description: 'Manage and organize your electrical design projects',
      icon: 'folder',
      color: 'primary',
      href: '/projects',
      category: 'project',
      isEnabled: true,
      requiresAuth: true,
    },
    {
      id: 'documentation',
      title: 'Documentation',
      description: 'Generate professional reports and technical documentation',
      icon: 'file-text',
      color: 'secondary',
      href: '/documentation',
      category: 'documentation',
      isEnabled: true,
      requiresAuth: true,
    },
  ],
  widgets: [
    {
      id: 'metrics',
      title: 'Dashboard Metrics',
      type: 'metrics',
      position: { x: 0, y: 0, w: 12, h: 2 },
      isVisible: true,
      isCollapsible: false,
      isCollapsed: false,
      lastUpdated: new Date().toISOString(),
    },
    {
      id: 'quick-actions',
      title: 'Quick Actions',
      type: 'quick_actions',
      position: { x: 0, y: 2, w: 12, h: 3 },
      isVisible: true,
      isCollapsible: true,
      isCollapsed: false,
      lastUpdated: new Date().toISOString(),
    },
    {
      id: 'recent-projects',
      title: 'Recent Projects',
      type: 'projects',
      position: { x: 0, y: 5, w: 8, h: 4 },
      isVisible: true,
      isCollapsible: true,
      isCollapsed: false,
      refreshInterval: 300000, // 5 minutes
      lastUpdated: new Date().toISOString(),
    },
    {
      id: 'recent-calculations',
      title: 'Recent Calculations',
      type: 'calculations',
      position: { x: 8, y: 5, w: 4, h: 4 },
      isVisible: true,
      isCollapsible: true,
      isCollapsed: false,
      refreshInterval: 300000, // 5 minutes
      lastUpdated: new Date().toISOString(),
    },
  ],
  recentActivity: [],
  preferences: {
    layout: 'grid',
    theme: 'light',
    widgetOrder: ['metrics', 'quick-actions', 'recent-projects', 'recent-calculations'],
    hiddenWidgets: [],
    refreshInterval: 300000, // 5 minutes
    showWelcomeMessage: true,
    defaultProjectView: 'active',
    calculationHistoryLimit: 10,
  },
}

/**
 * Format project status for display
 */
export function formatProjectStatus(status: ProjectSummary['status']): string {
  const statusMap = {
    active: 'Active',
    completed: 'Completed',
    on_hold: 'On Hold',
    draft: 'Draft',
  }
  return statusMap[status] || status
}

/**
 * Get project status color
 */
export function getProjectStatusColor(status: ProjectSummary['status']): string {
  const colorMap = {
    active: 'text-green-600 bg-green-100',
    completed: 'text-blue-600 bg-blue-100',
    on_hold: 'text-yellow-600 bg-yellow-100',
    draft: 'text-gray-600 bg-gray-100',
  }
  return colorMap[status] || 'text-gray-600 bg-gray-100'
}

/**
 * Format calculation type for display
 */
export function formatCalculationType(type: RecentCalculation['type']): string {
  const typeMap = {
    heat_tracing: 'Heat Tracing',
    load_calculation: 'Load Calculation',
    cable_sizing: 'Cable Sizing',
  }
  return typeMap[type] || type
}

/**
 * Get calculation type color
 */
export function getCalculationTypeColor(type: RecentCalculation['type']): string {
  const colorMap = {
    heat_tracing: 'text-orange-600 bg-orange-100',
    load_calculation: 'text-blue-600 bg-blue-100',
    cable_sizing: 'text-purple-600 bg-purple-100',
  }
  return colorMap[type] || 'text-gray-600 bg-gray-100'
}

/**
 * Format project priority for display
 */
export function formatProjectPriority(priority: ProjectSummary['priority']): string {
  const priorityMap = {
    low: 'Low',
    medium: 'Medium',
    high: 'High',
    critical: 'Critical',
  }
  return priorityMap[priority] || priority
}

/**
 * Get project priority color
 */
export function getProjectPriorityColor(priority: ProjectSummary['priority']): string {
  const colorMap = {
    low: 'text-gray-600 bg-gray-100',
    medium: 'text-blue-600 bg-blue-100',
    high: 'text-orange-600 bg-orange-100',
    critical: 'text-red-600 bg-red-100',
  }
  return colorMap[priority] || 'text-gray-600 bg-gray-100'
}

/**
 * Get brand color class for quick actions
 */
export function getBrandColorClass(
  color: QuickAction['color'],
  type: 'bg' | 'text' | 'border' = 'bg'
): string {
  const colorMap = {
    primary: 'brand-primary',
    secondary: 'brand-secondary',
    accent: 'brand-accent',
    dark: 'brand-dark',
  }

  return `${type}-${colorMap[color]}`
}

/**
 * Format relative time (e.g., "2 hours ago")
 */
export function formatRelativeTime(dateString: string): string {
  const date = new Date(dateString)
  const now = new Date()
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000)

  if (diffInSeconds < 60) {
    return 'Just now'
  } else if (diffInSeconds < 3600) {
    const minutes = Math.floor(diffInSeconds / 60)
    return `${minutes} minute${minutes > 1 ? 's' : ''} ago`
  } else if (diffInSeconds < 86400) {
    const hours = Math.floor(diffInSeconds / 3600)
    return `${hours} hour${hours > 1 ? 's' : ''} ago`
  } else if (diffInSeconds < 604800) {
    const days = Math.floor(diffInSeconds / 86400)
    return `${days} day${days > 1 ? 's' : ''} ago`
  } else {
    return date.toLocaleDateString()
  }
}

/**
 * Format date for display
 */
export function formatDate(
  dateString: string,
  format: 'short' | 'long' | 'time' = 'short'
): string {
  const date = new Date(dateString)

  switch (format) {
    case 'short':
      return date.toLocaleDateString()
    case 'long':
      return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
      })
    case 'time':
      return date.toLocaleTimeString('en-US', {
        hour: '2-digit',
        minute: '2-digit',
      })
    default:
      return date.toLocaleDateString()
  }
}

/**
 * Calculate project progress percentage
 */
export function calculateProjectProgress(project: ProjectSummary): number {
  // This would typically be calculated based on completed tasks/milestones
  // For now, return the stored progress value
  return Math.max(0, Math.min(100, project.progress))
}

/**
 * Get project by ID
 */
export function getProjectById(projects: ProjectSummary[], id: string): ProjectSummary | undefined {
  return projects.find((project) => project.id === id)
}

/**
 * Get calculation by ID
 */
export function getCalculationById(
  calculations: RecentCalculation[],
  id: string
): RecentCalculation | undefined {
  return calculations.find((calculation) => calculation.id === id)
}

/**
 * Filter projects by status
 */
export function filterProjectsByStatus(
  projects: ProjectSummary[],
  status: ProjectSummary['status'] | 'all'
): ProjectSummary[] {
  if (status === 'all') {
    return projects
  }
  return projects.filter((project) => project.status === status)
}

/**
 * Filter calculations by type
 */
export function filterCalculationsByType(
  calculations: RecentCalculation[],
  type: RecentCalculation['type'] | 'all'
): RecentCalculation[] {
  if (type === 'all') {
    return calculations
  }
  return calculations.filter((calculation) => calculation.type === type)
}

/**
 * Search projects by name or description
 */
export function searchProjects(projects: ProjectSummary[], query: string): ProjectSummary[] {
  if (!query.trim()) {
    return projects
  }

  const lowercaseQuery = query.toLowerCase()
  return projects.filter(
    (project) =>
      project.name.toLowerCase().includes(lowercaseQuery) ||
      project.description.toLowerCase().includes(lowercaseQuery)
  )
}

/**
 * Sort projects by field
 */
export function sortProjects(
  projects: ProjectSummary[],
  sortBy: 'name' | 'created_at' | 'last_modified' | 'priority',
  order: 'asc' | 'desc' = 'desc'
): ProjectSummary[] {
  const sorted = [...projects].sort((a, b) => {
    let aValue: any
    let bValue: any

    switch (sortBy) {
      case 'name':
        aValue = a.name.toLowerCase()
        bValue = b.name.toLowerCase()
        break
      case 'created_at':
        aValue = new Date(a.createdAt).getTime()
        bValue = new Date(b.createdAt).getTime()
        break
      case 'last_modified':
        aValue = new Date(a.lastModified).getTime()
        bValue = new Date(b.lastModified).getTime()
        break
      case 'priority':
        const priorityOrder = { low: 1, medium: 2, high: 3, critical: 4 }
        aValue = priorityOrder[a.priority]
        bValue = priorityOrder[b.priority]
        break
      default:
        return 0
    }

    if (aValue < bValue) return order === 'asc' ? -1 : 1
    if (aValue > bValue) return order === 'asc' ? 1 : -1
    return 0
  })

  return sorted
}

/**
 * Generate dashboard metrics summary
 */
export function generateMetricsSummary(data: DashboardData): string {
  const { metrics, projects, recentCalculations } = data
  return `${metrics.totalProjects} projects, ${metrics.completedCalculations} calculations, ${recentCalculations.length} recent activities`
}

/**
 * Check if widget should be refreshed
 */
export function shouldRefreshWidget(widget: DashboardWidget): boolean {
  if (!widget.refreshInterval) {
    return false
  }

  const lastUpdated = new Date(widget.lastUpdated).getTime()
  const now = new Date().getTime()
  const timeSinceUpdate = now - lastUpdated

  return timeSinceUpdate >= widget.refreshInterval
}

/**
 * Generate activity description
 */
export function generateActivityDescription(activity: ActivityItem): string {
  switch (activity.type) {
    case 'project_created':
      return `Created project "${activity.metadata?.projectName || 'Unknown'}"`
    case 'calculation_completed':
      return `Completed ${activity.metadata?.calculationType || 'calculation'} for project "${activity.metadata?.projectName || 'Unknown'}"`
    case 'user_login':
      return 'Logged into the system'
    case 'system_update':
      return `System updated: ${activity.metadata?.updateType || 'General update'}`
    default:
      return activity.description
  }
}
