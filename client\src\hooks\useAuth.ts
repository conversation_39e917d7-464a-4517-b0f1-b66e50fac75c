'use client'

/**
 * Main authentication hook that combines Zustand store and React Query
 */

import { useCurrentUser, useLogin, useLogout, useRegister } from '@/hooks/api/useAuth'
import { TokenManager } from '@/lib/auth/tokenManager'
import { useAuthStore } from '@/stores/authStore'
import type { LoginRequest, RegisterRequest } from '@/types/api'
import { useRouter } from 'next/navigation'
import { useEffect } from 'react'

export function useAuth() {
  const router = useRouter()
  const {
    user,
    token,
    isAuthenticated,
    isLoading,
    setAuth,
    clearAuth,
    setLoading,
    initializeAuth,
  } = useAuthStore()

  // React Query hooks
  const loginMutation = useLogin()
  const logoutMutation = useLogout()
  const registerMutation = useRegister()
  const { data: currentUser, isLoading: isUserLoading, error: userError } = useCurrentUser()

  // Initialize auth on mount
  useEffect(() => {
    initializeAuth()
    TokenManager.initializeTokens()
  }, [initializeAuth])

  // Sync current user data with store
  useEffect(() => {
    if (currentUser && isAuthenticated) {
      // Update user data in store if it's different
      if (JSON.stringify(user) !== JSON.stringify(currentUser)) {
        useAuthStore.getState().updateUser(currentUser)
      }
    }
  }, [currentUser, isAuthenticated, user])

  // Handle user fetch errors (token might be invalid)
  useEffect(() => {
    if (userError && isAuthenticated) {
      // If we get an auth error while supposedly authenticated, clear auth
      console.warn('Authentication error, clearing auth state:', userError)
      clearAuth()
      TokenManager.clearTokens()
    }
  }, [userError, isAuthenticated, clearAuth])

  /**
   * Login function
   */
  const login = async (credentials: LoginRequest) => {
    setLoading(true)
    try {
      const response = await loginMutation.mutateAsync(credentials)

      // Store tokens (API client token is set by useLogin hook)
      TokenManager.setAccessToken(response.access_token)

      // Update auth state
      setAuth(response.user, response.access_token)

      return response
    } catch (error) {
      clearAuth()
      TokenManager.clearTokens()
      throw error
    } finally {
      setLoading(false)
    }
  }

  /**
   * Logout function
   */
  const logout = async () => {
    setLoading(true)
    try {
      await logoutMutation.mutateAsync()
    } catch (error) {
      // Even if server logout fails, clear local state
      console.warn('Server logout failed, clearing local state:', error)
    } finally {
      clearAuth()
      TokenManager.clearTokens()
      setLoading(false)
      router.push('/login')
    }
  }

  /**
   * Register function
   */
  const register = async (userData: RegisterRequest) => {
    setLoading(true)
    try {
      const response = await registerMutation.mutateAsync(userData)
      return response
    } catch (error) {
      throw error
    } finally {
      setLoading(false)
    }
  }

  /**
   * Check if user has specific role
   */
  const hasRole = (role: string): boolean => {
    return user?.role === role
  }

  /**
   * Check if user is admin
   */
  const isAdmin = (): boolean => {
    return user?.is_admin === true || user?.role === 'ADMIN'
  }

  /**
   * Require authentication (redirect to login if not authenticated)
   */
  const requireAuth = () => {
    if (!isAuthenticated && !isLoading) {
      router.push('/login')
      return false
    }
    return true
  }

  /**
   * Require admin role (redirect if not admin)
   */
  const requireAdmin = () => {
    if (!requireAuth()) {
      return false
    }

    if (!isAdmin()) {
      router.push('/dashboard') // Redirect to dashboard instead of login
      return false
    }

    return true
  }

  return {
    // State
    user,
    token,
    isAuthenticated,
    isLoading:
      isLoading ||
      isUserLoading ||
      loginMutation.isPending ||
      logoutMutation.isPending ||
      registerMutation.isPending,

    // Actions
    login,
    logout,
    register,

    // Utilities
    hasRole,
    isAdmin,
    requireAuth,
    requireAdmin,

    // Mutation states
    loginError: loginMutation.error,
    logoutError: logoutMutation.error,
    registerError: registerMutation.error,
    isLoginPending: loginMutation.isPending,
    isLogoutPending: logoutMutation.isPending,
    isRegisterPending: registerMutation.isPending,
  }
}
