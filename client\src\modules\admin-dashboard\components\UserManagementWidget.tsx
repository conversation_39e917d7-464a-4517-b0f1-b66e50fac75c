/**
 * User management widget for admin dashboard
 */

'use client'

import React, { useState } from 'react'
import { UserManagementWidgetProps } from '../types'
import { formatUserRole, getRoleColor, formatRelativeTime } from '../utils'

export function UserManagementWidget({
  users,
  isLoading,
  onUserClick,
  onCreateUser,
  onBulkAction,
  className = '',
}: UserManagementWidgetProps) {
  const [selectedUsers, setSelectedUsers] = useState<string[]>([])
  const [showBulkActions, setShowBulkActions] = useState(false)

  // Calculate user statistics
  const stats = {
    total: users.length,
    active: users.filter((u) => u.isActive).length,
    inactive: users.filter((u) => !u.isActive).length,
    admins: users.filter((u) => u.role === 'ADMIN').length,
    editors: users.filter((u) => u.role === 'EDITOR').length,
    viewers: users.filter((u) => u.role === 'VIEWER').length,
  }

  const handleSelectUser = (userId: string) => {
    setSelectedUsers((prev) =>
      prev.includes(userId) ? prev.filter((id) => id !== userId) : [...prev, userId]
    )
  }

  const handleSelectAll = () => {
    if (selectedUsers.length === users.length) {
      setSelectedUsers([])
    } else {
      setSelectedUsers(users.map((u) => u.id))
    }
  }

  const handleBulkAction = (action: string) => {
    if (onBulkAction && selectedUsers.length > 0) {
      onBulkAction(action, selectedUsers)
      setSelectedUsers([])
      setShowBulkActions(false)
    }
  }

  if (isLoading) {
    return (
      <div className={`overflow-hidden rounded-lg bg-white shadow ${className}`}>
        <div className="p-6">
          <div className="animate-pulse">
            <div className="mb-4 h-6 w-1/2 rounded bg-gray-200"></div>
            <div className="space-y-3">
              {Array.from({ length: 5 }).map((_, i) => (
                <div key={i} className="flex items-center space-x-3">
                  <div className="h-4 w-4 rounded bg-gray-200"></div>
                  <div className="h-4 w-1/3 rounded bg-gray-200"></div>
                  <div className="h-4 w-1/4 rounded bg-gray-200"></div>
                  <div className="h-4 w-1/5 rounded bg-gray-200"></div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className={`overflow-hidden rounded-lg bg-white shadow ${className}`}>
      <div className="p-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-medium text-gray-900">User Management</h3>
          <div className="flex items-center space-x-2">
            {selectedUsers.length > 0 && (
              <div className="relative">
                <button
                  onClick={() => setShowBulkActions(!showBulkActions)}
                  className="inline-flex items-center rounded-md bg-blue-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-blue-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-blue-600"
                >
                  Bulk Actions ({selectedUsers.length})
                  <svg
                    className="ml-1 h-4 w-4"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M19 9l-7 7-7-7"
                    />
                  </svg>
                </button>

                {showBulkActions && (
                  <div className="absolute right-0 z-10 mt-2 w-48 rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5">
                    <button
                      onClick={() => handleBulkAction('activate')}
                      className="block w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100"
                    >
                      Activate Users
                    </button>
                    <button
                      onClick={() => handleBulkAction('deactivate')}
                      className="block w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100"
                    >
                      Deactivate Users
                    </button>
                    <button
                      onClick={() => handleBulkAction('change_role')}
                      className="block w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100"
                    >
                      Change Role
                    </button>
                  </div>
                )}
              </div>
            )}
            {onCreateUser && (
              <button
                onClick={onCreateUser}
                className="inline-flex items-center rounded-md bg-blue-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-blue-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-blue-600"
              >
                <svg className="mr-1 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 4v16m8-8H4"
                  />
                </svg>
                Add User
              </button>
            )}
          </div>
        </div>

        {/* Statistics */}
        <div className="mt-6 grid grid-cols-3 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-gray-900">{stats.total}</div>
            <div className="text-sm text-gray-500">Total Users</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">{stats.active}</div>
            <div className="text-sm text-gray-500">Active</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-red-600">{stats.inactive}</div>
            <div className="text-sm text-gray-500">Inactive</div>
          </div>
        </div>

        {/* Role Distribution */}
        <div className="mt-6">
          <h4 className="mb-3 text-sm font-medium text-gray-900">Role Distribution</h4>
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Administrators</span>
              <span className="inline-flex items-center rounded-full bg-red-100 px-2.5 py-0.5 text-xs font-medium text-red-800">
                {stats.admins}
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Editors</span>
              <span className="inline-flex items-center rounded-full bg-yellow-100 px-2.5 py-0.5 text-xs font-medium text-yellow-800">
                {stats.editors}
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Viewers</span>
              <span className="inline-flex items-center rounded-full bg-green-100 px-2.5 py-0.5 text-xs font-medium text-green-800">
                {stats.viewers}
              </span>
            </div>
          </div>
        </div>

        {/* Recent Users List */}
        <div className="mt-6">
          <div className="mb-3 flex items-center justify-between">
            <h4 className="text-sm font-medium text-gray-900">Recent Users</h4>
            <label className="flex items-center text-sm text-gray-600">
              <input
                type="checkbox"
                checked={selectedUsers.length === users.length && users.length > 0}
                onChange={handleSelectAll}
                className="mr-2 h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              Select All
            </label>
          </div>

          <div className="max-h-64 space-y-3 overflow-y-auto">
            {users.slice(0, 10).map((user) => (
              <div
                key={user.id}
                className={`flex items-center space-x-3 rounded-lg p-3 transition-colors ${
                  selectedUsers.includes(user.id) ? 'bg-blue-50' : 'hover:bg-gray-50'
                }`}
              >
                <input
                  type="checkbox"
                  checked={selectedUsers.includes(user.id)}
                  onChange={() => handleSelectUser(user.id)}
                  className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />

                <div className="min-w-0 flex-1">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <p className="truncate text-sm font-medium text-gray-900">{user.name}</p>
                      <span
                        className={`inline-flex items-center rounded-full px-2 py-1 text-xs font-medium ${getRoleColor(user.role)}`}
                      >
                        {formatUserRole(user.role)}
                      </span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span
                        className={`inline-flex items-center rounded-full px-2 py-1 text-xs font-medium ${
                          user.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                        }`}
                      >
                        {user.isActive ? 'Active' : 'Inactive'}
                      </span>
                    </div>
                  </div>
                  <div className="mt-1 flex items-center justify-between">
                    <p className="truncate text-sm text-gray-500">{user.email}</p>
                    <p className="text-xs text-gray-400">
                      {user.lastLogin
                        ? `Last login ${formatRelativeTime(user.lastLogin)}`
                        : 'Never logged in'}
                    </p>
                  </div>
                  <div className="mt-1 flex items-center space-x-4 text-xs text-gray-500">
                    <span>{user.projectCount} projects</span>
                    <span>{user.calculationCount} calculations</span>
                    <span>{user.loginCount} logins</span>
                  </div>
                </div>

                {onUserClick && (
                  <button
                    onClick={() => onUserClick(user)}
                    className="rounded-md p-1 text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    aria-label={`View ${user.name} details`}
                  >
                    <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M9 5l7 7-7 7"
                      />
                    </svg>
                  </button>
                )}
              </div>
            ))}
          </div>

          {users.length > 10 && (
            <div className="mt-3 text-center">
              <button className="text-sm text-blue-600 hover:text-blue-500">
                View all {users.length} users
              </button>
            </div>
          )}

          {users.length === 0 && (
            <div className="py-6 text-center">
              <svg
                className="mx-auto h-12 w-12 text-gray-400"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"
                />
              </svg>
              <h3 className="mt-2 text-sm font-medium text-gray-900">No users found</h3>
              <p className="mt-1 text-sm text-gray-500">Get started by creating a new user.</p>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
