# Testing Strategy

This document outlines the comprehensive testing strategy for the Components module, including unit tests, integration tests, E2E tests, and coverage requirements targeting 95%+ coverage.

## Testing Pyramid

### 1. Unit Tests (70% of tests)

- **Framework**: Vitest + React Testing Library
- **Target**: Individual components, hooks, utilities
- **Coverage**: 95%+ for critical paths
- **Speed**: Fast execution (< 1s per test)

### 2. Integration Tests (20% of tests)

- **Framework**: Vitest + React Testing Library + MSW
- **Target**: Component interactions, API integration
- **Coverage**: Critical user workflows
- **Speed**: Medium execution (1-5s per test)

### 3. E2E Tests (10% of tests)

- **Framework**: Playwright
- **Target**: Complete user journeys
- **Coverage**: Critical business flows
- **Speed**: Slower execution (10-30s per test)

## Unit Testing Strategy

### Component Testing

#### Atoms Testing

```typescript
// Example: ComponentBadge.test.tsx
describe('ComponentBadge', () => {
  it('renders with correct status styling', () => {
    render(<ComponentBadge status="active" />);
    expect(screen.getByRole('status')).toHaveClass('bg-green-100');
  });

  it('supports custom className', () => {
    render(<ComponentBadge status="active" className="custom-class" />);
    expect(screen.getByRole('status')).toHaveClass('custom-class');
  });

  it('meets accessibility requirements', async () => {
    const { container } = render(<ComponentBadge status="active" />);
    const results = await axe(container);
    expect(results).toHaveNoViolations();
  });
});
```

#### Molecules Testing

```typescript
// Example: ComponentCard.test.tsx
describe('ComponentCard', () => {
  const mockComponent = createMockComponent();
  const mockHandlers = {
    onSelect: vi.fn(),
    onEdit: vi.fn(),
    onDelete: vi.fn(),
    onView: vi.fn(),
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders component information correctly', () => {
    render(<ComponentCard component={mockComponent} {...mockHandlers} />);

    expect(screen.getByText(mockComponent.name)).toBeInTheDocument();
    expect(screen.getByText(mockComponent.manufacturer)).toBeInTheDocument();
  });

  it('handles selection interaction', async () => {
    const user = userEvent.setup();
    render(<ComponentCard component={mockComponent} {...mockHandlers} />);

    const checkbox = screen.getByRole('checkbox');
    await user.click(checkbox);

    expect(mockHandlers.onSelect).toHaveBeenCalledWith(mockComponent);
  });

  it('supports keyboard navigation', async () => {
    const user = userEvent.setup();
    render(<ComponentCard component={mockComponent} {...mockHandlers} />);

    const card = screen.getByRole('article');
    await user.tab();
    expect(card).toHaveFocus();

    await user.keyboard('{Enter}');
    expect(mockHandlers.onView).toHaveBeenCalledWith(mockComponent);
  });
});
```

#### Organisms Testing

```typescript
// Example: ComponentList.test.tsx
describe('ComponentList', () => {
  const mockComponents = createMockComponentList(10);

  it('renders component list with pagination', () => {
    render(
      <ComponentList
        components={mockComponents}
        total={100}
        page={1}
        pageSize={10}
      />
    );

    expect(screen.getAllByRole('article')).toHaveLength(10);
    expect(screen.getByRole('navigation', { name: /pagination/i })).toBeInTheDocument();
  });

  it('handles bulk selection', async () => {
    const user = userEvent.setup();
    const onBulkSelect = vi.fn();

    render(
      <ComponentList
        components={mockComponents}
        onBulkSelect={onBulkSelect}
      />
    );

    const selectAllCheckbox = screen.getByRole('checkbox', { name: /select all/i });
    await user.click(selectAllCheckbox);

    expect(onBulkSelect).toHaveBeenCalledWith(mockComponents.map(c => c.id));
  });
});
```

### Hook Testing

```typescript
// Example: useComponentStore.test.tsx
describe('useComponentStore', () => {
  beforeEach(() => {
    useComponentStore.getState().reset()
  })

  it('initializes with default state', () => {
    const { result } = renderHook(() => useComponentStore())

    expect(result.current.listState.viewMode).toBe('grid')
    expect(result.current.listState.selectedIds).toEqual([])
    expect(result.current.searchState.query).toBe('')
  })

  it('updates filters correctly', () => {
    const { result } = renderHook(() => useComponentStore())

    act(() => {
      result.current.updateFilters({ manufacturer: 'Siemens' })
    })

    expect(result.current.listState.filters.manufacturer).toBe('Siemens')
    expect(result.current.listState.pagination.page).toBe(1) // Reset to first page
  })

  it('manages component selection', () => {
    const { result } = renderHook(() => useComponentStore())

    act(() => {
      result.current.selectComponent(1)
      result.current.selectComponent(2)
    })

    expect(result.current.listState.selectedIds).toEqual([1, 2])

    act(() => {
      result.current.deselectComponent(1)
    })

    expect(result.current.listState.selectedIds).toEqual([2])
  })
})
```

### Utility Testing

```typescript
// Example: validation.test.ts
describe('Component Validation', () => {
  describe('validateComponent', () => {
    it('validates required fields', () => {
      const invalidComponent = { name: '' }
      const result = validateComponent(invalidComponent)

      expect(result.isValid).toBe(false)
      expect(result.errors).toContainEqual({
        field: 'name',
        message: 'Name is required',
        code: 'REQUIRED',
      })
    })

    it('validates field lengths', () => {
      const component = { name: 'a'.repeat(201) }
      const result = validateComponent(component)

      expect(result.isValid).toBe(false)
      expect(result.errors).toContainEqual({
        field: 'name',
        message: 'Name must not exceed 200 characters',
        code: 'MAX_LENGTH',
      })
    })

    it('validates price format', () => {
      const component = { unit_price: -10 }
      const result = validateComponent(component)

      expect(result.isValid).toBe(false)
      expect(result.errors).toContainEqual({
        field: 'unit_price',
        message: 'Price must be positive',
        code: 'INVALID_NUMBER',
      })
    })
  })
})
```

## Integration Testing Strategy

### API Integration Testing

```typescript
// Example: componentApi.integration.test.tsx
describe('Component API Integration', () => {
  beforeEach(() => {
    server.resetHandlers()
  })

  it('fetches components with filters', async () => {
    server.use(
      rest.get('/api/v1/components', (req, res, ctx) => {
        const manufacturer = req.url.searchParams.get('manufacturer')
        return res(ctx.json(createMockComponentResponse({ manufacturer })))
      })
    )

    const { result } = renderHook(() => useComponents({ manufacturer: 'Siemens' }))

    await waitFor(() => {
      expect(result.current.isSuccess).toBe(true)
    })

    expect(result.current.data?.items).toHaveLength(10)
    expect(result.current.data?.items[0].manufacturer).toBe('Siemens')
  })

  it('handles API errors gracefully', async () => {
    server.use(
      rest.get('/api/v1/components', (req, res, ctx) => {
        return res(ctx.status(500), ctx.json({ detail: 'Server error' }))
      })
    )

    const { result } = renderHook(() => useComponents())

    await waitFor(() => {
      expect(result.current.isError).toBe(true)
    })

    expect(result.current.error?.message).toBe('Server error')
  })
})
```

### Component Integration Testing

```typescript
// Example: ComponentBrowser.integration.test.tsx
describe('ComponentBrowser Integration', () => {
  it('filters components by manufacturer', async () => {
    const user = userEvent.setup();

    render(<ComponentBrowser />);

    // Wait for initial load
    await waitFor(() => {
      expect(screen.getAllByRole('article')).toHaveLength(20);
    });

    // Apply manufacturer filter
    const manufacturerSelect = screen.getByRole('combobox', { name: /manufacturer/i });
    await user.click(manufacturerSelect);
    await user.click(screen.getByRole('option', { name: 'Siemens' }));

    // Verify filtered results
    await waitFor(() => {
      const components = screen.getAllByRole('article');
      components.forEach(component => {
        expect(component).toHaveTextContent('Siemens');
      });
    });
  });

  it('performs bulk operations', async () => {
    const user = userEvent.setup();

    render(<ComponentBrowser />);

    await waitFor(() => {
      expect(screen.getAllByRole('article')).toHaveLength(20);
    });

    // Select multiple components
    const checkboxes = screen.getAllByRole('checkbox', { name: /select component/i });
    await user.click(checkboxes[0]);
    await user.click(checkboxes[1]);

    // Open bulk actions
    const bulkActionsButton = screen.getByRole('button', { name: /bulk actions/i });
    await user.click(bulkActionsButton);

    // Perform bulk delete
    const deleteButton = screen.getByRole('button', { name: /delete selected/i });
    await user.click(deleteButton);

    // Confirm deletion
    const confirmButton = screen.getByRole('button', { name: /confirm/i });
    await user.click(confirmButton);

    // Verify components are removed
    await waitFor(() => {
      expect(screen.getAllByRole('article')).toHaveLength(18);
    });
  });
});
```

## E2E Testing Strategy

### Critical User Journeys

```typescript
// Example: component-management.e2e.ts
test.describe('Component Management', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/components')
    await page.waitForLoadState('networkidle')
  })

  test('creates a new component', async ({ page }) => {
    // Navigate to create form
    await page.click('[data-testid="create-component-button"]')
    await page.waitForSelector('[data-testid="component-form"]')

    // Fill form
    await page.fill('[data-testid="name-input"]', 'Test Component')
    await page.fill('[data-testid="manufacturer-input"]', 'Test Manufacturer')
    await page.fill('[data-testid="model-number-input"]', 'TM-001')
    await page.selectOption('[data-testid="category-select"]', 'RESISTOR')
    await page.fill('[data-testid="price-input"]', '10.50')

    // Submit form
    await page.click('[data-testid="submit-button"]')

    // Verify success
    await expect(page.locator('[data-testid="success-message"]')).toBeVisible()
    await expect(page.locator('text=Test Component')).toBeVisible()
  })

  test('searches and filters components', async ({ page }) => {
    // Perform search
    await page.fill('[data-testid="search-input"]', 'Siemens')
    await page.press('[data-testid="search-input"]', 'Enter')

    // Wait for results
    await page.waitForSelector('[data-testid="component-list"]')

    // Verify search results
    const components = page.locator('[data-testid^="component-card-"]')
    await expect(components.first()).toContainText('Siemens')

    // Apply additional filter
    await page.selectOption('[data-testid="category-filter"]', 'SWITCH')

    // Verify filtered results
    await page.waitForLoadState('networkidle')
    const filteredComponents = page.locator('[data-testid^="component-card-"]')
    await expect(filteredComponents.first()).toContainText('SWITCH')
  })

  test('manages component lifecycle', async ({ page }) => {
    // Create component
    await page.click('[data-testid="create-component-button"]')
    await page.fill('[data-testid="name-input"]', 'Lifecycle Test')
    await page.fill('[data-testid="manufacturer-input"]', 'Test Corp')
    await page.fill('[data-testid="model-number-input"]', 'LT-001')
    await page.selectOption('[data-testid="category-select"]', 'CAPACITOR')
    await page.click('[data-testid="submit-button"]')

    // Edit component
    await page.click('[data-testid="edit-component-1"]')
    await page.fill('[data-testid="name-input"]', 'Updated Lifecycle Test')
    await page.click('[data-testid="submit-button"]')

    // Verify update
    await expect(page.locator('text=Updated Lifecycle Test')).toBeVisible()

    // Delete component
    await page.click('[data-testid="delete-component-1"]')
    await page.click('[data-testid="confirm-delete"]')

    // Verify deletion
    await expect(page.locator('text=Updated Lifecycle Test')).not.toBeVisible()
  })
})
```

### Accessibility Testing

```typescript
// Example: accessibility.e2e.ts
test.describe('Accessibility', () => {
  test('meets WCAG 2.1 AA standards', async ({ page }) => {
    await page.goto('/components')

    const accessibilityScanResults = await new AxeBuilder({ page }).analyze()
    expect(accessibilityScanResults.violations).toEqual([])
  })

  test('supports keyboard navigation', async ({ page }) => {
    await page.goto('/components')

    // Tab through interactive elements
    await page.keyboard.press('Tab')
    await expect(page.locator('[data-testid="search-input"]')).toBeFocused()

    await page.keyboard.press('Tab')
    await expect(page.locator('[data-testid="create-component-button"]')).toBeFocused()

    // Navigate component cards
    await page.keyboard.press('Tab')
    await expect(page.locator('[data-testid="component-card-1"]')).toBeFocused()

    // Activate with Enter
    await page.keyboard.press('Enter')
    await expect(page.locator('[data-testid="component-details"]')).toBeVisible()
  })

  test('works with screen readers', async ({ page }) => {
    await page.goto('/components')

    // Check ARIA labels
    await expect(page.locator('[aria-label="Search components"]')).toBeVisible()
    await expect(page.locator('[role="main"]')).toBeVisible()
    await expect(page.locator('[role="navigation"]')).toBeVisible()

    // Check live regions
    await page.fill('[data-testid="search-input"]', 'test')
    await expect(page.locator('[aria-live="polite"]')).toContainText('results')
  })
})
```

## Coverage Requirements

### Coverage Targets

- **Overall**: 95%+ code coverage
- **Critical Paths**: 100% coverage
- **Components**: 90%+ coverage
- **Hooks**: 95%+ coverage
- **Utilities**: 100% coverage

### Coverage Configuration

```typescript
// vitest.config.ts
export default defineConfig({
  test: {
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json', 'html'],
      thresholds: {
        global: {
          branches: 95,
          functions: 95,
          lines: 95,
          statements: 95,
        },
        'src/modules/components/utils/': {
          branches: 100,
          functions: 100,
          lines: 100,
          statements: 100,
        },
        'src/modules/components/hooks/': {
          branches: 95,
          functions: 95,
          lines: 95,
          statements: 95,
        },
      },
    },
  },
})
```

## Test Organization

### Directory Structure

```
__tests__/
├── unit/
│   ├── components/
│   │   ├── atoms/
│   │   ├── molecules/
│   │   └── organisms/
│   ├── hooks/
│   ├── utils/
│   └── schemas/
├── integration/
│   ├── api/
│   ├── components/
│   └── workflows/
├── e2e/
│   ├── critical-paths/
│   ├── accessibility/
│   └── performance/
├── fixtures/
├── mocks/
└── setup/
```

## Next Steps

1. Implement comprehensive unit test suite
2. Set up integration testing with MSW
3. Create E2E test suite with Playwright
4. Configure coverage reporting and thresholds
5. Integrate accessibility testing
6. Set up performance testing
7. Create CI/CD pipeline for automated testing
