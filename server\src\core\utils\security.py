# src/utils/security.py
"""Security utilities for Ultimate Electrical Designer.

This module provides security validation functions and utilities
for input validation, sanitization, and security checks.
"""

import hashlib
import html
import re
import secrets
from typing import Any, Dict, List, Optional, Tuple


def validate_input_length(
    value: str, min_length: int = 0, max_length: int = 1000, field_name: str = "input"
) -> bool:
    """Validate input string length.

    Args:
        value: Input string to validate
        min_length: Minimum allowed length
        max_length: Maximum allowed length
        field_name: Name of the field for error messages

    Returns:
        bool: True if valid, False otherwise

    Raises:
        ValueError: If length is invalid

    """
    if not isinstance(value, str):
        raise ValueError(f"{field_name} must be a string")

    length = len(value)
    if length < min_length:
        raise ValueError(f"{field_name} must be at least {min_length} characters long")

    if length > max_length:
        raise ValueError(
            f"{field_name} must be no more than {max_length} characters long"
        )

    return True


def sanitize_html(value: Any) -> str:
    """Sanitize HTML content by escaping special characters.

    Args:
        value: Input string to sanitize

    Returns:
        str: Sanitized string with HTML entities escaped

    """
    if not isinstance(value, str):
        return str(value)

    return html.escape(value, quote=True)


def validate_email(email: Any) -> bool:
    """Validate email address format.

    Args:
        email: Email address to validate

    Returns:
        bool: True if valid email format, False otherwise

    """
    if not isinstance(email, str):
        return False

    # Basic email regex pattern
    pattern = r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$"
    return bool(re.match(pattern, email))


def validate_alphanumeric(value: Any, allow_spaces: bool = False) -> bool:
    """Validate that string contains only alphanumeric characters.

    Args:
        value: String to validate
        allow_spaces: Whether to allow spaces

    Returns:
        bool: True if valid, False otherwise

    """
    if not isinstance(value, str):
        return False

    if allow_spaces:
        pattern = r"^[a-zA-Z0-9\s]+$"
    else:
        pattern = r"^[a-zA-Z0-9]+$"

    return bool(re.match(pattern, value))


def validate_filename(filename: Any) -> bool:
    """Validate filename for security (no path traversal).

    Args:
        filename: Filename to validate

    Returns:
        bool: True if safe filename, False otherwise

    """
    if not isinstance(filename, str):
        return False

    # Check for path traversal attempts
    if ".." in filename or "/" in filename or "\\" in filename:
        return False

    # Check for reserved characters
    reserved_chars = '<>:"|?*'
    if any(char in filename for char in reserved_chars):
        return False

    # Check length
    if len(filename) == 0 or len(filename) > 255:
        return False

    return True


def generate_secure_token(length: int = 32) -> str:
    """Generate a cryptographically secure random token.

    Args:
        length: Length of the token in bytes

    Returns:
        str: Secure random token as hex string

    """
    return secrets.token_hex(length)


def hash_password(password: str, salt: Optional[str] = None) -> Tuple[str, str]:
    """Hash a password with salt using SHA-256.

    Args:
        password: Password to hash
        salt: Optional salt (generated if not provided)

    Returns:
        tuple: (hashed_password, salt)

    """
    if salt is None:
        salt = generate_secure_token(16)

    # Combine password and salt
    salted_password = password + salt

    # Hash using SHA-256
    hashed = hashlib.sha256(salted_password.encode("utf-8")).hexdigest()

    return hashed, salt


def verify_password(password: str, hashed_password: str, salt: str) -> bool:
    """Verify a password against its hash.

    Args:
        password: Plain text password
        hashed_password: Stored hash
        salt: Salt used for hashing

    Returns:
        bool: True if password matches, False otherwise

    """
    computed_hash, _ = hash_password(password, salt)
    return computed_hash == hashed_password


def sanitize_sql_input(value: Any) -> str:
    """Basic SQL injection prevention by escaping single quotes.
    Note: This is a basic implementation. Use parameterized queries instead.

    Args:
        value: Input string to sanitize

    Returns:
        str: Sanitized string

    """
    if not isinstance(value, str):
        return str(value)

    # Escape single quotes
    return value.replace("'", "''")


def validate_url(url: Any) -> bool:
    """Validate URL format and check for common security issues.

    Args:
        url: URL to validate

    Returns:
        bool: True if valid and safe URL, False otherwise

    """
    if not isinstance(url, str):
        return False

    # Basic URL pattern
    url_pattern = r"^https?://[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}(/.*)?$"

    if not re.match(url_pattern, url):
        return False

    # Check for suspicious patterns
    suspicious_patterns = [
        "javascript:",
        "data:",
        "vbscript:",
        "file:",
        "ftp:",
    ]

    url_lower = url.lower()
    for pattern in suspicious_patterns:
        if pattern in url_lower:
            return False

    return True


def escape_shell_command(command: Any) -> str:
    """Escape shell command to prevent injection.

    Args:
        command: Command to escape

    Returns:
        str: Escaped command

    """
    if not isinstance(command, str):
        return str(command)

    # Remove or escape dangerous characters
    dangerous_chars = ["&", "|", ";", "$", "`", "(", ")", "<", ">", '"', "'"]

    command_str: str = command
    for char in dangerous_chars:
        command_str = command_str.replace(char, f"\\{char}")

    return command_str


def validate_json_keys(data: Any, allowed_keys: List[str]) -> bool:
    """Validate that JSON data only contains allowed keys.

    Args:
        data: Dictionary to validate
        allowed_keys: List of allowed key names

    Returns:
        bool: True if all keys are allowed, False otherwise

    """
    if not isinstance(data, dict):
        return False

    for key in data.keys():
        if key not in allowed_keys:
            return False

    return True


def rate_limit_key(identifier: str, action: str) -> str:
    """Generate a rate limiting key for an identifier and action.

    Args:
        identifier: User identifier (IP, user ID, etc.)
        action: Action being performed

    Returns:
        str: Rate limiting key

    """
    return f"rate_limit:{action}:{identifier}"


__all__ = [
    "validate_input_length",
    "sanitize_html",
    "validate_email",
    "validate_alphanumeric",
    "validate_filename",
    "generate_secure_token",
    "hash_password",
    "verify_password",
    "sanitize_sql_input",
    "validate_url",
    "escape_shell_command",
    "validate_json_keys",
    "rate_limit_key",
]
