# Authentication Components Testing Guide

This guide covers testing strategies, patterns, and best practices for authentication components.

## Testing Philosophy

Our testing approach follows the testing pyramid:

- **Unit Tests**: Individual component behavior
- **Integration Tests**: Component interactions
- **E2E Tests**: Complete user workflows

## Test Structure

### Test Organization

```
__tests__/
├── LoginForm.test.tsx           # Login component tests
├── RegisterForm.test.tsx        # Registration component tests
├── hooks/
│   └── useAuth.test.ts         # Authentication hook tests
└── utils/
    └── validation.test.ts      # Validation utility tests
```

## Testing Tools

- **Vitest**: Test runner and framework
- **React Testing Library**: Component testing utilities
- **Jest DOM**: Additional DOM matchers
- **MSW**: API mocking
- **Playwright**: E2E testing

## Unit Testing Patterns

### Component Testing Template

```typescript
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { vi } from 'vitest'
import { LoginForm } from '../LoginForm'

describe('LoginForm', () => {
  const mockOnSubmit = vi.fn()

  beforeEach(() => {
    mockOnSubmit.mockClear()
  })

  it('should render form elements', () => {
    render(<LoginForm onSubmit={mockOnSubmit} />)

    expect(screen.getByLabelText(/email/i)).toBeInTheDocument()
    expect(screen.getByLabelText(/password/i)).toBeInTheDocument()
    expect(screen.getByRole('button', { name: /sign in/i })).toBeInTheDocument()
  })

  it('should validate required fields', async () => {
    const user = userEvent.setup()
    render(<LoginForm onSubmit={mockOnSubmit} />)

    const submitButton = screen.getByRole('button', { name: /sign in/i })
    await user.click(submitButton)

    expect(screen.getByText(/email is required/i)).toBeInTheDocument()
    expect(screen.getByText(/password is required/i)).toBeInTheDocument()
    expect(mockOnSubmit).not.toHaveBeenCalled()
  })
})
```

### Validation Testing

```typescript
import { validateLoginForm, validateRegisterForm } from '../utils/validation'

describe('Validation Utils', () => {
  describe('validateLoginForm', () => {
    it('should validate correct email format', () => {
      const result = validateLoginForm({
        email: '<EMAIL>',
        password: 'ValidPass123!',
      })

      expect(result.success).toBe(true)
    })

    it('should reject invalid email format', () => {
      const result = validateLoginForm({
        email: 'invalid-email',
        password: 'ValidPass123!',
      })

      expect(result.success).toBe(false)
      expect(result.errors?.email).toContain('Invalid email')
    })
  })
})
```

## Integration Testing

### Authentication Flow Testing

```typescript
import { render, screen, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { AuthProvider } from '../hooks/useAuth'
import { LoginForm } from '../LoginForm'

const MockedAuthProvider = ({ children }: { children: React.ReactNode }) => {
  return (
    <AuthProvider>
      {children}
    </AuthProvider>
  )
}

describe('Authentication Integration', () => {
  it('should complete login flow', async () => {
    const user = userEvent.setup()

    render(
      <MockedAuthProvider>
        <LoginForm onSubmit={vi.fn()} />
      </MockedAuthProvider>
    )

    // Fill form
    await user.type(screen.getByLabelText(/email/i), '<EMAIL>')
    await user.type(screen.getByLabelText(/password/i), 'ValidPass123!')

    // Submit form
    await user.click(screen.getByRole('button', { name: /sign in/i }))

    // Verify success
    await waitFor(() => {
      expect(screen.getByText(/welcome/i)).toBeInTheDocument()
    })
  })
})
```

## Accessibility Testing

### Screen Reader Testing

```typescript
import { render, screen } from '@testing-library/react'
import { axe, toHaveNoViolations } from 'jest-axe'
import { LoginForm } from '../LoginForm'

expect.extend(toHaveNoViolations)

describe('LoginForm Accessibility', () => {
  it('should have no accessibility violations', async () => {
    const { container } = render(<LoginForm onSubmit={vi.fn()} />)
    const results = await axe(container)
    expect(results).toHaveNoViolations()
  })

  it('should have proper ARIA labels', () => {
    render(<LoginForm onSubmit={vi.fn()} />)

    expect(screen.getByLabelText(/email/i)).toHaveAttribute('aria-required', 'true')
    expect(screen.getByLabelText(/password/i)).toHaveAttribute('aria-required', 'true')
  })

  it('should announce errors to screen readers', async () => {
    const user = userEvent.setup()
    render(<LoginForm onSubmit={vi.fn()} />)

    await user.click(screen.getByRole('button', { name: /sign in/i }))

    const errorMessage = screen.getByText(/email is required/i)
    expect(errorMessage).toHaveAttribute('role', 'alert')
    expect(errorMessage).toHaveAttribute('aria-live', 'polite')
  })
})
```

### Keyboard Navigation Testing

```typescript
describe('Keyboard Navigation', () => {
  it('should support tab navigation', async () => {
    const user = userEvent.setup()
    render(<LoginForm onSubmit={vi.fn()} />)

    // Tab through form elements
    await user.tab()
    expect(screen.getByLabelText(/email/i)).toHaveFocus()

    await user.tab()
    expect(screen.getByLabelText(/password/i)).toHaveFocus()

    await user.tab()
    expect(screen.getByRole('button', { name: /sign in/i })).toHaveFocus()
  })

  it('should support Enter key submission', async () => {
    const mockOnSubmit = vi.fn()
    const user = userEvent.setup()

    render(<LoginForm onSubmit={mockOnSubmit} />)

    await user.type(screen.getByLabelText(/email/i), '<EMAIL>')
    await user.type(screen.getByLabelText(/password/i), 'ValidPass123!')
    await user.keyboard('{Enter}')

    await waitFor(() => {
      expect(mockOnSubmit).toHaveBeenCalled()
    })
  })
})
```

## Error Handling Testing

### Network Error Testing

```typescript
import { server } from '../../../test/mocks/server'
import { rest } from 'msw'

describe('Error Handling', () => {
  it('should handle network errors gracefully', async () => {
    // Mock network error
    server.use(
      rest.post('/api/auth/login', (req, res, ctx) => {
        return res.networkError('Network error')
      })
    )

    const user = userEvent.setup()
    render(<LoginForm onSubmit={vi.fn()} />)

    await user.type(screen.getByLabelText(/email/i), '<EMAIL>')
    await user.type(screen.getByLabelText(/password/i), 'ValidPass123!')
    await user.click(screen.getByRole('button', { name: /sign in/i }))

    await waitFor(() => {
      expect(screen.getByText(/network error/i)).toBeInTheDocument()
    })
  })

  it('should handle validation errors from server', async () => {
    server.use(
      rest.post('/api/auth/login', (req, res, ctx) => {
        return res(
          ctx.status(400),
          ctx.json({ message: 'Invalid credentials' })
        )
      })
    )

    const user = userEvent.setup()
    render(<LoginForm onSubmit={vi.fn()} />)

    await user.type(screen.getByLabelText(/email/i), '<EMAIL>')
    await user.type(screen.getByLabelText(/password/i), 'WrongPass123!')
    await user.click(screen.getByRole('button', { name: /sign in/i }))

    await waitFor(() => {
      expect(screen.getByText(/invalid credentials/i)).toBeInTheDocument()
    })
  })
})
```

## Performance Testing

### Render Performance

```typescript
import { render } from '@testing-library/react'
import { performance } from 'perf_hooks'
import { LoginForm } from '../LoginForm'

describe('Performance', () => {
  it('should render within performance budget', () => {
    const start = performance.now()

    render(<LoginForm onSubmit={vi.fn()} />)

    const end = performance.now()
    const renderTime = end - start

    // Should render within 16ms (60fps budget)
    expect(renderTime).toBeLessThan(16)
  })
})
```

## E2E Testing with Playwright

### Complete Authentication Flow

```typescript
import { test, expect } from '@playwright/test'

test.describe('Authentication E2E', () => {
  test('should complete login flow', async ({ page }) => {
    await page.goto('/login')

    // Fill login form
    await page.fill('[data-testid="email-input"]', '<EMAIL>')
    await page.fill('[data-testid="password-input"]', 'ValidPass123!')

    // Submit form
    await page.click('[data-testid="login-button"]')

    // Verify redirect to dashboard
    await expect(page).toHaveURL('/dashboard')
    await expect(page.locator('[data-testid="user-menu"]')).toBeVisible()
  })

  test('should show validation errors', async ({ page }) => {
    await page.goto('/login')

    // Submit empty form
    await page.click('[data-testid="login-button"]')

    // Verify error messages
    await expect(page.locator('[data-testid="email-error"]')).toBeVisible()
    await expect(page.locator('[data-testid="password-error"]')).toBeVisible()
  })
})
```

## Test Data Management

### Test Fixtures

```typescript
// test/fixtures/auth.ts
export const validUser = {
  email: '<EMAIL>',
  password: 'ValidPass123!',
  firstName: 'John',
  lastName: 'Doe',
}

export const invalidCredentials = {
  email: '<EMAIL>',
  password: 'WrongPassword',
}

export const weakPasswords = ['123456', 'password', 'qwerty', '12345678']

export const strongPasswords = ['ValidPass123!', 'SecureP@ssw0rd', 'MyStr0ng!Pass']
```

## Mocking Strategies

### API Mocking with MSW

```typescript
// test/mocks/handlers.ts
import { rest } from 'msw'

export const handlers = [
  rest.post('/api/auth/login', (req, res, ctx) => {
    return res(
      ctx.json({
        user: { id: '1', email: '<EMAIL>' },
        token: 'mock-token',
      })
    )
  }),

  rest.post('/api/auth/register', (req, res, ctx) => {
    return res(
      ctx.json({
        user: { id: '2', email: '<EMAIL>' },
        token: 'mock-token',
      })
    )
  }),
]
```

## Test Coverage Goals

- **Unit Tests**: 95%+ coverage
- **Integration Tests**: Critical paths covered
- **E2E Tests**: Happy path and error scenarios
- **Accessibility Tests**: All components tested

## Running Tests

```bash
# Run all tests
npm test

# Run auth tests only
npm test src/components/auth

# Run with coverage
npm test -- --coverage

# Run E2E tests
npm run test:e2e

# Run accessibility tests
npm run test:a11y
```

## Debugging Tests

### Debug Mode

```bash
# Run tests in debug mode
npm test -- --inspect-brk

# Run specific test file
npm test LoginForm.test.tsx -- --verbose
```

### Test Debugging Tips

1. Use `screen.debug()` to see rendered HTML
2. Add `await new Promise(r => setTimeout(r, 1000))` for manual inspection
3. Use `--verbose` flag for detailed output
4. Check browser dev tools in E2E tests

## Best Practices

1. **Test Behavior, Not Implementation**: Focus on user interactions
2. **Use Semantic Queries**: Prefer `getByRole` over `getByTestId`
3. **Mock External Dependencies**: Keep tests isolated
4. **Test Error States**: Don't just test happy paths
5. **Keep Tests Simple**: One assertion per test when possible
6. **Use Descriptive Names**: Test names should explain the scenario
