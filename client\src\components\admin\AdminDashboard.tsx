'use client'

import { AdminDashboardOverview } from '@/modules/admin-dashboard'

interface AdminDashboardProps {
  className?: string
}

/**
 * Legacy AdminDashboard component - now uses the new admin dashboard module
 * This maintains backward compatibility while providing the new comprehensive admin dashboard
 */
export function AdminDashboard({ className = '' }: AdminDashboardProps) {
  return <AdminDashboardOverview className={className} />
}
