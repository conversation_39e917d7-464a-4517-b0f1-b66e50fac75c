/**
 * Authentication Helper Utilities
 * Common utilities for authentication flows and form handling
 */

import type { LoginRequest, RegisterRequest } from '@/types/api'
import { sanitizeInput } from './validation'

/**
 * Form submission states
 */
export type FormState = 'idle' | 'submitting' | 'success' | 'error'

/**
 * Authentication error types
 */
export interface AuthError {
  type: 'validation' | 'network' | 'server' | 'unknown'
  message: string
  field?: string
  code?: string
}

/**
 * Parse API error response into user-friendly format
 */
export function parseAuthError(error: any): AuthError {
  // Network errors
  if (!error.response) {
    return {
      type: 'network',
      message:
        'Unable to connect to the server. Please check your internet connection and try again.',
    }
  }

  const { status, data } = error.response

  // Server validation errors (400)
  if (status === 400) {
    if (data?.detail && typeof data.detail === 'string') {
      return {
        type: 'validation',
        message: data.detail,
      }
    }
    if (data?.errors && Array.isArray(data.errors)) {
      // Handle field-specific validation errors
      const firstError = data.errors[0]
      return {
        type: 'validation',
        message: firstError.message || 'Please check your input and try again.',
        field: firstError.field,
      }
    }
    return {
      type: 'validation',
      message: 'Please check your input and try again.',
    }
  }

  // Authentication errors (401)
  if (status === 401) {
    return {
      type: 'server',
      message: 'Invalid email or password. Please check your credentials and try again.',
    }
  }

  // Conflict errors (409) - User already exists
  if (status === 409) {
    return {
      type: 'server',
      message:
        'An account with this email or username already exists. Please use a different email or try logging in.',
    }
  }

  // Rate limiting (429)
  if (status === 429) {
    return {
      type: 'server',
      message: 'Too many attempts. Please wait a few minutes before trying again.',
    }
  }

  // Server errors (500+)
  if (status >= 500) {
    return {
      type: 'server',
      message: 'Server error. Please try again later or contact support if the problem persists.',
    }
  }

  // Unknown errors
  return {
    type: 'unknown',
    message: 'An unexpected error occurred. Please try again.',
  }
}

/**
 * Sanitize login form data
 */
export function sanitizeLoginData(data: LoginRequest): LoginRequest {
  return {
    username: sanitizeInput(data.username.toLowerCase().trim()),
    password: data.password, // Don't sanitize password as it may contain special chars
  }
}

/**
 * Sanitize registration form data
 */
export function sanitizeRegisterData(data: RegisterRequest): RegisterRequest {
  return {
    name: sanitizeInput(data.name.trim()),
    email: sanitizeInput(data.email.toLowerCase().trim()),
    password: data.password, // Don't sanitize password
    confirmPassword: data.confirmPassword, // Don't sanitize password
  }
}

/**
 * Generate secure random string for form IDs
 */
export function generateFormId(prefix: string = 'form'): string {
  return `${prefix}-${Math.random().toString(36).substr(2, 9)}`
}

/**
 * Debounce function for real-time validation
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout
  return (...args: Parameters<T>) => {
    clearTimeout(timeout)
    timeout = setTimeout(() => func(...args), wait)
  }
}

/**
 * Check if form has unsaved changes
 */
export function hasUnsavedChanges(
  currentData: Record<string, any>,
  initialData: Record<string, any>
): boolean {
  return JSON.stringify(currentData) !== JSON.stringify(initialData)
}

/**
 * Format validation errors for display
 */
export function formatValidationErrors(errors: Record<string, string>): string[] {
  return Object.entries(errors).map(([field, message]) => {
    const fieldName = field.charAt(0).toUpperCase() + field.slice(1)
    return `${fieldName}: ${message}`
  })
}

/**
 * Check if password meets minimum requirements
 */
export function meetsMinimumPasswordRequirements(password: string): boolean {
  return (
    password.length >= 8 &&
    /[A-Z]/.test(password) &&
    /[a-z]/.test(password) &&
    /\d/.test(password) &&
    /[!@#$%^&*(),.?":{}|<>]/.test(password)
  )
}

/**
 * Generate accessibility announcement for form state changes
 */
export function generateA11yAnnouncement(
  state: FormState,
  type: 'login' | 'register',
  error?: string
): string {
  switch (state) {
    case 'submitting':
      return `${type === 'login' ? 'Signing in' : 'Creating account'}, please wait...`
    case 'success':
      return `${type === 'login' ? 'Sign in successful' : 'Account created successfully'}`
    case 'error':
      return `${type === 'login' ? 'Sign in failed' : 'Account creation failed'}. ${error || 'Please try again.'}`
    default:
      return ''
  }
}

/**
 * Focus management utilities
 */
export const focusUtils = {
  /**
   * Focus first error field in form
   */
  focusFirstError: (formElement: HTMLFormElement) => {
    const firstErrorField = formElement.querySelector('[aria-invalid="true"]') as HTMLElement
    if (firstErrorField) {
      firstErrorField.focus()
    }
  },

  /**
   * Focus first field in form
   */
  focusFirstField: (formElement: HTMLFormElement) => {
    const firstField = formElement.querySelector('input, select, textarea') as HTMLElement
    if (firstField) {
      firstField.focus()
    }
  },

  /**
   * Trap focus within element
   */
  trapFocus: (element: HTMLElement) => {
    const focusableElements = element.querySelectorAll(
      'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    )
    const firstElement = focusableElements[0] as HTMLElement
    const lastElement = focusableElements[focusableElements.length - 1] as HTMLElement

    const handleTabKey = (e: KeyboardEvent) => {
      if (e.key === 'Tab') {
        if (e.shiftKey) {
          if (document.activeElement === firstElement) {
            lastElement.focus()
            e.preventDefault()
          }
        } else {
          if (document.activeElement === lastElement) {
            firstElement.focus()
            e.preventDefault()
          }
        }
      }
    }

    element.addEventListener('keydown', handleTabKey)
    return () => element.removeEventListener('keydown', handleTabKey)
  },
}

/**
 * Local storage utilities for form persistence
 */
export const storageUtils = {
  /**
   * Save form data to localStorage (excluding sensitive fields)
   */
  saveFormData: (
    key: string,
    data: Record<string, any>,
    excludeFields: string[] = ['password', 'confirmPassword']
  ) => {
    try {
      const sanitizedData = Object.fromEntries(
        Object.entries(data).filter(([field]) => !excludeFields.includes(field))
      )
      localStorage.setItem(key, JSON.stringify(sanitizedData))
    } catch (error) {
      console.warn('Failed to save form data to localStorage:', error)
    }
  },

  /**
   * Load form data from localStorage
   */
  loadFormData: (key: string): Record<string, any> | null => {
    try {
      const data = localStorage.getItem(key)
      return data ? JSON.parse(data) : null
    } catch (error) {
      console.warn('Failed to load form data from localStorage:', error)
      return null
    }
  },

  /**
   * Clear form data from localStorage
   */
  clearFormData: (key: string) => {
    try {
      localStorage.removeItem(key)
    } catch (error) {
      console.warn('Failed to clear form data from localStorage:', error)
    }
  },
}

/**
 * Rate limiting utilities
 */
export const rateLimitUtils = {
  /**
   * Check if action is rate limited
   */
  isRateLimited: (
    key: string,
    maxAttempts: number = 5,
    windowMs: number = 15 * 60 * 1000
  ): boolean => {
    try {
      const attempts = JSON.parse(localStorage.getItem(`rate_limit_${key}`) || '[]')
      const now = Date.now()
      const validAttempts = attempts.filter((timestamp: number) => now - timestamp < windowMs)
      return validAttempts.length >= maxAttempts
    } catch {
      return false
    }
  },

  /**
   * Record attempt for rate limiting
   */
  recordAttempt: (key: string, maxAttempts: number = 5, windowMs: number = 15 * 60 * 1000) => {
    try {
      const attempts = JSON.parse(localStorage.getItem(`rate_limit_${key}`) || '[]')
      const now = Date.now()
      const validAttempts = attempts.filter((timestamp: number) => now - timestamp < windowMs)
      validAttempts.push(now)
      localStorage.setItem(`rate_limit_${key}`, JSON.stringify(validAttempts.slice(-maxAttempts)))
    } catch (error) {
      console.warn('Failed to record rate limit attempt:', error)
    }
  },

  /**
   * Clear rate limit data
   */
  clearRateLimit: (key: string) => {
    try {
      localStorage.removeItem(`rate_limit_${key}`)
    } catch (error) {
      console.warn('Failed to clear rate limit data:', error)
    }
  },
}
