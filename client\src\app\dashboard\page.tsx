'use client'

import { AdminDashboard } from '@/components/admin/AdminDashboard'
import { RouteGuard } from '@/components/auth/RouteGuard'
import { UserProfile } from '@/components/auth/UserProfile'
import { DashboardLayout } from '@/components/layout/DashboardLayout'
import { useAuth } from '@/hooks/useAuth'
import { DashboardOverview } from '@/modules/dashboard'
import { Suspense } from 'react'

function DashboardContent() {
  const { user, isAdmin } = useAuth()

  if (isAdmin()) {
    return <AdminDashboard />
  }

  return (
    <div className="space-y-8">
      {/* Main Dashboard Overview */}
      <Suspense fallback={<DashboardLoadingFallback />}>
        <DashboardOverview />
      </Suspense>

      {/* User Profile Section */}
      <UserProfile />
    </div>
  )
}

function DashboardLoadingFallback() {
  return (
    <div className="space-y-8">
      {/* Welcome Section Skeleton */}
      <div className="animate-pulse rounded-lg bg-gradient-to-r from-gray-100 to-gray-200 p-6">
        <div className="flex items-center justify-between">
          <div>
            <div className="mb-2 h-8 w-64 rounded bg-gray-300"></div>
            <div className="h-4 w-96 rounded bg-gray-300"></div>
          </div>
          <div className="hidden sm:block">
            <div className="h-4 w-32 rounded bg-gray-300"></div>
          </div>
        </div>
      </div>

      {/* Metrics Skeleton */}
      <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
        {Array.from({ length: 4 }).map((_, index) => (
          <div
            key={index}
            className="animate-pulse rounded-lg border border-gray-200 bg-white p-6 shadow-sm"
          >
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="h-10 w-10 rounded-lg bg-gray-200"></div>
              </div>
              <div className="ml-5 w-0 flex-1">
                <div className="mb-2 h-4 w-3/4 rounded bg-gray-200"></div>
                <div className="h-6 w-1/2 rounded bg-gray-200"></div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Quick Actions Skeleton */}
      <div className="animate-pulse rounded-lg border border-gray-200 bg-white shadow-sm">
        <div className="border-b border-gray-200 px-6 py-4">
          <div className="h-6 w-32 rounded bg-gray-200"></div>
        </div>
        <div className="p-6">
          <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
            {Array.from({ length: 6 }).map((_, index) => (
              <div key={index} className="animate-pulse rounded-lg border border-gray-200 p-6">
                <div className="flex items-start space-x-4">
                  <div className="h-12 w-12 rounded-lg bg-gray-200"></div>
                  <div className="flex-1">
                    <div className="mb-2 h-4 w-3/4 rounded bg-gray-200"></div>
                    <div className="mb-2 h-3 w-full rounded bg-gray-200"></div>
                    <div className="h-3 w-2/3 rounded bg-gray-200"></div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Projects and Calculations Grid Skeleton */}
      <div className="grid grid-cols-1 gap-8 lg:grid-cols-3">
        <div className="h-96 animate-pulse rounded-lg border border-gray-200 bg-white shadow-sm lg:col-span-2"></div>
        <div className="h-96 animate-pulse rounded-lg border border-gray-200 bg-white shadow-sm lg:col-span-1"></div>
      </div>
    </div>
  )
}

export default function DashboardPage() {
  return (
    <RouteGuard requireAuth={true}>
      <DashboardLayout title="Dashboard">
        <DashboardContent />
      </DashboardLayout>
    </RouteGuard>
  )
}
