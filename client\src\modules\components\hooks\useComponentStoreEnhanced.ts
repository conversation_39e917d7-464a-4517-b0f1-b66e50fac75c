/**
 * Enhanced Component Store Hook
 * Comprehensive Zustand store with validation, persistence, and type safety
 */

import { create } from 'zustand'
import { devtools, persist, subscribeWithSelector } from 'zustand/middleware'
import { immer } from 'zustand/middleware/immer'
import type {
  BulkOperationState,
  ComponentDisplayOptions,
  ComponentListState,
  ComponentSearchState,
  ComponentFormState,
  SidebarState,
  ModalState,
  UserPreferences,
  Notification,
  ComponentFilter,
  ViewMode,
  SortConfig,
  FilterState,
} from '../schemas'
import {
  createDefaultListState,
  createDefaultSearchState,
  createDefaultFilterState,
  createDefaultUserPreferences,
} from '../schemas'

// Enhanced store interface with comprehensive state management
interface ComponentStore {
  // Core State
  listState: ComponentListState
  searchState: ComponentSearchState
  filterState: FilterState
  bulkState: BulkOperationState
  formState: ComponentFormState
  sidebarState: SidebarState
  modalState: ModalState
  userPreferences: UserPreferences
  notifications: Notification[]

  // List Management Actions
  setListState: (state: Partial<ComponentListState>) => void
  updateFilters: (filters: Partial<ComponentFilter>) => void
  clearFilters: () => void
  setViewMode: (mode: ViewMode) => void
  setSortConfig: (config: SortConfig) => void
  setPage: (page: number) => void
  setPageSize: (size: number) => void

  // Search Management Actions
  setSearchState: (state: Partial<ComponentSearchState>) => void
  setSearchQuery: (query: string) => void
  addToSearchHistory: (query: string) => void
  clearSearchHistory: () => void
  toggleAdvancedSearch: () => void
  setSuggestions: (suggestions: string[]) => void

  // Filter Management Actions
  setFilterState: (state: Partial<FilterState>) => void
  addFilter: (key: string, value: any) => void
  removeFilter: (key: string) => void
  clearAllFilters: () => void
  toggleAdvancedFilters: () => void

  // Bulk Operations Actions
  setBulkState: (state: Partial<BulkOperationState>) => void
  selectComponent: (id: number) => void
  deselectComponent: (id: number) => void
  selectAll: (ids: number[]) => void
  clearSelection: () => void
  setBulkOperation: (operation: BulkOperationState['operation']) => void
  updateBulkProgress: (progress: number) => void

  // Form Management Actions
  setFormState: (state: Partial<ComponentFormState>) => void
  updateFormField: (field: string, value: any) => void
  setFormErrors: (errors: Record<string, string>) => void
  setFormTouched: (field: string, touched: boolean) => void
  resetForm: () => void
  setFormMode: (mode: ComponentFormState['mode']) => void

  // UI State Actions
  setSidebarState: (state: Partial<SidebarState>) => void
  toggleSidebar: () => void
  setSidebarSection: (section: SidebarState['active_section']) => void
  setModalState: (state: Partial<ModalState>) => void
  openModal: (type: ModalState['type'], data?: any) => void
  closeModal: () => void

  // User Preferences Actions
  setUserPreferences: (preferences: Partial<UserPreferences>) => void
  updateDisplayOptions: (options: Partial<ComponentDisplayOptions>) => void
  toggleDisplayOption: (option: keyof ComponentDisplayOptions) => void

  // Notification Actions
  addNotification: (notification: Omit<Notification, 'id'>) => void
  removeNotification: (id: string) => void
  clearNotifications: () => void

  // Computed Properties
  getActiveFilterCount: () => number
  getSelectedCount: () => number
  isComponentSelected: (id: number) => boolean
  hasActiveSearch: () => boolean
  getFilterSummary: () => string[]

  // Utility Actions
  reset: () => void
  resetToDefaults: () => void
}

// Default states
const defaultBulkState: BulkOperationState = {
  selected_ids: [],
  operation: undefined,
  is_processing: false,
  progress: 0,
  results: [],
  errors: [],
}

const defaultFormState: ComponentFormState = {
  data: {},
  errors: {},
  touched: {},
  is_dirty: false,
  is_submitting: false,
  is_valid: false,
  mode: 'create',
}

const defaultSidebarState: SidebarState = {
  is_open: true,
  width: 300,
  collapsed: false,
  active_section: 'filters',
}

const defaultModalState: ModalState = {
  is_open: false,
  type: undefined,
  data: undefined,
  loading: false,
  error: undefined,
}

// Enhanced store implementation with immer for immutable updates
export const useComponentStoreEnhanced = create<ComponentStore>()(
  devtools(
    persist(
      subscribeWithSelector(
        immer((set, get) => ({
          // Initial State
          listState: createDefaultListState(),
          searchState: createDefaultSearchState(),
          filterState: createDefaultFilterState(),
          bulkState: defaultBulkState,
          formState: defaultFormState,
          sidebarState: defaultSidebarState,
          modalState: defaultModalState,
          userPreferences: createDefaultUserPreferences(),
          notifications: [],

          // List Management Actions
          setListState: (state) =>
            set((draft) => {
              Object.assign(draft.listState, state)
            }),

          updateFilters: (filters) =>
            set((draft) => {
              Object.assign(draft.listState.filters, filters)
              draft.listState.pagination.page = 1 // Reset to first page
            }),

          clearFilters: () =>
            set((draft) => {
              draft.listState.filters = {}
              draft.listState.pagination.page = 1
              draft.filterState.active_filters = {}
              draft.filterState.filter_count = 0
            }),

          setViewMode: (mode) =>
            set((draft) => {
              draft.listState.view_mode = mode
            }),

          setSortConfig: (config) =>
            set((draft) => {
              draft.listState.sort_config = config
            }),

          setPage: (page) =>
            set((draft) => {
              draft.listState.pagination.page = page
            }),

          setPageSize: (size) =>
            set((draft) => {
              draft.listState.pagination.size = size
              draft.listState.pagination.page = 1 // Reset to first page
            }),

          // Search Management Actions
          setSearchState: (state) =>
            set((draft) => {
              Object.assign(draft.searchState, state)
            }),

          setSearchQuery: (query) =>
            set((draft) => {
              draft.searchState.query = query
              if (query && !draft.searchState.search_history.includes(query)) {
                draft.searchState.search_history.unshift(query)
                // Keep only last 10 searches
                if (draft.searchState.search_history.length > 10) {
                  draft.searchState.search_history = draft.searchState.search_history.slice(0, 10)
                }
              }
            }),

          addToSearchHistory: (query) =>
            set((draft) => {
              if (query && !draft.searchState.search_history.includes(query)) {
                draft.searchState.search_history.unshift(query)
                if (draft.searchState.search_history.length > 10) {
                  draft.searchState.search_history = draft.searchState.search_history.slice(0, 10)
                }
              }
            }),

          clearSearchHistory: () =>
            set((draft) => {
              draft.searchState.search_history = []
            }),

          toggleAdvancedSearch: () =>
            set((draft) => {
              draft.searchState.is_advanced_mode = !draft.searchState.is_advanced_mode
            }),

          setSuggestions: (suggestions) =>
            set((draft) => {
              draft.searchState.suggestions = suggestions
            }),

          // Filter Management Actions
          setFilterState: (state) =>
            set((draft) => {
              Object.assign(draft.filterState, state)
            }),

          addFilter: (key, value) =>
            set((draft) => {
              draft.filterState.active_filters[key] = value
              draft.filterState.filter_count = Object.keys(draft.filterState.active_filters).length
            }),

          removeFilter: (key) =>
            set((draft) => {
              delete draft.filterState.active_filters[key]
              draft.filterState.filter_count = Object.keys(draft.filterState.active_filters).length
            }),

          clearAllFilters: () =>
            set((draft) => {
              draft.filterState.active_filters = {}
              draft.filterState.filter_count = 0
            }),

          toggleAdvancedFilters: () =>
            set((draft) => {
              draft.filterState.show_advanced_filters = !draft.filterState.show_advanced_filters
            }),

          // Bulk Operations Actions
          setBulkState: (state) =>
            set((draft) => {
              Object.assign(draft.bulkState, state)
            }),

          selectComponent: (id) =>
            set((draft) => {
              if (!draft.bulkState.selected_ids.includes(id)) {
                draft.bulkState.selected_ids.push(id)
              }
            }),

          deselectComponent: (id) =>
            set((draft) => {
              const index = draft.bulkState.selected_ids.indexOf(id)
              if (index > -1) {
                draft.bulkState.selected_ids.splice(index, 1)
              }
            }),

          selectAll: (ids) =>
            set((draft) => {
              draft.bulkState.selected_ids = [...ids]
            }),

          clearSelection: () =>
            set((draft) => {
              draft.bulkState.selected_ids = []
              draft.bulkState.operation = undefined
            }),

          setBulkOperation: (operation) =>
            set((draft) => {
              draft.bulkState.operation = operation
            }),

          updateBulkProgress: (progress) =>
            set((draft) => {
              draft.bulkState.progress = progress
            }),

          // Form Management Actions
          setFormState: (state) =>
            set((draft) => {
              Object.assign(draft.formState, state)
            }),

          updateFormField: (field, value) =>
            set((draft) => {
              draft.formState.data[field] = value
              draft.formState.touched[field] = true
              draft.formState.is_dirty = true
            }),

          setFormErrors: (errors) =>
            set((draft) => {
              draft.formState.errors = errors
              draft.formState.is_valid = Object.keys(errors).length === 0
            }),

          setFormTouched: (field, touched) =>
            set((draft) => {
              draft.formState.touched[field] = touched
            }),

          resetForm: () =>
            set((draft) => {
              draft.formState = { ...defaultFormState }
            }),

          setFormMode: (mode) =>
            set((draft) => {
              draft.formState.mode = mode
            }),

          // UI State Actions
          setSidebarState: (state) =>
            set((draft) => {
              Object.assign(draft.sidebarState, state)
            }),

          toggleSidebar: () =>
            set((draft) => {
              draft.sidebarState.is_open = !draft.sidebarState.is_open
            }),

          setSidebarSection: (section) =>
            set((draft) => {
              draft.sidebarState.active_section = section
            }),

          setModalState: (state) =>
            set((draft) => {
              Object.assign(draft.modalState, state)
            }),

          openModal: (type, data) =>
            set((draft) => {
              draft.modalState.is_open = true
              draft.modalState.type = type
              draft.modalState.data = data
              draft.modalState.loading = false
              draft.modalState.error = undefined
            }),

          closeModal: () =>
            set((draft) => {
              draft.modalState = { ...defaultModalState }
            }),

          // User Preferences Actions
          setUserPreferences: (preferences) =>
            set((draft) => {
              Object.assign(draft.userPreferences, preferences)
            }),

          updateDisplayOptions: (options) =>
            set((draft) => {
              Object.assign(draft.userPreferences.display_options, options)
            }),

          toggleDisplayOption: (option) =>
            set((draft) => {
              draft.userPreferences.display_options[option] =
                !draft.userPreferences.display_options[option]
            }),

          // Notification Actions
          addNotification: (notification) =>
            set((draft) => {
              const id = Date.now().toString()
              draft.notifications.push({ ...notification, id })
            }),

          removeNotification: (id) =>
            set((draft) => {
              const index = draft.notifications.findIndex((n) => n.id === id)
              if (index > -1) {
                draft.notifications.splice(index, 1)
              }
            }),

          clearNotifications: () =>
            set((draft) => {
              draft.notifications = []
            }),

          // Computed Properties
          getActiveFilterCount: () => {
            const state = get()
            return state.filterState.filter_count
          },

          getSelectedCount: () => {
            const state = get()
            return state.bulkState.selected_ids.length
          },

          isComponentSelected: (id) => {
            const state = get()
            return state.bulkState.selected_ids.includes(id)
          },

          hasActiveSearch: () => {
            const state = get()
            return state.searchState.query.length > 0
          },

          getFilterSummary: () => {
            const state = get()
            return Object.entries(state.filterState.active_filters).map(
              ([key, value]) => `${key}: ${value}`
            )
          },

          // Utility Actions
          reset: () =>
            set((draft) => {
              draft.listState = createDefaultListState()
              draft.searchState = createDefaultSearchState()
              draft.filterState = createDefaultFilterState()
              draft.bulkState = { ...defaultBulkState }
              draft.formState = { ...defaultFormState }
              draft.modalState = { ...defaultModalState }
              draft.notifications = []
            }),

          resetToDefaults: () =>
            set((draft) => {
              draft.listState = createDefaultListState()
              draft.searchState = createDefaultSearchState()
              draft.filterState = createDefaultFilterState()
              draft.bulkState = { ...defaultBulkState }
              draft.formState = { ...defaultFormState }
              draft.sidebarState = { ...defaultSidebarState }
              draft.modalState = { ...defaultModalState }
              draft.userPreferences = createDefaultUserPreferences()
              draft.notifications = []
            }),
        }))
      ),
      {
        name: 'component-store-enhanced',
        partialize: (state) => ({
          // Only persist user preferences and some UI state
          userPreferences: state.userPreferences,
          sidebarState: {
            is_open: state.sidebarState.is_open,
            width: state.sidebarState.width,
            active_section: state.sidebarState.active_section,
          },
          searchState: {
            search_history: state.searchState.search_history,
            saved_searches: state.searchState.saved_searches,
          },
          listState: {
            view_mode: state.listState.view_mode,
            sort_config: state.listState.sort_config,
            pagination: {
              size: state.listState.pagination.size,
            },
          },
        }),
        version: 1,
      }
    ),
    {
      name: 'component-store-enhanced',
      enabled: process.env.NODE_ENV === 'development',
    }
  )
)
