/**
 * Type definitions for the dashboard domain
 */

import type { UserRead } from '@/types/api'

// Dashboard data interfaces
export interface DashboardMetrics {
  totalProjects: number
  activeProjects: number
  completedCalculations: number
  recentActivity: number
  systemUptime: string
  lastLogin: string
}

export interface ProjectSummary {
  id: string
  name: string
  description: string
  status: 'active' | 'completed' | 'on_hold' | 'draft'
  progress: number
  lastModified: string
  createdAt: string
  owner: string
  type: 'heat_tracing' | 'load_calculation' | 'cable_sizing' | 'general'
  priority: 'low' | 'medium' | 'high' | 'critical'
}

export interface RecentCalculation {
  id: string
  name: string
  type: 'heat_tracing' | 'load_calculation' | 'cable_sizing'
  projectId: string
  projectName: string
  result: string
  status: 'completed' | 'in_progress' | 'failed'
  createdAt: string
  lastModified: string
  calculatedBy: string
}

export interface QuickAction {
  id: string
  title: string
  description: string
  icon: string
  color: 'primary' | 'secondary' | 'accent' | 'dark'
  href: string
  category: 'calculation' | 'project' | 'management' | 'documentation'
  isEnabled: boolean
  requiresAuth: boolean
  requiredRole?: string
}

export interface DashboardWidget {
  id: string
  title: string
  type: 'metrics' | 'projects' | 'calculations' | 'quick_actions' | 'profile' | 'activity'
  position: { x: number; y: number; w: number; h: number }
  isVisible: boolean
  isCollapsible: boolean
  isCollapsed: boolean
  refreshInterval?: number
  lastUpdated: string
}

export interface ActivityItem {
  id: string
  type: 'project_created' | 'calculation_completed' | 'user_login' | 'system_update'
  title: string
  description: string
  timestamp: string
  userId: string
  userName: string
  metadata?: Record<string, any>
}

export interface DashboardPreferences {
  layout: 'grid' | 'list' | 'compact'
  theme: 'light' | 'dark' | 'auto'
  widgetOrder: string[]
  hiddenWidgets: string[]
  refreshInterval: number
  showWelcomeMessage: boolean
  defaultProjectView: 'all' | 'active' | 'recent'
  calculationHistoryLimit: number
}

export interface DashboardData {
  metrics: DashboardMetrics
  projects: ProjectSummary[]
  recentCalculations: RecentCalculation[]
  quickActions: QuickAction[]
  widgets: DashboardWidget[]
  recentActivity: ActivityItem[]
  preferences: DashboardPreferences
}

// Component prop interfaces
export interface DashboardOverviewProps {
  data?: Partial<DashboardData>
  isLoading?: boolean
  error?: Error | null
  className?: string
}

export interface MetricsWidgetProps {
  metrics: DashboardMetrics
  isLoading?: boolean
  className?: string
}

export interface ProjectsWidgetProps {
  projects: ProjectSummary[]
  isLoading?: boolean
  onProjectClick?: (project: ProjectSummary) => void
  onCreateProject?: () => void
  className?: string
}

export interface RecentCalculationsWidgetProps {
  calculations: RecentCalculation[]
  isLoading?: boolean
  onCalculationClick?: (calculation: RecentCalculation) => void
  onViewAll?: () => void
  className?: string
}

export interface QuickActionsWidgetProps {
  actions: QuickAction[]
  onActionClick?: (action: QuickAction) => void
  className?: string
}

export interface ActivityWidgetProps {
  activities: ActivityItem[]
  isLoading?: boolean
  onViewAll?: () => void
  className?: string
}

export interface UserProfileWidgetProps {
  user: UserRead
  onEditProfile?: () => void
  onChangePassword?: () => void
  onLogout?: () => void
  className?: string
}

export interface DashboardLayoutProps {
  children: React.ReactNode
  user: UserRead
  isLoading?: boolean
  className?: string
}

// Store interfaces
export interface DashboardState {
  // Data state
  data: DashboardData | null
  isLoading: boolean
  error: Error | null
  lastUpdated: string | null

  // UI state
  selectedProject: string | null
  activeWidget: string | null
  isRefreshing: boolean
  showWelcomeModal: boolean

  // Filter and search state
  projectFilter: 'all' | 'active' | 'completed' | 'on_hold'
  calculationFilter: 'all' | 'heat_tracing' | 'load_calculation' | 'cable_sizing'
  searchQuery: string

  // Layout state
  layout: 'grid' | 'list' | 'compact'
  sidebarCollapsed: boolean

  // Actions
  setData: (data: DashboardData) => void
  updateMetrics: (metrics: Partial<DashboardMetrics>) => void
  addProject: (project: ProjectSummary) => void
  updateProject: (id: string, updates: Partial<ProjectSummary>) => void
  removeProject: (id: string) => void
  addCalculation: (calculation: RecentCalculation) => void
  setLoading: (loading: boolean) => void
  setError: (error: Error | null) => void
  setSelectedProject: (projectId: string | null) => void
  setActiveWidget: (widgetId: string | null) => void
  setProjectFilter: (filter: 'all' | 'active' | 'completed' | 'on_hold') => void
  setCalculationFilter: (
    filter: 'all' | 'heat_tracing' | 'load_calculation' | 'cable_sizing'
  ) => void
  setSearchQuery: (query: string) => void
  setLayout: (layout: 'grid' | 'list' | 'compact') => void
  setSidebarCollapsed: (collapsed: boolean) => void
  setShowWelcomeModal: (show: boolean) => void
  refreshData: () => void
  resetState: () => void
}

// API interfaces
export interface DashboardApiResponse {
  data: DashboardData
  status: 'success' | 'error'
  message?: string
  timestamp: string
}

export interface ProjectsApiParams {
  limit?: number
  offset?: number
  status?: string
  type?: string
  sortBy?: 'name' | 'created_at' | 'last_modified' | 'priority'
  sortOrder?: 'asc' | 'desc'
}

export interface CalculationsApiParams {
  limit?: number
  offset?: number
  type?: string
  projectId?: string
  status?: string
  sortBy?: 'created_at' | 'last_modified' | 'name'
  sortOrder?: 'asc' | 'desc'
}

export interface MetricsApiParams {
  timeRange?: '24h' | '7d' | '30d' | '90d'
  includeDetails?: boolean
}

// Utility types
export type DashboardSection = 'overview' | 'projects' | 'calculations' | 'profile' | 'settings'

export type WidgetSize = 'small' | 'medium' | 'large' | 'full'

export type ProjectStatus = ProjectSummary['status']
export type CalculationType = RecentCalculation['type']
export type ActivityType = ActivityItem['type']
export type QuickActionCategory = QuickAction['category']

// Form interfaces
export interface ProjectFormData {
  name: string
  description: string
  type: ProjectSummary['type']
  priority: ProjectSummary['priority']
}

export interface PreferencesFormData {
  layout: DashboardPreferences['layout']
  theme: DashboardPreferences['theme']
  refreshInterval: number
  showWelcomeMessage: boolean
  defaultProjectView: DashboardPreferences['defaultProjectView']
  calculationHistoryLimit: number
}

// Error types
export interface DashboardError extends Error {
  code?: string
  details?: Record<string, any>
  timestamp?: string
}

// Event types
export interface DashboardEvent {
  type: 'project_selected' | 'calculation_started' | 'widget_refreshed' | 'preferences_updated'
  payload: Record<string, any>
  timestamp: string
}
