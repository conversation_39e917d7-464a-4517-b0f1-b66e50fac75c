---
type: "always_apply"
---

# Development Rules & Standards
## Ultimate Electrical Designer

**Document Version:** 1.0  
**Last Updated:** July 2025

---

## Overview

This document establishes comprehensive development rules that ensure engineering-grade quality, maintain architectural consistency, and enforce professional standards suitable for mission-critical electrical design applications.

---

## Zero Tolerance Policies

### 1. Code Quality Standards
**Policy:** Zero tolerance for code quality violations that compromise system reliability or maintainability.

#### Backend (Python) Requirements
- **100% MyPy compliance** for all production code
- **Zero Ruff linting errors** in committed code
- **Complete type hints** for all public APIs and critical internal functions
- **Docstring coverage** for all public methods using Google style format

#### Frontend (TypeScript) Requirements
- **Zero ESLint errors** in committed code
- **Zero Prettier formatting violations** in committed code
- **Strict TypeScript mode** with no `any` types in production code
- **Complete type annotations** for all component props and API interfaces

### 2. Testing Standards
**Policy:** Comprehensive testing with zero tolerance for test failures in main branch.

#### Test Coverage Requirements
- **95%+ test pass rate** required for all commits
- **100% test coverage** for critical business logic (calculations, authentication, security)
- **85%+ test coverage** for all other modules
- **Real database testing** - no mocking of database operations

#### Test Categories (All Must Pass)
```bash
# Backend Testing
make test-unit              # Core utilities and validation
make test-integration       # Services, middleware, database
make test-api              # API endpoint tests
make test-calculations     # Electrical calculation tests
make test-standards        # IEEE/IEC/EN standards compliance
make test-security         # Security validation tests
make test-types            # MyPy type checking validation

# Frontend Testing
npm run test               # Unit tests with Vitest
npm run test:e2e          # End-to-end tests with Playwright
npm run type-check        # TypeScript compilation check
npm run lint              # ESLint validation
npm run format:check      # Prettier formatting check
```

### 3. Security Standards
**Policy:** Zero tolerance for security vulnerabilities or authentication bypasses.

#### Security Requirements
- **Role-based access control (RBAC)** for all protected endpoints
- **Input validation** at all API boundaries using Pydantic schemas
- **SQL injection prevention** through parameterized queries only
- **XSS prevention** through proper output encoding and CSP headers
- **CSRF protection** for all state-changing operations

---

## Architectural Rules

### 1. 5-Layer Architecture Compliance
**Rule:** All backend code must strictly adhere to the 5-layer architecture pattern.

#### Layer Responsibilities
1. **API Layer** (`src/api/`): HTTP request/response handling, input validation, authentication
2. **Service Layer** (`src/core/services/`): Business logic, workflow orchestration, transaction management
3. **Repository Layer** (`src/core/repositories/`): Data access abstraction, query optimization
4. **Model Layer** (`src/core/models/`): Data structure definition, relationships, constraints
5. **Schema Layer** (`src/core/schemas/`): Request/response validation, data transformation

#### Dependency Rules
- **Downward dependencies only**: Higher layers may depend on lower layers, never upward
- **No layer skipping**: Each layer must interact only with adjacent layers
- **Interface segregation**: Use dependency injection for loose coupling between layers

### 2. Unified Error Handling
**Rule:** All error handling must use the unified error handling system.

#### Required Decorators
```python
# Service layer methods
@handle_service_errors("method_name")
@monitor_service_performance("method_name")

# API endpoints
@handle_api_errors("endpoint_name")

# Database operations
@handle_database_errors("operation_name")

# Security operations
@handle_security_errors("security_operation")
```

### 3. Frontend Module Organization
**Rule:** Frontend code must follow Domain-Driven Design (DDD) principles with clear module boundaries.

#### Module Structure Requirements
```python
src/modules/{domain}/
├── components/     # Domain-specific UI components
├── hooks/          # Domain-specific React hooks
├── services/       # Domain API services
├── types/          # Domain type definitions
└── __tests__/      # Domain-specific tests
```

---

## Code Quality Rules

### 1. Type Safety Requirements
**Rule:** Complete type safety across the entire codebase.

#### Python Type Annotations
```python
# ✅ Required format - Complete type hints
from typing import Optional, List, Dict, Any
from src.core.schemas.base import BaseSchema

def calculate_voltage_drop(
    current: float,
    resistance: float,
    length: float,
    temperature: Optional[float] = None
) -> VoltageDropResult:
    """Calculate voltage drop with complete type safety."""
    pass

# ✅ Required format - Optional[T] instead of T | None
def get_component_by_id(component_id: int) -> Optional[ComponentSchema]:
    """Retrieve component with proper Optional typing."""
    pass
```

#### TypeScript Type Definitions
```typescript
// ✅ Required format - Strict typing
interface ComponentFormData {
  name: string;
  category: ComponentCategory;
  specifications: ComponentSpecifications;
  isActive: boolean;
}

// ✅ Required format - API response types
type ApiResponse<T> = {
  data: T;
  message: string;
  success: boolean;
};
```

### 2. Documentation Standards
**Rule:** Complete documentation for all public APIs and complex business logic.

#### Required Documentation
- **API endpoints**: OpenAPI/Swagger documentation with examples
- **Component props**: TypeScript interfaces with JSDoc comments
- **Business logic**: Inline comments explaining engineering calculations
- **Configuration**: Environment variables and settings documentation

### 3. Performance Standards
**Rule:** All code must meet performance benchmarks for professional electrical design applications.

#### Performance Requirements
- **API Response Time**: < 200ms for standard operations
- **Calculation Performance**: < 500ms for complex electrical calculations
- **Memory Usage**: < 100MB for typical calculation operations
- **Database Queries**: < 100ms for standard CRUD operations

---

## Development Workflow Rules

### 1. Git Workflow Standards
**Rule:** Structured Git workflow with conventional commits and protected branches.

#### Commit Message Format
```
type(scope): description

feat(auth): implement JWT token refresh mechanism
fix(calc): correct voltage drop calculation for aluminum conductors
docs(api): update component management endpoint documentation
test(e2e): add comprehensive authentication flow tests
```

#### Branch Protection Rules
- **Main branch**: Requires pull request review and passing CI/CD
- **Feature branches**: Must be up-to-date with main before merging
- **Hotfix branches**: Emergency fixes with expedited review process

### 2. Code Review Standards
**Rule:** All code changes require peer review with specific quality gates.

#### Review Checklist
- [ ] **Architecture compliance**: Follows 5-layer pattern and DDD principles
- [ ] **Type safety**: Complete type annotations and MyPy/TypeScript compliance
- [ ] **Test coverage**: Adequate test coverage with passing tests
- [ ] **Documentation**: Updated documentation for API changes
- [ ] **Performance**: No performance regressions introduced
- [ ] **Security**: No security vulnerabilities or authentication bypasses

### 3. Deployment Standards
**Rule:** Automated deployment with quality gates and rollback capabilities.

#### Deployment Pipeline
1. **Pre-deployment**: All tests pass, type checking passes, security scan passes
2. **Staging deployment**: Automated deployment to staging environment
3. **Integration testing**: Full E2E test suite execution
4. **Production deployment**: Blue-green deployment with health checks
5. **Post-deployment**: Monitoring and alerting validation

---

## Engineering Standards Compliance

### 1. IEC/EN Standards Integration
**Rule:** All electrical calculations must comply with relevant international standards.

#### Standards Implementation
- **IEC Standards**: International electrotechnical commission standards validation
- **EN Standards**: European electrical standards verification
- **Code Compliance**: Automated validation against local electrical codes

### 2. Calculation Accuracy Requirements
**Rule:** Engineering calculations must meet professional accuracy standards.

#### Accuracy Standards
- **Voltage Drop Calculations**: ±0.1% accuracy compared to manual calculations
- **Load Calculations**: ±0.5% accuracy for demand factor applications
- **Short Circuit Analysis**: ±1% accuracy for fault current calculations
- **Heat Tracing Calculations**: ±2% accuracy for thermal analysis

### 3. Professional Documentation Standards
**Rule:** All generated documentation must meet professional engineering standards.

#### Documentation Requirements
- **Calculation Reports**: Include methodology, assumptions, and references
- **Component Specifications**: Complete technical specifications with manufacturer data
- **Compliance Certificates**: Official documentation for regulatory submissions
- **Drawing Integration**: Seamless integration with CAD systems

---

## Quality Assurance Rules

### 1. Continuous Integration Requirements
**Rule:** All code changes must pass comprehensive CI/CD pipeline.

#### CI/CD Pipeline Stages
1. **Code Quality**: Linting, formatting, and type checking
2. **Security Scanning**: Vulnerability scanning and dependency checking
3. **Unit Testing**: Comprehensive unit test execution
4. **Integration Testing**: Database and API integration tests
5. **E2E Testing**: Full application workflow testing
6. **Performance Testing**: Load testing and performance benchmarking

### 2. Monitoring and Alerting
**Rule:** Comprehensive monitoring with proactive alerting for system health.

#### Monitoring Requirements
- **Application Performance**: Response times, throughput, error rates
- **System Resources**: CPU, memory, disk usage, database performance
- **Security Events**: Authentication failures, suspicious activities
- **Business Metrics**: User engagement, calculation accuracy, system usage

### 3. Backup and Recovery
**Rule:** Comprehensive backup strategy with tested recovery procedures.

#### Backup Requirements
- **Database Backups**: Daily automated backups with point-in-time recovery
- **Configuration Backups**: Version-controlled configuration management
- **Code Repository**: Distributed version control with multiple remotes
- **Recovery Testing**: Monthly recovery procedure testing and validation

---

## Enforcement and Compliance

### 1. Automated Enforcement
**Tool Integration:** Quality gates enforced through automated tooling.

#### Pre-commit Hooks
```bash
# Automatically enforced on every commit
- Linting and formatting validation
- Type checking execution
- Unit test execution for changed files
- Security scanning for sensitive data
```

#### CI/CD Enforcement
- **Pull Request Gates**: All quality checks must pass before merge
- **Deployment Gates**: Production deployment blocked on test failures
- **Monitoring Alerts**: Automatic rollback on performance degradation

### 2. Manual Review Process
**Human Oversight:** Critical changes require senior engineer review.

#### Review Requirements
- **Architecture Changes**: Senior architect approval required
- **Security Changes**: Security team review and approval
- **Performance Changes**: Performance impact assessment required
- **Standards Changes**: Engineering standards committee approval

---

These development rules establish the comprehensive governance framework for maintaining engineering-grade quality throughout the Ultimate Electrical Designer development lifecycle. All team members must adhere to these rules to ensure the delivery of professional-quality electrical design software that meets the highest industry standards.
