/**
 * @vitest-environment jsdom
 */

import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { fireEvent, render, screen } from '@testing-library/react'
import { beforeEach, describe, expect, it, vi } from 'vitest'
import { FeaturesSection } from '../../components/FeaturesSection'
import { defaultLandingPageData } from '../../utils'

// Mock Intersection Observer
const mockIntersectionObserver = vi.fn()
mockIntersectionObserver.mockReturnValue({
  observe: () => null,
  unobserve: () => null,
  disconnect: () => null,
})
window.IntersectionObserver = mockIntersectionObserver

const createTestQueryClient = () =>
  new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
      },
    },
  })

const renderWithProviders = (component: React.ReactElement) => {
  const queryClient = createTestQueryClient()
  return render(<QueryClientProvider client={queryClient}>{component}</QueryClientProvider>)
}

describe('FeaturesSection', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('renders all features from the data', () => {
    renderWithProviders(<FeaturesSection features={defaultLandingPageData.features} />)

    // Check that all feature titles are rendered
    expect(screen.getByText('Heat Tracing Design')).toBeInTheDocument()
    expect(screen.getByText('Load Calculations')).toBeInTheDocument()
    expect(screen.getByText('Project Management')).toBeInTheDocument()
    expect(screen.getByText('Cable Sizing')).toBeInTheDocument()
    expect(screen.getByText('Safety Compliance')).toBeInTheDocument()
    expect(screen.getByText('Documentation')).toBeInTheDocument()
  })

  it('renders section header with correct content', () => {
    renderWithProviders(<FeaturesSection features={defaultLandingPageData.features} />)

    expect(screen.getByText('Professional Features')).toBeInTheDocument()
    expect(screen.getByText('Engineering-Grade Capabilities')).toBeInTheDocument()
    expect(screen.getByText(/Built for professional electrical engineers/)).toBeInTheDocument()
  })

  it('renders feature descriptions', () => {
    renderWithProviders(<FeaturesSection features={defaultLandingPageData.features} />)

    expect(screen.getByText(/Advanced heat tracing calculations/)).toBeInTheDocument()
    expect(screen.getByText(/Comprehensive electrical load analysis/)).toBeInTheDocument()
    expect(screen.getByText(/Organize and track multiple projects/)).toBeInTheDocument()
  })

  it('renders "Learn more" links for each feature', () => {
    renderWithProviders(<FeaturesSection features={defaultLandingPageData.features} />)

    const learnMoreLinks = screen.getAllByText('Learn more')
    expect(learnMoreLinks).toHaveLength(defaultLandingPageData.features.length)
  })

  it('handles mouse hover interactions', () => {
    renderWithProviders(<FeaturesSection features={defaultLandingPageData.features} />)

    const firstFeatureCard = screen.getByText('Heat Tracing Design').closest('div')
    expect(firstFeatureCard).toBeInTheDocument()

    if (firstFeatureCard) {
      fireEvent.mouseEnter(firstFeatureCard)
      fireEvent.mouseLeave(firstFeatureCard)
    }
  })

  it('renders with proper accessibility attributes', () => {
    renderWithProviders(<FeaturesSection features={defaultLandingPageData.features} />)

    // Check for proper heading hierarchy
    const sectionHeading = screen.getByRole('heading', { level: 2 })
    expect(sectionHeading).toHaveTextContent('Engineering-Grade Capabilities')

    // Check for feature headings
    const featureHeadings = screen.getAllByRole('heading', { level: 3 })
    expect(featureHeadings).toHaveLength(defaultLandingPageData.features.length)

    // Check for articles (feature cards)
    const articles = screen.getAllByRole('article')
    expect(articles).toHaveLength(defaultLandingPageData.features.length)
  })

  it('applies custom className when provided', () => {
    const { container } = renderWithProviders(
      <FeaturesSection features={defaultLandingPageData.features} className="custom-class" />
    )

    const sectionElement = container.querySelector('section')
    expect(sectionElement).toHaveClass('custom-class')
  })

  it('renders empty state when no features provided', () => {
    renderWithProviders(<FeaturesSection features={[]} />)

    // Section header should still be rendered
    expect(screen.getByText('Engineering-Grade Capabilities')).toBeInTheDocument()

    // But no feature cards
    const articles = screen.queryAllByRole('article')
    expect(articles).toHaveLength(0)
  })

  it('renders feature icons correctly', () => {
    renderWithProviders(<FeaturesSection features={defaultLandingPageData.features} />)

    // Check that decorative elements exist
    const decorativeElements = screen.getAllByLabelText('', { exact: false })
    expect(decorativeElements.length).toBeGreaterThanOrEqual(0)
  })

  it('has proper grid layout classes', () => {
    const { container } = renderWithProviders(
      <FeaturesSection features={defaultLandingPageData.features} />
    )

    const gridContainer = container.querySelector('.grid')
    expect(gridContainer).toHaveClass('grid-cols-1', 'md:grid-cols-2', 'lg:grid-cols-3')
  })

  it('renders with animation classes', () => {
    const { container } = renderWithProviders(
      <FeaturesSection features={defaultLandingPageData.features} />
    )

    const animatedElements = container.querySelectorAll('.animate-slideIn')
    expect(animatedElements.length).toBeGreaterThan(0)
  })
})
