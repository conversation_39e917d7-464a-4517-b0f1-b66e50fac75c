'use client'

import { But<PERSON> } from '@/components/ui/button'
import { useAuth } from '@/hooks/useAuth'
import { cn } from '@/lib/utils'
import Link from 'next/link'
import { useEffect, useRef } from 'react'
import { useLandingPageStore } from '../hooks'
import type { HeroSectionProps } from '../types'

/**
 * Modern hero section with enhanced animations and professional design
 */
export function HeroSection({
  hero,
  className,
}: Omit<HeroSectionProps, 'isAuthenticated' | 'user'>) {
  const { isAuthenticated, user } = useAuth()
  const { setHeroVisible } = useLandingPageStore()
  const heroRef = useRef<HTMLElement>(null)

  // Intersection observer for animations
  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        setHeroVisible(entry.isIntersecting)
      },
      {
        threshold: 0.3,
        rootMargin: '0px 0px -10% 0px', // Trigger slightly before element is fully visible
      }
    )

    if (heroRef.current) {
      observer.observe(heroRef.current)
    }

    return () => observer.disconnect()
  }, [setHeroVisible])

  return (
    <section
      ref={heroRef}
      className={cn(
        'relative overflow-hidden bg-gradient-to-br from-neutral-900 via-brand-dark to-brand-secondary',
        className
      )}
    >
      {/* Background Pattern */}
      {hero.backgroundPattern && (
        <div className="absolute inset-0 opacity-5" aria-hidden="true">
          <svg className="absolute inset-0 h-full w-full" fill="none" viewBox="0 0 400 400">
            <defs>
              <pattern id="hero-grid" width="40" height="40" patternUnits="userSpaceOnUse">
                <path d="M 40 0 L 0 0 0 40" fill="none" stroke="currentColor" strokeWidth="1" />
              </pattern>
            </defs>
            <rect width="100%" height="100%" fill="url(#hero-grid)" />
          </svg>
        </div>
      )}

      {/* Floating Elements */}
      {hero.floatingElements && (
        <>
          <div
            className="absolute left-10 top-20 h-20 w-20 animate-pulse rounded-full bg-brand-primary/10 blur-xl"
            aria-hidden="true"
          />
          <div
            className="absolute bottom-20 right-10 h-32 w-32 animate-pulse rounded-full bg-brand-accent/10 blur-xl"
            style={{ animationDelay: '1s' }}
            aria-hidden="true"
          />
          <div
            className="absolute left-1/4 top-1/2 h-16 w-16 animate-pulse rounded-full bg-brand-secondary/10 blur-xl"
            style={{ animationDelay: '2s' }}
            aria-hidden="true"
          />
        </>
      )}

      <div className="relative mx-auto max-w-7xl px-4 py-16 sm:px-6 sm:py-20 lg:px-8 lg:py-32">
        <div className="text-center">
          {/* Engineering Badge */}
          <div className="animate-fadeIn mb-6 inline-flex items-center rounded-full border border-white/20 bg-white/10 px-4 py-2 backdrop-blur-sm sm:mb-8 sm:px-6 sm:py-3">
            <svg
              className="mr-2 h-4 w-4 text-brand-accent sm:mr-3 sm:h-5 sm:w-5"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              aria-hidden="true"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
            <span className="text-xs font-semibold uppercase tracking-wide text-white sm:text-sm">
              {hero.badge.text}
            </span>
          </div>

          {/* Main Title */}
          <h1 className="animate-slideIn mb-4 text-4xl font-bold text-white sm:mb-6 sm:text-5xl lg:text-7xl">
            Ultimate Electrical
            <span className="block bg-gradient-to-r from-brand-primary to-brand-accent bg-clip-text text-transparent">
              Designer
            </span>
          </h1>

          {/* Subtitle */}
          <p
            className="animate-slideIn mx-auto mb-8 max-w-3xl px-4 text-lg leading-relaxed text-white/90 sm:mb-10 sm:px-0 sm:text-xl"
            style={{ animationDelay: '0.2s' }}
          >
            {hero.description}
          </p>

          {/* Action Buttons */}
          <div
            className="animate-slideIn mb-8 flex flex-col justify-center gap-3 px-4 sm:mb-12 sm:flex-row sm:gap-4 sm:px-0 lg:gap-6"
            style={{ animationDelay: '0.4s' }}
          >
            {isAuthenticated ? (
              <>
                <Button
                  size="lg"
                  className="h-12 touch-manipulation bg-brand-primary px-6 text-base font-semibold text-white shadow-xl transition-all duration-300 hover:scale-105 hover:bg-brand-primary/90 sm:h-14 sm:px-8 sm:text-lg"
                  asChild
                >
                  <Link href="/dashboard">
                    <svg
                      className="mr-2 h-4 w-4 sm:h-5 sm:w-5"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                      aria-hidden="true"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"
                      />
                    </svg>
                    Go to Dashboard
                  </Link>
                </Button>
                <div className="flex items-center justify-center py-3 text-base text-white/90 sm:text-lg">
                  <div className="flex items-center space-x-2">
                    <div
                      className="h-2 w-2 animate-pulse rounded-full bg-brand-accent"
                      aria-hidden="true"
                    />
                    <span className="truncate">Welcome back, {user?.name}!</span>
                  </div>
                </div>
              </>
            ) : (
              <>
                <Button
                  size="lg"
                  className="h-12 touch-manipulation bg-brand-primary px-6 text-base font-semibold text-white shadow-xl transition-all duration-300 hover:scale-105 hover:bg-brand-primary/90 sm:h-14 sm:px-8 sm:text-lg"
                  asChild
                >
                  <Link href="/login">
                    <svg
                      className="mr-2 h-4 w-4 sm:h-5 sm:w-5"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                      aria-hidden="true"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M13 10V3L4 14h7v7l9-11h-7z"
                      />
                    </svg>
                    Start Designing
                  </Link>
                </Button>
                <Button
                  size="lg"
                  className="h-12 touch-manipulation border-2 border-white/30 px-6 text-base font-semibold text-white backdrop-blur-sm transition-all duration-300 hover:scale-105 hover:bg-white/10 sm:h-14 sm:px-8 sm:text-lg"
                  variant="outline"
                  asChild
                >
                  <Link href="/dashboard">
                    <svg
                      className="mr-2 h-4 w-4 sm:h-5 sm:w-5"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                      aria-hidden="true"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                      />
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                      />
                    </svg>
                    View Demo
                  </Link>
                </Button>
              </>
            )}
          </div>

          {/* Trust Indicators */}
          <div
            className="animate-slideIn flex flex-wrap justify-center gap-4 px-4 text-white/70 sm:gap-6 sm:px-0 lg:gap-8"
            style={{ animationDelay: '0.6s' }}
          >
            <div className="flex items-center space-x-2">
              <svg
                className="h-4 w-4 text-brand-accent sm:h-5 sm:w-5"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                aria-hidden="true"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"
                />
              </svg>
              <span className="text-xs font-medium sm:text-sm">Enterprise Security</span>
            </div>
            <div className="flex items-center space-x-2">
              <svg
                className="h-4 w-4 text-brand-accent sm:h-5 sm:w-5"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                aria-hidden="true"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                />
              </svg>
              <span className="text-xs font-medium sm:text-sm">Code Compliant</span>
            </div>
            <div className="flex items-center space-x-2">
              <svg
                className="h-4 w-4 text-brand-accent sm:h-5 sm:w-5"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                aria-hidden="true"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M13 10V3L4 14h7v7l9-11h-7z"
                />
              </svg>
              <span className="text-xs font-medium sm:text-sm">Industry Standard</span>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
