/**
 * Settings Reset Component
 * Ultimate Electrical Designer - Settings & User Preferences
 */

'use client'

import React, { useState } from 'react'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog'
import { Button } from '@/components/ui/button'
import { Checkbox } from '@/components/ui/checkbox'
import { Label } from '@/components/ui/label'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { RotateCcw, AlertTriangle, Info, Trash2, RefreshCw } from 'lucide-react'
import { useSettings } from '../hooks/useSettings'
import { SETTINGS_CATEGORIES, DEFAULT_USER_PREFERENCES } from '../constants'
import { formatPreferenceValue } from '../utils'
import type { SettingsCategory, UserPreferences } from '../types'

/**
 * Settings Reset Component
 */
export function SettingsReset() {
  const settings = useSettings()
  const { ui, preferences, hasChanges, actions } = settings

  const [confirmReset, setConfirmReset] = useState(false)
  const [selectedCategories, setSelectedCategories] = useState<SettingsCategory[]>([])
  const [resetType, setResetType] = useState<'all' | 'categories' | 'unsaved'>('all')

  // Handle category selection
  const handleCategoryToggle = (categoryId: SettingsCategory) => {
    setSelectedCategories((prev) =>
      prev.includes(categoryId) ? prev.filter((id) => id !== categoryId) : [...prev, categoryId]
    )
  }

  // Handle reset type change
  const handleResetTypeChange = (type: 'all' | 'categories' | 'unsaved') => {
    setResetType(type)
    if (type !== 'categories') {
      setSelectedCategories([])
    }
  }

  // Handle reset confirmation
  const handleResetConfirm = async () => {
    try {
      if (resetType === 'unsaved') {
        // Just reset form data
        actions.resetForm()
      } else if (resetType === 'categories') {
        // Reset specific categories (would need backend support)
        // For now, reset all
        await actions.resetPreferences()
      } else {
        // Reset all preferences
        await actions.resetPreferences()
      }

      setConfirmReset(false)
      setSelectedCategories([])
      setResetType('all')
    } catch (error) {
      console.error('Reset failed:', error)
    }
  }

  // Get affected settings preview
  const getAffectedSettings = () => {
    if (!preferences) return []

    const affected: Array<{
      category: string
      field: string
      current: any
      default: any
    }> = []

    if (resetType === 'unsaved') {
      // Show only unsaved changes
      Object.keys(settings.formData).forEach((key) => {
        const currentValue = preferences[key as keyof UserPreferences]
        const defaultValue = DEFAULT_USER_PREFERENCES[key as keyof UserPreferences]

        if (currentValue !== defaultValue) {
          affected.push({
            category: 'Current',
            field: key,
            current: currentValue,
            default: defaultValue,
          })
        }
      })
    } else {
      // Show all settings that differ from defaults
      Object.keys(DEFAULT_USER_PREFERENCES).forEach((key) => {
        const currentValue = preferences[key as keyof UserPreferences]
        const defaultValue = DEFAULT_USER_PREFERENCES[key as keyof UserPreferences]

        if (currentValue !== defaultValue) {
          // Find category for this field
          const category = SETTINGS_CATEGORIES.find((cat) =>
            cat.sections.some((section) => section.fields.some((field) => field.id === key))
          )

          if (
            resetType === 'all' ||
            (resetType === 'categories' &&
              selectedCategories.includes(category?.id as SettingsCategory))
          ) {
            affected.push({
              category: category?.label || 'Unknown',
              field: key,
              current: currentValue,
              default: defaultValue,
            })
          }
        }
      })
    }

    return affected
  }

  const affectedSettings = getAffectedSettings()

  return (
    <>
      {/* Reset Dialog */}
      <Dialog
        open={ui.showResetDialog}
        onOpenChange={(open) => !open && actions.closeResetDialog()}
      >
        <DialogContent className="max-h-[80vh] max-w-2xl">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <RotateCcw className="h-5 w-5" />
              Reset Settings
            </DialogTitle>
            <DialogDescription>
              Choose what settings to reset to their default values. This action cannot be undone.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            {/* Reset Type Selection */}
            <div className="space-y-3">
              <Label>Reset Options</Label>

              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="reset-unsaved"
                    checked={resetType === 'unsaved'}
                    onCheckedChange={() => handleResetTypeChange('unsaved')}
                    disabled={!hasChanges}
                  />
                  <Label htmlFor="reset-unsaved" className="text-sm">
                    Reset unsaved changes only
                  </Label>
                  {hasChanges && (
                    <Badge variant="secondary" className="text-xs">
                      {Object.keys(settings.formData).length} changes
                    </Badge>
                  )}
                </div>

                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="reset-categories"
                    checked={resetType === 'categories'}
                    onCheckedChange={() => handleResetTypeChange('categories')}
                  />
                  <Label htmlFor="reset-categories" className="text-sm">
                    Reset specific categories
                  </Label>
                </div>

                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="reset-all"
                    checked={resetType === 'all'}
                    onCheckedChange={() => handleResetTypeChange('all')}
                  />
                  <Label htmlFor="reset-all" className="text-sm">
                    Reset all settings to defaults
                  </Label>
                </div>
              </div>
            </div>

            {/* Category Selection */}
            {resetType === 'categories' && (
              <div className="space-y-3">
                <Separator />
                <Label>Select Categories to Reset</Label>

                <div className="grid grid-cols-2 gap-2">
                  {SETTINGS_CATEGORIES.map((category) => (
                    <div key={category.id} className="flex items-center space-x-2">
                      <Checkbox
                        id={`reset-${category.id}`}
                        checked={selectedCategories.includes(category.id)}
                        onCheckedChange={() => handleCategoryToggle(category.id)}
                      />
                      <Label htmlFor={`reset-${category.id}`} className="text-sm">
                        {category.label}
                      </Label>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Affected Settings Preview */}
            {affectedSettings.length > 0 && (
              <div className="space-y-3">
                <Separator />
                <Label>Settings to be Reset ({affectedSettings.length})</Label>

                <div className="max-h-48 overflow-y-auto rounded-md border">
                  <div className="space-y-2 p-3">
                    {affectedSettings.map((setting, index) => (
                      <div key={index} className="flex items-center justify-between text-sm">
                        <div className="flex-1">
                          <span className="font-medium">{setting.field}</span>
                          <span className="ml-2 text-muted-foreground">({setting.category})</span>
                        </div>
                        <div className="flex items-center gap-2 text-xs">
                          <span className="text-muted-foreground">
                            {formatPreferenceValue(
                              setting.field as keyof UserPreferences,
                              setting.current
                            )}
                          </span>
                          <span>→</span>
                          <span className="text-primary">
                            {formatPreferenceValue(
                              setting.field as keyof UserPreferences,
                              setting.default
                            )}
                          </span>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}

            {/* Warning */}
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                This action cannot be undone. Consider exporting your current settings before
                resetting.
              </AlertDescription>
            </Alert>

            {/* No Changes Info */}
            {affectedSettings.length === 0 && (
              <Alert>
                <Info className="h-4 w-4" />
                <AlertDescription>
                  {resetType === 'unsaved'
                    ? 'No unsaved changes to reset.'
                    : 'All selected settings are already at their default values.'}
                </AlertDescription>
              </Alert>
            )}
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={actions.closeResetDialog}>
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={() => setConfirmReset(true)}
              disabled={affectedSettings.length === 0}
            >
              <RotateCcw className="mr-2 h-4 w-4" />
              Reset Settings
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Confirmation Dialog */}
      <AlertDialog open={confirmReset} onOpenChange={setConfirmReset}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-destructive" />
              Confirm Reset
            </AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to reset {affectedSettings.length} setting
              {affectedSettings.length !== 1 ? 's' : ''} to their default values? This action cannot
              be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleResetConfirm}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              Reset Settings
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  )
}

/**
 * Quick Reset Button Component
 */
export function QuickResetButton({
  variant = 'outline',
  size = 'sm',
  className = '',
}: {
  variant?: 'default' | 'outline' | 'ghost' | 'destructive'
  size?: 'sm' | 'default' | 'lg'
  className?: string
}) {
  const { actions } = useSettings()

  return (
    <Button variant={variant} size={size} onClick={actions.openResetDialog} className={className}>
      <RotateCcw className="mr-2 h-4 w-4" />
      Reset
    </Button>
  )
}

/**
 * Reset Unsaved Changes Button
 */
export function ResetUnsavedButton({
  variant = 'ghost',
  size = 'sm',
  className = '',
}: {
  variant?: 'default' | 'outline' | 'ghost' | 'destructive'
  size?: 'sm' | 'default' | 'lg'
  className?: string
}) {
  const { hasChanges, actions } = useSettings()

  if (!hasChanges) return null

  return (
    <Button variant={variant} size={size} onClick={actions.resetForm} className={className}>
      <RefreshCw className="mr-2 h-4 w-4" />
      Discard Changes
    </Button>
  )
}

export default SettingsReset
