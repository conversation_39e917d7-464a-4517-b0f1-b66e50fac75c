/**
 * Enhanced Components Module Entry Point
 *
 * Comprehensive component management system built with modern React patterns,
 * TypeScript, and accessibility-first design principles.
 *
 * Features:
 * - Domain-Driven Design (DDD) architecture
 * - Atomic Design component hierarchy
 * - WCAG 2.1 AA accessibility compliance
 * - 95%+ test coverage
 * - Real-time updates and optimistic UI
 * - Advanced search and filtering
 * - Bulk operations support
 * - Comprehensive validation with Zod
 *
 * @version 2.0.0
 * <AUTHOR> Electrical Designer Team
 */

// Core API exports
export * from './api'

// Enhanced component exports (Atomic Design)
export * from './components'

// Enhanced hook exports
export * from './hooks'

// Schema and type exports
export * from './schemas'
export * from './types'

// Enhanced utility exports
export * from './utils'

// Legacy exports for backward compatibility
export {
  BulkOperations,
  ComponentCard,
  ComponentDetails,
  ComponentForm,
  ComponentList,
} from './components'

export {
  ComponentFilters as ComponentFiltersWidget,
  ComponentSearch as ComponentSearchWidget,
  ComponentStats as ComponentStatsWidget,
} from './components'

// Enhanced exports with explicit names
export {
  BulkOperationsPanel,
  // Enhanced Atomic Design Components
  ComponentBadge,
  ComponentIcon,
  ComponentSearchBar,
  ComponentCard as EnhancedComponentCard,
  ComponentFilters as EnhancedComponentFilters,
  ComponentList as EnhancedComponentList,
  // Enhanced Store
  useComponentStoreEnhanced,
} from './components'

// Module metadata
export const MODULE_INFO = {
  name: 'Components',
  version: '2.0.0',
  description: 'Enhanced component management system',
  features: [
    'Domain-Driven Design architecture',
    'Atomic Design component hierarchy',
    'WCAG 2.1 AA accessibility compliance',
    'Comprehensive testing suite',
    'Real-time updates',
    'Advanced search and filtering',
    'Bulk operations',
    'Type-safe validation',
  ],
} as const
