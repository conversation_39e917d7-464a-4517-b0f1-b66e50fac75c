/**
 * Authentication form validation schemas and utilities
 * Uses Zod for type-safe validation with comprehensive error handling
 */

import { z } from 'zod'

// Password validation regex patterns
const PASSWORD_PATTERNS = {
  uppercase: /[A-Z]/,
  lowercase: /[a-z]/,
  digit: /\d/,
  special: /[!@#$%^&*(),.?":{}|<>]/,
} as const

// Username validation pattern (alphanumeric + underscore/hyphen)
const USERNAME_PATTERN = /^[a-zA-Z0-9_-]+$/

/**
 * Password strength validation with detailed requirements
 */
const passwordSchema = z
  .string()
  .min(8, 'Password must be at least 8 characters long')
  .max(128, 'Password must not exceed 128 characters')
  .refine(
    (password) => PASSWORD_PATTERNS.uppercase.test(password),
    'Password must contain at least one uppercase letter'
  )
  .refine(
    (password) => PASSWORD_PATTERNS.lowercase.test(password),
    'Password must contain at least one lowercase letter'
  )
  .refine(
    (password) => PASSWORD_PATTERNS.digit.test(password),
    'Password must contain at least one number'
  )
  .refine(
    (password) => PASSWORD_PATTERNS.special.test(password),
    'Password must contain at least one special character (!@#$%^&*(),.?":{}|<>)'
  )

/**
 * Username validation schema
 */
const usernameSchema = z
  .string()
  .min(3, 'Username must be at least 3 characters long')
  .max(50, 'Username must not exceed 50 characters')
  .refine(
    (username) => USERNAME_PATTERN.test(username),
    'Username can only contain letters, numbers, underscores, and hyphens'
  )

/**
 * Email validation schema
 */
const emailSchema = z
  .string()
  .email('Please enter a valid email address')
  .max(254, 'Email address is too long')

/**
 * Login form validation schema
 */
export const loginSchema = z.object({
  username: z
    .string()
    .min(1, 'Username or email is required')
    .max(254, 'Username or email is too long'),
  password: z
    .string()
    .min(1, 'Password is required')
    .min(8, 'Password must be at least 8 characters long'),
})

/**
 * Registration form validation schema
 */
export const registerSchema = z
  .object({
    name: usernameSchema,
    email: emailSchema,
    password: passwordSchema,
    confirmPassword: z.string().min(1, 'Please confirm your password'),
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: 'Passwords do not match',
    path: ['confirmPassword'],
  })

/**
 * Password change validation schema
 */
export const passwordChangeSchema = z
  .object({
    currentPassword: z.string().min(1, 'Current password is required'),
    newPassword: passwordSchema,
    confirmPassword: z.string().min(1, 'Please confirm your new password'),
  })
  .refine((data) => data.newPassword === data.confirmPassword, {
    message: 'New passwords do not match',
    path: ['confirmPassword'],
  })
  .refine((data) => data.currentPassword !== data.newPassword, {
    message: 'New password must be different from current password',
    path: ['newPassword'],
  })

// Type exports for form data
export type LoginFormData = z.infer<typeof loginSchema>
export type RegisterFormData = z.infer<typeof registerSchema>
export type PasswordChangeFormData = z.infer<typeof passwordChangeSchema>

/**
 * Validation result interface
 */
export interface ValidationResult {
  success: boolean
  errors: Record<string, string>
  data?: any
}

/**
 * Validate form data against a schema
 */
export function validateForm<T>(schema: z.ZodSchema<T>, data: unknown): ValidationResult {
  try {
    const validatedData = schema.parse(data)
    return {
      success: true,
      errors: {},
      data: validatedData,
    }
  } catch (error) {
    if (error instanceof z.ZodError) {
      const errors: Record<string, string> = {}
      error.issues.forEach((issue) => {
        const path = issue.path.join('.')
        errors[path] = issue.message
      })
      return {
        success: false,
        errors,
      }
    }
    return {
      success: false,
      errors: { general: 'Validation failed' },
    }
  }
}

/**
 * Validate a single field against its schema
 */
export function validateField(
  fieldName: string,
  value: unknown,
  schema: z.ZodSchema
): { isValid: boolean; error?: string } {
  try {
    // For simple validation, just validate the value directly
    // This is a simplified approach since we can't easily access schema.shape
    if (fieldName === 'password') {
      passwordSchema.parse(value)
    } else if (fieldName === 'email') {
      emailSchema.parse(value)
    } else if (fieldName === 'username' || fieldName === 'name') {
      usernameSchema.parse(value)
    } else {
      // Generic string validation
      z.string().min(1).parse(value)
    }
    return { isValid: true }
  } catch (error) {
    if (error instanceof z.ZodError) {
      const fieldError = error.issues.find((issue) => issue.path.includes(fieldName))
      return {
        isValid: false,
        error: fieldError?.message || 'Invalid value',
      }
    }
    return { isValid: false, error: 'Validation failed' }
  }
}

/**
 * Password strength calculation
 */
export interface PasswordStrength {
  score: number // 0-4
  label: 'Very Weak' | 'Weak' | 'Fair' | 'Good' | 'Strong'
  color: 'red' | 'orange' | 'yellow' | 'blue' | 'green'
  requirements: {
    length: boolean
    uppercase: boolean
    lowercase: boolean
    digit: boolean
    special: boolean
  }
}

/**
 * Calculate password strength score and requirements
 */
export function calculatePasswordStrength(password: string): PasswordStrength {
  const requirements = {
    length: password.length >= 8,
    uppercase: PASSWORD_PATTERNS.uppercase.test(password),
    lowercase: PASSWORD_PATTERNS.lowercase.test(password),
    digit: PASSWORD_PATTERNS.digit.test(password),
    special: PASSWORD_PATTERNS.special.test(password),
  }

  const score = Object.values(requirements).filter(Boolean).length

  const strengthMap = {
    0: { label: 'Very Weak' as const, color: 'red' as const },
    1: { label: 'Very Weak' as const, color: 'red' as const },
    2: { label: 'Weak' as const, color: 'orange' as const },
    3: { label: 'Fair' as const, color: 'yellow' as const },
    4: { label: 'Good' as const, color: 'blue' as const },
    5: { label: 'Strong' as const, color: 'green' as const },
  }

  const { label, color } = strengthMap[score as keyof typeof strengthMap]

  return {
    score,
    label,
    color,
    requirements,
  }
}

/**
 * Sanitize input to prevent XSS
 */
export function sanitizeInput(input: string): string {
  return input
    .replace(/[<>]/g, '') // Remove angle brackets
    .replace(/javascript:/gi, '') // Remove javascript: protocol
    .replace(/on\w+=/gi, '') // Remove event handlers
    .trim()
}

/**
 * Check if email format is valid (additional validation beyond Zod)
 */
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email) && email.length <= 254
}

/**
 * Check if username format is valid
 */
export function isValidUsername(username: string): boolean {
  return USERNAME_PATTERN.test(username) && username.length >= 3 && username.length <= 50
}

/**
 * Sanitize login data to prevent XSS and injection attacks
 */
export function sanitizeLoginData(data: LoginFormData): LoginFormData {
  return {
    username: sanitizeInput(data.username),
    password: data.password, // Don't sanitize password as it may contain special chars
  }
}

/**
 * Sanitize registration data to prevent XSS and injection attacks
 */
export function sanitizeRegisterData(data: RegisterFormData): RegisterFormData {
  return {
    name: sanitizeInput(data.name),
    email: sanitizeInput(data.email),
    password: data.password, // Don't sanitize password as it may contain special chars
    confirmPassword: data.confirmPassword, // Don't sanitize password as it may contain special chars
  }
}

/**
 * Parse authentication errors into user-friendly messages
 */
export function parseAuthError(error: unknown): { message: string; field?: string } {
  if (!error) {
    return { message: 'An unknown error occurred' }
  }

  if (typeof error === 'string') {
    return { message: error }
  }

  if (error instanceof Error) {
    return { message: error.message }
  }

  if (typeof error === 'object' && error !== null) {
    const errorObj = error as any

    // Handle API error responses
    if (errorObj.message) {
      return { message: errorObj.message, field: errorObj.field }
    }

    // Handle validation errors
    if (errorObj.errors && Array.isArray(errorObj.errors)) {
      const firstError = errorObj.errors[0]
      return {
        message: firstError.message || 'Validation error',
        field: firstError.field,
      }
    }

    // Handle network errors
    if (errorObj.status === 401) {
      return { message: 'Invalid username or password' }
    }

    if (errorObj.status === 429) {
      return { message: 'Too many login attempts. Please try again later.' }
    }

    if (errorObj.status >= 500) {
      return { message: 'Server error. Please try again later.' }
    }
  }

  return { message: 'An unexpected error occurred. Please try again.' }
}
