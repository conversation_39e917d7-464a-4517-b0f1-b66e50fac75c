/**
 * Unit tests for admin dashboard Zustand store
 */

import { renderHook, act } from '@testing-library/react'
import {
  useAdminDashboardStore,
  useAdminDashboardSelectors,
} from '../../hooks/useAdminDashboardStore'
import type {
  AdminDashboardData,
  UserManagementSummary,
  ProjectOversightSummary,
} from '../../types'

// Mock data
const mockUser: UserManagementSummary = {
  id: '1',
  name: 'Test User',
  email: '<EMAIL>',
  role: 'EDITOR',
  isActive: true,
  lastLogin: '2024-01-01T00:00:00Z',
  createdAt: '2024-01-01T00:00:00Z',
  projectCount: 5,
  calculationCount: 10,
  loginCount: 20,
}

const mockProject: ProjectOversightSummary = {
  id: '1',
  name: 'Test Project',
  description: 'Test Description',
  owner: 'Test Owner',
  ownerEmail: '<EMAIL>',
  status: 'active',
  type: 'heat_tracing',
  priority: 'high',
  progress: 75,
  createdAt: '2024-01-01T00:00:00Z',
  lastModified: '2024-01-01T00:00:00Z',
  calculationCount: 3,
  teamSize: 2,
}

const mockData: AdminDashboardData = {
  metrics: {
    totalUsers: 100,
    activeUsers: 80,
    inactiveUsers: 20,
    adminUsers: 5,
    editorUsers: 30,
    viewerUsers: 65,
    totalProjects: 50,
    activeProjects: 30,
    completedProjects: 15,
    totalCalculations: 200,
    completedCalculations: 180,
    failedCalculations: 20,
    systemUptime: '5 days, 3 hours',
    serverLoad: 0.65,
    memoryUsage: 75,
    diskUsage: 60,
    databaseConnections: 25,
    apiRequestsToday: 1500,
    errorRate: 2.5,
    lastBackup: '2024-01-01T00:00:00Z',
  },
  userSummaries: [mockUser],
  componentLibraryStats: {
    totalComponents: 500,
    activeComponents: 450,
    categoriesCount: 10,
    recentlyAdded: 5,
    mostUsedComponents: [],
    componentsByCategory: [],
  },
  projectSummaries: [mockProject],
  auditLogs: [],
  securityAlerts: [],
  systemConfiguration: [],
  quickActions: [],
  widgets: [],
}

describe('useAdminDashboardStore', () => {
  beforeEach(() => {
    // Reset store state before each test
    useAdminDashboardStore.getState().resetState()
  })

  describe('Initial State', () => {
    it('should have correct initial state', () => {
      const { result } = renderHook(() => useAdminDashboardStore())

      expect(result.current.data).toBeNull()
      expect(result.current.isLoading).toBe(false)
      expect(result.current.error).toBeNull()
      expect(result.current.lastUpdated).toBeNull()
      expect(result.current.selectedUser).toBeNull()
      expect(result.current.selectedProject).toBeNull()
      expect(result.current.userFilter).toBe('all')
      expect(result.current.projectFilter).toBe('all')
      expect(result.current.layout).toBe('grid')
      expect(result.current.sidebarCollapsed).toBe(false)
    })
  })

  describe('Data Actions', () => {
    it('should set data correctly', () => {
      const { result } = renderHook(() => useAdminDashboardStore())

      act(() => {
        result.current.setData(mockData)
      })

      expect(result.current.data).toEqual(mockData)
      expect(result.current.lastUpdated).toBeTruthy()
      expect(result.current.error).toBeNull()
    })

    it('should update metrics correctly', () => {
      const { result } = renderHook(() => useAdminDashboardStore())

      act(() => {
        result.current.setData(mockData)
      })

      const updatedMetrics = { totalUsers: 150, activeUsers: 120 }

      act(() => {
        result.current.updateMetrics(updatedMetrics)
      })

      expect(result.current.data?.metrics.totalUsers).toBe(150)
      expect(result.current.data?.metrics.activeUsers).toBe(120)
      expect(result.current.data?.metrics.inactiveUsers).toBe(20) // Should remain unchanged
    })

    it('should add user correctly', () => {
      const { result } = renderHook(() => useAdminDashboardStore())

      act(() => {
        result.current.setData(mockData)
      })

      const newUser: UserManagementSummary = {
        ...mockUser,
        id: '2',
        name: 'New User',
        email: '<EMAIL>',
      }

      act(() => {
        result.current.addUser(newUser)
      })

      expect(result.current.data?.userSummaries).toHaveLength(2)
      expect(result.current.data?.userSummaries[1]).toEqual(newUser)
    })

    it('should update user correctly', () => {
      const { result } = renderHook(() => useAdminDashboardStore())

      act(() => {
        result.current.setData(mockData)
      })

      const updates = { name: 'Updated Name', isActive: false }

      act(() => {
        result.current.updateUser('1', updates)
      })

      expect(result.current.data?.userSummaries[0].name).toBe('Updated Name')
      expect(result.current.data?.userSummaries[0].isActive).toBe(false)
      expect(result.current.data?.userSummaries[0].email).toBe('<EMAIL>') // Should remain unchanged
    })

    it('should remove user correctly', () => {
      const { result } = renderHook(() => useAdminDashboardStore())

      act(() => {
        result.current.setData(mockData)
      })

      act(() => {
        result.current.removeUser('1')
      })

      expect(result.current.data?.userSummaries).toHaveLength(0)
    })

    it('should add project correctly', () => {
      const { result } = renderHook(() => useAdminDashboardStore())

      act(() => {
        result.current.setData(mockData)
      })

      const newProject: ProjectOversightSummary = {
        ...mockProject,
        id: '2',
        name: 'New Project',
      }

      act(() => {
        result.current.addProject(newProject)
      })

      expect(result.current.data?.projectSummaries).toHaveLength(2)
      expect(result.current.data?.projectSummaries[1]).toEqual(newProject)
    })

    it('should update project correctly', () => {
      const { result } = renderHook(() => useAdminDashboardStore())

      act(() => {
        result.current.setData(mockData)
      })

      const updates = { name: 'Updated Project', progress: 90 }

      act(() => {
        result.current.updateProject('1', updates)
      })

      expect(result.current.data?.projectSummaries[0].name).toBe('Updated Project')
      expect(result.current.data?.projectSummaries[0].progress).toBe(90)
    })
  })

  describe('UI State Actions', () => {
    it('should set loading state correctly', () => {
      const { result } = renderHook(() => useAdminDashboardStore())

      act(() => {
        result.current.setLoading(true)
      })

      expect(result.current.isLoading).toBe(true)

      act(() => {
        result.current.setLoading(false)
      })

      expect(result.current.isLoading).toBe(false)
    })

    it('should set error correctly', () => {
      const { result } = renderHook(() => useAdminDashboardStore())
      const error = new Error('Test error')

      act(() => {
        result.current.setError(error)
      })

      expect(result.current.error).toBe(error)

      act(() => {
        result.current.setError(null)
      })

      expect(result.current.error).toBeNull()
    })

    it('should set selected user correctly', () => {
      const { result } = renderHook(() => useAdminDashboardStore())

      act(() => {
        result.current.setSelectedUser('user-123')
      })

      expect(result.current.selectedUser).toBe('user-123')

      act(() => {
        result.current.setSelectedUser(null)
      })

      expect(result.current.selectedUser).toBeNull()
    })

    it('should set filters correctly', () => {
      const { result } = renderHook(() => useAdminDashboardStore())

      act(() => {
        result.current.setUserFilter('active')
      })

      expect(result.current.userFilter).toBe('active')

      act(() => {
        result.current.setProjectFilter('completed')
      })

      expect(result.current.projectFilter).toBe('completed')

      act(() => {
        result.current.setSearchQuery('test query')
      })

      expect(result.current.searchQuery).toBe('test query')
    })

    it('should set layout options correctly', () => {
      const { result } = renderHook(() => useAdminDashboardStore())

      act(() => {
        result.current.setLayout('list')
      })

      expect(result.current.layout).toBe('list')

      act(() => {
        result.current.setSidebarCollapsed(true)
      })

      expect(result.current.sidebarCollapsed).toBe(true)
    })
  })

  describe('Reset State', () => {
    it('should reset state to initial values', () => {
      const { result } = renderHook(() => useAdminDashboardStore())

      // Set some state
      act(() => {
        result.current.setData(mockData)
        result.current.setSelectedUser('user-123')
        result.current.setUserFilter('active')
        result.current.setLayout('list')
      })

      // Reset state
      act(() => {
        result.current.resetState()
      })

      expect(result.current.data).toBeNull()
      expect(result.current.selectedUser).toBeNull()
      expect(result.current.userFilter).toBe('all')
      expect(result.current.layout).toBe('grid')
    })
  })
})

describe('useAdminDashboardSelectors', () => {
  beforeEach(() => {
    useAdminDashboardStore.getState().resetState()
  })

  it('should filter users correctly', () => {
    const { result } = renderHook(() => {
      const store = useAdminDashboardStore()
      const selectors = useAdminDashboardSelectors()
      return { store, selectors }
    })

    // Set up data with multiple users
    const users: UserManagementSummary[] = [
      { ...mockUser, id: '1', role: 'ADMIN', isActive: true },
      { ...mockUser, id: '2', role: 'EDITOR', isActive: false },
      { ...mockUser, id: '3', role: 'VIEWER', isActive: true },
    ]

    act(() => {
      result.current.store.setData({ ...mockData, userSummaries: users })
    })

    // Test 'all' filter
    expect(result.current.selectors.filteredUsers).toHaveLength(3)

    // Test 'active' filter
    act(() => {
      result.current.store.setUserFilter('active')
    })
    expect(result.current.selectors.filteredUsers).toHaveLength(2)

    // Test 'admin' filter
    act(() => {
      result.current.store.setUserFilter('admin')
    })
    expect(result.current.selectors.filteredUsers).toHaveLength(1)
    expect(result.current.selectors.filteredUsers[0].role).toBe('ADMIN')
  })

  it('should calculate user statistics correctly', () => {
    const { result } = renderHook(() => useAdminDashboardSelectors())

    const users: UserManagementSummary[] = [
      { ...mockUser, id: '1', role: 'ADMIN', isActive: true },
      { ...mockUser, id: '2', role: 'EDITOR', isActive: false },
      { ...mockUser, id: '3', role: 'VIEWER', isActive: true },
      { ...mockUser, id: '4', role: 'EDITOR', isActive: true },
    ]

    act(() => {
      useAdminDashboardStore.getState().setData({ ...mockData, userSummaries: users })
    })

    expect(result.current.userStats.total).toBe(4)
    expect(result.current.userStats.active).toBe(3)
    expect(result.current.userStats.inactive).toBe(1)
    expect(result.current.userStats.admins).toBe(1)
    expect(result.current.userStats.editors).toBe(2)
    expect(result.current.userStats.viewers).toBe(1)
  })

  it('should detect data staleness correctly', () => {
    const { result } = renderHook(() => useAdminDashboardSelectors())

    // No data - should be stale
    expect(result.current.isDataStale).toBe(true)

    // Fresh data
    act(() => {
      useAdminDashboardStore.getState().setData(mockData)
    })
    expect(result.current.isDataStale).toBe(false)

    // Simulate old data (more than 5 minutes old)
    const oldTimestamp = new Date(Date.now() - 6 * 60 * 1000).toISOString()
    act(() => {
      useAdminDashboardStore.setState({ lastUpdated: oldTimestamp })
    })
    expect(result.current.isDataStale).toBe(true)
  })
})
