/**
 * Password Input Component with Show/Hide Toggle
 * Secure password input with accessibility features and strength indicator
 */

'use client'

import { cn } from '@/lib/utils'
import { forwardRef, useState } from 'react'
import { CompactPasswordStrength, PasswordStrengthIndicator } from './PasswordStrength'

interface PasswordInputProps extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'type'> {
  label?: string
  error?: string
  showStrength?: boolean
  strengthVariant?: 'full' | 'compact'
  helperText?: string
  required?: boolean
}

export const PasswordInput = forwardRef<HTMLInputElement, PasswordInputProps>(
  (
    {
      label,
      error,
      showStrength = false,
      strengthVariant = 'full',
      helperText,
      required = false,
      className,
      value = '',
      ...props
    },
    ref
  ) => {
    const [showPassword, setShowPassword] = useState(false)
    const passwordValue = String(value)

    const togglePasswordVisibility = () => {
      setShowPassword(!showPassword)
    }

    return (
      <div className="space-y-2">
        {/* Label */}
        {label && (
          <label htmlFor={props.id} className="form-label">
            {label}
            {required && (
              <span className="ml-1 text-error" aria-label="required">
                *
              </span>
            )}
          </label>
        )}

        {/* Input Container */}
        <div className="relative">
          {/* Lock Icon */}
          <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
            <svg
              className="h-5 w-5 text-neutral-400"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              aria-hidden="true"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"
              />
            </svg>
          </div>

          {/* Password Input */}
          <input
            ref={ref}
            type={showPassword ? 'text' : 'password'}
            autoComplete="current-password"
            className={cn(
              'form-input pl-10 pr-12',
              {
                'border-error focus:border-error focus:ring-error/20': error,
                'border-neutral-300 focus:border-brand-secondary focus:ring-brand-secondary/20':
                  !error,
              },
              className
            )}
            value={value}
            aria-invalid={error ? 'true' : 'false'}
            aria-describedby={
              error ? `${props.id}-error` : helperText ? `${props.id}-helper` : undefined
            }
            {...props}
          />

          {/* Show/Hide Toggle Button */}
          <button
            type="button"
            onClick={togglePasswordVisibility}
            className="absolute inset-y-0 right-0 flex items-center pr-3 transition-colors hover:text-neutral-600 focus:text-neutral-600 focus:outline-none"
            aria-label={showPassword ? 'Hide password' : 'Show password'}
            tabIndex={0}
          >
            {showPassword ? (
              <svg
                className="h-5 w-5 text-neutral-400"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                aria-hidden="true"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21"
                />
              </svg>
            ) : (
              <svg
                className="h-5 w-5 text-neutral-400"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                aria-hidden="true"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                />
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                />
              </svg>
            )}
          </button>
        </div>

        {/* Error Message */}
        {error && (
          <p id={`${props.id}-error`} className="form-error" role="alert" aria-live="polite">
            {error}
          </p>
        )}

        {/* Helper Text */}
        {helperText && !error && (
          <p id={`${props.id}-helper`} className="text-sm text-neutral-500">
            {helperText}
          </p>
        )}

        {/* Password Strength Indicator */}
        {showStrength && passwordValue && (
          <div className="mt-3">
            {strengthVariant === 'full' ? (
              <PasswordStrengthIndicator password={passwordValue} />
            ) : (
              <CompactPasswordStrength password={passwordValue} />
            )}
          </div>
        )}
      </div>
    )
  }
)

PasswordInput.displayName = 'PasswordInput'

/**
 * Confirm Password Input Component
 * Specialized password input for password confirmation with matching validation
 */
interface ConfirmPasswordInputProps extends Omit<PasswordInputProps, 'showStrength'> {
  originalPassword: string
  showMatchIndicator?: boolean
}

export const ConfirmPasswordInput = forwardRef<HTMLInputElement, ConfirmPasswordInputProps>(
  ({ originalPassword, showMatchIndicator = true, value = '', error, ...props }, ref) => {
    const passwordValue = String(value)
    const isMatching = passwordValue && passwordValue === originalPassword
    const hasValue = passwordValue.length > 0

    // Override error if passwords don't match and we have a value
    const displayError = error || (hasValue && !isMatching ? 'Passwords do not match' : undefined)

    return (
      <div className="space-y-2">
        <PasswordInput ref={ref} value={value} error={displayError} {...props} />

        {/* Match Indicator */}
        {showMatchIndicator && hasValue && (
          <div className="flex items-center space-x-2">
            <div
              className={cn(
                'flex h-4 w-4 items-center justify-center rounded-full transition-colors duration-200',
                {
                  'bg-green-100 text-green-600': isMatching,
                  'bg-red-100 text-red-600': !isMatching,
                }
              )}
            >
              {isMatching ? (
                <svg className="h-3 w-3" fill="currentColor" viewBox="0 0 20 20">
                  <path
                    fillRule="evenodd"
                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                    clipRule="evenodd"
                  />
                </svg>
              ) : (
                <svg className="h-3 w-3" fill="currentColor" viewBox="0 0 20 20">
                  <path
                    fillRule="evenodd"
                    d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                    clipRule="evenodd"
                  />
                </svg>
              )}
            </div>
            <span
              className={cn('text-sm transition-colors duration-200', {
                'text-green-700': isMatching,
                'text-red-600': !isMatching,
              })}
            >
              {isMatching ? 'Passwords match' : 'Passwords do not match'}
            </span>
          </div>
        )}
      </div>
    )
  }
)

ConfirmPasswordInput.displayName = 'ConfirmPasswordInput'
