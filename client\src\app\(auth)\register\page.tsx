/**
 * Registration Page
 * User registration page with comprehensive form validation and security features
 */

'use client'

import { AuthLayout } from '@/components/auth/layouts/AuthLayout'
import { RegisterForm } from '@/components/auth/RegisterForm'
import { RouteGuard } from '@/components/auth/RouteGuard'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { useState } from 'react'

function RegisterContent() {
  const router = useRouter()
  const [showSuccess, setShowSuccess] = useState(false)

  const handleRegistrationSuccess = () => {
    setShowSuccess(true)

    // Redirect to login page after showing success message
    setTimeout(() => {
      router.push('/login?message=registration-success')
    }, 3000)
  }

  if (showSuccess) {
    return (
      <AuthLayout
        title="Account Created Successfully!"
        subtitle="Welcome to Ultimate Electrical Designer. You can now sign in with your new account."
        showBackToHome={false}
      >
        <div className="space-y-6 text-center">
          {/* Success Icon */}
          <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-full bg-success/10">
            <svg
              className="h-8 w-8 text-success"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
          </div>

          {/* Success Message */}
          <div className="space-y-3">
            <h3 className="text-lg font-semibold text-neutral-900">
              Your account has been created successfully!
            </h3>
            <p className="text-neutral-600">
              You will be redirected to the sign-in page in a few seconds, or you can click the
              button below.
            </p>
          </div>

          {/* Action Button */}
          <div className="space-y-4">
            <Link
              href="/login"
              className="inline-flex h-12 w-full items-center justify-center rounded-lg bg-brand-secondary px-6 text-base font-semibold text-white transition-colors hover:bg-brand-secondary/90 focus:outline-none focus:ring-2 focus:ring-brand-secondary focus:ring-offset-2"
            >
              <svg className="mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1"
                />
              </svg>
              Continue to Sign In
            </Link>

            <p className="text-sm text-neutral-500">
              <Link
                href="/"
                className="font-medium text-neutral-600 transition-colors hover:text-neutral-900"
              >
                ← Return to homepage
              </Link>
            </p>
          </div>
        </div>
      </AuthLayout>
    )
  }

  return (
    <AuthLayout
      title="Create your account"
      subtitle="Join Ultimate Electrical Designer and access professional engineering tools."
      showBackToHome={false}
    >
      <RegisterForm className="space-y-6" onSuccess={handleRegistrationSuccess} />

      {/* Footer Links */}
      <div className="mt-6 space-y-4 text-center">
        <p className="text-caption">
          Already have an account?{' '}
          <Link
            href="/login"
            className="font-medium text-brand-secondary transition-colors hover:text-brand-secondary/80"
          >
            Sign in here
          </Link>
        </p>
        <p className="text-caption">
          <Link
            href="/"
            className="font-medium text-neutral-600 transition-colors hover:text-neutral-900"
          >
            ← Return to homepage
          </Link>
        </p>
      </div>
    </AuthLayout>
  )
}

export default function RegisterPage() {
  return (
    <RouteGuard requireAuth={false}>
      <RegisterContent />
    </RouteGuard>
  )
}
