/**
 * Type definitions for the admin dashboard domain
 */

import type { UserRead } from '@/types/api'

// Admin dashboard data interfaces
export interface AdminDashboardMetrics {
  totalUsers: number
  activeUsers: number
  inactiveUsers: number
  adminUsers: number
  editorUsers: number
  viewerUsers: number
  totalProjects: number
  activeProjects: number
  completedProjects: number
  totalCalculations: number
  completedCalculations: number
  failedCalculations: number
  systemUptime: string
  serverLoad: number
  memoryUsage: number
  diskUsage: number
  databaseConnections: number
  apiRequestsToday: number
  errorRate: number
  lastBackup: string
}

export interface UserManagementSummary {
  id: string
  name: string
  email: string
  role: 'ADMIN' | 'EDITOR' | 'VIEWER'
  isActive: boolean
  lastLogin: string | null
  createdAt: string
  projectCount: number
  calculationCount: number
  loginCount: number
}

export interface ComponentLibraryStats {
  totalComponents: number
  activeComponents: number
  categoriesCount: number
  recentlyAdded: number
  mostUsedComponents: Array<{
    id: string
    name: string
    category: string
    usageCount: number
  }>
  componentsByCategory: Array<{
    category: string
    count: number
  }>
}

export interface ProjectOversightSummary {
  id: string
  name: string
  description: string
  owner: string
  ownerEmail: string
  status: 'active' | 'completed' | 'on_hold' | 'draft'
  type: 'heat_tracing' | 'load_calculation' | 'cable_sizing' | 'general'
  priority: 'low' | 'medium' | 'high' | 'critical'
  progress: number
  createdAt: string
  lastModified: string
  calculationCount: number
  teamSize: number
}

export interface AuditLogEntry {
  id: string
  timestamp: string
  userId: string
  userName: string
  userEmail: string
  action: 'login' | 'logout' | 'create' | 'update' | 'delete' | 'export' | 'import' | 'admin_action'
  resource: string
  resourceId: string | null
  details: string
  ipAddress: string
  userAgent: string
  success: boolean
  errorMessage?: string
}

export interface SecurityAlert {
  id: string
  type:
    | 'failed_login'
    | 'suspicious_activity'
    | 'unauthorized_access'
    | 'data_breach'
    | 'system_error'
  severity: 'low' | 'medium' | 'high' | 'critical'
  title: string
  description: string
  timestamp: string
  userId?: string
  userName?: string
  ipAddress?: string
  resolved: boolean
  resolvedBy?: string
  resolvedAt?: string
  metadata?: Record<string, any>
}

export interface SystemConfiguration {
  id: string
  category: 'authentication' | 'security' | 'performance' | 'features' | 'integrations'
  key: string
  value: string
  description: string
  dataType: 'string' | 'number' | 'boolean' | 'json'
  isEditable: boolean
  requiresRestart: boolean
  lastModified: string
  modifiedBy: string
}

export interface QuickAdminAction {
  id: string
  title: string
  description: string
  icon: string
  color: 'primary' | 'secondary' | 'accent' | 'dark' | 'success' | 'warning' | 'danger'
  category: 'user_management' | 'system_maintenance' | 'data_operations' | 'security' | 'monitoring'
  action: string
  requiresConfirmation: boolean
  isEnabled: boolean
  requiredPermissions: string[]
}

export interface AdminDashboardWidget {
  id: string
  title: string
  type:
    | 'system_metrics'
    | 'user_management'
    | 'component_library'
    | 'project_oversight'
    | 'audit_logs'
    | 'security_monitoring'
    | 'system_configuration'
    | 'quick_actions'
  position: { x: number; y: number; w: number; h: number }
  isVisible: boolean
  isCollapsible: boolean
  isCollapsed: boolean
  refreshInterval?: number
  lastUpdated: string
  permissions: string[]
}

export interface AdminDashboardData {
  metrics: AdminDashboardMetrics
  userSummaries: UserManagementSummary[]
  componentLibraryStats: ComponentLibraryStats
  projectSummaries: ProjectOversightSummary[]
  auditLogs: AuditLogEntry[]
  securityAlerts: SecurityAlert[]
  systemConfiguration: SystemConfiguration[]
  quickActions: QuickAdminAction[]
  widgets: AdminDashboardWidget[]
}

// Component prop interfaces
export interface AdminDashboardOverviewProps {
  data?: Partial<AdminDashboardData>
  isLoading?: boolean
  error?: Error | null
  className?: string
}

export interface SystemMetricsWidgetProps {
  metrics: AdminDashboardMetrics
  isLoading?: boolean
  onRefresh?: () => void
  className?: string
}

export interface UserManagementWidgetProps {
  users: UserManagementSummary[]
  isLoading?: boolean
  onUserClick?: (user: UserManagementSummary) => void
  onCreateUser?: () => void
  onBulkAction?: (action: string, userIds: string[]) => void
  className?: string
}

export interface ComponentLibraryWidgetProps {
  stats: ComponentLibraryStats
  isLoading?: boolean
  onViewDetails?: () => void
  onManageComponents?: () => void
  className?: string
}

export interface ProjectOversightWidgetProps {
  projects: ProjectOversightSummary[]
  isLoading?: boolean
  onProjectClick?: (project: ProjectOversightSummary) => void
  onViewAll?: () => void
  className?: string
}

export interface AuditLogsWidgetProps {
  logs: AuditLogEntry[]
  isLoading?: boolean
  onViewDetails?: (log: AuditLogEntry) => void
  onExportLogs?: () => void
  onFilterChange?: (filters: AuditLogFilters) => void
  className?: string
}

export interface SecurityMonitoringWidgetProps {
  alerts: SecurityAlert[]
  isLoading?: boolean
  onAlertClick?: (alert: SecurityAlert) => void
  onResolveAlert?: (alertId: string) => void
  onViewAll?: () => void
  className?: string
}

export interface SystemConfigurationWidgetProps {
  configurations: SystemConfiguration[]
  isLoading?: boolean
  onConfigChange?: (config: SystemConfiguration, newValue: string) => void
  onViewAll?: () => void
  className?: string
}

export interface QuickAdminActionsWidgetProps {
  actions: QuickAdminAction[]
  onActionClick?: (action: QuickAdminAction) => void
  className?: string
}

// Store interfaces
export interface AdminDashboardState {
  // Data state
  data: AdminDashboardData | null
  isLoading: boolean
  error: Error | null
  lastUpdated: string | null

  // UI state
  selectedUser: string | null
  selectedProject: string | null
  activeWidget: string | null
  isRefreshing: boolean
  showUserModal: boolean
  showConfigModal: boolean

  // Filter and search state
  userFilter: 'all' | 'active' | 'inactive' | 'admin' | 'editor' | 'viewer'
  projectFilter: 'all' | 'active' | 'completed' | 'on_hold' | 'draft'
  auditLogFilter: AuditLogFilters
  securityAlertFilter: 'all' | 'unresolved' | 'critical' | 'high' | 'medium' | 'low'
  searchQuery: string

  // Layout state
  layout: 'grid' | 'list' | 'compact'
  sidebarCollapsed: boolean

  // Actions
  setData: (data: AdminDashboardData) => void
  updateMetrics: (metrics: Partial<AdminDashboardMetrics>) => void
  addUser: (user: UserManagementSummary) => void
  updateUser: (id: string, updates: Partial<UserManagementSummary>) => void
  removeUser: (id: string) => void
  addProject: (project: ProjectOversightSummary) => void
  updateProject: (id: string, updates: Partial<ProjectOversightSummary>) => void
  addAuditLog: (log: AuditLogEntry) => void
  addSecurityAlert: (alert: SecurityAlert) => void
  resolveSecurityAlert: (alertId: string, resolvedBy: string) => void
  updateConfiguration: (id: string, value: string, modifiedBy: string) => void
  setLoading: (loading: boolean) => void
  setError: (error: Error | null) => void
  setSelectedUser: (userId: string | null) => void
  setSelectedProject: (projectId: string | null) => void
  setActiveWidget: (widgetId: string | null) => void
  setUserFilter: (filter: 'all' | 'active' | 'inactive' | 'admin' | 'editor' | 'viewer') => void
  setProjectFilter: (filter: 'all' | 'active' | 'completed' | 'on_hold' | 'draft') => void
  setAuditLogFilter: (filter: AuditLogFilters) => void
  setSecurityAlertFilter: (
    filter: 'all' | 'unresolved' | 'critical' | 'high' | 'medium' | 'low'
  ) => void
  setSearchQuery: (query: string) => void
  setLayout: (layout: 'grid' | 'list' | 'compact') => void
  setSidebarCollapsed: (collapsed: boolean) => void
  setShowUserModal: (show: boolean) => void
  setShowConfigModal: (show: boolean) => void
  refreshData: () => void
  resetState: () => void
}

// API interfaces
export interface AdminDashboardApiResponse {
  data: AdminDashboardData
  status: 'success' | 'error'
  message?: string
  timestamp: string
}

export interface UsersApiParams {
  limit?: number
  offset?: number
  role?: string
  isActive?: boolean
  sortBy?: 'name' | 'email' | 'created_at' | 'last_login'
  sortOrder?: 'asc' | 'desc'
  search?: string
}

export interface ProjectsApiParams {
  limit?: number
  offset?: number
  status?: string
  type?: string
  priority?: string
  ownerId?: string
  sortBy?: 'name' | 'created_at' | 'last_modified' | 'priority'
  sortOrder?: 'asc' | 'desc'
  search?: string
}

export interface AuditLogFilters {
  startDate?: string
  endDate?: string
  userId?: string
  action?: string
  resource?: string
  success?: boolean
}

export interface AuditLogsApiParams extends AuditLogFilters {
  limit?: number
  offset?: number
  sortBy?: 'timestamp' | 'user_name' | 'action' | 'resource'
  sortOrder?: 'asc' | 'desc'
}

export interface SecurityAlertsApiParams {
  limit?: number
  offset?: number
  type?: string
  severity?: string
  resolved?: boolean
  startDate?: string
  endDate?: string
  sortBy?: 'timestamp' | 'severity' | 'type'
  sortOrder?: 'asc' | 'desc'
}

// Utility types
export type AdminDashboardSection =
  | 'overview'
  | 'users'
  | 'projects'
  | 'components'
  | 'audit'
  | 'security'
  | 'config'
export type WidgetSize = 'small' | 'medium' | 'large' | 'full'
export type UserRole = UserManagementSummary['role']
export type ProjectStatus = ProjectOversightSummary['status']
export type AuditAction = AuditLogEntry['action']
export type SecurityAlertType = SecurityAlert['type']
export type SecurityAlertSeverity = SecurityAlert['severity']
export type ConfigCategory = SystemConfiguration['category']

// Form interfaces
export interface UserFormData {
  name: string
  email: string
  role: UserRole
  isActive: boolean
  password?: string
}

export interface ProjectFormData {
  name: string
  description: string
  type: ProjectOversightSummary['type']
  priority: ProjectOversightSummary['priority']
  ownerId: string
}

export interface ConfigFormData {
  key: string
  value: string
  description: string
  category: ConfigCategory
  dataType: SystemConfiguration['dataType']
  requiresRestart: boolean
}

// Error types
export interface AdminDashboardError extends Error {
  code?: string
  details?: Record<string, any>
  timestamp?: string
}

// Event types
export interface AdminDashboardEvent {
  type:
    | 'user_selected'
    | 'project_selected'
    | 'config_updated'
    | 'alert_resolved'
    | 'widget_refreshed'
  payload: Record<string, any>
  timestamp: string
}
