# Page snapshot

```yaml
- main:
    - button "Back"
    - heading "Create New Component" [level=1]
    - paragraph: Add a new electrical component to the catalog
    - heading "Add Component" [level=3]
    - form:
        - text: Component Name *
        - textbox "Component Name *"
        - text: Manufacturer *
        - textbox "Manufacturer *"
        - paragraph: Manufacturer is required
        - text: Model Number *
        - textbox "Model Number *": E2ER001
        - text: Part Number *
        - textbox "Part Number *": E2E-R-001
        - text: Category
        - combobox "Category":
            - option "Select category" [selected]
            - option "Resistor"
            - option "Capacitor"
            - option "Inductor"
        - text: Component Type
        - combobox "Component Type":
            - option "Select component type" [selected]
            - option "Fixed Resistor"
            - option "Variable Resistor"
            - option "Ceramic Capacitor"
            - option "Electrolytic Capacitor"
        - text: Description
        - textbox "Description"
        - text: Unit Price
        - spinbutton "Unit Price"
        - text: Currency
        - combobox "Currency":
            - option "USD"
            - option "EUR" [selected]
            - option "GBP"
            - option "CAD"
        - text: Weight (kg)
        - spinbutton "Weight (kg)"
        - checkbox "Active" [checked]
        - text: Active
        - checkbox "Preferred"
        - text: Preferred
        - button "Cancel"
        - button "Create"
- button "Open Tanstack query devtools":
    - img
- alert
```
