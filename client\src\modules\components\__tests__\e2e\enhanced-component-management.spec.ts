/**
 * Enhanced Component Management E2E Tests
 * Comprehensive end-to-end tests for the redesigned component management system
 */

import { test, expect } from '@playwright/test'
import { AxeBuilder } from '@axe-core/playwright'

// Test data for the enhanced system
const enhancedTestComponent = {
  name: 'Enhanced Test Component',
  manufacturer: 'Enhanced Manufacturer',
  modelNumber: 'ETM-001',
  category: 'CAPACITOR',
  componentType: 'capacitor',
  price: '25.50',
  currency: 'EUR',
  weight: '0.015',
  description: 'Enhanced test component with comprehensive specifications',
  specifications: {
    voltage: '230V',
    current: '10A',
    power: '100W',
    temperature_range: '-40°C to +85°C',
  },
}

test.describe('Enhanced Component Management System', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/components')
    await page.waitForLoadState('networkidle')
  })

  test.describe('Enhanced Component Display', () => {
    test('displays components with enhanced atomic design structure', async ({ page }) => {
      // Verify atomic design components are present
      await expect(page.locator('[data-testid^="component-badge-"]')).toBeVisible()
      await expect(page.locator('[data-testid^="component-icon-"]')).toBeVisible()
      await expect(page.locator('[data-testid^="component-card-"]')).toBeVisible()

      // Check component card structure
      const firstCard = page.locator('[data-testid^="component-card-"]').first()
      await expect(firstCard.locator('[data-testid*="badge"]')).toBeVisible()
      await expect(firstCard.locator('[data-testid*="icon"]')).toBeVisible()
    })

    test('shows component status with proper badges', async ({ page }) => {
      const componentCard = page.locator('[data-testid^="component-card-"]').first()

      // Check for status badge
      const statusBadge = componentCard.locator('[data-testid*="badge"]')
      await expect(statusBadge).toBeVisible()

      // Verify badge has proper ARIA attributes
      await expect(statusBadge).toHaveAttribute('role', 'status')
      await expect(statusBadge).toHaveAttribute('aria-label')
    })

    test('displays component icons based on type', async ({ page }) => {
      const componentCard = page.locator('[data-testid^="component-card-"]').first()

      // Check for component icon
      const componentIcon = componentCard.locator('[data-testid*="icon"]')
      await expect(componentIcon).toBeVisible()

      // Verify icon has proper accessibility attributes
      await expect(componentIcon).toHaveAttribute('role', 'img')
      await expect(componentIcon).toHaveAttribute('aria-label')
    })
  })

  test.describe('Enhanced Search Functionality', () => {
    test('provides advanced search with suggestions', async ({ page }) => {
      const searchBar = page.locator('[data-testid="component-search-bar"]')
      const searchInput = searchBar.locator('input')

      // Focus search input
      await searchInput.click()

      // Verify suggestions dropdown appears
      await expect(page.locator('text=Suggestions')).toBeVisible()

      // Type partial query
      await searchInput.fill('Sie')

      // Verify filtered suggestions
      await expect(page.locator('text=Siemens contactors')).toBeVisible()

      // Select suggestion
      await page.click('text=Siemens contactors')

      // Verify search was applied
      await expect(searchInput).toHaveValue('Siemens contactors')
      await expect(page.locator('text=Searching: Siemens contactors')).toBeVisible()
    })

    test('maintains search history with accessibility', async ({ page }) => {
      const searchInput = page.locator('[data-testid="component-search-bar-input"]')

      // Perform multiple searches
      await searchInput.fill('First Search')
      await searchInput.press('Enter')
      await page.waitForLoadState('networkidle')

      await page.click('[data-testid="component-search-bar-clear"]')
      await searchInput.fill('Second Search')
      await searchInput.press('Enter')
      await page.waitForLoadState('networkidle')

      // Clear and check history
      await page.click('[data-testid="component-search-bar-clear"]')
      await searchInput.click()

      // Verify history section
      await expect(page.locator('text=Recent Searches')).toBeVisible()
      await expect(page.locator('text=Second Search')).toBeVisible()
      await expect(page.locator('text=First Search')).toBeVisible()

      // Test keyboard navigation in history
      await page.keyboard.press('ArrowDown')
      await page.keyboard.press('Enter')

      // Verify history item was selected
      await expect(searchInput).toHaveValue('Second Search')
    })

    test('supports advanced search mode', async ({ page }) => {
      // Toggle advanced search
      await page.click('[data-testid="component-search-bar-advanced"]')

      // Verify advanced search indicator
      await expect(page.locator('text=Advanced')).toBeVisible()

      // Check that advanced search affects the search behavior
      const searchInput = page.locator('[data-testid="component-search-bar-input"]')
      await searchInput.fill('complex search query')
      await searchInput.press('Enter')

      // Verify advanced search badge is shown
      await expect(page.locator('[data-testid*="advanced"]')).toBeVisible()
    })
  })

  test.describe('Enhanced Filtering System', () => {
    test('provides comprehensive filtering options', async ({ page }) => {
      const filtersPanel = page.locator('[data-testid="component-filters"]')

      // Verify basic filters are available
      await expect(filtersPanel.locator('select[id="manufacturer-filter"]')).toBeVisible()
      await expect(filtersPanel.locator('select[id="category-filter"]')).toBeVisible()

      // Open advanced filters
      await page.click('[data-testid="component-filters-toggle-advanced"]')

      // Verify advanced filters are available
      await expect(filtersPanel.locator('input[id="price-min"]')).toBeVisible()
      await expect(filtersPanel.locator('input[id="price-max"]')).toBeVisible()
      await expect(filtersPanel.locator('input[id="weight-min"]')).toBeVisible()
      await expect(filtersPanel.locator('input[id="weight-max"]')).toBeVisible()
      await expect(filtersPanel.locator('input[id="date-from"]')).toBeVisible()
      await expect(filtersPanel.locator('input[id="date-to"]')).toBeVisible()
    })

    test('shows active filters with removal capability', async ({ page }) => {
      // Apply multiple filters
      await page.selectOption('select[id="manufacturer-filter"]', 'Siemens')
      await page.check('input[id="is-preferred"]')

      // Open advanced filters and set price range
      await page.click('[data-testid="component-filters-toggle-advanced"]')
      await page.fill('input[id="price-min"]', '10')
      await page.fill('input[id="price-max"]', '100')

      // Verify active filters are displayed
      await expect(page.locator('text=Active Filters:')).toBeVisible()

      // Verify individual filter chips
      const filterChips = page.locator('[data-testid*="filter-chip"]')
      await expect(filterChips).toHaveCount(3) // manufacturer, is_preferred, price_range

      // Remove a specific filter
      const manufacturerChip = page.locator('text=manufacturer: Siemens').locator('..')
      await manufacturerChip.locator('button').click()

      // Verify filter was removed
      await expect(page.locator('text=manufacturer: Siemens')).not.toBeVisible()
      await expect(page.locator('select[id="manufacturer-filter"]')).toHaveValue('')
    })

    test('supports filter presets and saved filters', async ({ page }) => {
      // This would test saved filter functionality if implemented
      // Apply some filters
      await page.selectOption('select[id="manufacturer-filter"]', 'Siemens')
      await page.check('input[id="is-preferred"]')

      // Save filter preset (if implemented)
      // await page.click('[data-testid="save-filter-preset"]')
      // await page.fill('[data-testid="preset-name"]', 'Preferred Siemens')
      // await page.click('[data-testid="confirm-save"]')

      // For now, just verify the filters are applied
      await expect(page.locator('text=2').first()).toBeVisible() // Filter count
    })
  })

  test.describe('Enhanced Bulk Operations', () => {
    test('provides comprehensive bulk operation options', async ({ page }) => {
      // Select multiple components
      const checkboxes = page.locator('input[type="checkbox"][aria-label*="Select"]')
      await checkboxes.nth(0).check()
      await checkboxes.nth(1).check()
      await checkboxes.nth(2).check()

      // Verify bulk operations panel
      const bulkPanel = page.locator('[data-testid="bulk-operations-panel"]')
      await expect(bulkPanel).toBeVisible()
      await expect(bulkPanel.locator('text=3 selected')).toBeVisible()

      // Verify all bulk operation buttons are available
      await expect(bulkPanel.locator('[data-testid="bulk-operations-edit"]')).toBeVisible()
      await expect(bulkPanel.locator('[data-testid="bulk-operations-duplicate"]')).toBeVisible()
      await expect(bulkPanel.locator('[data-testid="bulk-operations-preferred"]')).toBeVisible()
      await expect(bulkPanel.locator('[data-testid="bulk-operations-export"]')).toBeVisible()
      await expect(bulkPanel.locator('[data-testid="bulk-operations-archive"]')).toBeVisible()
      await expect(bulkPanel.locator('[data-testid="bulk-operations-delete"]')).toBeVisible()
    })

    test('shows progress for bulk operations', async ({ page }) => {
      // Select components
      const checkbox = page.locator('input[type="checkbox"][aria-label*="Select"]').first()
      await checkbox.check()

      // Start a bulk operation (duplicate is non-destructive)
      await page.click('[data-testid="bulk-operations-duplicate"]')

      // Verify progress indicator appears (if implemented)
      // await expect(page.locator('[data-testid="bulk-progress"]')).toBeVisible()

      // For now, just verify the operation was initiated
      await page.waitForLoadState('networkidle')
    })

    test('provides detailed confirmation for destructive operations', async ({ page }) => {
      // Select components
      const checkbox = page.locator('input[type="checkbox"][aria-label*="Select"]').first()
      await checkbox.check()

      // Initiate delete operation
      await page.click('[data-testid="bulk-operations-delete"]')

      // Verify comprehensive confirmation dialog
      const dialog = page.locator('[role="dialog"]')
      await expect(dialog).toBeVisible()
      await expect(dialog.locator('text=Delete Components')).toBeVisible()
      await expect(dialog.locator('text=cannot be undone')).toBeVisible()

      // Verify selected components are listed
      await expect(dialog.locator('text=Selected Components:')).toBeVisible()

      // Cancel the operation
      await page.click('button:has-text("Cancel")')
      await expect(dialog).not.toBeVisible()
    })
  })

  test.describe('Enhanced Component Cards', () => {
    test('displays comprehensive component information', async ({ page }) => {
      const componentCard = page.locator('[data-testid^="component-card-"]').first()

      // Verify card structure
      await expect(componentCard.locator('[data-testid*="icon"]')).toBeVisible()
      await expect(componentCard.locator('[data-testid*="badge"]')).toBeVisible()

      // Check for component details
      await expect(componentCard.locator('text*=Model:')).toBeVisible()

      // Verify action buttons
      await expect(componentCard.locator('[data-testid*="preferred"]')).toBeVisible()
      await expect(componentCard.locator('[data-testid*="actions"]')).toBeVisible()
    })

    test('supports different card variants', async ({ page }) => {
      // Switch to list view for compact cards
      await page.click('[data-testid="component-list-view-list"]')

      // Verify compact card layout
      const compactCards = page.locator('[data-testid^="component-card-"]')
      await expect(compactCards.first()).toBeVisible()

      // Switch to table view for minimal cards
      await page.click('[data-testid="component-list-view-table"]')

      // Verify minimal card layout
      const minimalCards = page.locator('[data-testid^="component-card-"]')
      await expect(minimalCards.first()).toBeVisible()
    })

    test('provides accessible card interactions', async ({ page }) => {
      const componentCard = page.locator('[data-testid^="component-card-"]').first()

      // Verify card is focusable
      await componentCard.focus()
      await expect(componentCard).toBeFocused()

      // Test keyboard activation
      await page.keyboard.press('Enter')

      // Verify component details opened (or appropriate action occurred)
      await page.waitForLoadState('networkidle')

      // Test preferred toggle with keyboard
      await componentCard.focus()
      await page.keyboard.press('Tab') // Focus preferred button
      const preferredButton = componentCard.locator('[data-testid*="preferred"]')
      await expect(preferredButton).toBeFocused()

      await page.keyboard.press('Enter')
      // Verify preferred status changed (visual feedback)
    })
  })

  test.describe('Enhanced Accessibility Features', () => {
    test('provides comprehensive keyboard navigation', async ({ page }) => {
      // Test tab order through main interface elements
      await page.keyboard.press('Tab') // Search input
      await expect(page.locator('[data-testid="component-search-bar-input"]')).toBeFocused()

      await page.keyboard.press('Tab') // Advanced search button
      await expect(page.locator('[data-testid="component-search-bar-advanced"]')).toBeFocused()

      await page.keyboard.press('Tab') // Create button
      await expect(page.locator('[data-testid="create-component-button"]')).toBeFocused()

      await page.keyboard.press('Tab') // View mode buttons
      await expect(page.locator('[data-testid="component-list-view-grid"]')).toBeFocused()

      // Continue through component cards
      await page.keyboard.press('Tab')
      await page.keyboard.press('Tab')
      await page.keyboard.press('Tab')
      const firstCard = page.locator('[data-testid^="component-card-"]').first()
      await expect(firstCard).toBeFocused()
    })

    test('supports screen reader navigation', async ({ page }) => {
      // Verify landmark regions
      await expect(page.locator('[role="main"]')).toBeVisible()
      await expect(page.locator('[role="navigation"]')).toBeVisible()
      await expect(page.locator('[role="search"]')).toBeVisible()

      // Verify heading hierarchy
      const headings = page.locator('h1, h2, h3, h4, h5, h6')
      const headingCount = await headings.count()
      expect(headingCount).toBeGreaterThan(0)

      // Verify ARIA live regions for dynamic content
      await expect(page.locator('[aria-live="polite"]')).toBeVisible()

      // Test search results announcement
      const searchInput = page.locator('[data-testid="component-search-bar-input"]')
      await searchInput.fill('test')
      await page.waitForTimeout(500) // Wait for debounced search

      // Verify live region is updated
      const liveRegion = page.locator('[aria-live="polite"]')
      await expect(liveRegion).toContainText('results')
    })

    test('meets enhanced WCAG 2.1 AA standards', async ({ page }) => {
      // Run comprehensive accessibility scan
      const accessibilityScanResults = await new AxeBuilder({ page })
        .withTags(['wcag2a', 'wcag2aa', 'wcag21aa'])
        .analyze()

      expect(accessibilityScanResults.violations).toEqual([])

      // Verify color contrast for interactive elements
      const buttons = page.locator('button:visible')
      const buttonCount = await buttons.count()

      // Sample check for button visibility (contrast would be checked by axe)
      for (let i = 0; i < Math.min(buttonCount, 10); i++) {
        await expect(buttons.nth(i)).toBeVisible()
      }
    })

    test('supports reduced motion preferences', async ({ page }) => {
      // Enable reduced motion
      await page.emulateMedia({ reducedMotion: 'reduce' })

      // Verify animations are disabled or reduced
      const animatedElements = page.locator('.animate-pulse, .animate-spin, .transition-all')
      const elementCount = await animatedElements.count()

      // Elements should still be visible but with reduced animation
      for (let i = 0; i < elementCount; i++) {
        await expect(animatedElements.nth(i)).toBeVisible()
      }
    })
  })

  test.describe('Performance and Optimization', () => {
    test('loads efficiently with large datasets', async ({ page }) => {
      // Navigate to page with many components
      await page.goto('/components?size=100')

      const startTime = Date.now()
      await page.waitForLoadState('networkidle')
      const loadTime = Date.now() - startTime

      // Should load within reasonable time
      expect(loadTime).toBeLessThan(5000)

      // Verify virtual scrolling or pagination is working
      const visibleCards = page.locator('[data-testid^="component-card-"]:visible')
      const visibleCount = await visibleCards.count()

      // Should not render all components at once
      expect(visibleCount).toBeLessThan(100)
      expect(visibleCount).toBeGreaterThan(0)
    })

    test('handles real-time updates efficiently', async ({ page }) => {
      // This would test WebSocket updates if implemented
      const initialCardCount = await page.locator('[data-testid^="component-card-"]').count()

      // Simulate real-time update (would need WebSocket mock)
      // For now, just verify the page handles dynamic updates
      await page.reload()
      await page.waitForLoadState('networkidle')

      const newCardCount = await page.locator('[data-testid^="component-card-"]').count()
      expect(newCardCount).toBeGreaterThanOrEqual(0)
    })
  })
})
