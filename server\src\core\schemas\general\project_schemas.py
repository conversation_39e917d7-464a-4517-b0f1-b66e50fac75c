# !/usr/bin/env python3
"""Project schemas for Ultimate Electrical Designer.

This module provides Pydantic schemas for project management operations,
supporting comprehensive project lifecycle management.
"""

from datetime import datetime
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, Field

from src.core.enums import ProjectStatus
from src.core.schemas.base import BaseSchema, PaginatedResponseSchema, TimestampMixin


class ProjectBaseSchema(BaseSchema):
    """Base schema for projects."""

    name: str = Field(..., min_length=1, max_length=255, description="Project name")
    description: Optional[str] = Field(None, description="Project description")
    status: ProjectStatus = Field(ProjectStatus.DRAFT, description="Project status")
    client_name: Optional[str] = Field(None, description="Client name")
    client_contact: Optional[str] = Field(
        None, description="Client contact information"
    )
    location: Optional[str] = Field(None, description="Project location")
    start_date: Optional[datetime] = Field(None, description="Project start date")
    end_date: Optional[datetime] = Field(None, description="Project end date")
    budget: Optional[float] = Field(None, ge=0, description="Project budget")
    currency: Optional[str] = Field("USD", description="Budget currency")


class ProjectCreateSchema(ProjectBaseSchema):
    """Schema for creating projects."""

    owner_id: int = Field(..., description="Project owner user ID")


class ProjectUpdateSchema(BaseModel):
    """Schema for updating projects."""

    name: Optional[str] = Field(
        None, min_length=1, max_length=255, description="Project name"
    )
    description: Optional[str] = Field(None, description="Project description")
    status: Optional[ProjectStatus] = Field(None, description="Project status")
    client_name: Optional[str] = Field(None, description="Client name")
    client_contact: Optional[str] = Field(
        None, description="Client contact information"
    )
    location: Optional[str] = Field(None, description="Project location")
    start_date: Optional[datetime] = Field(None, description="Project start date")
    end_date: Optional[datetime] = Field(None, description="Project end date")
    budget: Optional[float] = Field(None, ge=0, description="Project budget")
    currency: Optional[str] = Field(None, description="Budget currency")


class ProjectReadSchema(ProjectBaseSchema, TimestampMixin):
    """Schema for reading projects."""

    id: int = Field(..., description="Unique identifier")
    owner_id: int = Field(..., description="Project owner user ID")

    class Config:
        """Configuration for the schema."""

        from_attributes = True


class ProjectListResponseSchema(PaginatedResponseSchema):
    """Paginated response schema for projects."""

    items: List[ProjectReadSchema] = Field(..., description="List of projects")


class ProjectSummarySchema(BaseSchema):
    """Schema for project summaries."""

    id: int = Field(..., description="Unique identifier")
    name: str = Field(..., description="Project name")
    status: ProjectStatus = Field(..., description="Project status")
    client_name: Optional[str] = Field(None, description="Client name")
    location: Optional[str] = Field(None, description="Project location")
    start_date: Optional[datetime] = Field(None, description="Project start date")
    end_date: Optional[datetime] = Field(None, description="Project end date")
    budget: Optional[float] = Field(None, description="Project budget")
    currency: Optional[str] = Field(None, description="Budget currency")
    owner_id: int = Field(..., description="Project owner user ID")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: datetime = Field(..., description="Last update timestamp")

    class Config:
        """Configuration for the schema."""

        from_attributes = True


__all__ = [
    "ProjectBaseSchema",
    "ProjectCreateSchema",
    "ProjectUpdateSchema",
    "ProjectReadSchema",
    "ProjectListResponseSchema",
    "ProjectSummarySchema",
]
