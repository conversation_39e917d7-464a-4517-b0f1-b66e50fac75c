'use client'

import { cn } from '@/lib/utils'
import { useEffect, useRef, useState } from 'react'
import type { TrustIndicatorProps } from '../types'

/**
 * Individual trust indicator component with animation
 */
function TrustIndicatorItem({
  indicator,
  index,
  isVisible,
}: {
  indicator: { id: string; icon: string; label: string; value: string }
  index: number
  isVisible: boolean
}) {
  const [count, setCount] = useState(0)
  const [hasAnimated, setHasAnimated] = useState(false)

  // Extract number from value for animation
  const extractNumber = (value: string): number => {
    const match = value.match(/(\d+(?:\.\d+)?)/)
    return match ? parseFloat(match[1]) : 0
  }

  const targetNumber = extractNumber(indicator.value)
  const isNumeric = targetNumber > 0

  // Animate numbers when visible
  useEffect(() => {
    if (isVisible && !hasAnimated && isNumeric) {
      setHasAnimated(true)
      const duration = 2000 // 2 seconds
      const steps = 60
      const increment = targetNumber / steps
      let current = 0

      const timer = setInterval(() => {
        current += increment
        if (current >= targetNumber) {
          setCount(targetNumber)
          clearInterval(timer)
        } else {
          setCount(Math.floor(current))
        }
      }, duration / steps)

      return () => clearInterval(timer)
    }
  }, [isVisible, hasAnimated, isNumeric, targetNumber])

  const getIcon = (iconName: string) => {
    const iconMap: Record<string, JSX.Element> = {
      lock: (
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"
        />
      ),
      'check-circle': (
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
        />
      ),
      zap: (
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          d="M13 10V3L4 14h7v7l9-11h-7z"
        />
      ),
      users: (
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"
        />
      ),
      building: (
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"
        />
      ),
    }

    return iconMap[iconName] || iconMap['check-circle']
  }

  const formatValue = (value: string, animatedCount: number): string => {
    if (!isNumeric) return value

    // Replace the number in the original string with the animated count
    return value.replace(/\d+(?:\.\d+)?/, animatedCount.toString())
  }

  return (
    <div
      className={cn(
        'flex flex-col items-center rounded-xl border border-white/20 bg-white/50 p-6 text-center backdrop-blur-sm transition-all duration-500',
        'hover:scale-105 hover:bg-white/70 hover:shadow-lg',
        isVisible ? 'translate-y-0 opacity-100' : 'translate-y-4 opacity-0'
      )}
      style={{
        transitionDelay: `${index * 0.1}s`,
        animationDelay: `${index * 0.1}s`,
      }}
    >
      {/* Icon */}
      <div className="mb-3 flex h-12 w-12 items-center justify-center rounded-full bg-brand-accent/20">
        <svg
          className="h-6 w-6 text-brand-accent"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
          aria-hidden="true"
        >
          {getIcon(indicator.icon)}
        </svg>
      </div>

      {/* Value */}
      <div className="mb-2 text-2xl font-bold text-white">
        {isNumeric && isVisible ? formatValue(indicator.value, count) : indicator.value}
      </div>

      {/* Label */}
      <div className="text-sm font-medium text-white/90">{indicator.label}</div>
    </div>
  )
}

/**
 * Trust indicators section with animated statistics
 */
export function TrustIndicators({ indicators, className }: TrustIndicatorProps) {
  const [isVisible, setIsVisible] = useState(false)
  const sectionRef = useRef<HTMLDivElement>(null)

  // Intersection observer for animations
  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true)
        }
      },
      { threshold: 0.3 }
    )

    if (sectionRef.current) {
      observer.observe(sectionRef.current)
    }

    return () => observer.disconnect()
  }, [])

  return (
    <div
      ref={sectionRef}
      className={cn(
        'bg-gradient-to-r from-brand-dark/90 via-brand-secondary/90 to-brand-primary/90 py-16 backdrop-blur-sm',
        className
      )}
    >
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        {/* Section Title */}
        <div className="mb-12 text-center">
          <h3 className="mb-4 text-2xl font-bold text-white">Trusted by Industry Leaders</h3>
          <p className="mx-auto max-w-2xl text-white/80">
            Join thousands of professional engineers and Fortune 500 companies who rely on our
            platform
          </p>
        </div>

        {/* Trust Indicators Grid */}
        <div className="grid grid-cols-2 gap-4 md:grid-cols-3 lg:grid-cols-6">
          {indicators.map((indicator, index) => (
            <TrustIndicatorItem
              key={indicator.id}
              indicator={indicator}
              index={index}
              isVisible={isVisible}
            />
          ))}
        </div>

        {/* Additional Trust Elements */}
        <div className="mt-12 flex flex-wrap justify-center gap-8 text-white/70">
          <div className="flex items-center space-x-2">
            <svg
              className="h-5 w-5 text-brand-accent"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              aria-hidden="true"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"
              />
            </svg>
            <span className="text-sm font-medium">SOC 2 Type II Certified</span>
          </div>
          <div className="flex items-center space-x-2">
            <svg
              className="h-5 w-5 text-brand-accent"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              aria-hidden="true"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
            <span className="text-sm font-medium">ISO 27001 Compliant</span>
          </div>
          <div className="flex items-center space-x-2">
            <svg
              className="h-5 w-5 text-brand-accent"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              aria-hidden="true"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"
              />
            </svg>
            <span className="text-sm font-medium">GDPR Compliant</span>
          </div>
        </div>
      </div>
    </div>
  )
}
