'use client'

import { useAuth } from '@/hooks/useAuth'
import { cn } from '@/lib/utils'
import dynamic from 'next/dynamic'
import { memo, useCallback } from 'react'
import { useDashboardData } from '../hooks'
import type { DashboardOverviewProps } from '../types'
import { defaultDashboardData } from '../utils'
import { MetricsWidget } from './MetricsWidget'

// Lazy load components for better performance
const QuickActionsWidget = dynamic(
  () => import('./QuickActionsWidget').then((mod) => ({ default: mod.QuickActionsWidget })),
  {
    loading: () => (
      <div className="h-64 animate-pulse rounded-lg border border-gray-200 bg-white shadow-sm" />
    ),
  }
)

const ProjectsWidget = dynamic(
  () => import('./ProjectsWidget').then((mod) => ({ default: mod.ProjectsWidget })),
  {
    loading: () => (
      <div className="h-96 animate-pulse rounded-lg border border-gray-200 bg-white shadow-sm" />
    ),
  }
)

const RecentCalculationsWidget = dynamic(
  () =>
    import('./RecentCalculationsWidget').then((mod) => ({ default: mod.RecentCalculationsWidget })),
  {
    loading: () => (
      <div className="h-96 animate-pulse rounded-lg border border-gray-200 bg-white shadow-sm" />
    ),
  }
)

/**
 * Main dashboard overview component
 */
export const DashboardOverview = memo(function DashboardOverview({
  data,
  isLoading: externalLoading = false,
  error: externalError = null,
  className,
}: DashboardOverviewProps) {
  const { user } = useAuth()
  const { data: fetchedData, isLoading: queryLoading, error: queryError } = useDashboardData()

  // Use provided data, fetched data, or fallback to default
  const dashboardData = data || fetchedData || defaultDashboardData
  const isLoading = externalLoading || queryLoading
  const error = externalError || queryError

  // Handle project click
  const handleProjectClick = useCallback((project: any) => {
    console.log('Project clicked:', project)
    // Navigate to project details
    window.location.href = `/projects/${project.id}`
  }, [])

  // Handle calculation click
  const handleCalculationClick = useCallback((calculation: any) => {
    console.log('Calculation clicked:', calculation)
    // Navigate to calculation details
    window.location.href = `/projects/${calculation.projectId}/calculations/${calculation.id}`
  }, [])

  // Handle quick action click
  const handleQuickActionClick = useCallback((action: any) => {
    console.log('Quick action clicked:', action)
    // Handle action based on type
    if (action.href) {
      window.location.href = action.href
    }
  }, [])

  // Handle create project
  const handleCreateProject = useCallback(() => {
    console.log('Create project clicked')
    window.location.href = '/projects/new'
  }, [])

  // Handle view all calculations
  const handleViewAllCalculations = useCallback(() => {
    console.log('View all calculations clicked')
    window.location.href = '/calculations'
  }, [])

  // Handle error state
  if (error) {
    return (
      <div className={cn('rounded-lg border border-red-200 bg-red-50 p-6', className)}>
        <div className="flex items-center">
          <svg
            className="h-5 w-5 text-red-400"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
            />
          </svg>
          <h3 className="ml-3 text-sm font-medium text-red-800">Error loading dashboard</h3>
        </div>
        <div className="mt-2 text-sm text-red-700">
          <p>There was a problem loading your dashboard data. Please try refreshing the page.</p>
        </div>
        <div className="mt-4">
          <button
            onClick={() => window.location.reload()}
            className="rounded-md bg-red-100 px-4 py-2 text-sm font-medium text-red-800 transition-colors duration-200 hover:bg-red-200"
          >
            Refresh Page
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className={cn('space-y-8', className)}>
      {/* Welcome Section */}
      <div className="rounded-lg bg-gradient-to-r from-brand-primary/10 via-brand-secondary/10 to-brand-accent/10 p-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">
              Welcome back, {user?.name || 'Engineer'}!
            </h1>
            <p className="mt-1 text-gray-600">
              Here&apos;s what&apos;s happening with your electrical design projects today.
            </p>
          </div>
          <div className="hidden sm:block">
            <div className="flex items-center space-x-2 text-sm text-gray-500">
              <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                />
              </svg>
              <span>Last updated: {new Date().toLocaleTimeString()}</span>
            </div>
          </div>
        </div>
      </div>

      {/* Metrics Section */}
      <section aria-label="Dashboard metrics">
        <MetricsWidget metrics={dashboardData.metrics} isLoading={isLoading} />
      </section>

      {/* Quick Actions Section */}
      <section aria-label="Quick actions">
        <QuickActionsWidget
          actions={dashboardData.quickActions}
          onActionClick={handleQuickActionClick}
        />
      </section>

      {/* Projects and Calculations Grid */}
      <div className="grid grid-cols-1 gap-8 lg:grid-cols-3">
        {/* Projects Widget */}
        <section aria-label="Recent projects" className="lg:col-span-2">
          <ProjectsWidget
            projects={dashboardData.projects}
            isLoading={isLoading}
            onProjectClick={handleProjectClick}
            onCreateProject={handleCreateProject}
          />
        </section>

        {/* Recent Calculations Widget */}
        <section aria-label="Recent calculations" className="lg:col-span-1">
          <RecentCalculationsWidget
            calculations={dashboardData.recentCalculations}
            isLoading={isLoading}
            onCalculationClick={handleCalculationClick}
            onViewAll={handleViewAllCalculations}
          />
        </section>
      </div>

      {/* Additional Information */}
      <div className="rounded-lg border border-gray-200 bg-white p-6 shadow-sm">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-medium text-gray-900">System Status</h3>
            <p className="mt-1 text-sm text-gray-500">
              All systems operational • Uptime: {dashboardData.metrics.systemUptime}
            </p>
          </div>
          <div className="flex items-center space-x-2">
            <div className="h-2 w-2 rounded-full bg-green-400"></div>
            <span className="text-sm font-medium text-green-600">Online</span>
          </div>
        </div>
      </div>
    </div>
  )
})

DashboardOverview.displayName = 'DashboardOverview'
