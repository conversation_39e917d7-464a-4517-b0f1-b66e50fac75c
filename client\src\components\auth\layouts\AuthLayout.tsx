/**
 * Authentication Layout Component
 * Shared layout for login and register pages with professional design
 */

'use client'

import { ReactNode } from 'react'
import Link from 'next/link'
import { cn } from '@/lib/utils'

interface AuthLayoutProps {
  children: ReactNode
  title: string
  subtitle?: string
  showBackToHome?: boolean
  className?: string
}

export function AuthLayout({
  children,
  title,
  subtitle,
  showBackToHome = true,
  className = '',
}: AuthLayoutProps) {
  return (
    <div className={cn('engineering-layout flex min-h-screen', className)}>
      {/* Left Side - Branding & Information */}
      <div className="hidden lg:flex lg:w-1/2 lg:flex-col lg:justify-center lg:px-12 xl:px-16">
        <div className="mx-auto max-w-md">
          {/* Brand Logo */}
          <div className="mb-8 flex items-center space-x-3">
            <div className="flex h-12 w-12 items-center justify-center rounded-xl bg-gradient-to-br from-brand-primary to-brand-secondary shadow-lg">
              <svg
                className="h-7 w-7 text-white"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M13 10V3L4 14h7v7l9-11h-7z"
                />
              </svg>
            </div>
            <div className="flex flex-col">
              <span className="text-xl font-bold text-neutral-900">
                Ultimate Electrical Designer
              </span>
              <span className="text-sm font-medium uppercase tracking-wide text-neutral-500">
                Engineering Grade
              </span>
            </div>
          </div>

          {/* Welcome Message */}
          <div className="space-y-6">
            <h1 className="heading-secondary">Professional Electrical Design Platform</h1>
            <p className="text-lead">
              Access industry-leading tools for electrical engineering, heat tracing calculations,
              and comprehensive project management.
            </p>

            {/* Feature Highlights */}
            <div className="space-y-4">
              <FeatureHighlight
                icon={
                  <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                    />
                  </svg>
                }
                title="Engineering-Grade Calculations"
                description="Industry-standard electrical design and analysis tools"
                color="brand-accent"
              />
              <FeatureHighlight
                icon={
                  <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"
                    />
                  </svg>
                }
                title="Secure & Compliant"
                description="Built-in safety checks and code compliance verification"
                color="brand-secondary"
              />
              <FeatureHighlight
                icon={
                  <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
                    />
                  </svg>
                }
                title="Professional Reports"
                description="Generate comprehensive documentation and specifications"
                color="brand-primary"
              />
            </div>
          </div>
        </div>
      </div>

      {/* Right Side - Form Content */}
      <div className="flex w-full flex-col justify-center px-4 py-12 sm:px-6 lg:w-1/2 lg:px-12 xl:px-16">
        <div className="mx-auto w-full max-w-md">
          {/* Mobile Brand Header */}
          <div className="mb-8 text-center lg:hidden">
            <div className="mb-4 flex items-center justify-center space-x-3">
              <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-gradient-to-br from-brand-primary to-brand-secondary shadow-md">
                <svg
                  className="h-6 w-6 text-white"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M13 10V3L4 14h7v7l9-11h-7z"
                  />
                </svg>
              </div>
              <span className="text-lg font-bold text-neutral-900">
                Ultimate Electrical Designer
              </span>
            </div>
          </div>

          {/* Page Header */}
          <div className="mb-8">
            <h2 className="heading-tertiary text-center lg:text-left">{title}</h2>
            {subtitle && <p className="text-body mt-3 text-center lg:text-left">{subtitle}</p>}
          </div>

          {/* Form Content */}
          <div className="card-engineering">{children}</div>

          {/* Footer Links */}
          {showBackToHome && (
            <div className="mt-6 text-center">
              <p className="text-caption">
                <Link
                  href="/"
                  className="font-medium text-neutral-600 transition-colors hover:text-neutral-900"
                >
                  ← Return to homepage
                </Link>
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

interface FeatureHighlightProps {
  icon: ReactNode
  title: string
  description: string
  color: 'brand-primary' | 'brand-secondary' | 'brand-accent'
}

function FeatureHighlight({ icon, title, description, color }: FeatureHighlightProps) {
  return (
    <div className="flex items-start space-x-3">
      <div
        className={cn('flex h-8 w-8 items-center justify-center rounded-lg', {
          'bg-brand-primary/10 text-brand-primary': color === 'brand-primary',
          'bg-brand-secondary/10 text-brand-secondary': color === 'brand-secondary',
          'bg-brand-accent/10 text-brand-accent': color === 'brand-accent',
        })}
      >
        {icon}
      </div>
      <div>
        <h3 className="text-sm font-semibold text-neutral-900">{title}</h3>
        <p className="text-sm text-neutral-600">{description}</p>
      </div>
    </div>
  )
}

/**
 * Simple Auth Layout for minimal pages
 */
interface SimpleAuthLayoutProps {
  children: ReactNode
  title: string
  subtitle?: string
  className?: string
}

export function SimpleAuthLayout({
  children,
  title,
  subtitle,
  className = '',
}: SimpleAuthLayoutProps) {
  return (
    <div
      className={cn(
        'flex min-h-screen items-center justify-center bg-gray-50 px-4 py-12 sm:px-6 lg:px-8',
        className
      )}
    >
      <div className="w-full max-w-md space-y-8">
        {/* Header */}
        <div className="text-center">
          <div className="mb-6 flex items-center justify-center space-x-3">
            <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-gradient-to-br from-brand-primary to-brand-secondary shadow-md">
              <svg
                className="h-6 w-6 text-white"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M13 10V3L4 14h7v7l9-11h-7z"
                />
              </svg>
            </div>
            <span className="text-lg font-bold text-neutral-900">Ultimate Electrical Designer</span>
          </div>
          <h2 className="text-3xl font-extrabold text-gray-900">{title}</h2>
          {subtitle && <p className="mt-2 text-sm text-gray-600">{subtitle}</p>}
        </div>

        {/* Content */}
        {children}

        {/* Footer */}
        <div className="text-center">
          <p className="text-sm text-gray-600">
            <Link href="/" className="font-medium text-blue-600 hover:text-blue-500">
              ← Return to homepage
            </Link>
          </p>
        </div>
      </div>
    </div>
  )
}
