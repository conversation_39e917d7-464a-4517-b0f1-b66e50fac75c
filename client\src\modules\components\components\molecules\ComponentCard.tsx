/**
 * ComponentCard Molecule
 * Enhanced card component for displaying component information with accessibility support
 */

import React from 'react'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader } from '@/components/ui/card'
import { Checkbox } from '@/components/ui/checkbox'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'
import { cn } from '@/lib/utils'
import {
  Edit,
  MoreHorizontal,
  Star,
  Trash2,
  View,
  Heart,
  Copy,
  ExternalLink,
  Package,
  DollarSign,
  Weight,
  Ruler,
  StarOff,
} from 'lucide-react'
import type { ComponentRead } from '../../schemas'
import { ComponentBadge, useComponentBadgeProps } from '../atoms/ComponentBadge'
import { ComponentIcon, useComponentIconProps } from '../atoms/ComponentIcon'
import {
  formatPrice,
  formatComponentName,
  formatComponentDescription,
  formatWeight,
  formatDimensions,
} from '../../utils'

export interface ComponentCardProps {
  component: ComponentRead
  isSelected?: boolean
  showActions?: boolean
  showSelection?: boolean
  compact?: boolean
  variant?: 'default' | 'minimal' | 'detailed'
  className?: string
  onSelect?: (component: ComponentRead) => void
  onEdit?: (component: ComponentRead) => void
  onDelete?: (component: ComponentRead) => void
  onView?: (component: ComponentRead) => void
  onTogglePreferred?: (component: ComponentRead) => void
  onCopy?: (component: ComponentRead) => void
  onExternalLink?: (component: ComponentRead) => void
  'data-testid'?: string
}

export const ComponentCard = React.forwardRef<HTMLDivElement, ComponentCardProps>(
  (
    {
      component,
      isSelected = false,
      showActions = true,
      showSelection = false,
      compact = false,
      variant = 'default',
      className,
      onSelect,
      onEdit,
      onDelete,
      onView,
      onTogglePreferred,
      onCopy,
      onExternalLink,
      'data-testid': testId,
      ...props
    },
    ref
  ) => {
    const badgeProps = useComponentBadgeProps(component)
    const iconProps = useComponentIconProps(component)

    const handleCardClick = (e: React.MouseEvent) => {
      // Don't trigger card click if clicking on interactive elements
      if ((e.target as HTMLElement).closest('button, input, [role="button"]')) {
        return
      }

      if (onView) {
        onView(component)
      }
    }

    const handleKeyDown = (e: React.KeyboardEvent) => {
      if (e.key === 'Enter' || e.key === ' ') {
        e.preventDefault()
        if (onView) {
          onView(component)
        }
      }
    }

    const handleSelectionChange = (checked: boolean) => {
      if (onSelect && checked) {
        onSelect(component)
      }
    }

    const handleTogglePreferred = () => {
      if (onTogglePreferred) {
        onTogglePreferred(component)
      }
    }

    const cardContent = (
      <>
        {/* Header */}
        <CardHeader className={cn('pb-3', compact && 'pb-2')}>
          <div className="flex items-start justify-between gap-2">
            <div className="flex min-w-0 flex-1 items-start gap-3">
              {/* Selection checkbox */}
              {showSelection && (
                <Checkbox
                  checked={isSelected}
                  onCheckedChange={handleSelectionChange}
                  aria-label={`Select ${formatComponentName(component)}`}
                  data-testid={`${testId || 'component-card'}-checkbox`}
                />
              )}

              {/* Component icon */}
              <ComponentIcon
                {...iconProps}
                size={compact ? 'sm' : 'md'}
                className="mt-1 flex-shrink-0"
              />

              {/* Component info */}
              <div className="min-w-0 flex-1">
                <h3
                  className={cn(
                    'truncate font-semibold text-gray-900',
                    compact ? 'text-sm' : 'text-base'
                  )}
                >
                  {formatComponentName(component)}
                </h3>

                <div className="mt-1 flex items-center gap-2">
                  <span className={cn('truncate text-gray-600', compact ? 'text-xs' : 'text-sm')}>
                    {component.manufacturer}
                  </span>

                  <ComponentBadge {...badgeProps} size={compact ? 'sm' : 'md'} />
                </div>

                {component.model_number && (
                  <p className={cn('mt-1 truncate text-gray-500', compact ? 'text-xs' : 'text-sm')}>
                    Model: {component.model_number}
                  </p>
                )}
              </div>
            </div>

            {/* Actions */}
            {showActions && (
              <div className="flex items-center gap-1">
                {/* Preferred toggle */}
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={handleTogglePreferred}
                        className={cn('h-8 w-8 p-0', component.is_preferred && 'text-yellow-500')}
                        aria-label={
                          component.is_preferred ? 'Remove from preferred' : 'Add to preferred'
                        }
                        data-testid={`${testId || 'component-card'}-preferred`}
                      >
                        {component.is_preferred ? (
                          <Star className="h-4 w-4 fill-current" />
                        ) : (
                          <StarOff className="h-4 w-4" />
                        )}
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      {component.is_preferred ? 'Remove from preferred' : 'Add to preferred'}
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>

                {/* More actions dropdown */}
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-8 w-8 p-0"
                      aria-label="More actions"
                      data-testid={`${testId || 'component-card'}-actions`}
                    >
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    {onView && (
                      <DropdownMenuItem onClick={() => onView(component)}>
                        <View className="mr-2 h-4 w-4" />
                        View Details
                      </DropdownMenuItem>
                    )}
                    {onEdit && (
                      <DropdownMenuItem onClick={() => onEdit(component)}>
                        <Edit className="mr-2 h-4 w-4" />
                        Edit
                      </DropdownMenuItem>
                    )}
                    {onCopy && (
                      <DropdownMenuItem onClick={() => onCopy(component)}>
                        <Copy className="mr-2 h-4 w-4" />
                        Duplicate
                      </DropdownMenuItem>
                    )}
                    {onExternalLink && (
                      <DropdownMenuItem onClick={() => onExternalLink(component)}>
                        <ExternalLink className="mr-2 h-4 w-4" />
                        External Link
                      </DropdownMenuItem>
                    )}
                    {onDelete && (
                      <>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem
                          onClick={() => onDelete(component)}
                          className="text-red-600"
                        >
                          <Trash2 className="mr-2 h-4 w-4" />
                          Delete
                        </DropdownMenuItem>
                      </>
                    )}
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            )}
          </div>
        </CardHeader>

        {/* Content */}
        {!compact && variant !== 'minimal' && (
          <CardContent className="pt-0">
            {/* Description */}
            {component.description && (
              <p className="mb-3 text-sm text-gray-600">
                {formatComponentDescription(component.description, 120)}
              </p>
            )}

            {/* Details grid */}
            <div className="grid grid-cols-2 gap-3 text-sm">
              {/* Price */}
              {component.unit_price && (
                <div className="flex items-center gap-2">
                  <DollarSign className="h-4 w-4 text-gray-400" />
                  <span className="font-medium">
                    {formatPrice(component.unit_price, component.currency)}
                  </span>
                </div>
              )}

              {/* Category */}
              <div className="flex items-center gap-2">
                <Package className="h-4 w-4 text-gray-400" />
                <span className="truncate text-gray-600">{component.category}</span>
              </div>

              {/* Weight */}
              {component.weight_kg && (
                <div className="flex items-center gap-2">
                  <Weight className="h-4 w-4 text-gray-400" />
                  <span className="text-gray-600">{formatWeight(component.weight_kg)}</span>
                </div>
              )}

              {/* Dimensions */}
              {component.dimensions && (
                <div className="flex items-center gap-2">
                  <Ruler className="h-4 w-4 text-gray-400" />
                  <span className="truncate text-gray-600">
                    {formatDimensions(component.dimensions)}
                  </span>
                </div>
              )}
            </div>

            {/* Specifications preview */}
            {variant === 'detailed' && component.specifications && (
              <div className="mt-3 border-t pt-3">
                <h4 className="mb-2 text-xs font-medium text-gray-700">Key Specifications</h4>
                <div className="grid grid-cols-1 gap-1 text-xs">
                  {Object.entries(component.specifications as Record<string, any>)
                    .slice(0, 3)
                    .map(([key, value]) => (
                      <div key={key} className="flex justify-between">
                        <span className="capitalize text-gray-500">{key.replace(/_/g, ' ')}:</span>
                        <span className="font-medium text-gray-700">{String(value)}</span>
                      </div>
                    ))}
                </div>
              </div>
            )}
          </CardContent>
        )}
      </>
    )

    return (
      <Card
        ref={ref}
        className={cn(
          'cursor-pointer transition-all duration-200 hover:shadow-md',
          isSelected && 'ring-2 ring-blue-500 ring-offset-2',
          !component.is_active && 'opacity-60',
          compact && 'p-3',
          className
        )}
        onClick={handleCardClick}
        onKeyDown={handleKeyDown}
        tabIndex={0}
        role="article"
        aria-label={`Component: ${formatComponentName(component)}`}
        data-testid={testId || `component-card-${component.id}`}
        {...props}
      >
        {cardContent}
      </Card>
    )
  }
)

ComponentCard.displayName = 'ComponentCard'

// Convenience variants
export const CompactComponentCard = (props: Omit<ComponentCardProps, 'compact'>) => (
  <ComponentCard compact {...props} />
)

export const MinimalComponentCard = (props: Omit<ComponentCardProps, 'variant'>) => (
  <ComponentCard variant="minimal" {...props} />
)

export const DetailedComponentCard = (props: Omit<ComponentCardProps, 'variant'>) => (
  <ComponentCard variant="detailed" {...props} />
)
