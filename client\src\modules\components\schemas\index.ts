/**
 * Component Schemas Index
 * Central export point for all component-related Zod schemas and types
 */

// Component schemas
export * from './componentSchemas'
export * from './searchSchemas'
export * from './uiSchemas'

// Re-export commonly used schemas for convenience
export {
  ComponentCreateSchema,
  ComponentUpdateSchema,
  ComponentReadSchema,
  ComponentSummarySchema,
  ComponentPaginatedResponseSchema,
  ComponentStatsSchema,
  ComponentBulkCreateSchema,
  ComponentBulkUpdateSchema,
  ComponentValidationResultSchema,
} from './componentSchemas'

export {
  ComponentSearchSchema,
  ComponentFilterSchema,
  ComponentAdvancedSearchSchema,
  ComponentAdvancedSearchResponseSchema,
  SearchSuggestionsSchema,
  SavedSearchSchema,
} from './searchSchemas'

export {
  ComponentListStateSchema,
  SearchStateSchema,
  FilterStateSchema,
  BulkOperationStateSchema,
  ComponentFormStateSchema,
  ComponentDisplayOptionsSchema,
  UserPreferencesSchema,
} from './uiSchemas'

// Re-export commonly used types
export type {
  ComponentCreate,
  ComponentUpdate,
  ComponentRead,
  ComponentSummary,
  ComponentPaginatedResponse,
  ComponentStats,
  ComponentBulkCreate,
  ComponentBulkUpdate,
  ComponentValidationResult,
} from './componentSchemas'

export type {
  ComponentSearch,
  ComponentFilter,
  ComponentAdvancedSearch,
  ComponentAdvancedSearchResponse,
  SearchSuggestions,
  SavedSearch,
  RangeFilter,
  SpecificationFilter,
  AdvancedFilter,
} from './searchSchemas'

export type {
  ComponentListState,
  SearchState,
  FilterState,
  BulkOperationState,
  ComponentFormState,
  ComponentDisplayOptions,
  UserPreferences,
  ViewMode,
  SortConfig,
  PaginationConfig,
  Notification,
  Toast,
} from './uiSchemas'
