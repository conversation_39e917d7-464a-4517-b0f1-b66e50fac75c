# Authentication Components Implementation Guide

This guide provides detailed implementation instructions for integrating and extending the authentication components.

## Quick Start

### 1. Basic Integration

```tsx
// pages/LoginPage.tsx
import { LoginForm } from '@/components/auth'
import { useAuth } from '@/components/auth/hooks'

export function LoginPage() {
  const { login, isLoading, error } = useAuth()

  const handleLogin = async (credentials: LoginCredentials) => {
    try {
      await login(credentials)
      // Redirect handled by useAuth hook
    } catch (err) {
      // Error handled by useAuth hook
    }
  }

  return (
    <div className="flex min-h-screen items-center justify-center">
      <div className="w-full max-w-md">
        <LoginForm onSubmit={handleLogin} isLoading={isLoading} error={error} />
      </div>
    </div>
  )
}
```

### 2. Router Integration

```tsx
// App.tsx
import { BrowserRouter, Routes, Route, Navigate } from 'react-router-dom'
import { AuthProvider, useAuth } from '@/components/auth/hooks'
import { LoginPage } from './pages/LoginPage'
import { Dashboard } from './pages/Dashboard'

function ProtectedRoute({ children }: { children: React.ReactNode }) {
  const { isAuthenticated, isLoading } = useAuth()

  if (isLoading) return <div>Loading...</div>

  return isAuthenticated ? children : <Navigate to="/login" />
}

function App() {
  return (
    <AuthProvider>
      <BrowserRouter>
        <Routes>
          <Route path="/login" element={<LoginPage />} />
          <Route path="/register" element={<RegisterPage />} />
          <Route
            path="/dashboard"
            element={
              <ProtectedRoute>
                <Dashboard />
              </ProtectedRoute>
            }
          />
          <Route path="/" element={<Navigate to="/dashboard" />} />
        </Routes>
      </BrowserRouter>
    </AuthProvider>
  )
}
```

## Advanced Configuration

### 1. Custom Validation Rules

```tsx
// utils/customValidation.ts
import { z } from 'zod'

// Extend base validation with custom rules
export const customLoginSchema = z.object({
  email: z
    .string()
    .email('Invalid email format')
    .refine((email) => email.endsWith('@company.com'), {
      message: 'Must use company email',
    }),
  password: z
    .string()
    .min(12, 'Password must be at least 12 characters')
    .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/, {
      message: 'Password must contain uppercase, lowercase, number, and special character',
    }),
})

// Custom validation hook
export function useCustomValidation() {
  return {
    validateLogin: (data: LoginCredentials) => {
      try {
        customLoginSchema.parse(data)
        return { success: true, errors: {} }
      } catch (error) {
        if (error instanceof z.ZodError) {
          const errors: Record<string, string> = {}
          error.errors.forEach((err) => {
            const path = err.path.join('.')
            errors[path] = err.message
          })
          return { success: false, errors }
        }
        return { success: false, errors: { general: 'Validation failed' } }
      }
    },
  }
}
```

### 2. Custom Authentication Provider

```tsx
// hooks/useCustomAuth.ts
import { createContext, useContext, useReducer, useEffect } from 'react'
import { AuthState, AuthAction, User } from '../types'

const AuthContext = createContext<{
  state: AuthState
  dispatch: React.Dispatch<AuthAction>
} | null>(null)

function authReducer(state: AuthState, action: AuthAction): AuthState {
  switch (action.type) {
    case 'LOGIN_START':
      return { ...state, isLoading: true, error: null }
    case 'LOGIN_SUCCESS':
      return {
        ...state,
        isLoading: false,
        isAuthenticated: true,
        user: action.payload.user,
        token: action.payload.token,
      }
    case 'LOGIN_ERROR':
      return {
        ...state,
        isLoading: false,
        error: action.payload.error,
      }
    case 'LOGOUT':
      return {
        ...state,
        isAuthenticated: false,
        user: null,
        token: null,
      }
    default:
      return state
  }
}

export function CustomAuthProvider({ children }: { children: React.ReactNode }) {
  const [state, dispatch] = useReducer(authReducer, {
    isAuthenticated: false,
    isLoading: false,
    user: null,
    token: null,
    error: null,
  })

  // Auto-login from stored token
  useEffect(() => {
    const token = localStorage.getItem('auth_token')
    if (token) {
      // Validate token and restore user session
      validateAndRestoreSession(token)
    }
  }, [])

  const login = async (credentials: LoginCredentials) => {
    dispatch({ type: 'LOGIN_START' })

    try {
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(credentials),
      })

      if (!response.ok) {
        throw new Error('Login failed')
      }

      const data = await response.json()

      // Store token
      localStorage.setItem('auth_token', data.token)

      dispatch({
        type: 'LOGIN_SUCCESS',
        payload: { user: data.user, token: data.token },
      })
    } catch (error) {
      dispatch({
        type: 'LOGIN_ERROR',
        payload: { error: error.message },
      })
    }
  }

  const logout = () => {
    localStorage.removeItem('auth_token')
    dispatch({ type: 'LOGOUT' })
  }

  return <AuthContext.Provider value={{ state, dispatch }}>{children}</AuthContext.Provider>
}

export function useCustomAuth() {
  const context = useContext(AuthContext)
  if (!context) {
    throw new Error('useCustomAuth must be used within CustomAuthProvider')
  }

  const { state, dispatch } = context

  return {
    ...state,
    login,
    logout,
  }
}
```

### 3. Form Customization

```tsx
// components/CustomLoginForm.tsx
import { useState } from 'react'
import { LoginForm } from '@/components/auth'
import { Button } from '@/components/ui/Button'

interface CustomLoginFormProps {
  onSubmit: (credentials: LoginCredentials) => Promise<void>
  isLoading?: boolean
  error?: string | null
  showSocialLogin?: boolean
  showForgotPassword?: boolean
}

export function CustomLoginForm({
  onSubmit,
  isLoading,
  error,
  showSocialLogin = true,
  showForgotPassword = true,
}: CustomLoginFormProps) {
  const [rememberMe, setRememberMe] = useState(false)

  const handleSubmit = async (credentials: LoginCredentials) => {
    // Add remember me logic
    if (rememberMe) {
      localStorage.setItem('remember_me', 'true')
    }

    await onSubmit(credentials)
  }

  return (
    <div className="space-y-6">
      <LoginForm onSubmit={handleSubmit} isLoading={isLoading} error={error} />

      {/* Remember Me Checkbox */}
      <div className="flex items-center">
        <input
          id="remember-me"
          type="checkbox"
          checked={rememberMe}
          onChange={(e) => setRememberMe(e.target.checked)}
          className="h-4 w-4 rounded border-gray-300 text-brand-primary focus:ring-brand-primary"
        />
        <label htmlFor="remember-me" className="ml-2 block text-sm text-gray-900">
          Remember me
        </label>
      </div>

      {/* Social Login */}
      {showSocialLogin && (
        <div className="space-y-3">
          <div className="relative">
            <div className="absolute inset-0 flex items-center">
              <div className="w-full border-t border-gray-300" />
            </div>
            <div className="relative flex justify-center text-sm">
              <span className="bg-white px-2 text-gray-500">Or continue with</span>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-3">
            <Button variant="outline" onClick={() => handleSocialLogin('google')}>
              <GoogleIcon className="mr-2 h-5 w-5" />
              Google
            </Button>
            <Button variant="outline" onClick={() => handleSocialLogin('microsoft')}>
              <MicrosoftIcon className="mr-2 h-5 w-5" />
              Microsoft
            </Button>
          </div>
        </div>
      )}

      {/* Forgot Password */}
      {showForgotPassword && (
        <div className="text-center">
          <button
            type="button"
            onClick={() => handleForgotPassword()}
            className="text-sm text-brand-primary hover:text-brand-primary/80"
          >
            Forgot your password?
          </button>
        </div>
      )}
    </div>
  )
}
```

## API Integration

### 1. Authentication Service

```tsx
// services/authService.ts
import { LoginCredentials, RegisterData, AuthResponse } from '../types'

class AuthService {
  private baseURL = process.env.REACT_APP_API_URL || '/api'

  async login(credentials: LoginCredentials): Promise<AuthResponse> {
    const response = await fetch(`${this.baseURL}/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(credentials),
    })

    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.message || 'Login failed')
    }

    return response.json()
  }

  async register(data: RegisterData): Promise<AuthResponse> {
    const response = await fetch(`${this.baseURL}/auth/register`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    })

    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.message || 'Registration failed')
    }

    return response.json()
  }

  async logout(): Promise<void> {
    const token = localStorage.getItem('auth_token')

    if (token) {
      await fetch(`${this.baseURL}/auth/logout`, {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${token}`,
        },
      })
    }

    localStorage.removeItem('auth_token')
  }

  async refreshToken(): Promise<string> {
    const refreshToken = localStorage.getItem('refresh_token')

    if (!refreshToken) {
      throw new Error('No refresh token available')
    }

    const response = await fetch(`${this.baseURL}/auth/refresh`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ refreshToken }),
    })

    if (!response.ok) {
      throw new Error('Token refresh failed')
    }

    const data = await response.json()
    localStorage.setItem('auth_token', data.token)

    return data.token
  }

  async validateToken(token: string): Promise<User> {
    const response = await fetch(`${this.baseURL}/auth/validate`, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    })

    if (!response.ok) {
      throw new Error('Token validation failed')
    }

    return response.json()
  }
}

export const authService = new AuthService()
```

### 2. HTTP Interceptor

```tsx
// utils/httpClient.ts
import { authService } from '../services/authService'

class HttpClient {
  private baseURL = process.env.REACT_APP_API_URL || '/api'

  async request<T>(endpoint: string, options: RequestInit = {}): Promise<T> {
    const url = `${this.baseURL}${endpoint}`
    const token = localStorage.getItem('auth_token')

    const config: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
        ...(token && { Authorization: `Bearer ${token}` }),
        ...options.headers,
      },
      ...options,
    }

    let response = await fetch(url, config)

    // Handle token expiration
    if (response.status === 401 && token) {
      try {
        const newToken = await authService.refreshToken()

        // Retry request with new token
        config.headers = {
          ...config.headers,
          Authorization: `Bearer ${newToken}`,
        }

        response = await fetch(url, config)
      } catch (error) {
        // Refresh failed, redirect to login
        localStorage.removeItem('auth_token')
        localStorage.removeItem('refresh_token')
        window.location.href = '/login'
        throw error
      }
    }

    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.message || 'Request failed')
    }

    return response.json()
  }

  get<T>(endpoint: string): Promise<T> {
    return this.request<T>(endpoint)
  }

  post<T>(endpoint: string, data?: any): Promise<T> {
    return this.request<T>(endpoint, {
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
    })
  }

  put<T>(endpoint: string, data?: any): Promise<T> {
    return this.request<T>(endpoint, {
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
    })
  }

  delete<T>(endpoint: string): Promise<T> {
    return this.request<T>(endpoint, {
      method: 'DELETE',
    })
  }
}

export const httpClient = new HttpClient()
```

## Error Handling

### 1. Error Boundary

```tsx
// components/AuthErrorBoundary.tsx
import { Component, ErrorInfo, ReactNode } from 'react'

interface Props {
  children: ReactNode
  fallback?: ReactNode
}

interface State {
  hasError: boolean
  error?: Error
}

export class AuthErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props)
    this.state = { hasError: false }
  }

  static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error }
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('Auth error:', error, errorInfo)

    // Log to error reporting service
    if (process.env.NODE_ENV === 'production') {
      // logErrorToService(error, errorInfo)
    }
  }

  render() {
    if (this.state.hasError) {
      return (
        this.props.fallback || (
          <div className="flex min-h-screen items-center justify-center">
            <div className="text-center">
              <h2 className="mb-4 text-2xl font-bold text-gray-900">Authentication Error</h2>
              <p className="mb-4 text-gray-600">Something went wrong with authentication.</p>
              <button
                onClick={() => window.location.reload()}
                className="rounded bg-brand-primary px-4 py-2 text-white hover:bg-brand-primary/90"
              >
                Reload Page
              </button>
            </div>
          </div>
        )
      )
    }

    return this.props.children
  }
}
```

### 2. Global Error Handler

```tsx
// hooks/useErrorHandler.ts
import { useCallback } from 'react'
import { toast } from 'react-hot-toast'

export function useErrorHandler() {
  const handleError = useCallback((error: Error | string) => {
    const message = typeof error === 'string' ? error : error.message

    // Log error
    console.error('Application error:', error)

    // Show user-friendly message
    if (message.includes('network') || message.includes('fetch')) {
      toast.error('Network error. Please check your connection.')
    } else if (message.includes('unauthorized') || message.includes('401')) {
      toast.error('Session expired. Please log in again.')
      // Redirect to login
      window.location.href = '/login'
    } else if (message.includes('forbidden') || message.includes('403')) {
      toast.error('You do not have permission to perform this action.')
    } else {
      toast.error(message || 'An unexpected error occurred.')
    }
  }, [])

  return { handleError }
}
```

## Security Considerations

### 1. Token Storage

```tsx
// utils/secureStorage.ts
class SecureStorage {
  private static instance: SecureStorage

  static getInstance(): SecureStorage {
    if (!SecureStorage.instance) {
      SecureStorage.instance = new SecureStorage()
    }
    return SecureStorage.instance
  }

  setToken(token: string): void {
    // Use httpOnly cookies in production
    if (process.env.NODE_ENV === 'production') {
      // Set secure, httpOnly cookie via API call
      this.setSecureCookie('auth_token', token)
    } else {
      // Use localStorage for development
      localStorage.setItem('auth_token', token)
    }
  }

  getToken(): string | null {
    if (process.env.NODE_ENV === 'production') {
      // Token will be sent automatically with requests
      return null
    } else {
      return localStorage.getItem('auth_token')
    }
  }

  removeToken(): void {
    if (process.env.NODE_ENV === 'production') {
      this.clearSecureCookie('auth_token')
    } else {
      localStorage.removeItem('auth_token')
    }
  }

  private async setSecureCookie(name: string, value: string): Promise<void> {
    await fetch('/api/auth/set-cookie', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ name, value }),
    })
  }

  private async clearSecureCookie(name: string): Promise<void> {
    await fetch('/api/auth/clear-cookie', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ name }),
    })
  }
}

export const secureStorage = SecureStorage.getInstance()
```

### 2. CSRF Protection

```tsx
// utils/csrfProtection.ts
export async function getCSRFToken(): Promise<string> {
  const response = await fetch('/api/csrf-token')
  const data = await response.json()
  return data.token
}

export function addCSRFHeader(headers: Record<string, string> = {}): Record<string, string> {
  const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content')

  if (csrfToken) {
    headers['X-CSRF-Token'] = csrfToken
  }

  return headers
}
```

## Performance Optimization

### 1. Code Splitting

```tsx
// Lazy load auth components
import { lazy, Suspense } from 'react'

const LoginForm = lazy(() => import('./components/LoginForm'))
const RegisterForm = lazy(() => import('./components/RegisterForm'))

function AuthPage() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <LoginForm onSubmit={handleLogin} />
    </Suspense>
  )
}
```

### 2. Memoization

```tsx
// Memoize expensive operations
import { useMemo, useCallback } from 'react'

export function useAuthMemo() {
  const memoizedValidation = useMemo(() => {
    return createValidationSchema()
  }, [])

  const memoizedSubmit = useCallback(async (data: LoginCredentials) => {
    // Expensive submit logic
  }, [])

  return { memoizedValidation, memoizedSubmit }
}
```

## Deployment Considerations

### 1. Environment Configuration

```tsx
// config/auth.ts
export const authConfig = {
  apiUrl: process.env.REACT_APP_API_URL || 'http://localhost:3001',
  tokenKey: process.env.REACT_APP_TOKEN_KEY || 'auth_token',
  refreshTokenKey: process.env.REACT_APP_REFRESH_TOKEN_KEY || 'refresh_token',
  tokenExpiry: parseInt(process.env.REACT_APP_TOKEN_EXPIRY || '3600'),
  enableRememberMe: process.env.REACT_APP_ENABLE_REMEMBER_ME === 'true',
  enableSocialLogin: process.env.REACT_APP_ENABLE_SOCIAL_LOGIN === 'true',
  csrfProtection: process.env.REACT_APP_CSRF_PROTECTION === 'true',
}
```

### 2. Build Optimization

```json
// package.json
{
  "scripts": {
    "build:auth": "vite build --mode auth",
    "analyze:auth": "npm run build:auth && npx vite-bundle-analyzer dist"
  }
}
```

This implementation guide provides comprehensive patterns for integrating and extending the authentication components in various scenarios.
