/**
 * Enhanced authentication hook with RBAC integration
 * Extends the base useAuth hook with role-based access control features
 */

import { useAuth } from '@/hooks/useAuth'
import { useUserRolesSummary, useUserHasRole, useUserHasPermission, useUserEffectivePermissions } from '@/hooks/api/useRbac'
import { useLogSecurityEvent } from '@/hooks/api/useAudit'
import { useRouter } from 'next/navigation'
import { useCallback, useMemo } from 'react'
import type { UserRolesSummary } from '@/types/api'

interface RbacAuthReturn {
  // All base auth functionality
  user: any
  token: string | null
  isAuthenticated: boolean
  isLoading: boolean
  login: (credentials: any) => Promise<any>
  logout: () => Promise<void>
  register: (userData: any) => Promise<any>
  
  // RBAC-specific functionality
  userRoles: UserRolesSummary | undefined
  isRolesLoading: boolean
  rolesError: Error | null
  
  // Permission checking methods
  hasRole: (roleName: string) => boolean
  hasPermission: (permission: string) => boolean
  hasAnyRole: (roleNames: string[]) => boolean
  hasAllRoles: (roleNames: string[]) => boolean
  hasAnyPermission: (permissions: string[]) => boolean
  hasAllPermissions: (permissions: string[]) => boolean
  
  // Enhanced authorization methods
  requireRole: (roleName: string, redirectTo?: string) => boolean
  requirePermission: (permission: string, redirectTo?: string) => boolean
  requireAnyRole: (roleNames: string[], redirectTo?: string) => boolean
  requireAllRoles: (roleNames: string[], redirectTo?: string) => boolean
  requireAnyPermission: (permissions: string[], redirectTo?: string) => boolean
  requireAllPermissions: (permissions: string[], redirectTo?: string) => boolean
  
  // Role management
  getActiveRoles: () => string[]
  getEffectivePermissions: () => string[]
  isRoleExpired: (roleName: string) => boolean
  
  // Security logging
  logSecurityEvent: (event: string, details?: Record<string, any>) => void
}

export function useRbacAuth(): RbacAuthReturn {
  const router = useRouter()
  const baseAuth = useAuth()
  const { user, isAuthenticated, isLoading: baseLoading } = baseAuth
  
  // RBAC-specific queries
  const { 
    data: userRoles, 
    isLoading: isRolesLoading, 
    error: rolesError 
  } = useUserRolesSummary(user?.id || 0)
  
  // Security logging
  const logSecurityEventMutation = useLogSecurityEvent()
  
  // Permission checking methods
  const hasRole = useCallback((roleName: string): boolean => {
    if (!userRoles || !userRoles.active_roles) return false
    return userRoles.active_roles.some(role => role.name === roleName)
  }, [userRoles])
  
  const hasPermission = useCallback((permission: string): boolean => {
    if (!userRoles || !userRoles.effective_permissions) return false
    return userRoles.effective_permissions.includes(permission)
  }, [userRoles])
  
  const hasAnyRole = useCallback((roleNames: string[]): boolean => {
    return roleNames.some(roleName => hasRole(roleName))
  }, [hasRole])
  
  const hasAllRoles = useCallback((roleNames: string[]): boolean => {
    return roleNames.every(roleName => hasRole(roleName))
  }, [hasRole])
  
  const hasAnyPermission = useCallback((permissions: string[]): boolean => {
    return permissions.some(permission => hasPermission(permission))
  }, [hasPermission])
  
  const hasAllPermissions = useCallback((permissions: string[]): boolean => {
    return permissions.every(permission => hasPermission(permission))
  }, [hasPermission])
  
  // Enhanced authorization methods with security logging
  const requireRole = useCallback((roleName: string, redirectTo: string = '/dashboard'): boolean => {
    if (!isAuthenticated) {
      logSecurityEventMutation.mutate({
        action_type: 'UNAUTHORIZED_ACCESS_ATTEMPT',
        action_description: `Attempted to access role-protected resource without authentication. Required role: ${roleName}`,
        severity: 'MEDIUM',
        metadata: { required_role: roleName, redirect_to: redirectTo }
      })
      router.push('/login')
      return false
    }
    
    if (!hasRole(roleName)) {
      logSecurityEventMutation.mutate({
        action_type: 'INSUFFICIENT_PRIVILEGES',
        action_description: `User lacks required role for resource access. Required role: ${roleName}`,
        severity: 'MEDIUM',
        metadata: { 
          required_role: roleName, 
          user_roles: userRoles?.active_roles?.map(r => r.name) || [],
          redirect_to: redirectTo
        }
      })
      router.push(redirectTo)
      return false
    }
    
    return true
  }, [isAuthenticated, hasRole, router, logSecurityEventMutation, userRoles])
  
  const requirePermission = useCallback((permission: string, redirectTo: string = '/dashboard'): boolean => {
    if (!isAuthenticated) {
      logSecurityEventMutation.mutate({
        action_type: 'UNAUTHORIZED_ACCESS_ATTEMPT',
        action_description: `Attempted to access permission-protected resource without authentication. Required permission: ${permission}`,
        severity: 'MEDIUM',
        metadata: { required_permission: permission, redirect_to: redirectTo }
      })
      router.push('/login')
      return false
    }
    
    if (!hasPermission(permission)) {
      logSecurityEventMutation.mutate({
        action_type: 'INSUFFICIENT_PRIVILEGES',
        action_description: `User lacks required permission for resource access. Required permission: ${permission}`,
        severity: 'MEDIUM',
        metadata: { 
          required_permission: permission, 
          user_permissions: userRoles?.effective_permissions || [],
          redirect_to: redirectTo
        }
      })
      router.push(redirectTo)
      return false
    }
    
    return true
  }, [isAuthenticated, hasPermission, router, logSecurityEventMutation, userRoles])
  
  const requireAnyRole = useCallback((roleNames: string[], redirectTo: string = '/dashboard'): boolean => {
    if (!isAuthenticated) {
      logSecurityEventMutation.mutate({
        action_type: 'UNAUTHORIZED_ACCESS_ATTEMPT',
        action_description: `Attempted to access role-protected resource without authentication. Required any of roles: ${roleNames.join(', ')}`,
        severity: 'MEDIUM',
        metadata: { required_roles: roleNames, redirect_to: redirectTo }
      })
      router.push('/login')
      return false
    }
    
    if (!hasAnyRole(roleNames)) {
      logSecurityEventMutation.mutate({
        action_type: 'INSUFFICIENT_PRIVILEGES',
        action_description: `User lacks any of the required roles for resource access. Required any of: ${roleNames.join(', ')}`,
        severity: 'MEDIUM',
        metadata: { 
          required_roles: roleNames, 
          user_roles: userRoles?.active_roles?.map(r => r.name) || [],
          redirect_to: redirectTo
        }
      })
      router.push(redirectTo)
      return false
    }
    
    return true
  }, [isAuthenticated, hasAnyRole, router, logSecurityEventMutation, userRoles])
  
  const requireAllRoles = useCallback((roleNames: string[], redirectTo: string = '/dashboard'): boolean => {
    if (!isAuthenticated) {
      logSecurityEventMutation.mutate({
        action_type: 'UNAUTHORIZED_ACCESS_ATTEMPT',
        action_description: `Attempted to access role-protected resource without authentication. Required all roles: ${roleNames.join(', ')}`,
        severity: 'MEDIUM',
        metadata: { required_roles: roleNames, redirect_to: redirectTo }
      })
      router.push('/login')
      return false
    }
    
    if (!hasAllRoles(roleNames)) {
      logSecurityEventMutation.mutate({
        action_type: 'INSUFFICIENT_PRIVILEGES',
        action_description: `User lacks all required roles for resource access. Required all of: ${roleNames.join(', ')}`,
        severity: 'MEDIUM',
        metadata: { 
          required_roles: roleNames, 
          user_roles: userRoles?.active_roles?.map(r => r.name) || [],
          redirect_to: redirectTo
        }
      })
      router.push(redirectTo)
      return false
    }
    
    return true
  }, [isAuthenticated, hasAllRoles, router, logSecurityEventMutation, userRoles])
  
  const requireAnyPermission = useCallback((permissions: string[], redirectTo: string = '/dashboard'): boolean => {
    if (!isAuthenticated) {
      logSecurityEventMutation.mutate({
        action_type: 'UNAUTHORIZED_ACCESS_ATTEMPT',
        action_description: `Attempted to access permission-protected resource without authentication. Required any of permissions: ${permissions.join(', ')}`,
        severity: 'MEDIUM',
        metadata: { required_permissions: permissions, redirect_to: redirectTo }
      })
      router.push('/login')
      return false
    }
    
    if (!hasAnyPermission(permissions)) {
      logSecurityEventMutation.mutate({
        action_type: 'INSUFFICIENT_PRIVILEGES',
        action_description: `User lacks any of the required permissions for resource access. Required any of: ${permissions.join(', ')}`,
        severity: 'MEDIUM',
        metadata: { 
          required_permissions: permissions, 
          user_permissions: userRoles?.effective_permissions || [],
          redirect_to: redirectTo
        }
      })
      router.push(redirectTo)
      return false
    }
    
    return true
  }, [isAuthenticated, hasAnyPermission, router, logSecurityEventMutation, userRoles])
  
  const requireAllPermissions = useCallback((permissions: string[], redirectTo: string = '/dashboard'): boolean => {
    if (!isAuthenticated) {
      logSecurityEventMutation.mutate({
        action_type: 'UNAUTHORIZED_ACCESS_ATTEMPT',
        action_description: `Attempted to access permission-protected resource without authentication. Required all permissions: ${permissions.join(', ')}`,
        severity: 'MEDIUM',
        metadata: { required_permissions: permissions, redirect_to: redirectTo }
      })
      router.push('/login')
      return false
    }
    
    if (!hasAllPermissions(permissions)) {
      logSecurityEventMutation.mutate({
        action_type: 'INSUFFICIENT_PRIVILEGES',
        action_description: `User lacks all required permissions for resource access. Required all of: ${permissions.join(', ')}`,
        severity: 'MEDIUM',
        metadata: { 
          required_permissions: permissions, 
          user_permissions: userRoles?.effective_permissions || [],
          redirect_to: redirectTo
        }
      })
      router.push(redirectTo)
      return false
    }
    
    return true
  }, [isAuthenticated, hasAllPermissions, router, logSecurityEventMutation, userRoles])
  
  // Role management methods
  const getActiveRoles = useCallback((): string[] => {
    return userRoles?.active_roles?.map(role => role.name) || []
  }, [userRoles])
  
  const getEffectivePermissions = useCallback((): string[] => {
    return userRoles?.effective_permissions || []
  }, [userRoles])
  
  const isRoleExpired = useCallback((roleName: string): boolean => {
    const expiredRoles = userRoles?.expired_roles || []
    return expiredRoles.some(role => role.name === roleName)
  }, [userRoles])
  
  // Security logging helper
  const logSecurityEvent = useCallback((event: string, details?: Record<string, any>) => {
    logSecurityEventMutation.mutate({
      action_type: event,
      action_description: `Security event: ${event}`,
      severity: 'INFO',
      metadata: details
    })
  }, [logSecurityEventMutation])
  
  // Combined loading state
  const isLoading = useMemo(() => {
    return baseLoading || isRolesLoading
  }, [baseLoading, isRolesLoading])
  
  return {
    // Base auth functionality
    ...baseAuth,
    isLoading,
    
    // RBAC-specific data
    userRoles,
    isRolesLoading,
    rolesError,
    
    // Permission checking methods
    hasRole,
    hasPermission,
    hasAnyRole,
    hasAllRoles,
    hasAnyPermission,
    hasAllPermissions,
    
    // Enhanced authorization methods
    requireRole,
    requirePermission,
    requireAnyRole,
    requireAllRoles,
    requireAnyPermission,
    requireAllPermissions,
    
    // Role management
    getActiveRoles,
    getEffectivePermissions,
    isRoleExpired,
    
    // Security logging
    logSecurityEvent,
  }
}