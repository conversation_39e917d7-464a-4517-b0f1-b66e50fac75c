/**
 * Zod validation schemas for the Settings module
 * Ultimate Electrical Designer - Settings & User Preferences
 */

import { z } from 'zod'
import { VALIDATION_RULES } from '../constants'

// Base schema for common fields
const BaseSettingsSchema = z.object({
  id: z.number().optional(),
  created_at: z.string().optional(),
  updated_at: z.string().optional(),
})

// User Preferences Schema (matches backend)
export const UserPreferencesSchema = BaseSettingsSchema.extend({
  user_id: z.number().optional(),
  theme: z.enum(['light', 'dark', 'system']).default('system'),
  language: z.string().min(2).max(5).default('en'),
  timezone: z.string().min(1).max(50).default('UTC'),
  date_format: z.string().min(1).max(20).default('YYYY-MM-DD'),
  time_format: z.enum(['12h', '24h']).default('24h'),
  units_system: z.enum(['metric', 'imperial']).default('metric'),
  notifications_enabled: z.boolean().default(true),
  email_notifications: z.boolean().default(true),
  auto_save_interval: z
    .number()
    .min(VALIDATION_RULES.AUTO_SAVE_INTERVAL.min)
    .max(VALIDATION_RULES.AUTO_SAVE_INTERVAL.max)
    .default(300),
  dashboard_layout: z.enum(['default', 'compact', 'expanded']).default('default'),
  calculation_precision: z
    .number()
    .min(VALIDATION_RULES.CALCULATION_PRECISION.min)
    .max(VALIDATION_RULES.CALCULATION_PRECISION.max)
    .default(2),
  auto_save_enabled: z.boolean().default(true),
})

// Partial update schema for preferences
export const UserPreferencesUpdateSchema = UserPreferencesSchema.partial().omit({
  id: true,
  user_id: true,
  created_at: true,
  updated_at: true,
})

// Engineering Settings Schema
export const EngineeringSettingsSchema = z.object({
  default_voltage: z.string().min(1).max(10).default('230V'),
  default_frequency: z.string().min(1).max(10).default('50Hz'),
  default_temperature_unit: z.enum(['°C', '°F', 'K']).default('°C'),
  default_currency: z.string().length(3).default('EUR'),
  safety_factor: z
    .number()
    .min(VALIDATION_RULES.SAFETY_FACTOR.min)
    .max(VALIDATION_RULES.SAFETY_FACTOR.max)
    .default(1.2),
  calculation_standards: z.array(z.string()).default(['IEC', 'EN']),
  preferred_manufacturers: z.array(z.string()).default([]),
  component_rating_system: z.string().min(1).max(20).default('IP66'),
})

// Settings Field Schema
export const SettingsFieldSchema = z.object({
  id: z.string().min(1),
  label: z.string().min(1),
  description: z.string().optional(),
  type: z.enum(['text', 'number', 'boolean', 'select', 'multiselect', 'slider', 'color']),
  value: z.any(),
  options: z
    .array(
      z.object({
        value: z.any(),
        label: z.string(),
        description: z.string().optional(),
      })
    )
    .optional(),
  validation: z
    .object({
      required: z.boolean().optional(),
      min: z.number().optional(),
      max: z.number().optional(),
      pattern: z.string().optional(),
      custom: z.function().optional(),
    })
    .optional(),
  disabled: z.boolean().optional(),
  hidden: z.boolean().optional(),
})

// Settings Section Schema
export const SettingsSectionSchema = z.object({
  id: z.string().min(1),
  label: z.string().min(1),
  description: z.string().optional(),
  fields: z.array(SettingsFieldSchema),
})

// Settings Category Schema
export const SettingsCategorySchema = z.object({
  id: z.enum(['account', 'appearance', 'notifications', 'privacy', 'advanced', 'engineering']),
  label: z.string().min(1),
  description: z.string().min(1),
  icon: z.string().min(1),
  sections: z.array(SettingsSectionSchema),
})

// Settings Export Schema
export const SettingsExportSchema = z.object({
  preferences: UserPreferencesSchema,
  exported_at: z.string(),
  version: z.string().default('1.0'),
  metadata: z
    .object({
      user_id: z.number().optional(),
      export_type: z.enum(['full', 'partial']).default('full'),
      categories: z
        .array(
          z.enum(['account', 'appearance', 'notifications', 'privacy', 'advanced', 'engineering'])
        )
        .optional(),
    })
    .optional(),
})

// Settings Import Schema
export const SettingsImportSchema = z.object({
  preferences: UserPreferencesUpdateSchema,
  version: z.string().optional(),
  validate: z.boolean().default(true),
  merge: z.boolean().default(true),
})

// Settings Validation Error Schema
export const SettingsValidationErrorSchema = z.object({
  field: z.string().min(1),
  message: z.string().min(1),
  code: z.string().min(1),
})

// Settings Validation Warning Schema
export const SettingsValidationWarningSchema = z.object({
  field: z.string().min(1),
  message: z.string().min(1),
  code: z.string().min(1),
})

// Settings Validation Result Schema
export const SettingsValidationResultSchema = z.object({
  isValid: z.boolean(),
  errors: z.array(SettingsValidationErrorSchema),
  warnings: z.array(SettingsValidationWarningSchema),
})

// UI State Schema
export const SettingsUIStateSchema = z.object({
  activeCategory: z
    .enum(['account', 'appearance', 'notifications', 'privacy', 'advanced', 'engineering'])
    .default('account'),
  searchQuery: z.string().default(''),
  isLoading: z.boolean().default(false),
  isSaving: z.boolean().default(false),
  hasUnsavedChanges: z.boolean().default(false),
  showResetDialog: z.boolean().default(false),
  showImportDialog: z.boolean().default(false),
  showExportDialog: z.boolean().default(false),
  errors: z.record(z.string(), z.string()).default({}),
  lastSaved: z.string().optional(),
})

// API Response Schema
export const SettingsApiResponseSchema = z.object({
  data: z.any().optional(),
  error: z
    .object({
      message: z.string(),
      code: z.string(),
      details: z.any().optional(),
    })
    .optional(),
  success: z.boolean(),
})

// Form validation schemas for specific use cases
export const AccountFormSchema = z.object({
  first_name: z.string().min(1, 'First name is required').max(50, 'First name too long'),
  last_name: z.string().min(1, 'Last name is required').max(50, 'Last name too long'),
  email: z.string().email('Invalid email address'),
  two_factor_enabled: z.boolean().optional(),
})

export const AppearanceFormSchema = z.object({
  theme: z.enum(['light', 'dark', 'system']),
  language: z.string().min(2).max(5),
  timezone: z.string().min(1),
  date_format: z.string().min(1),
  time_format: z.enum(['12h', '24h']),
  dashboard_layout: z.enum(['default', 'compact', 'expanded']),
})

export const NotificationsFormSchema = z.object({
  notifications_enabled: z.boolean(),
  email_notifications: z.boolean(),
})

export const PrivacyFormSchema = z.object({
  analytics_enabled: z.boolean(),
  crash_reporting: z.boolean(),
})

export const AdvancedFormSchema = z.object({
  auto_save_enabled: z.boolean(),
  auto_save_interval: z
    .number()
    .min(
      VALIDATION_RULES.AUTO_SAVE_INTERVAL.min,
      `Minimum ${VALIDATION_RULES.AUTO_SAVE_INTERVAL.min} seconds`
    )
    .max(
      VALIDATION_RULES.AUTO_SAVE_INTERVAL.max,
      `Maximum ${VALIDATION_RULES.AUTO_SAVE_INTERVAL.max} seconds`
    ),
  debug_mode: z.boolean(),
  api_logging: z.boolean(),
})

export const EngineeringFormSchema = z.object({
  units_system: z.enum(['metric', 'imperial']),
  default_temperature_unit: z.enum(['°C', '°F', 'K']),
  default_currency: z.string().length(3, 'Currency code must be 3 characters'),
  calculation_precision: z
    .number()
    .min(
      VALIDATION_RULES.CALCULATION_PRECISION.min,
      `Minimum ${VALIDATION_RULES.CALCULATION_PRECISION.min} decimal places`
    )
    .max(
      VALIDATION_RULES.CALCULATION_PRECISION.max,
      `Maximum ${VALIDATION_RULES.CALCULATION_PRECISION.max} decimal places`
    ),
  safety_factor: z
    .number()
    .min(
      VALIDATION_RULES.SAFETY_FACTOR.min,
      `Minimum safety factor ${VALIDATION_RULES.SAFETY_FACTOR.min}`
    )
    .max(
      VALIDATION_RULES.SAFETY_FACTOR.max,
      `Maximum safety factor ${VALIDATION_RULES.SAFETY_FACTOR.max}`
    ),
  default_voltage: z.string().min(1, 'Voltage is required'),
  default_frequency: z.string().min(1, 'Frequency is required'),
  calculation_standards: z.array(z.string()).min(1, 'At least one standard must be selected'),
  component_rating_system: z.string().min(1, 'Rating system is required'),
})

// Password change schema
export const PasswordChangeSchema = z
  .object({
    current_password: z.string().min(1, 'Current password is required'),
    new_password: z
      .string()
      .min(
        VALIDATION_RULES.PASSWORD_MIN_LENGTH,
        `Password must be at least ${VALIDATION_RULES.PASSWORD_MIN_LENGTH} characters`
      )
      .regex(
        /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,
        'Password must contain at least one lowercase letter, one uppercase letter, and one number'
      ),
    confirm_password: z.string().min(1, 'Password confirmation is required'),
  })
  .refine((data) => data.new_password === data.confirm_password, {
    message: "Passwords don't match",
    path: ['confirm_password'],
  })

// Import file validation schema
export const ImportFileSchema = z
  .object({
    file: z.instanceof(File).refine((file) => {
      return file.type === 'application/json' || file.name.endsWith('.json')
    }, 'File must be a JSON file'),
  })
  .refine((data) => {
    return data.file.size <= 1024 * 1024 // 1MB limit
  }, 'File size must be less than 1MB')

// Search query schema
export const SearchQuerySchema = z.object({
  query: z.string().max(100, 'Search query too long'),
  category: z
    .enum(['account', 'appearance', 'notifications', 'privacy', 'advanced', 'engineering'])
    .optional(),
  includeDescriptions: z.boolean().default(true),
})

// Export options schema
export const ExportOptionsSchema = z.object({
  categories: z
    .array(z.enum(['account', 'appearance', 'notifications', 'privacy', 'advanced', 'engineering']))
    .optional(),
  includeMetadata: z.boolean().default(true),
  format: z.enum(['json']).default('json'),
})

// Type inference helpers
export type UserPreferences = z.infer<typeof UserPreferencesSchema>
export type UserPreferencesUpdate = z.infer<typeof UserPreferencesUpdateSchema>
export type EngineeringSettings = z.infer<typeof EngineeringSettingsSchema>
export type SettingsField = z.infer<typeof SettingsFieldSchema>
export type SettingsSection = z.infer<typeof SettingsSectionSchema>
export type SettingsCategory = z.infer<typeof SettingsCategorySchema>
export type SettingsExport = z.infer<typeof SettingsExportSchema>
export type SettingsImport = z.infer<typeof SettingsImportSchema>
export type SettingsValidationResult = z.infer<typeof SettingsValidationResultSchema>
export type SettingsUIState = z.infer<typeof SettingsUIStateSchema>
export type SettingsApiResponse = z.infer<typeof SettingsApiResponseSchema>
export type AccountForm = z.infer<typeof AccountFormSchema>
export type AppearanceForm = z.infer<typeof AppearanceFormSchema>
export type NotificationsForm = z.infer<typeof NotificationsFormSchema>
export type PrivacyForm = z.infer<typeof PrivacyFormSchema>
export type AdvancedForm = z.infer<typeof AdvancedFormSchema>
export type EngineeringForm = z.infer<typeof EngineeringFormSchema>
export type PasswordChange = z.infer<typeof PasswordChangeSchema>
export type ImportFile = z.infer<typeof ImportFileSchema>
export type SearchQuery = z.infer<typeof SearchQuerySchema>
export type ExportOptions = z.infer<typeof ExportOptionsSchema>

// Validation helper functions
export const validateUserPreferences = (data: unknown): UserPreferences => {
  return UserPreferencesSchema.parse(data)
}

export const validateUserPreferencesUpdate = (data: unknown): UserPreferencesUpdate => {
  return UserPreferencesUpdateSchema.parse(data)
}

export const validateSettingsExport = (data: unknown): SettingsExport => {
  return SettingsExportSchema.parse(data)
}

export const safeValidateSettingsExport = (
  data: unknown
): { success: true; data: SettingsExport } | { success: false; error: string } => {
  try {
    const result = SettingsExportSchema.parse(data)
    return { success: true, data: result }
  } catch (error) {
    return { success: false, error: error instanceof Error ? error.message : 'Validation failed' }
  }
}

export const validateSettingsImport = (data: unknown): SettingsImport => {
  return SettingsImportSchema.parse(data)
}

// Safe validation functions that return results instead of throwing
export const safeValidateUserPreferences = (
  data: unknown
): { success: true; data: UserPreferences } | { success: false; error: z.ZodError } => {
  const result = UserPreferencesSchema.safeParse(data)
  return result.success
    ? { success: true, data: result.data }
    : { success: false, error: result.error }
}

export const safeValidateUserPreferencesUpdate = (
  data: unknown
): { success: true; data: UserPreferencesUpdate } | { success: false; error: z.ZodError } => {
  const result = UserPreferencesUpdateSchema.safeParse(data)
  return result.success
    ? { success: true, data: result.data }
    : { success: false, error: result.error }
}
