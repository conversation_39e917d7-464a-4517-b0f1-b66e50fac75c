/**
 * TypeScript type definitions for the Settings module
 * Ultimate Electrical Designer - Settings & User Preferences
 */

// Base types for settings management
export interface BaseSettings {
  id?: number
  created_at?: string
  updated_at?: string
}

// User Preferences (matches backend schema)
export interface UserPreferences extends BaseSettings {
  user_id?: number
  theme: 'light' | 'dark' | 'system'
  language: string
  timezone: string
  date_format: string
  time_format: '12h' | '24h'
  units_system: 'metric' | 'imperial'
  notifications_enabled: boolean
  email_notifications: boolean
  auto_save_interval: number
  dashboard_layout: 'default' | 'compact' | 'expanded'
  calculation_precision: number
  auto_save_enabled: boolean
}

// Partial update type for preferences
export interface UserPreferencesUpdate {
  theme?: 'light' | 'dark' | 'system'
  language?: string
  timezone?: string
  date_format?: string
  time_format?: '12h' | '24h'
  units_system?: 'metric' | 'imperial'
  notifications_enabled?: boolean
  email_notifications?: boolean
  auto_save_interval?: number
  dashboard_layout?: 'default' | 'compact' | 'expanded'
  calculation_precision?: number
  auto_save_enabled?: boolean
}

// Engineering-specific settings
export interface EngineeringSettings {
  default_voltage: string
  default_frequency: string
  default_temperature_unit: '°C' | '°F' | 'K'
  default_currency: string
  safety_factor: number
  calculation_standards: string[]
  preferred_manufacturers: string[]
  component_rating_system: string
}

// Application configuration
export interface AppConfiguration {
  version: string
  environment: 'development' | 'testing' | 'production'
  features: {
    advanced_calculations: boolean
    bulk_operations: boolean
    export_functionality: boolean
    real_time_sync: boolean
  }
  limits: {
    max_components_per_project: number
    max_file_upload_size: number
    session_timeout: number
  }
}

// Settings categories for UI organization
export type SettingsCategory =
  | 'account'
  | 'appearance'
  | 'notifications'
  | 'privacy'
  | 'advanced'
  | 'engineering'

export interface SettingsCategoryConfig {
  id: SettingsCategory
  label: string
  description: string
  icon: string
  sections: SettingsSection[]
}

export interface SettingsSection {
  id: string
  label: string
  description?: string
  fields: SettingsField[]
}

export interface SettingsField {
  id: string
  label: string
  description?: string
  type: 'text' | 'number' | 'boolean' | 'select' | 'multiselect' | 'slider' | 'color'
  value: any
  options?: SettingsFieldOption[]
  validation?: SettingsFieldValidation
  disabled?: boolean
  hidden?: boolean
}

export interface SettingsFieldOption {
  value: any
  label: string
  description?: string
}

export interface SettingsFieldValidation {
  required?: boolean
  min?: number
  max?: number
  pattern?: string
  custom?: (value: any) => string | null
}

// Settings export/import format
export interface SettingsExport {
  preferences: UserPreferences
  exported_at: string
  version: string
  metadata?: {
    user_id?: number
    export_type: 'full' | 'partial'
    categories?: SettingsCategory[]
  }
}

export interface SettingsImport {
  preferences: Partial<UserPreferences>
  version?: string
  validate?: boolean
  merge?: boolean
}

// Settings validation results
export interface SettingsValidationResult {
  isValid: boolean
  errors: SettingsValidationError[]
  warnings: SettingsValidationWarning[]
}

export interface SettingsValidationError {
  field: string
  message: string
  code: string
}

export interface SettingsValidationWarning {
  field: string
  message: string
  code: string
}

// UI state types
export interface SettingsUIState {
  activeCategory: SettingsCategory
  searchQuery: string
  isLoading: boolean
  isSaving: boolean
  hasUnsavedChanges: boolean
  showResetDialog: boolean
  showImportDialog: boolean
  showExportDialog: boolean
  errors: Record<string, string>
  lastSaved?: string
}

// Settings store state
export interface SettingsStoreState {
  // UI state
  ui: SettingsUIState

  // Temporary form state
  formData: Partial<UserPreferences>

  // Cross-tab sync
  syncEnabled: boolean
  lastSyncTime?: string

  // Actions
  setActiveCategory: (category: SettingsCategory) => void
  setSearchQuery: (query: string) => void
  updateFormData: (data: Partial<UserPreferences>) => void
  resetFormData: () => void
  setLoading: (loading: boolean) => void
  setSaving: (saving: boolean) => void
  setError: (field: string, error: string) => void
  clearErrors: () => void
  setHasUnsavedChanges: (hasChanges: boolean) => void
  showDialog: (dialog: 'reset' | 'import' | 'export', show: boolean) => void
}

// API response types
export interface SettingsApiResponse<T = any> {
  data?: T
  error?: {
    message: string
    code: string
    details?: any
  }
  success: boolean
}

// React Query keys
export const SettingsQueryKeys = {
  all: ['settings'] as const,
  preferences: () => [...SettingsQueryKeys.all, 'preferences'] as const,
  export: () => [...SettingsQueryKeys.all, 'export'] as const,
} as const

// Mutation keys
export const SettingsMutationKeys = {
  updatePreferences: 'updatePreferences',
  resetPreferences: 'resetPreferences',
  importPreferences: 'importPreferences',
} as const

// Constants
export const SETTINGS_STORAGE_KEY = 'ued-settings-store'
export const SETTINGS_SYNC_CHANNEL = 'ued-settings-sync'
export const SETTINGS_VERSION = '1.0'

// Default values
export const DEFAULT_USER_PREFERENCES: UserPreferences = {
  theme: 'system',
  language: 'en',
  timezone: 'UTC',
  date_format: 'YYYY-MM-DD',
  time_format: '24h',
  units_system: 'metric',
  notifications_enabled: true,
  email_notifications: true,
  auto_save_interval: 300,
  dashboard_layout: 'default',
  calculation_precision: 2,
  auto_save_enabled: true,
}

export const DEFAULT_ENGINEERING_SETTINGS: EngineeringSettings = {
  default_voltage: '230V',
  default_frequency: '50Hz',
  default_temperature_unit: '°C',
  default_currency: 'EUR',
  safety_factor: 1.2,
  calculation_standards: ['IEC', 'EN'],
  preferred_manufacturers: [],
  component_rating_system: 'IP66',
}

// Theme configuration
export interface ThemeConfig {
  name: string
  label: string
  colors: {
    primary: string
    secondary: string
    background: string
    foreground: string
    muted: string
    accent: string
    destructive: string
    border: string
    input: string
    ring: string
  }
}

// Language configuration
export interface LanguageConfig {
  code: string
  name: string
  nativeName: string
  flag: string
  rtl?: boolean
}

// Timezone configuration
export interface TimezoneConfig {
  value: string
  label: string
  offset: string
  region: string
}

// Units configuration
export interface UnitsConfig {
  system: 'metric' | 'imperial'
  temperature: '°C' | '°F'
  length: 'mm' | 'in'
  weight: 'kg' | 'lb'
  pressure: 'bar' | 'psi'
  power: 'kW' | 'hp'
}
