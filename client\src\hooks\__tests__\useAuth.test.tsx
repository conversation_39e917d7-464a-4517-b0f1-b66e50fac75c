import * as apiHooks from '@/hooks/api/useAuth'
import { useAuthStore } from '@/stores/authStore'
import { mockAdminUser, mockLoginResponse, mockUser } from '@/test/utils'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { act, renderHook } from '@testing-library/react'
import { beforeEach, describe, expect, it } from 'vitest'
import { useAuth } from '../useAuth'

// Mock the token manager
vi.mock('@/lib/auth/tokenManager', () => ({
  TokenManager: {
    setAccessToken: vi.fn(),
    clearTokens: vi.fn(),
    initializeTokens: vi.fn(),
  },
}))

// Mock API hooks
vi.mock('@/hooks/api/useAuth', () => ({
  useLogin: vi.fn(),
  useLogout: vi.fn(),
  useRegister: vi.fn(),
  useCurrentUser: vi.fn(),
}))

const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  })

  return ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
  )
}

describe('useAuth Hook', () => {
  beforeEach(() => {
    // Reset auth store
    useAuthStore.getState().clearAuth()
    vi.clearAllMocks()

    // Default mock for useCurrentUser to represent a logged-out state
    vi.mocked(apiHooks.useCurrentUser).mockReturnValue({
      data: null,
      isLoading: false,
      error: null,
    } as any)

    // Default mocks for mutations
    vi.mocked(apiHooks.useLogin).mockReturnValue({
      mutateAsync: vi.fn(),
      isPending: false,
      error: null,
    } as any)
    vi.mocked(apiHooks.useLogout).mockReturnValue({
      mutateAsync: vi.fn(),
      isPending: false,
      error: null,
    } as any)
    vi.mocked(apiHooks.useRegister).mockReturnValue({
      mutateAsync: vi.fn(),
      isPending: false,
      error: null,
    } as any)
  })

  it('initializes with default state', () => {
    const { result } = renderHook(() => useAuth(), {
      wrapper: createWrapper(),
    })

    expect(result.current.user).toBeNull()
    expect(result.current.isAuthenticated).toBe(false)
    expect(result.current.isLoading).toBe(false)
  })

  it('handles successful login', async () => {
    const loginMock = vi.fn().mockResolvedValue(mockLoginResponse)
    vi.mocked(apiHooks.useLogin).mockReturnValue({
      mutateAsync: loginMock,
    } as any)

    const { result } = renderHook(() => useAuth(), { wrapper: createWrapper() })

    await act(async () => {
      await result.current.login({
        username: '<EMAIL>',
        password: 'password123',
      })
    })

    expect(loginMock).toHaveBeenCalledWith({
      username: '<EMAIL>',
      password: 'password123',
    })
    expect(result.current.isAuthenticated).toBe(true)
    expect(result.current.user).toEqual(mockUser)
  })

  it('handles login failure', async () => {
    const error = new Error('Invalid credentials')
    const loginMock = vi.fn().mockRejectedValue(error)
    vi.mocked(apiHooks.useLogin).mockReturnValue({
      mutateAsync: loginMock,
    } as any)

    const { result } = renderHook(() => useAuth(), { wrapper: createWrapper() })

    await act(async () => {
      await expect(
        result.current.login({
          username: '<EMAIL>',
          password: 'wrongpassword',
        })
      ).rejects.toThrow(error)
    })

    expect(result.current.isAuthenticated).toBe(false)
    expect(result.current.user).toBeNull()
  })

  it('handles logout', async () => {
    const logoutMock = vi.fn().mockResolvedValue({})
    vi.mocked(apiHooks.useLogout).mockReturnValue({
      mutateAsync: logoutMock,
    } as any)

    // Set initial authenticated state
    act(() => {
      useAuthStore.getState().setAuth(mockUser, 'mock-token')
    })

    const { result } = renderHook(() => useAuth(), { wrapper: createWrapper() })

    expect(result.current.isAuthenticated).toBe(true)

    await act(async () => {
      await result.current.logout()
    })

    expect(logoutMock).toHaveBeenCalled()
    expect(result.current.isAuthenticated).toBe(false)
    expect(result.current.user).toBeNull()
  })

  it('checks user roles correctly', () => {
    vi.mocked(apiHooks.useCurrentUser).mockReturnValue({
      data: mockUser,
      isLoading: false,
      error: null,
    } as any)

    act(() => {
      useAuthStore.getState().setAuth(mockUser, 'mock-token')
    })

    const { result } = renderHook(() => useAuth(), {
      wrapper: createWrapper(),
    })

    expect(result.current.hasRole('VIEWER')).toBe(true)
    expect(result.current.hasRole('ADMIN')).toBe(false)
    expect(result.current.isAdmin()).toBe(false)
  })

  it('checks admin role correctly', () => {
    vi.mocked(apiHooks.useCurrentUser).mockReturnValue({
      data: mockAdminUser,
      isLoading: false,
      error: null,
    } as any)

    act(() => {
      useAuthStore.getState().setAuth(mockAdminUser, 'mock-admin-token')
    })

    const { result } = renderHook(() => useAuth(), { wrapper: createWrapper() })

    expect(result.current.user).toEqual(mockAdminUser)
    expect(result.current.isAdmin()).toBe(true)
    expect(result.current.hasRole('ADMIN')).toBe(true)
    expect(result.current.hasRole('VIEWER')).toBe(false)
  })
})
