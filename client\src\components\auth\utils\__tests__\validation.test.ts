/**
 * Validation Utilities Tests
 * Comprehensive tests for authentication form validation
 */

import { describe, it, expect } from 'vitest'
import {
  loginSchema,
  registerSchema,
  passwordChangeSchema,
  validateForm,
  calculatePasswordStrength,
  sanitizeInput,
  isValidEmail,
  isValidUsername,
} from '../validation'

describe('Authentication Validation', () => {
  describe('loginSchema', () => {
    it('should validate correct login data', () => {
      const validData = {
        username: '<EMAIL>',
        password: 'TestPassword123!',
      }

      const result = validateForm(loginSchema, validData)
      expect(result.success).toBe(true)
      expect(result.errors).toEqual({})
    })

    it('should reject empty username', () => {
      const invalidData = {
        username: '',
        password: 'TestPassword123!',
      }

      const result = validateForm(loginSchema, invalidData)
      expect(result.success).toBe(false)
      expect(result.errors.username).toBe('Username or email is required')
    })

    it('should reject short password', () => {
      const invalidData = {
        username: '<EMAIL>',
        password: '123',
      }

      const result = validateForm(loginSchema, invalidData)
      expect(result.success).toBe(false)
      expect(result.errors.password).toBe('Password must be at least 8 characters long')
    })
  })

  describe('registerSchema', () => {
    it('should validate correct registration data', () => {
      const validData = {
        name: 'testuser',
        email: '<EMAIL>',
        password: 'TestPassword123!',
        confirmPassword: 'TestPassword123!',
      }

      const result = validateForm(registerSchema, validData)
      expect(result.success).toBe(true)
      expect(result.errors).toEqual({})
    })

    it('should reject invalid username', () => {
      const invalidData = {
        name: 'ab', // Too short
        email: '<EMAIL>',
        password: 'TestPassword123!',
        confirmPassword: 'TestPassword123!',
      }

      const result = validateForm(registerSchema, invalidData)
      expect(result.success).toBe(false)
      expect(result.errors.name).toBe('Username must be at least 3 characters long')
    })

    it('should reject invalid email', () => {
      const invalidData = {
        name: 'testuser',
        email: 'invalid-email',
        password: 'TestPassword123!',
        confirmPassword: 'TestPassword123!',
      }

      const result = validateForm(registerSchema, invalidData)
      expect(result.success).toBe(false)
      expect(result.errors.email).toBe('Please enter a valid email address')
    })

    it('should reject weak password', () => {
      const invalidData = {
        name: 'testuser',
        email: '<EMAIL>',
        password: 'password', // No uppercase, numbers, or special chars
        confirmPassword: 'password',
      }

      const result = validateForm(registerSchema, invalidData)
      expect(result.success).toBe(false)
      expect(result.errors.password).toContain('uppercase')
    })

    it('should reject mismatched passwords', () => {
      const invalidData = {
        name: 'testuser',
        email: '<EMAIL>',
        password: 'TestPassword123!',
        confirmPassword: 'DifferentPassword123!',
      }

      const result = validateForm(registerSchema, invalidData)
      expect(result.success).toBe(false)
      expect(result.errors.confirmPassword).toBe('Passwords do not match')
    })
  })

  describe('passwordChangeSchema', () => {
    it('should validate correct password change data', () => {
      const validData = {
        currentPassword: 'OldPassword123!',
        newPassword: 'NewPassword123!',
        confirmPassword: 'NewPassword123!',
      }

      const result = validateForm(passwordChangeSchema, validData)
      expect(result.success).toBe(true)
      expect(result.errors).toEqual({})
    })

    it('should reject same current and new password', () => {
      const invalidData = {
        currentPassword: 'SamePassword123!',
        newPassword: 'SamePassword123!',
        confirmPassword: 'SamePassword123!',
      }

      const result = validateForm(passwordChangeSchema, invalidData)
      expect(result.success).toBe(false)
      expect(result.errors.newPassword).toBe('New password must be different from current password')
    })
  })

  describe('calculatePasswordStrength', () => {
    it('should return very weak for empty password', () => {
      const strength = calculatePasswordStrength('')
      expect(strength.score).toBe(0)
      expect(strength.label).toBe('Very Weak')
      expect(strength.color).toBe('red')
    })

    it('should return weak for password with only length', () => {
      const strength = calculatePasswordStrength('password')
      expect(strength.score).toBe(2) // length + lowercase
      expect(strength.label).toBe('Weak')
      expect(strength.color).toBe('orange')
    })

    it('should return strong for password meeting all requirements', () => {
      const strength = calculatePasswordStrength('TestPassword123!')
      expect(strength.score).toBe(5)
      expect(strength.label).toBe('Strong')
      expect(strength.color).toBe('green')
      expect(strength.requirements.length).toBe(true)
      expect(strength.requirements.uppercase).toBe(true)
      expect(strength.requirements.lowercase).toBe(true)
      expect(strength.requirements.digit).toBe(true)
      expect(strength.requirements.special).toBe(true)
    })

    it('should return fair for password with some requirements', () => {
      const strength = calculatePasswordStrength('TestPassword')
      expect(strength.score).toBe(3) // length + uppercase + lowercase
      expect(strength.label).toBe('Fair')
      expect(strength.color).toBe('yellow')
    })
  })

  describe('sanitizeInput', () => {
    it('should remove dangerous characters', () => {
      const input = '<script>alert("xss")</script>'
      const sanitized = sanitizeInput(input)
      expect(sanitized).toBe('scriptalert("xss")/script')
    })

    it('should remove javascript protocol', () => {
      const input = 'javascript:alert("xss")'
      const sanitized = sanitizeInput(input)
      expect(sanitized).toBe('alert("xss")')
    })

    it('should remove event handlers', () => {
      const input = 'onclick=alert("xss")'
      const sanitized = sanitizeInput(input)
      expect(sanitized).toBe('alert("xss")')
    })

    it('should trim whitespace', () => {
      const input = '  test  '
      const sanitized = sanitizeInput(input)
      expect(sanitized).toBe('test')
    })
  })

  describe('isValidEmail', () => {
    it('should validate correct email addresses', () => {
      expect(isValidEmail('<EMAIL>')).toBe(true)
      expect(isValidEmail('<EMAIL>')).toBe(true)
      expect(isValidEmail('<EMAIL>')).toBe(true)
    })

    it('should reject invalid email addresses', () => {
      expect(isValidEmail('invalid')).toBe(false)
      expect(isValidEmail('test@')).toBe(false)
      expect(isValidEmail('@example.com')).toBe(false)
      expect(isValidEmail('<EMAIL>')).toBe(false)
    })

    it('should reject emails that are too long', () => {
      const longEmail = 'a'.repeat(250) + '@example.com'
      expect(isValidEmail(longEmail)).toBe(false)
    })
  })

  describe('isValidUsername', () => {
    it('should validate correct usernames', () => {
      expect(isValidUsername('testuser')).toBe(true)
      expect(isValidUsername('test_user')).toBe(true)
      expect(isValidUsername('test-user')).toBe(true)
      expect(isValidUsername('user123')).toBe(true)
    })

    it('should reject invalid usernames', () => {
      expect(isValidUsername('ab')).toBe(false) // Too short
      expect(isValidUsername('test user')).toBe(false) // Contains space
      expect(isValidUsername('test@user')).toBe(false) // Contains @
      expect(isValidUsername('test.user')).toBe(false) // Contains dot
    })

    it('should reject usernames that are too long', () => {
      const longUsername = 'a'.repeat(51)
      expect(isValidUsername(longUsername)).toBe(false)
    })
  })
})
