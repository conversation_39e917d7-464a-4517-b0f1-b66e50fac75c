'use client'

import { Button } from '@/components/ui/button'
import { cn } from '@/lib/utils'
import { memo } from 'react'
import type { RecentCalculationsWidgetProps } from '../types'
import { formatCalculationType, getCalculationTypeColor, formatRelativeTime } from '../utils'

/**
 * Dashboard recent calculations widget component
 */
export const RecentCalculationsWidget = memo(function RecentCalculationsWidget({
  calculations,
  isLoading = false,
  onCalculationClick,
  onViewAll,
  className,
}: RecentCalculationsWidgetProps) {
  if (isLoading) {
    return (
      <div className={cn('rounded-lg border border-gray-200 bg-white shadow-sm', className)}>
        <div className="border-b border-gray-200 px-6 py-4">
          <div className="h-6 w-40 animate-pulse rounded bg-gray-200"></div>
        </div>
        <div className="p-6">
          <div className="space-y-4">
            {Array.from({ length: 3 }).map((_, index) => (
              <div key={index} className="animate-pulse">
                <div className="flex items-center space-x-3">
                  <div className="h-8 w-8 rounded-lg bg-gray-200"></div>
                  <div className="flex-1">
                    <div className="mb-2 h-4 w-3/4 rounded bg-gray-200"></div>
                    <div className="h-3 w-1/2 rounded bg-gray-200"></div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  const getCalculationIcon = (type: string) => {
    switch (type) {
      case 'heat_tracing':
        return (
          <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M13 10V3L4 14h7v7l9-11h-7z"
            />
          </svg>
        )
      case 'load_calculation':
        return (
          <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z"
            />
          </svg>
        )
      case 'cable_sizing':
        return (
          <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"
            />
          </svg>
        )
      default:
        return (
          <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
            />
          </svg>
        )
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return (
          <svg
            className="h-4 w-4 text-green-500"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
          </svg>
        )
      case 'in_progress':
        return (
          <svg
            className="h-4 w-4 text-yellow-500"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
            />
          </svg>
        )
      case 'failed':
        return (
          <svg
            className="h-4 w-4 text-red-500"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M6 18L18 6M6 6l12 12"
            />
          </svg>
        )
      default:
        return null
    }
  }

  return (
    <div className={cn('rounded-lg border border-gray-200 bg-white shadow-sm', className)}>
      {/* Header */}
      <div className="border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-medium text-gray-900">Recent Calculations</h3>
          {onViewAll && calculations.length > 0 && (
            <Button
              onClick={onViewAll}
              variant="ghost"
              size="sm"
              className="text-brand-primary hover:bg-brand-primary/10 hover:text-brand-primary/90"
            >
              View All
            </Button>
          )}
        </div>
      </div>

      {/* Content */}
      <div className="p-6">
        {calculations.length === 0 ? (
          <div className="py-8 text-center">
            <svg
              className="mx-auto h-12 w-12 text-gray-400"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z"
              />
            </svg>
            <h3 className="mt-2 text-sm font-medium text-gray-900">No calculations yet</h3>
            <p className="mt-1 text-sm text-gray-500">
              Start by creating a new calculation in one of your projects.
            </p>
          </div>
        ) : (
          <div className="space-y-4">
            {calculations.slice(0, 5).map((calculation) => (
              <div
                key={calculation.id}
                className={cn(
                  'flex items-center space-x-3 rounded-lg border border-gray-200 p-3 transition-colors duration-200 hover:border-gray-300',
                  onCalculationClick && 'cursor-pointer hover:bg-gray-50'
                )}
                onClick={() => onCalculationClick?.(calculation)}
              >
                <div
                  className={cn(
                    'flex-shrink-0 rounded-lg p-2',
                    getCalculationTypeColor(calculation.type)
                  )}
                >
                  {getCalculationIcon(calculation.type)}
                </div>
                <div className="min-w-0 flex-1">
                  <div className="flex items-center space-x-2">
                    <p className="truncate text-sm font-medium text-gray-900">{calculation.name}</p>
                    {getStatusIcon(calculation.status)}
                    <span
                      className={cn(
                        'inline-flex items-center rounded-full px-2 py-1 text-xs font-medium',
                        getCalculationTypeColor(calculation.type)
                      )}
                    >
                      {formatCalculationType(calculation.type)}
                    </span>
                  </div>
                  <div className="mt-1 flex items-center justify-between">
                    <p className="truncate text-xs text-gray-500">{calculation.projectName}</p>
                    <p className="text-xs text-gray-400">
                      {formatRelativeTime(calculation.createdAt)}
                    </p>
                  </div>
                  {calculation.result && (
                    <p className="mt-1 text-xs font-medium text-gray-600">
                      Result: {calculation.result}
                    </p>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  )
})

RecentCalculationsWidget.displayName = 'RecentCalculationsWidget'
