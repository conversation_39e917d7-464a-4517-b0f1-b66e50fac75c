/**
 * Configuration Manager Component
 * Ultimate Electrical Designer - Settings & User Preferences
 */

'use client'

import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Progress } from '@/components/ui/progress'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Separator } from '@/components/ui/separator'
import {
  AlertTriangle,
  CheckCircle,
  Clock,
  Download,
  Info,
  RefreshCcw,
  RefreshCw,
  RotateCcw,
  Settings,
  Shield,
  Upload,
} from 'lucide-react'
import { useState } from 'react'
import { useSettings } from '../hooks/useSettings'
import { useSettingsExport } from '../hooks/useSettingsExport'
import { useSettingsSync } from '../hooks/useSettingsSync'
import { formatPreferenceValue } from '../utils'

interface ConfigurationManagerProps {
  className?: string
  showAdvanced?: boolean
}

/**
 * Configuration Manager Component
 */
export function ConfigurationManager({
  className = '',
  showAdvanced = false,
}: ConfigurationManagerProps) {
  const settings = useSettings()
  const sync = useSettingsSync()
  const exportHook = useSettingsExport()

  const [activeTab, setActiveTab] = useState<'overview' | 'sync' | 'backup' | 'advanced'>(
    'overview'
  )

  const { preferences, ui, hasChanges, changedFields, actions } = settings

  // Get settings summary
  const getSettingsSummary = () => {
    if (!preferences) return []

    return [
      { label: 'Theme', value: formatPreferenceValue('theme', preferences.theme) },
      { label: 'Language', value: formatPreferenceValue('language', preferences.language) },
      {
        label: 'Units System',
        value: formatPreferenceValue('units_system', preferences.units_system),
      },
      {
        label: 'Auto Save',
        value: formatPreferenceValue('auto_save_enabled', preferences.auto_save_enabled),
      },
      {
        label: 'Notifications',
        value: formatPreferenceValue('notifications_enabled', preferences.notifications_enabled),
      },
    ]
  }

  const settingsSummary = getSettingsSummary()

  return (
    <div className={`configuration-manager ${className}`}>
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Configuration Manager
          </CardTitle>
          <CardDescription>Manage your settings, synchronization, and data backup</CardDescription>
        </CardHeader>
        <CardContent>
          {/* Tab Navigation */}
          <div className="mb-6 flex space-x-1 rounded-lg bg-muted p-1">
            {[
              { id: 'overview', label: 'Overview', icon: Info },
              { id: 'sync', label: 'Sync', icon: RefreshCw },
              { id: 'backup', label: 'Backup', icon: Shield },
              ...(showAdvanced ? [{ id: 'advanced', label: 'Advanced', icon: Settings }] : []),
            ].map((tab) => (
              <Button
                key={tab.id}
                variant={activeTab === tab.id ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setActiveTab(tab.id as any)}
                className="flex items-center gap-2"
              >
                <tab.icon className="h-4 w-4" />
                {tab.label}
              </Button>
            ))}
          </div>

          {/* Overview Tab */}
          {activeTab === 'overview' && (
            <div className="space-y-6">
              {/* Current Settings Summary */}
              <div>
                <h3 className="mb-3 text-lg font-medium">Current Settings</h3>
                <div className="grid gap-3">
                  {settingsSummary.map((setting, index) => (
                    <div key={index} className="flex items-center justify-between py-2">
                      <span className="text-sm font-medium">{setting.label}</span>
                      <Badge variant="secondary">{setting.value}</Badge>
                    </div>
                  ))}
                </div>
              </div>

              <Separator />

              {/* Status */}
              <div>
                <h3 className="mb-3 text-lg font-medium">Status</h3>
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Unsaved Changes</span>
                    <div className="flex items-center gap-2">
                      {hasChanges ? (
                        <>
                          <AlertTriangle className="h-4 w-4 text-amber-500" />
                          <Badge variant="outline">{changedFields.length} changes</Badge>
                        </>
                      ) : (
                        <>
                          <CheckCircle className="h-4 w-4 text-green-500" />
                          <Badge variant="outline">All saved</Badge>
                        </>
                      )}
                    </div>
                  </div>

                  <div className="flex items-center justify-between">
                    <span className="text-sm">Sync Status</span>
                    <div className="flex items-center gap-2">
                      {sync.isEnabled ? (
                        <>
                          <RefreshCw className="h-4 w-4 text-green-500" />
                          <Badge variant="outline">Enabled</Badge>
                        </>
                      ) : (
                        <>
                          <RefreshCcw className="h-4 w-4 text-gray-500" />
                          <Badge variant="outline">Disabled</Badge>
                        </>
                      )}
                    </div>
                  </div>

                  {sync.lastSyncTime && (
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Last Sync</span>
                      <div className="flex items-center gap-2">
                        <Clock className="h-4 w-4 text-muted-foreground" />
                        <span className="text-xs text-muted-foreground">
                          {new Date(sync.lastSyncTime).toLocaleString()}
                        </span>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              <Separator />

              {/* Quick Actions */}
              <div>
                <h3 className="mb-3 text-lg font-medium">Quick Actions</h3>
                <div className="grid grid-cols-2 gap-3">
                  <Button
                    variant="outline"
                    onClick={actions.savePreferences}
                    disabled={!hasChanges || ui.isSaving}
                  >
                    Save Changes
                  </Button>

                  <Button variant="outline" onClick={actions.openExportDialog}>
                    <Download className="mr-2 h-4 w-4" />
                    Export
                  </Button>

                  <Button variant="outline" onClick={actions.openImportDialog}>
                    <Upload className="mr-2 h-4 w-4" />
                    Import
                  </Button>

                  <Button variant="outline" onClick={actions.openResetDialog}>
                    <RotateCcw className="mr-2 h-4 w-4" />
                    Reset
                  </Button>
                </div>
              </div>
            </div>
          )}

          {/* Sync Tab */}
          {activeTab === 'sync' && (
            <div className="space-y-6">
              <div>
                <h3 className="mb-3 text-lg font-medium">Cross-Tab Synchronization</h3>
                <p className="mb-4 text-sm text-muted-foreground">
                  Keep your settings synchronized across multiple browser tabs and windows.
                </p>

                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <span className="font-medium">Enable Sync</span>
                      <p className="text-xs text-muted-foreground">
                        Automatically sync changes across tabs
                      </p>
                    </div>
                    <Button
                      variant={sync.isEnabled ? 'default' : 'outline'}
                      size="sm"
                      onClick={sync.isEnabled ? sync.disableSync : sync.enableSync}
                    >
                      {sync.isEnabled ? (
                        <>
                          <RefreshCw className="mr-2 h-4 w-4" />
                          Enabled
                        </>
                      ) : (
                        <>
                          <RefreshCcw className="mr-2 h-4 w-4" />
                          Disabled
                        </>
                      )}
                    </Button>
                  </div>

                  {sync.isEnabled && (
                    <Alert>
                      <Info className="h-4 w-4" />
                      <AlertDescription>
                        Sync is active. Changes made in this tab will be automatically synchronized
                        to other open tabs.
                      </AlertDescription>
                    </Alert>
                  )}

                  <div className="rounded-lg bg-muted p-3">
                    <h4 className="mb-2 font-medium">Sync Information</h4>
                    <div className="space-y-1 text-sm">
                      <div className="flex justify-between">
                        <span>Status:</span>
                        <span>{sync.isConnected ? 'Connected' : 'Disconnected'}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Tab ID:</span>
                        <span className="font-mono text-xs">{sync.tabId}</span>
                      </div>
                      {sync.lastSyncTime && (
                        <div className="flex justify-between">
                          <span>Last Sync:</span>
                          <span>{new Date(sync.lastSyncTime).toLocaleTimeString()}</span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Backup Tab */}
          {activeTab === 'backup' && (
            <div className="space-y-6">
              <div>
                <h3 className="mb-3 text-lg font-medium">Backup & Restore</h3>
                <p className="mb-4 text-sm text-muted-foreground">
                  Export your settings for backup or import settings from a backup file.
                </p>

                <div className="space-y-4">
                  {/* Export Section */}
                  <div className="rounded-lg border p-4">
                    <h4 className="mb-2 font-medium">Export Settings</h4>
                    <p className="mb-3 text-sm text-muted-foreground">
                      Create a backup file of your current settings.
                    </p>

                    {exportHook.isExporting && (
                      <div className="mb-3">
                        <Progress value={exportHook.exportProgress} className="w-full" />
                        <p className="mt-1 text-xs text-muted-foreground">
                          Exporting... {exportHook.exportProgress}%
                        </p>
                      </div>
                    )}

                    <div className="flex gap-2">
                      <Button
                        onClick={() => exportHook.exportSettings()}
                        disabled={exportHook.isExporting}
                      >
                        <Download className="mr-2 h-4 w-4" />
                        Export to File
                      </Button>

                      {exportHook.lastExport && (
                        <Button
                          variant="outline"
                          onClick={() => exportHook.exportToClipboard(exportHook.lastExport!)}
                        >
                          Copy to Clipboard
                        </Button>
                      )}
                    </div>
                  </div>

                  {/* Import Section */}
                  <div className="rounded-lg border p-4">
                    <h4 className="mb-2 font-medium">Import Settings</h4>
                    <p className="mb-3 text-sm text-muted-foreground">
                      Restore settings from a backup file.
                    </p>

                    {exportHook.isImporting && (
                      <div className="mb-3">
                        <Progress value={exportHook.importProgress} className="w-full" />
                        <p className="mt-1 text-xs text-muted-foreground">
                          Importing... {exportHook.importProgress}%
                        </p>
                      </div>
                    )}

                    <div className="flex gap-2">
                      <Button
                        variant="outline"
                        onClick={actions.openImportDialog}
                        disabled={exportHook.isImporting}
                      >
                        <Upload className="mr-2 h-4 w-4" />
                        Import from File
                      </Button>

                      <Button
                        variant="outline"
                        onClick={() => exportHook.importFromClipboard()}
                        disabled={exportHook.isImporting}
                      >
                        Import from Clipboard
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Advanced Tab */}
          {activeTab === 'advanced' && showAdvanced && (
            <div className="space-y-6">
              <div>
                <h3 className="mb-3 text-lg font-medium">Advanced Configuration</h3>

                <Alert variant="destructive" className="mb-4">
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription>
                    Advanced settings are for experienced users only. Incorrect values may affect
                    application performance.
                  </AlertDescription>
                </Alert>

                <div className="space-y-4">
                  <div className="rounded-lg border p-4">
                    <h4 className="mb-2 font-medium">Debug Information</h4>
                    <ScrollArea className="h-32 w-full">
                      <pre className="text-xs text-muted-foreground">
                        {JSON.stringify(
                          {
                            preferences: preferences ? Object.keys(preferences).length : 0,
                            formData: Object.keys(settings.formData).length,
                            hasChanges,
                            changedFields,
                            syncEnabled: sync.isEnabled,
                            syncConnected: sync.isConnected,
                          },
                          null,
                          2
                        )}
                      </pre>
                    </ScrollArea>
                  </div>

                  <div className="rounded-lg border p-4">
                    <h4 className="mb-2 font-medium">Cache Management</h4>
                    <p className="mb-3 text-sm text-muted-foreground">
                      Clear cached settings data and force a refresh.
                    </p>
                    <Button
                      variant="outline"
                      onClick={() => {
                        // Clear query cache and reload
                        window.location.reload()
                      }}
                    >
                      Clear Cache & Reload
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}

export default ConfigurationManager
