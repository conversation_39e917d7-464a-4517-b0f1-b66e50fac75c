/**
 * Unit tests for admin dashboard utilities
 */

import {
  formatBytes,
  formatUptime,
  calculatePercentage,
  getStatusColor,
  formatUserRole,
  getRoleColor,
  formatProjectStatus,
  getProjectStatusColor,
  getProjectPriorityColor,
  formatAuditAction,
  getAuditActionColor,
  getSecurityAlertColor,
  formatSecurityAlertType,
  getConfigCategoryColor,
  validateConfigValue,
  formatRelativeTime,
  generateHealthSummary,
  filterAndSortData,
} from '../utils'

describe('Admin Dashboard Utils', () => {
  describe('formatBytes', () => {
    it('should format bytes correctly', () => {
      expect(formatBytes(0)).toBe('0 Bytes')
      expect(formatBytes(1024)).toBe('1 KB')
      expect(formatBytes(1048576)).toBe('1 MB')
      expect(formatBytes(**********)).toBe('1 GB')
      expect(formatBytes(1536)).toBe('1.5 KB')
    })

    it('should handle decimal places', () => {
      expect(formatBytes(1536, 0)).toBe('2 KB')
      expect(formatBytes(1536, 1)).toBe('1.5 KB')
      expect(formatBytes(1536, 3)).toBe('1.500 KB')
    })
  })

  describe('formatUptime', () => {
    it('should format uptime strings correctly', () => {
      expect(formatUptime('5 days, 3 hours, 45 minutes')).toBe('5 days, 3 hours, 45 minutes')
      expect(formatUptime('1 day, 1 hour, 1 minute')).toBe('1 day, 1 hour, 1 minute')
      expect(formatUptime('2 days')).toBe('2 days')
    })

    it('should handle invalid uptime strings', () => {
      expect(formatUptime('invalid')).toBe('invalid')
      expect(formatUptime('')).toBe('')
    })
  })

  describe('calculatePercentage', () => {
    it('should calculate percentages correctly', () => {
      expect(calculatePercentage(50, 100)).toBe(50)
      expect(calculatePercentage(1, 3)).toBe(33)
      expect(calculatePercentage(2, 3)).toBe(67)
    })

    it('should handle edge cases', () => {
      expect(calculatePercentage(0, 100)).toBe(0)
      expect(calculatePercentage(100, 100)).toBe(100)
      expect(calculatePercentage(50, 0)).toBe(0)
    })
  })

  describe('getStatusColor', () => {
    const thresholds = { warning: 70, critical: 90 }

    it('should return correct colors for normal metrics', () => {
      expect(getStatusColor(95, thresholds)).toBe('success')
      expect(getStatusColor(75, thresholds)).toBe('warning')
      expect(getStatusColor(65, thresholds)).toBe('danger')
    })

    it('should return correct colors for inverted metrics', () => {
      expect(getStatusColor(5, thresholds, true)).toBe('success')
      expect(getStatusColor(75, thresholds, true)).toBe('warning')
      expect(getStatusColor(95, thresholds, true)).toBe('danger')
    })
  })

  describe('formatUserRole', () => {
    it('should format user roles correctly', () => {
      expect(formatUserRole('ADMIN')).toBe('Administrator')
      expect(formatUserRole('EDITOR')).toBe('Editor')
      expect(formatUserRole('VIEWER')).toBe('Viewer')
    })

    it('should handle unknown roles', () => {
      expect(formatUserRole('UNKNOWN' as any)).toBe('UNKNOWN')
    })
  })

  describe('getRoleColor', () => {
    it('should return correct role colors', () => {
      expect(getRoleColor('ADMIN')).toBe('text-red-600 bg-red-100')
      expect(getRoleColor('EDITOR')).toBe('text-yellow-600 bg-yellow-100')
      expect(getRoleColor('VIEWER')).toBe('text-green-600 bg-green-100')
    })

    it('should handle unknown roles', () => {
      expect(getRoleColor('UNKNOWN' as any)).toBe('text-gray-600 bg-gray-100')
    })
  })

  describe('formatProjectStatus', () => {
    it('should format project statuses correctly', () => {
      expect(formatProjectStatus('active')).toBe('Active')
      expect(formatProjectStatus('completed')).toBe('Completed')
      expect(formatProjectStatus('on_hold')).toBe('On Hold')
      expect(formatProjectStatus('draft')).toBe('Draft')
    })
  })

  describe('getProjectStatusColor', () => {
    it('should return correct project status colors', () => {
      expect(getProjectStatusColor('active')).toBe('text-green-600 bg-green-100')
      expect(getProjectStatusColor('completed')).toBe('text-blue-600 bg-blue-100')
      expect(getProjectStatusColor('on_hold')).toBe('text-yellow-600 bg-yellow-100')
      expect(getProjectStatusColor('draft')).toBe('text-gray-600 bg-gray-100')
    })
  })

  describe('getProjectPriorityColor', () => {
    it('should return correct project priority colors', () => {
      expect(getProjectPriorityColor('critical')).toBe('text-red-600 bg-red-100')
      expect(getProjectPriorityColor('high')).toBe('text-orange-600 bg-orange-100')
      expect(getProjectPriorityColor('medium')).toBe('text-yellow-600 bg-yellow-100')
      expect(getProjectPriorityColor('low')).toBe('text-green-600 bg-green-100')
    })
  })

  describe('formatAuditAction', () => {
    it('should format audit actions correctly', () => {
      expect(formatAuditAction('login')).toBe('Login')
      expect(formatAuditAction('logout')).toBe('Logout')
      expect(formatAuditAction('create')).toBe('Create')
      expect(formatAuditAction('admin_action')).toBe('Admin Action')
    })
  })

  describe('getAuditActionColor', () => {
    it('should return correct audit action colors', () => {
      expect(getAuditActionColor('login')).toBe('text-green-600 bg-green-100')
      expect(getAuditActionColor('delete')).toBe('text-red-600 bg-red-100')
      expect(getAuditActionColor('create')).toBe('text-blue-600 bg-blue-100')
    })
  })

  describe('getSecurityAlertColor', () => {
    it('should return correct security alert colors', () => {
      expect(getSecurityAlertColor('critical')).toBe('text-red-600 bg-red-100 border-red-200')
      expect(getSecurityAlertColor('high')).toBe('text-orange-600 bg-orange-100 border-orange-200')
      expect(getSecurityAlertColor('medium')).toBe(
        'text-yellow-600 bg-yellow-100 border-yellow-200'
      )
      expect(getSecurityAlertColor('low')).toBe('text-blue-600 bg-blue-100 border-blue-200')
    })
  })

  describe('formatSecurityAlertType', () => {
    it('should format security alert types correctly', () => {
      expect(formatSecurityAlertType('failed_login')).toBe('Failed Login')
      expect(formatSecurityAlertType('suspicious_activity')).toBe('Suspicious Activity')
      expect(formatSecurityAlertType('unauthorized_access')).toBe('Unauthorized Access')
      expect(formatSecurityAlertType('data_breach')).toBe('Data Breach')
      expect(formatSecurityAlertType('system_error')).toBe('System Error')
    })
  })

  describe('getConfigCategoryColor', () => {
    it('should return correct config category colors', () => {
      expect(getConfigCategoryColor('authentication')).toBe('text-blue-600 bg-blue-100')
      expect(getConfigCategoryColor('security')).toBe('text-red-600 bg-red-100')
      expect(getConfigCategoryColor('performance')).toBe('text-green-600 bg-green-100')
      expect(getConfigCategoryColor('features')).toBe('text-purple-600 bg-purple-100')
      expect(getConfigCategoryColor('integrations')).toBe('text-yellow-600 bg-yellow-100')
    })
  })

  describe('validateConfigValue', () => {
    it('should validate string values', () => {
      expect(validateConfigValue('test', 'string')).toEqual({ isValid: true })
    })

    it('should validate number values', () => {
      expect(validateConfigValue('123', 'number')).toEqual({ isValid: true })
      expect(validateConfigValue('abc', 'number')).toEqual({
        isValid: false,
        error: 'Value must be a valid number',
      })
    })

    it('should validate boolean values', () => {
      expect(validateConfigValue('true', 'boolean')).toEqual({ isValid: true })
      expect(validateConfigValue('false', 'boolean')).toEqual({ isValid: true })
      expect(validateConfigValue('maybe', 'boolean')).toEqual({
        isValid: false,
        error: 'Value must be true or false',
      })
    })

    it('should validate JSON values', () => {
      expect(validateConfigValue('{"key": "value"}', 'json')).toEqual({ isValid: true })
      expect(validateConfigValue('invalid json', 'json')).toEqual({
        isValid: false,
        error: 'Value must be valid JSON',
      })
    })
  })

  describe('formatRelativeTime', () => {
    it('should format recent times correctly', () => {
      const now = new Date()
      const fiveMinutesAgo = new Date(now.getTime() - 5 * 60 * 1000)
      const twoHoursAgo = new Date(now.getTime() - 2 * 60 * 60 * 1000)
      const threeDaysAgo = new Date(now.getTime() - 3 * 24 * 60 * 60 * 1000)

      expect(formatRelativeTime(fiveMinutesAgo.toISOString())).toBe('5 minutes ago')
      expect(formatRelativeTime(twoHoursAgo.toISOString())).toBe('2 hours ago')
      expect(formatRelativeTime(threeDaysAgo.toISOString())).toBe('3 days ago')
    })

    it('should handle invalid dates', () => {
      expect(formatRelativeTime('invalid')).toBe('Unknown')
    })
  })

  describe('generateHealthSummary', () => {
    it('should generate healthy status for good metrics', () => {
      const metrics = {
        errorRate: 1,
        memoryUsage: 50,
        diskUsage: 60,
        serverLoad: 0.5,
      } as any

      const summary = generateHealthSummary(metrics)
      expect(summary.status).toBe('healthy')
      expect(summary.issues).toHaveLength(0)
      expect(summary.score).toBeGreaterThan(80)
    })

    it('should generate warning status for moderate issues', () => {
      const metrics = {
        errorRate: 3,
        memoryUsage: 85,
        diskUsage: 60,
        serverLoad: 0.5,
      } as any

      const summary = generateHealthSummary(metrics)
      expect(summary.status).toBe('warning')
      expect(summary.issues.length).toBeGreaterThan(0)
    })

    it('should generate critical status for severe issues', () => {
      const metrics = {
        errorRate: 10,
        memoryUsage: 95,
        diskUsage: 98,
        serverLoad: 0.95,
      } as any

      const summary = generateHealthSummary(metrics)
      expect(summary.status).toBe('critical')
      expect(summary.issues.length).toBeGreaterThan(0)
      expect(summary.score).toBeLessThan(60)
    })
  })

  describe('filterAndSortData', () => {
    const testData = [
      { id: '1', name: 'Alice', role: 'ADMIN', score: 95 },
      { id: '2', name: 'Bob', role: 'EDITOR', score: 85 },
      { id: '3', name: 'Charlie', role: 'VIEWER', score: 75 },
    ]

    it('should filter data by search query', () => {
      const result = filterAndSortData(testData, 'alice', 'name', 'asc', ['name', 'role'])
      expect(result).toHaveLength(1)
      expect(result[0].name).toBe('Alice')
    })

    it('should sort data correctly', () => {
      const result = filterAndSortData(testData, '', 'score', 'desc', ['name'])
      expect(result[0].score).toBe(95)
      expect(result[2].score).toBe(75)
    })

    it('should handle empty search query', () => {
      const result = filterAndSortData(testData, '', 'name', 'asc', ['name'])
      expect(result).toHaveLength(3)
    })

    it('should handle case-insensitive search', () => {
      const result = filterAndSortData(testData, 'ALICE', 'name', 'asc', ['name'])
      expect(result).toHaveLength(1)
      expect(result[0].name).toBe('Alice')
    })
  })
})
