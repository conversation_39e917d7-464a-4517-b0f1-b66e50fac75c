import '@testing-library/jest-dom'
import { cleanup } from '@testing-library/react'
import React from 'react'
import { afterAll, afterEach, beforeAll } from 'vitest'

// Mock Next.js router
const mockRouter = {
  push: vi.fn(),
  replace: vi.fn(),
  prefetch: vi.fn(),
  back: vi.fn(),
  forward: vi.fn(),
  refresh: vi.fn(),
  pathname: '/',
  query: {},
  asPath: '/',
  route: '/',
  events: {
    on: vi.fn(),
    off: vi.fn(),
    emit: vi.fn(),
  },
}

vi.mock('next/navigation', () => ({
  useRouter: () => mockRouter,
  usePathname: () => '/',
  useSearchParams: () => new URLSearchParams(),
}))

// Mock Next.js Link component
vi.mock('next/link', () => {
  return {
    default: ({ children, href, ...props }: any) => {
      return React.createElement('a', { href, ...props }, children)
    },
  }
})

// Mock window.localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
}

Object.defineProperty(window, 'localStorage', {
  value: localStorageMock,
})

// Mock window.fetch
global.fetch = vi.fn()

// Mock SubmitEvent for JSdom compatibility
if (typeof SubmitEvent === 'undefined') {
  global.SubmitEvent = class SubmitEvent extends Event {
    submitter: HTMLElement | null
    constructor(
      type: string,
      eventInitDict?: SubmitEventInit & { submitter?: HTMLElement | null }
    ) {
      super(type, eventInitDict)
      this.submitter = eventInitDict?.submitter || null
    }
  }
}

// Mock HTMLFormElement.prototype.requestSubmit for JSdom compatibility
if (typeof HTMLFormElement !== 'undefined' && !HTMLFormElement.prototype.requestSubmit) {
  HTMLFormElement.prototype.requestSubmit = function (
    this: HTMLFormElement,
    submitter?: HTMLElement
  ) {
    // If submitter is provided, validate it
    if (submitter && !this.contains(submitter)) {
      throw new DOMException('Form does not contain submitter', 'NotFoundError')
    }

    // Create and dispatch submit event
    const event = new SubmitEvent('submit', {
      bubbles: true,
      cancelable: true,
      submitter: submitter || null,
    })

    const cancelled = !this.dispatchEvent(event)

    // If not cancelled and no preventDefault was called, trigger form submission
    if (!cancelled && !event.defaultPrevented) {
      // Trigger form submission behavior
      if (this.onsubmit) {
        this.onsubmit(event)
      }
    }
  }
}

// Cleanup after each test
afterEach(() => {
  cleanup()
  vi.clearAllMocks()
})

// Setup and teardown
beforeAll(() => {
  // Setup any global test configuration
})

afterAll(() => {
  // Cleanup any global test configuration
})
