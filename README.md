# Ultimate Electrical Designer

[](https://opensource.org/licenses/MIT)
[](https://www.python.org/downloads/)
[](https://fastapi.tiangolo.com/)
[](https://dotnet.microsoft.com/)
[](https://nextjs.org/)

An engineering-grade electrical design platform that provides comprehensive tools for professional electrical system design, heat tracing calculations, and standards compliance validation. Built to meet the highest standards of professional electrical design applications with immaculate attention to detail.

-----

## 🎯 Project Overview

The Ultimate Electrical Designer is a complete electrical engineering platform designed for professional electrical system design, specializing in:

  - **Heat Tracing Design**: Complete thermal analysis and cable selection
  - **Electrical Systems**: Power distribution, cable routing, and switchboard design
  - **Standards Validation**: Automated compliance checking against international standards
  - **Report Generation**: Professional documentation and calculation reports
  - **Component Management**: Comprehensive electrical component catalog

### Key Features

#### 🔥 Heat Tracing Design

  - Complete thermal analysis and heat loss calculations
  - Automated selection of self-regulating and series resistance cables
  - Power requirement calculations and circuit design
  - Standards compliance validation against IEC and EN thermal standards

#### ⚡ Electrical System Design

  - Complete electrical system design and analysis
  - Optimized cable routing with installation method considerations
  - Professional switchboard layout and component selection
  - Comprehensive electrical load calculations and balancing

#### 📊 Component Management

  - Extensive database of electrical components across 13 professional categories
  - Hierarchical organization with standards mapping
  - Component specifications mapped to relevant standards
  - Flexible data import and export capabilities

#### 📋 Standards Compliance

  - **IEC Standards**: IEC-60079 (ATEX), IEC-61508 (Functional Safety), IEC-60364 (Low-Voltage Installations), IEC-60287 (Cable Current Rating)
  - **EN Standards**: EN-50110 (Operation), EN-60204 (Machinery Safety), EN-50522 (Earthing)
  - **IEEE Standards**: Comprehensive electrical engineering standards compliance

-----

## 🏗️ Architecture

The Ultimate Electrical Designer follows a **5-layer architecture pattern** ensuring separation of concerns, maintainability, and scalability.

[Architecture](docs/structure.md)
[Technology Stack](docs/tech.md)

-----

## 🚀 Quick Start

This project uses a **monorepo structure** with a central `Makefile` to orchestrate common development tasks across all services.

### Development Standards

[Rules](docs/rules.md)

1.  **Robust design principles:** Apply **SOLID** principles for structural design, ensuring maintainability and flexibility through focused responsibilities, extensibility, and proper abstraction. Complement these with practices like **DRY**, **KISS**, and **TDD** to streamline implementation, reduce complexity, and enhance overall code quality.
2.  **5-Phase Methodology:** Adopt a systematic 5-phase approach for each feature or task, ensuring a structured, quality-driven development process.
    1.  **Discovery & Analysis:** Understand the current state of the system, identify requirements, and define the scope of the main task.
    2.  **Task Planning:** Break down tasks into smaller, manageable units to ensure efficient progress.
    3.  **Implementation:** Execute changes with engineering-grade quality, focusing on unified patterns and professional electrical design standards.
    4.  **Verification:** Ensure all requirements are met through comprehensive testing and compliance verification.
    5.  **Documentation & Handover:** Prepare comprehensive documentation and create a handover package for future development and AI agent transfer.
3.  **Unified Patterns:** Apply consistent "unified patterns" for calculations, service layers, and repositories, using decorators for error handling, performance monitoring, and memory optimization.
4.  **Quality & Standards Focus:** Ensure immaculate attention to detail, adhere to professional electrical design standards (IEEE/IEC/EN), complete type safety with MyPy validation, and comprehensive testing (including real database connections).
5.  **Key Success Metrics:** Define success through high unified patterns compliance (≥90%), extensive test coverage (≥85%), 100% test pass rates, and zero remaining placeholder implementations.

### Prerequisites

  - **Python**: 3.13+ with **Poetry**
  - **Node.js**: 18+ with **npm** (for frontend)
  - **.NET**: 8.0+ SDK (for C# services)
  - **Docker** & **Docker Compose** (for C# services)
  - **Database**: PostgreSQL (recommended for production) or SQLite (for development)

### Getting Started

To set up your development environment and run all services:

1.  **Clone the repository**:
    ```bash
    git clone https://github.com/debaneee/ultimate-electrical-designer.git
    cd ultimate-electrical-designer
    ```
2.  **Install project dependencies & hooks**:
    This command installs Python dependencies (via Poetry) and Node.js dependencies (via npm), and sets up pre-commit hooks for both the backend and frontend.
    ```bash
    make dev-setup
    ```
3.  **Configure environment variables**:
    Create `.env` files in `server/` and `client/` directories.
      - **Backend**: Configure database connection, security keys, etc. Refer to `server/src/config/settings.py` for required variables or check `docs/developer-handbooks/020-getting-started.md`.
      - **Frontend**: Configure API endpoints.
4.  **Database Initialization (Backend)**:
    Navigate to the `server/` directory and run database migrations. If this is a fresh setup, you might want to reset the database first (use with caution as it deletes data).
    ```bash
    # To run migrations:
    make db-migrate
    # To reset the database (DANGER: Deletes all data):
    make db-reset
    ```
5.  **Seed Development Data (Backend)**:
    ```bash
    cd server
    poetry run python src/main.py seed-data --environment development
    poetry run python src/main.py create-superuser 'Admin' 'Pass123' '<EMAIL>'
    cd .. # Go back to root
    ```
6.  **Start all services**:
    This command will concurrently start the Next.js frontend, FastAPI backend, and the C\# CAD Integrator and Computation Engine services (via Docker Compose).
    ```bash
    make start-all
    ```
      - **Frontend**: Available at `http://localhost:3000`
      - **Backend**: Available at `http://localhost:8000`
      - **C# Services**: Check your Docker logs for details, default ports might be `5001` and `5002` (as per `docker-compose.yml` configuration).

-----

## 📖 Documentation

### API Documentation

  - **Development**: `http://localhost:8000/docs` (Swagger UI)
  - **Alternative**: `http://localhost:8000/redoc` (ReDoc)
  - **Health Check**: `http://localhost:8000/api/v1/health`

-----

## 🧪 Testing

The project employs a robust testing strategy across all components. All commands are run from the **project root directory** using `make`.

### General Testing Commands

  - **Run all tests (Backend & Frontend)**:
    ```bash
    make test
    ```
  - **Install Playwright browsers (if needed for E2E tests)**:
    ```bash
    make pre-commit-install # or cd client && npx playwright install
    ```

### Backend Testing (Python)

  - **Run all backend tests**:
    ```bash
    make test-server
    ```
  - **Run unit tests**:
    ```bash
    make test-unit
    ```
  - **Run integration tests**:
    ```bash
    make test-integration
    ```
  - **Generate test coverage report**:
    ```bash
    make test-coverage
    ```

### Frontend Testing (Next.js)

  - **Run all frontend tests**:
    ```bash
    make test-client
    ```
  - **Generate report for failing tests**:
    ```bash
    make test-client-failing
    ```

-----

## 🔧 Development

The project maintains engineering-grade code quality with:

  - **Zero Tolerance Policies**: No warnings, no technical debt
  - **Unified Patterns**: Consistent error handling and monitoring
  - **Type Safety**: Full TypeScript and Python type annotations
  - **Standards Compliance**: IEEE/IEC/EN standards adherence

### Code Quality Commands

All code quality commands are run from the **project root directory** using `make`.

  - **Run all linting checks (Python & Next.js)**:
    ```bash
    make lint
    ```
  - **Format all code (Python & Next.js)**:
    ```bash
    make format
    ```
  - **Check Python code formatting**:
    ```bash
    make check-format-server
    ```
  - **Check Next.js code formatting**:
    ```bash
    make check-format-client
    ```
  - **Run Python security scanning**:
    ```bash
    make security-check
    ```
  - **Run comprehensive Python type safety validation**:
    ```bash
    make type-check
    ```
  - **Run critical Python modules type checking**:
    ```bash
    make type-check-critical
    ```
  - **Run full Python type checking (may show SQLAlchemy issues)**:
    ```bash
    make type-check-full
    ```
  - **Run TypeScript type checking for the client**:
    ```bash
    make type-check-client
    ```
  - **Install pre-commit hooks**:
    ```bash
    make pre-commit-install
    ```
  - **Manually run pre-commit hooks on all files**:
    ```bash
    make pre-commit-run
    ```
  - **Clean up temporary files and caches**:
    ```bash
    make clean
    ```

### Backend Development Specifics (if running directly)

While `make` commands are recommended, you can also run individual backend development tasks by navigating to `server/` and using `poetry run`:

```bash
cd server
poetry run uvicorn src.main:app --reload --host 0.0.0.0 --port 8000 # Development server
poetry run pytest                                                    # Run all tests
poetry run ruff check .                                              # Linting
poetry run mypy src/                                                 # Type checking
cd .. # Go back to root
```

### Frontend Development Specifics (if running directly)

Similarly, for frontend tasks:

```bash
cd client
npm run dev                                      # Development server
npm run build                                    # Production build
npm run lint                                     # ESLint
npm run type-check                               # TypeScript checking
npm run test                                     # Unit tests
npm run test:e2e                                 # E2E tests
cd .. # Go back to root
```

-----

## 🐳 Docker Deployment

### Docker Support

The project includes Docker support for all services:

  - **Backend**: `server/Dockerfile` (Python FastAPI)
  - **CAD Integration**: `cad-integrator-service/Dockerfile` (C# .NET)
  - **Computation Engine**: `computation-engine-service/Dockerfile` (C# .NET)

### Development Environment with Docker Compose

For a full local development environment using Docker Compose, which includes database and other services, use the `make start-all` command. This assumes a `docker-compose.yml` file is configured in the project root.

```bash
# Start all services via Docker Compose (as part of make start-all)
make start-all
```

For individual service containerization (less common for full development, but useful for build/testing):

```bash
# Build individual service images
make build:cad-integrator      # Builds cad-integrator-service image
make build:computation-engine  # Builds computation-engine-service image
# To build the backend server image (you'd typically do this from server/ or via CI)
# cd server && docker build -t ued-backend .

# Run services (example, typically handled by docker-compose)
# docker run -p 8000:8000 ued-backend
# docker run -p 5000:5000 ued-cad-service # Check actual port in docker-compose
# docker run -p 5001:5001 ued-compute-service # Check actual port in docker-compose
```

-----

## 🤝 Contributing

### Development Workflow

1.  **Fork** the repository
2.  **Create** a feature branch (`git checkout -b feature/amazing-feature`)
3.  **Follow** the [Development Standards](docs/rules.md)
4.  **Test** your changes thoroughly using `make test`
5.  **Commit** with conventional commit messages
6.  **Push** to your branch (`git push origin feature/amazing-feature`)
7.  **Open** a Pull Request

### Code Standards

  - **SOLID design principles** with single responsibility, open/closed, dependency inversion
  - **Clean architecture** with proper separation of concerns
  - Backend code must strictly adhere to the **5-layer architecture pattern**
  - Frontend code must follow **Domain-Driven Design (DDD) principles** with clear module boundaries
  - **Complete type safety** across the entire codebase
  - **Zero tolerance for code quality violations** that compromise system reliability or maintainability.
  - **Zero tolerance for test failures** in main branch
  - **Zero tolerance for security vulnerabilities** or authentication bypasses

-----

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE.md) file for details.

-----

## 🏢 Professional Use

The Ultimate Electrical Designer is designed for professional electrical engineering applications with:

  - **Engineering-Grade Quality**: Zero tolerance for warnings or technical debt
  - **Standards Compliance**: Full IEEE/IEC/EN standards adherence
  - **Professional Documentation**: Comprehensive technical documentation
  - **Scalable Architecture**: Enterprise-ready microservices architecture
  - **Comprehensive Testing**: Unit, integration, and performance testing

-----

## 📞 Support

  - **Issues**: [GitHub Issues](https://github.com/debaneee/ultimate-electrical-designer/issues)
  - **Discussions**: [GitHub Discussions](https://github.com/debaneee/ultimate-electrical-designer/discussions)

-----

**Built with engineering excellence for professional electrical design applications.**