/**
 * Password Strength Indicator Component
 * Provides real-time visual feedback on password strength and requirements
 */

'use client'

import { cn } from '@/lib/utils'
import { calculatePasswordStrength, type PasswordStrength } from '../../utils/validation'

interface PasswordStrengthProps {
  password: string
  className?: string
  showRequirements?: boolean
}

export function PasswordStrengthIndicator({
  password,
  className = '',
  showRequirements = true,
}: PasswordStrengthProps) {
  const strength = calculatePasswordStrength(password)

  if (!password) {
    return null
  }

  return (
    <div className={cn('space-y-3', className)}>
      {/* Strength Bar */}
      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <span className="text-sm font-medium text-neutral-700">Password Strength</span>
          <span
            className={cn('text-sm font-semibold', {
              'text-red-600': strength.color === 'red',
              'text-orange-500': strength.color === 'orange',
              'text-yellow-500': strength.color === 'yellow',
              'text-blue-600': strength.color === 'blue',
              'text-green-600': strength.color === 'green',
            })}
          >
            {strength.label}
          </span>
        </div>

        {/* Progress Bar */}
        <div className="flex space-x-1">
          {[1, 2, 3, 4, 5].map((segment) => (
            <div
              key={segment}
              className={cn('h-2 flex-1 rounded-full transition-colors duration-200', {
                'bg-red-500': segment <= strength.score && strength.color === 'red',
                'bg-orange-500': segment <= strength.score && strength.color === 'orange',
                'bg-yellow-500': segment <= strength.score && strength.color === 'yellow',
                'bg-blue-500': segment <= strength.score && strength.color === 'blue',
                'bg-green-500': segment <= strength.score && strength.color === 'green',
                'bg-neutral-200': segment > strength.score,
              })}
            />
          ))}
        </div>
      </div>

      {/* Requirements Checklist */}
      {showRequirements && (
        <div className="space-y-2">
          <span className="text-sm font-medium text-neutral-700">Requirements</span>
          <div className="grid grid-cols-1 gap-1">
            <RequirementItem met={strength.requirements.length} text="At least 8 characters" />
            <RequirementItem met={strength.requirements.uppercase} text="One uppercase letter" />
            <RequirementItem met={strength.requirements.lowercase} text="One lowercase letter" />
            <RequirementItem met={strength.requirements.digit} text="One number" />
            <RequirementItem met={strength.requirements.special} text="One special character" />
          </div>
        </div>
      )}
    </div>
  )
}

interface RequirementItemProps {
  met: boolean
  text: string
}

function RequirementItem({ met, text }: RequirementItemProps) {
  return (
    <div className="flex items-center space-x-2">
      <div
        className={cn(
          'flex h-4 w-4 items-center justify-center rounded-full transition-colors duration-200',
          {
            'bg-green-100 text-green-600': met,
            'bg-neutral-100 text-neutral-400': !met,
          }
        )}
      >
        {met ? (
          <svg className="h-3 w-3" fill="currentColor" viewBox="0 0 20 20">
            <path
              fillRule="evenodd"
              d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
              clipRule="evenodd"
            />
          </svg>
        ) : (
          <svg className="h-3 w-3" fill="currentColor" viewBox="0 0 20 20">
            <path
              fillRule="evenodd"
              d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
              clipRule="evenodd"
            />
          </svg>
        )}
      </div>
      <span
        className={cn('text-sm transition-colors duration-200', {
          'text-green-700': met,
          'text-neutral-500': !met,
        })}
      >
        {text}
      </span>
    </div>
  )
}

/**
 * Compact Password Strength Indicator (for smaller spaces)
 */
export function CompactPasswordStrength({
  password,
  className = '',
}: Omit<PasswordStrengthProps, 'showRequirements'>) {
  const strength = calculatePasswordStrength(password)

  if (!password) {
    return null
  }

  return (
    <div className={cn('flex items-center space-x-2', className)}>
      {/* Mini Progress Bar */}
      <div className="flex space-x-1">
        {[1, 2, 3, 4, 5].map((segment) => (
          <div
            key={segment}
            className={cn('h-1 w-3 rounded-full transition-colors duration-200', {
              'bg-red-500': segment <= strength.score && strength.color === 'red',
              'bg-orange-500': segment <= strength.score && strength.color === 'orange',
              'bg-yellow-500': segment <= strength.score && strength.color === 'yellow',
              'bg-blue-500': segment <= strength.score && strength.color === 'blue',
              'bg-green-500': segment <= strength.score && strength.color === 'green',
              'bg-neutral-200': segment > strength.score,
            })}
          />
        ))}
      </div>

      {/* Strength Label */}
      <span
        className={cn('text-xs font-medium', {
          'text-red-600': strength.color === 'red',
          'text-orange-500': strength.color === 'orange',
          'text-yellow-500': strength.color === 'yellow',
          'text-blue-600': strength.color === 'blue',
          'text-green-600': strength.color === 'green',
        })}
      >
        {strength.label}
      </span>
    </div>
  )
}

export type { PasswordStrength }
