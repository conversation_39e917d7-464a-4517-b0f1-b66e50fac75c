/**
 * Enhanced Formatting Utilities
 * Comprehensive formatting functions for component data display
 */

import type { ComponentRead, ComponentDimensions } from '../schemas'

// Internationalization support
const DEFAULT_LOCALE = 'en-US'
const EU_LOCALE = 'de-DE'

// Currency formatting with proper localization
export function formatPrice(
  price: string | number | null | undefined,
  currency: string = 'EUR',
  locale: string = EU_LOCALE
): string {
  if (price === null || price === undefined || price === '') {
    return 'N/A'
  }

  const numericPrice = typeof price === 'string' ? parseFloat(price) : price

  if (isNaN(numericPrice)) {
    return 'N/A'
  }

  try {
    return new Intl.NumberFormat(locale, {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(numericPrice)
  } catch (error) {
    // Fallback for invalid currency codes
    return `${numericPrice.toFixed(2)} ${currency}`
  }
}

// Enhanced price formatting with range support
export function formatPriceRange(
  minPrice: number | null,
  maxPrice: number | null,
  currency: string = 'EUR',
  locale: string = EU_LOCALE
): string {
  if (!minPrice && !maxPrice) return 'N/A'
  if (minPrice && !maxPrice) return formatPrice(minPrice, currency, locale)
  if (!minPrice && maxPrice) return `≤ ${formatPrice(maxPrice, currency, locale)}`
  if (minPrice === maxPrice) return formatPrice(minPrice, currency, locale)

  return `${formatPrice(minPrice, currency, locale)} - ${formatPrice(maxPrice, currency, locale)}`
}

// Weight formatting with unit conversion
export function formatWeight(
  weight: number | null | undefined,
  unit: 'kg' | 'g' | 'lb' | 'oz' = 'kg',
  precision: number = 2
): string {
  if (weight === null || weight === undefined) {
    return 'N/A'
  }

  if (weight === 0) {
    return `0 ${unit}`
  }

  // Auto-convert to appropriate unit for better readability
  if (unit === 'kg') {
    if (weight < 0.001) {
      return `${(weight * 1000000).toFixed(precision)} mg`
    } else if (weight < 1) {
      return `${(weight * 1000).toFixed(precision)} g`
    } else if (weight >= 1000) {
      return `${(weight / 1000).toFixed(precision)} t`
    }
  }

  return `${weight.toFixed(precision)} ${unit}`
}

// Enhanced dimensions formatting with multiple formats
export function formatDimensions(
  dimensions: ComponentDimensions | null | undefined,
  unit: string = 'mm',
  format: 'compact' | 'detailed' | 'technical' = 'compact'
): string {
  if (!dimensions) {
    return 'N/A'
  }

  const { length, width, height, diameter } = dimensions

  // Handle circular components
  if (diameter) {
    switch (format) {
      case 'technical':
        return `⌀${diameter} ${unit}`
      case 'detailed':
        return `Diameter: ${diameter} ${unit}`
      default:
        return `⌀${diameter} ${unit}`
    }
  }

  // Handle rectangular components
  const parts = []
  if (length !== undefined && length !== null) parts.push(length)
  if (width !== undefined && width !== null) parts.push(width)
  if (height !== undefined && height !== null) parts.push(height)

  if (parts.length === 0) {
    return 'N/A'
  }

  switch (format) {
    case 'technical':
      return `${parts.join(' × ')} ${unit} (L×W×H)`
    case 'detailed':
      const labels = ['Length', 'Width', 'Height'].slice(0, parts.length)
      return labels.map((label, i) => `${label}: ${parts[i]} ${unit}`).join(', ')
    default:
      return `${parts.join(' × ')} ${unit}`
  }
}

// Component name formatting with fallbacks
export function formatComponentName(component: ComponentRead): string {
  // Priority order: display_name > full_name > manufacturer + model_number > name
  if (component.display_name?.trim()) {
    return component.display_name.trim()
  }

  if (component.full_name?.trim()) {
    return component.full_name.trim()
  }

  if (component.manufacturer?.trim() && component.model_number?.trim()) {
    return `${component.manufacturer.trim()} ${component.model_number.trim()}`
  }

  return component.name?.trim() || 'Unnamed Component'
}

// Component description formatting with truncation
export function formatComponentDescription(
  description: string | null | undefined,
  maxLength: number = 100,
  addEllipsis: boolean = true
): string {
  if (!description?.trim()) {
    return 'No description available'
  }

  const trimmed = description.trim()

  if (trimmed.length <= maxLength) {
    return trimmed
  }

  const truncated = trimmed.substring(0, maxLength)
  const lastSpace = truncated.lastIndexOf(' ')

  // Try to break at word boundary
  const result = lastSpace > maxLength * 0.8 ? truncated.substring(0, lastSpace) : truncated

  return addEllipsis ? `${result}...` : result
}

// Status formatting with color coding
export function formatComponentStatus(component: ComponentRead): {
  text: string
  color: string
  variant: 'default' | 'secondary' | 'destructive' | 'outline'
} {
  if (!component.is_active) {
    return {
      text: 'Inactive',
      color: 'text-gray-500',
      variant: 'secondary',
    }
  }

  if (component.is_preferred) {
    return {
      text: 'Preferred',
      color: 'text-green-600',
      variant: 'default',
    }
  }

  // Check stock status
  switch (component.stock_status) {
    case 'available':
      return {
        text: 'Available',
        color: 'text-green-600',
        variant: 'default',
      }
    case 'limited':
      return {
        text: 'Limited Stock',
        color: 'text-yellow-600',
        variant: 'outline',
      }
    case 'out_of_stock':
      return {
        text: 'Out of Stock',
        color: 'text-red-600',
        variant: 'destructive',
      }
    case 'discontinued':
      return {
        text: 'Discontinued',
        color: 'text-gray-600',
        variant: 'secondary',
      }
    case 'on_order':
      return {
        text: 'On Order',
        color: 'text-blue-600',
        variant: 'outline',
      }
    default:
      return {
        text: 'Active',
        color: 'text-gray-900',
        variant: 'outline',
      }
  }
}

// Date formatting with relative time
export function formatDate(
  date: string | Date | null | undefined,
  format: 'short' | 'long' | 'relative' | 'technical' = 'short',
  locale: string = DEFAULT_LOCALE
): string {
  if (!date) {
    return 'N/A'
  }

  const dateObj = typeof date === 'string' ? new Date(date) : date

  if (isNaN(dateObj.getTime())) {
    return 'Invalid Date'
  }

  switch (format) {
    case 'relative':
      return formatRelativeTime(dateObj, locale)
    case 'long':
      return dateObj.toLocaleDateString(locale, {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
      })
    case 'technical':
      return dateObj.toISOString()
    default:
      return dateObj.toLocaleDateString(locale, {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
      })
  }
}

// Relative time formatting
function formatRelativeTime(date: Date, locale: string = DEFAULT_LOCALE): string {
  const now = new Date()
  const diffMs = now.getTime() - date.getTime()
  const diffSeconds = Math.floor(diffMs / 1000)
  const diffMinutes = Math.floor(diffSeconds / 60)
  const diffHours = Math.floor(diffMinutes / 60)
  const diffDays = Math.floor(diffHours / 24)

  if (diffSeconds < 60) {
    return 'Just now'
  } else if (diffMinutes < 60) {
    return `${diffMinutes} minute${diffMinutes !== 1 ? 's' : ''} ago`
  } else if (diffHours < 24) {
    return `${diffHours} hour${diffHours !== 1 ? 's' : ''} ago`
  } else if (diffDays < 7) {
    return `${diffDays} day${diffDays !== 1 ? 's' : ''} ago`
  } else {
    return date.toLocaleDateString(locale, {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    })
  }
}

// Number formatting with units
export function formatNumber(
  value: number | null | undefined,
  options: {
    unit?: string
    precision?: number
    locale?: string
    notation?: 'standard' | 'scientific' | 'engineering' | 'compact'
  } = {}
): string {
  if (value === null || value === undefined) {
    return 'N/A'
  }

  const { unit = '', precision = 2, locale = DEFAULT_LOCALE, notation = 'standard' } = options

  try {
    const formatted = new Intl.NumberFormat(locale, {
      minimumFractionDigits: 0,
      maximumFractionDigits: precision,
      notation,
    }).format(value)

    return unit ? `${formatted} ${unit}` : formatted
  } catch (error) {
    // Fallback formatting
    const formatted = value.toFixed(precision)
    return unit ? `${formatted} ${unit}` : formatted
  }
}

// Percentage formatting
export function formatPercentage(
  value: number | null | undefined,
  precision: number = 1,
  locale: string = DEFAULT_LOCALE
): string {
  if (value === null || value === undefined) {
    return 'N/A'
  }

  try {
    return new Intl.NumberFormat(locale, {
      style: 'percent',
      minimumFractionDigits: 0,
      maximumFractionDigits: precision,
    }).format(value / 100)
  } catch (error) {
    return `${value.toFixed(precision)}%`
  }
}

// File size formatting
export function formatFileSize(bytes: number | null | undefined): string {
  if (bytes === null || bytes === undefined || bytes === 0) {
    return '0 B'
  }

  const units = ['B', 'KB', 'MB', 'GB', 'TB']
  const base = 1024
  const unitIndex = Math.floor(Math.log(bytes) / Math.log(base))
  const size = bytes / Math.pow(base, unitIndex)

  return `${size.toFixed(1)} ${units[unitIndex]}`
}

// Specification formatting
export function formatSpecification(key: string, value: any, unit?: string): string {
  if (value === null || value === undefined || value === '') {
    return 'N/A'
  }

  // Handle different value types
  if (typeof value === 'boolean') {
    return value ? 'Yes' : 'No'
  }

  if (typeof value === 'number') {
    return formatNumber(value, { unit, precision: 3 })
  }

  if (typeof value === 'string') {
    // Try to detect and format electrical values
    const electricalPatterns = {
      voltage: /^(\d+(?:\.\d+)?)\s*(V|kV|mV)$/i,
      current: /^(\d+(?:\.\d+)?)\s*(A|mA|kA)$/i,
      power: /^(\d+(?:\.\d+)?)\s*(W|kW|MW|mW)$/i,
      resistance: /^(\d+(?:\.\d+)?)\s*(Ω|ohm|R)$/i,
      frequency: /^(\d+(?:\.\d+)?)\s*(Hz|kHz|MHz|GHz)$/i,
    }

    for (const [type, pattern] of Object.entries(electricalPatterns)) {
      const match = value.match(pattern)
      if (match) {
        const [, number, detectedUnit] = match
        return `${parseFloat(number)} ${detectedUnit || unit || ''}`
      }
    }

    return unit ? `${value} ${unit}` : value
  }

  return String(value)
}

// Text truncation with smart word breaking
export function truncateText(
  text: string | null | undefined,
  maxLength: number,
  options: {
    addEllipsis?: boolean
    breakOnWord?: boolean
    suffix?: string
  } = {}
): string {
  if (!text) return ''

  const { addEllipsis = true, breakOnWord = true, suffix = '...' } = options

  if (text.length <= maxLength) {
    return text
  }

  let truncated = text.substring(0, maxLength)

  if (breakOnWord) {
    const lastSpace = truncated.lastIndexOf(' ')
    if (lastSpace > maxLength * 0.7) {
      truncated = truncated.substring(0, lastSpace)
    }
  }

  return addEllipsis ? `${truncated}${suffix}` : truncated
}

// Search result highlighting
export function highlightSearchTerms(
  text: string,
  searchTerms: string[],
  className: string = 'bg-yellow-200'
): string {
  if (!searchTerms.length) return text

  let highlighted = text

  searchTerms.forEach((term) => {
    if (term.trim()) {
      const regex = new RegExp(`(${term.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi')
      highlighted = highlighted.replace(regex, `<mark class="${className}">$1</mark>`)
    }
  })

  return highlighted
}
