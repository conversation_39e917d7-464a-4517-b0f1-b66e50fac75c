/**
 * Unit tests for SettingsPanel component
 * Ultimate Electrical Designer - Settings & User Preferences
 */

import React from 'react'
import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import {
  SettingsPanel,
  CompactSettingsPanel,
  CategorySettingsPanel,
} from '../../components/SettingsPanel'
import { useSettings } from '../../hooks/useSettings'
import type { UseSettingsReturn } from '../../hooks/useSettings'

// Mock the useSettings hook
vi.mock('../../hooks/useSettings')

// Mock the toast hook
vi.mock('@/hooks/useToast', () => ({
  toast: vi.fn(),
}))

// Mock UI components
vi.mock('@/components/ui/card', () => ({
  Card: ({ children, ...props }: any) => (
    <div data-testid="card" {...props}>
      {children}
    </div>
  ),
  CardContent: ({ children, ...props }: any) => (
    <div data-testid="card-content" {...props}>
      {children}
    </div>
  ),
  CardDescription: ({ children, ...props }: any) => (
    <div data-testid="card-description" {...props}>
      {children}
    </div>
  ),
  CardHeader: ({ children, ...props }: any) => (
    <div data-testid="card-header" {...props}>
      {children}
    </div>
  ),
  CardTitle: ({ children, ...props }: any) => (
    <div data-testid="card-title" {...props}>
      {children}
    </div>
  ),
}))

vi.mock('@/components/ui/tabs', () => ({
  Tabs: ({ children, value, onValueChange, ...props }: any) => (
    <div data-testid="tabs" data-value={value} {...props}>
      {React.Children.map(children, (child) => React.cloneElement(child, { onValueChange }))}
    </div>
  ),
  TabsContent: ({ children, value, ...props }: any) => (
    <div data-testid="tabs-content" data-value={value} {...props}>
      {children}
    </div>
  ),
  TabsList: ({ children, ...props }: any) => (
    <div data-testid="tabs-list" {...props}>
      {children}
    </div>
  ),
  TabsTrigger: ({ children, value, ...props }: any) => (
    <button data-testid="tabs-trigger" data-value={value} {...props}>
      {children}
    </button>
  ),
}))

vi.mock('@/components/ui/button', () => ({
  Button: ({ children, onClick, disabled, ...props }: any) => (
    <button onClick={onClick} disabled={disabled} data-testid="button" {...props}>
      {children}
    </button>
  ),
}))

vi.mock('@/components/ui/badge', () => ({
  Badge: ({ children, ...props }: any) => (
    <span data-testid="badge" {...props}>
      {children}
    </span>
  ),
}))

// Mock child components
vi.mock('../../components/SettingsSearch', () => ({
  SettingsSearch: () => <div data-testid="settings-search">Settings Search</div>,
}))

vi.mock('../../components/PreferencesForm', () => ({
  PreferencesForm: ({ category }: { category: string }) => (
    <div data-testid="preferences-form" data-category={category}>
      Preferences Form for {category}
    </div>
  ),
}))

vi.mock('../../components/ImportExportDialog', () => ({
  ImportExportDialog: () => <div data-testid="import-export-dialog">Import Export Dialog</div>,
}))

vi.mock('../../components/SettingsReset', () => ({
  SettingsReset: () => <div data-testid="settings-reset">Settings Reset</div>,
}))

const mockUseSettings: UseSettingsReturn = {
  preferences: {
    theme: 'light',
    language: 'en',
    timezone: 'UTC',
    date_format: 'YYYY-MM-DD',
    time_format: '24h',
    units_system: 'metric',
    notifications_enabled: true,
    email_notifications: true,
    auto_save_interval: 300,
    dashboard_layout: 'default',
    calculation_precision: 2,
    auto_save_enabled: true,
  },
  isLoadingPreferences: false,
  preferencesError: null,
  ui: {
    activeCategory: 'account',
    searchQuery: '',
    isLoading: false,
    isSaving: false,
    hasUnsavedChanges: false,
    showResetDialog: false,
    showImportDialog: false,
    showExportDialog: false,
    errors: {},
  },
  formData: {},
  hasChanges: false,
  changedFields: [],
  actions: {
    setActiveCategory: vi.fn(),
    setSearchQuery: vi.fn(),
    clearSearch: vi.fn(),
    updateField: vi.fn(),
    updateMultipleFields: vi.fn(),
    resetForm: vi.fn(),
    validateForm: vi.fn(),
    savePreferences: vi.fn(),
    resetPreferences: vi.fn(),
    exportPreferences: vi.fn(),
    importPreferences: vi.fn(),
    setError: vi.fn(),
    clearErrors: vi.fn(),
    clearFieldError: vi.fn(),
    openResetDialog: vi.fn(),
    closeResetDialog: vi.fn(),
    openImportDialog: vi.fn(),
    closeImportDialog: vi.fn(),
    openExportDialog: vi.fn(),
    closeExportDialog: vi.fn(),
    closeAllDialogs: vi.fn(),
    enableSync: vi.fn(),
    disableSync: vi.fn(),
  },
  autoSave: {
    isEnabled: false,
    isAutoSaving: false,
    autoSaveError: null,
    enableAutoSave: vi.fn(),
    disableAutoSave: vi.fn(),
  },
}

const TestWrapper = ({ children }: { children: React.ReactNode }) => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  })

  return <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
}

describe('SettingsPanel', () => {
  beforeEach(() => {
    vi.mocked(useSettings).mockReturnValue(mockUseSettings)
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  describe('Basic Rendering', () => {
    it('should render settings panel with header', () => {
      render(
        <TestWrapper>
          <SettingsPanel />
        </TestWrapper>
      )

      expect(screen.getByText('Settings')).toBeInTheDocument()
      expect(screen.getByText('Manage your account settings and preferences')).toBeInTheDocument()
    })

    it('should render all category tabs', () => {
      render(
        <TestWrapper>
          <SettingsPanel />
        </TestWrapper>
      )

      const tabsTriggers = screen.getAllByTestId('tabs-trigger')
      expect(tabsTriggers).toHaveLength(6) // 6 categories
    })

    it('should render search component when showSearch is true', () => {
      render(
        <TestWrapper>
          <SettingsPanel showSearch={true} />
        </TestWrapper>
      )

      expect(screen.getByTestId('settings-search')).toBeInTheDocument()
    })

    it('should not render search component when showSearch is false', () => {
      render(
        <TestWrapper>
          <SettingsPanel showSearch={false} />
        </TestWrapper>
      )

      expect(screen.queryByTestId('settings-search')).not.toBeInTheDocument()
    })

    it('should render action buttons when showActions is true', () => {
      render(
        <TestWrapper>
          <SettingsPanel showActions={true} />
        </TestWrapper>
      )

      expect(screen.getByText('Import')).toBeInTheDocument()
      expect(screen.getByText('Export')).toBeInTheDocument()
      expect(screen.getByText('Reset')).toBeInTheDocument()
    })

    it('should not render action buttons when showActions is false', () => {
      render(
        <TestWrapper>
          <SettingsPanel showActions={false} />
        </TestWrapper>
      )

      expect(screen.queryByText('Import')).not.toBeInTheDocument()
      expect(screen.queryByText('Export')).not.toBeInTheDocument()
      expect(screen.queryByText('Reset')).not.toBeInTheDocument()
    })
  })

  describe('State Display', () => {
    it('should show unsaved changes indicator when there are changes', () => {
      const mockWithChanges = {
        ...mockUseSettings,
        hasChanges: true,
        changedFields: ['theme', 'language'],
      }
      vi.mocked(useSettings).mockReturnValue(mockWithChanges)

      render(
        <TestWrapper>
          <SettingsPanel />
        </TestWrapper>
      )

      expect(screen.getByText('2 unsaved changes')).toBeInTheDocument()
    })

    it('should show auto-saving indicator when auto-saving', () => {
      const mockAutoSaving = {
        ...mockUseSettings,
        autoSave: {
          ...mockUseSettings.autoSave,
          isAutoSaving: true,
        },
      }
      vi.mocked(useSettings).mockReturnValue(mockAutoSaving)

      render(
        <TestWrapper>
          <SettingsPanel autoSave={true} />
        </TestWrapper>
      )

      expect(screen.getByText('Auto-saving...')).toBeInTheDocument()
    })

    it('should show loading overlay when loading', () => {
      const mockLoading = {
        ...mockUseSettings,
        ui: {
          ...mockUseSettings.ui,
          isLoading: true,
        },
      }
      vi.mocked(useSettings).mockReturnValue(mockLoading)

      render(
        <TestWrapper>
          <SettingsPanel />
        </TestWrapper>
      )

      expect(screen.getByText('Loading settings...')).toBeInTheDocument()
    })

    it('should show validation errors when present', () => {
      const mockWithErrors = {
        ...mockUseSettings,
        ui: {
          ...mockUseSettings.ui,
          errors: {
            theme: 'Invalid theme',
            language: 'Invalid language',
          },
        },
      }
      vi.mocked(useSettings).mockReturnValue(mockWithErrors)

      render(
        <TestWrapper>
          <SettingsPanel />
        </TestWrapper>
      )

      expect(screen.getByText('Validation Errors')).toBeInTheDocument()
      expect(screen.getByText('theme: Invalid theme')).toBeInTheDocument()
      expect(screen.getByText('language: Invalid language')).toBeInTheDocument()
    })
  })

  describe('User Interactions', () => {
    it('should call setActiveCategory when tab is clicked', async () => {
      render(
        <TestWrapper>
          <SettingsPanel />
        </TestWrapper>
      )

      const tabs = screen.getByTestId('tabs')
      fireEvent.change(tabs, { target: { value: 'appearance' } })

      await waitFor(() => {
        expect(mockUseSettings.actions.setActiveCategory).toHaveBeenCalledWith('appearance')
      })
    })

    it('should call savePreferences when save button is clicked', async () => {
      const mockWithChanges = {
        ...mockUseSettings,
        hasChanges: true,
      }
      vi.mocked(useSettings).mockReturnValue(mockWithChanges)

      render(
        <TestWrapper>
          <SettingsPanel autoSave={false} />
        </TestWrapper>
      )

      const saveButton = screen.getByText('Save Changes')
      fireEvent.click(saveButton)

      await waitFor(() => {
        expect(mockUseSettings.actions.savePreferences).toHaveBeenCalled()
      })
    })

    it('should call openResetDialog when reset button is clicked', async () => {
      render(
        <TestWrapper>
          <SettingsPanel />
        </TestWrapper>
      )

      const resetButton = screen.getByText('Reset')
      fireEvent.click(resetButton)

      await waitFor(() => {
        expect(mockUseSettings.actions.openResetDialog).toHaveBeenCalled()
      })
    })

    it('should call openExportDialog when export button is clicked', async () => {
      render(
        <TestWrapper>
          <SettingsPanel />
        </TestWrapper>
      )

      const exportButton = screen.getByText('Export')
      fireEvent.click(exportButton)

      await waitFor(() => {
        expect(mockUseSettings.actions.openExportDialog).toHaveBeenCalled()
      })
    })

    it('should call openImportDialog when import button is clicked', async () => {
      render(
        <TestWrapper>
          <SettingsPanel />
        </TestWrapper>
      )

      const importButton = screen.getByText('Import')
      fireEvent.click(importButton)

      await waitFor(() => {
        expect(mockUseSettings.actions.openImportDialog).toHaveBeenCalled()
      })
    })
  })

  describe('Button States', () => {
    it('should disable save button when no changes', () => {
      render(
        <TestWrapper>
          <SettingsPanel autoSave={false} />
        </TestWrapper>
      )

      const saveButton = screen.getByText('Save Changes')
      expect(saveButton).toBeDisabled()
    })

    it('should disable save button when saving', () => {
      const mockSaving = {
        ...mockUseSettings,
        hasChanges: true,
        ui: {
          ...mockUseSettings.ui,
          isSaving: true,
        },
      }
      vi.mocked(useSettings).mockReturnValue(mockSaving)

      render(
        <TestWrapper>
          <SettingsPanel autoSave={false} />
        </TestWrapper>
      )

      const saveButton = screen.getByText('Saving...')
      expect(saveButton).toBeDisabled()
    })

    it('should disable action buttons when saving', () => {
      const mockSaving = {
        ...mockUseSettings,
        ui: {
          ...mockUseSettings.ui,
          isSaving: true,
        },
      }
      vi.mocked(useSettings).mockReturnValue(mockSaving)

      render(
        <TestWrapper>
          <SettingsPanel />
        </TestWrapper>
      )

      expect(screen.getByText('Import')).toBeDisabled()
      expect(screen.getByText('Export')).toBeDisabled()
      expect(screen.getByText('Reset')).toBeDisabled()
    })
  })

  describe('Compact Settings Panel', () => {
    it('should render compact version without search and actions', () => {
      render(
        <TestWrapper>
          <CompactSettingsPanel />
        </TestWrapper>
      )

      expect(screen.queryByTestId('settings-search')).not.toBeInTheDocument()
      expect(screen.queryByText('Import')).not.toBeInTheDocument()
      expect(screen.queryByText('Export')).not.toBeInTheDocument()
    })
  })

  describe('Category Settings Panel', () => {
    it('should set active category on mount', () => {
      render(
        <TestWrapper>
          <CategorySettingsPanel category="appearance" />
        </TestWrapper>
      )

      expect(mockUseSettings.actions.setActiveCategory).toHaveBeenCalledWith('appearance')
    })

    it('should render preferences form for specific category', () => {
      render(
        <TestWrapper>
          <CategorySettingsPanel category="appearance" />
        </TestWrapper>
      )

      const preferencesForm = screen.getByTestId('preferences-form')
      expect(preferencesForm).toHaveAttribute('data-category', 'appearance')
    })
  })
})
