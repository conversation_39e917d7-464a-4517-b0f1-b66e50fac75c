/**
 * Activity Log Viewer Component
 * Displays activity logs with filtering and search capabilities
 */

import React, { useState, useMemo } from 'react'
import { useActivityLogs } from '@/hooks/api/useAudit'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { CalendarIcon, FilterIcon, RefreshCwIcon, SearchIcon } from 'lucide-react'
import { format } from 'date-fns'
import type { ActivityLog, ActivityLogFilter, ErrorSeverity } from '@/types/api'

interface ActivityLogViewerProps {
  userId?: number
  showFilters?: boolean
  showSearch?: boolean
  autoRefresh?: boolean
  refreshInterval?: number
  onLogClick?: (log: ActivityLog) => void
  className?: string
}

export function ActivityLogViewer({
  userId,
  showFilters = true,
  showSearch = true,
  autoRefresh = false,
  refreshInterval = 30000,
  onLogClick,
  className = ''
}: ActivityLogViewerProps) {
  const [filters, setFilters] = useState<ActivityLogFilter>({
    user_id: userId,
  })
  const [searchTerm, setSearchTerm] = useState('')
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize] = useState(20)

  const queryParams = useMemo(() => ({
    ...filters,
    skip: (currentPage - 1) * pageSize,
    limit: pageSize,
  }), [filters, currentPage, pageSize])

  const { data: activityLogs, isLoading, error, refetch } = useActivityLogs(queryParams)

  const filteredLogs = useMemo(() => {
    if (!activityLogs?.items) return []
    
    if (!searchTerm) return activityLogs.items
    
    return activityLogs.items.filter(log => 
      log.action_description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      log.action_type.toLowerCase().includes(searchTerm.toLowerCase()) ||
      log.target_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      log.category?.toLowerCase().includes(searchTerm.toLowerCase())
    )
  }, [activityLogs?.items, searchTerm])

  const getSeverityColor = (severity: ErrorSeverity): string => {
    switch (severity) {
      case 'CRITICAL': return 'bg-red-100 text-red-800 border-red-300'
      case 'HIGH': return 'bg-orange-100 text-orange-800 border-orange-300'
      case 'MEDIUM': return 'bg-yellow-100 text-yellow-800 border-yellow-300'
      case 'LOW': return 'bg-blue-100 text-blue-800 border-blue-300'
      case 'INFO': return 'bg-green-100 text-green-800 border-green-300'
      default: return 'bg-gray-100 text-gray-800 border-gray-300'
    }
  }

  const getStatusColor = (status: string): string => {
    switch (status.toUpperCase()) {
      case 'SUCCESS': return 'bg-green-100 text-green-800 border-green-300'
      case 'FAILED': return 'bg-red-100 text-red-800 border-red-300'
      case 'WARNING': return 'bg-yellow-100 text-yellow-800 border-yellow-300'
      default: return 'bg-gray-100 text-gray-800 border-gray-300'
    }
  }

  const handleFilterChange = (key: keyof ActivityLogFilter, value: any) => {
    setFilters(prev => ({ ...prev, [key]: value }))
    setCurrentPage(1)
  }

  const clearFilters = () => {
    setFilters({ user_id: userId })
    setSearchTerm('')
    setCurrentPage(1)
  }

  if (error) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="text-red-600">Error Loading Activity Logs</CardTitle>
          <CardDescription>
            {error.message || 'Failed to load activity logs'}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Button onClick={() => refetch()} variant="outline">
            <RefreshCwIcon className="h-4 w-4 mr-2" />
            Retry
          </Button>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span>Activity Logs</span>
          <div className="flex items-center space-x-2">
            {autoRefresh && (
              <Badge variant="secondary">
                Auto-refresh: {refreshInterval / 1000}s
              </Badge>
            )}
            <Button
              variant="outline"
              size="sm"
              onClick={() => refetch()}
              disabled={isLoading}
            >
              <RefreshCwIcon className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
            </Button>
          </div>
        </CardTitle>
        <CardDescription>
          View and filter system activity logs
        </CardDescription>
      </CardHeader>
      
      <CardContent>
        {/* Search and Filters */}
        {(showSearch || showFilters) && (
          <div className="mb-6 space-y-4">
            {showSearch && (
              <div className="flex items-center space-x-2">
                <SearchIcon className="h-4 w-4 text-gray-500" />
                <Input
                  placeholder="Search logs..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="flex-1"
                />
              </div>
            )}
            
            {showFilters && (
              <div className="flex items-center space-x-4 flex-wrap">
                <div className="flex items-center space-x-2">
                  <FilterIcon className="h-4 w-4 text-gray-500" />
                  <span className="text-sm text-gray-600">Filters:</span>
                </div>
                
                <Select
                  value={filters.severity || ''}
                  onValueChange={(value) => handleFilterChange('severity', value || undefined)}
                >
                  <SelectTrigger className="w-40">
                    <SelectValue placeholder="Severity" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">All Severities</SelectItem>
                    <SelectItem value="CRITICAL">Critical</SelectItem>
                    <SelectItem value="HIGH">High</SelectItem>
                    <SelectItem value="MEDIUM">Medium</SelectItem>
                    <SelectItem value="LOW">Low</SelectItem>
                    <SelectItem value="INFO">Info</SelectItem>
                  </SelectContent>
                </Select>
                
                <Select
                  value={filters.status || ''}
                  onValueChange={(value) => handleFilterChange('status', value || undefined)}
                >
                  <SelectTrigger className="w-40">
                    <SelectValue placeholder="Status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">All Status</SelectItem>
                    <SelectItem value="SUCCESS">Success</SelectItem>
                    <SelectItem value="FAILED">Failed</SelectItem>
                    <SelectItem value="WARNING">Warning</SelectItem>
                  </SelectContent>
                </Select>
                
                <Select
                  value={filters.category || ''}
                  onValueChange={(value) => handleFilterChange('category', value || undefined)}
                >
                  <SelectTrigger className="w-40">
                    <SelectValue placeholder="Category" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">All Categories</SelectItem>
                    <SelectItem value="AUTHENTICATION">Authentication</SelectItem>
                    <SelectItem value="AUTHORIZATION">Authorization</SelectItem>
                    <SelectItem value="DATA_CHANGE">Data Change</SelectItem>
                    <SelectItem value="SYSTEM">System</SelectItem>
                    <SelectItem value="SECURITY">Security</SelectItem>
                  </SelectContent>
                </Select>
                
                <Button
                  variant="outline"
                  size="sm"
                  onClick={clearFilters}
                  className="text-gray-600"
                >
                  Clear Filters
                </Button>
              </div>
            )}
          </div>
        )}

        {/* Activity Logs Table */}
        <div className="border rounded-lg overflow-hidden">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Time</TableHead>
                <TableHead>Action</TableHead>
                <TableHead>Description</TableHead>
                <TableHead>Target</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Severity</TableHead>
                <TableHead>Category</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {isLoading ? (
                <TableRow>
                  <TableCell colSpan={7} className="text-center py-8">
                    <div className="flex items-center justify-center">
                      <RefreshCwIcon className="h-4 w-4 animate-spin mr-2" />
                      Loading activity logs...
                    </div>
                  </TableCell>
                </TableRow>
              ) : filteredLogs.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={7} className="text-center py-8 text-gray-500">
                    No activity logs found
                  </TableCell>
                </TableRow>
              ) : (
                filteredLogs.map((log) => (
                  <TableRow
                    key={log.id}
                    className={`hover:bg-gray-50 ${onLogClick ? 'cursor-pointer' : ''}`}
                    onClick={() => onLogClick?.(log)}
                  >
                    <TableCell className="font-mono text-sm">
                      {format(new Date(log.created_at), 'MMM dd, HH:mm:ss')}
                    </TableCell>
                    <TableCell>
                      <code className="text-sm bg-gray-100 px-2 py-1 rounded">
                        {log.action_type}
                      </code>
                    </TableCell>
                    <TableCell className="max-w-md truncate">
                      {log.action_description}
                    </TableCell>
                    <TableCell>
                      {log.target_name ? (
                        <div className="text-sm">
                          <div className="font-medium">{log.target_name}</div>
                          {log.target_type && (
                            <div className="text-gray-500">{log.target_type}</div>
                          )}
                        </div>
                      ) : (
                        <span className="text-gray-400">-</span>
                      )}
                    </TableCell>
                    <TableCell>
                      <Badge className={getStatusColor(log.status)}>
                        {log.status}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <Badge className={getSeverityColor(log.severity)}>
                        {log.severity}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      {log.category ? (
                        <Badge variant="outline">{log.category}</Badge>
                      ) : (
                        <span className="text-gray-400">-</span>
                      )}
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>

        {/* Pagination */}
        {activityLogs && activityLogs.pages > 1 && (
          <div className="mt-4 flex items-center justify-between">
            <div className="text-sm text-gray-600">
              Showing {((currentPage - 1) * pageSize) + 1} to {Math.min(currentPage * pageSize, activityLogs.total)} of {activityLogs.total} logs
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                disabled={currentPage === 1}
              >
                Previous
              </Button>
              <span className="text-sm">
                Page {currentPage} of {activityLogs.pages}
              </span>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(prev => Math.min(activityLogs.pages, prev + 1))}
                disabled={currentPage === activityLogs.pages}
              >
                Next
              </Button>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}