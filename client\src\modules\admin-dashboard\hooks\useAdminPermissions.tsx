/**
 * Admin permissions hook for role-based access control
 */

import React from 'react'
import { useAuth } from '@/hooks/useAuth'
import { hasAdminPermission, requiresConfirmation, getPermissionErrorMessage } from '../utils'

export interface AdminPermissions {
  // Permission checking functions
  canRead: (resource: string) => boolean
  canWrite: (resource: string) => boolean
  canDelete: (resource: string) => boolean
  canPerform: (action: string) => boolean

  // Action validation
  requiresConfirmation: (action: string) => boolean
  getErrorMessage: (action: string) => string

  // Convenience permission checks
  canManageUsers: boolean
  canViewSystem: boolean
  canModifySystem: boolean
  canViewSecurity: boolean
  canModifySecurity: boolean
  canViewAudit: boolean
  canExportAudit: boolean
  canViewConfig: boolean
  canModifyConfig: boolean
  canViewProjects: boolean
  canModifyProjects: boolean
  canViewComponents: boolean
  canModifyComponents: boolean

  // Overall admin status
  isAdmin: boolean
  isAuthenticated: boolean
}

/**
 * Hook for admin permission checking and validation
 */
export function useAdminPermissions(): AdminPermissions {
  const { user, isAdmin, isAuthenticated } = useAuth()

  const canRead = (resource: string): boolean => {
    return hasAdminPermission(`${resource}:read`, user?.role, isAdmin())
  }

  const canWrite = (resource: string): boolean => {
    return hasAdminPermission(`${resource}:write`, user?.role, isAdmin())
  }

  const canDelete = (resource: string): boolean => {
    return hasAdminPermission(`${resource}:delete`, user?.role, isAdmin())
  }

  const canPerform = (action: string): boolean => {
    return hasAdminPermission(action, user?.role, isAdmin())
  }

  return {
    // Permission checking functions
    canRead,
    canWrite,
    canDelete,
    canPerform,

    // Action validation
    requiresConfirmation,
    getErrorMessage: getPermissionErrorMessage,

    // Convenience permission checks
    canManageUsers: canWrite('user'),
    canViewSystem: canRead('system'),
    canModifySystem: canWrite('system'),
    canViewSecurity: canRead('security'),
    canModifySecurity: canWrite('security'),
    canViewAudit: canRead('audit'),
    canExportAudit: canPerform('audit:export'),
    canViewConfig: canRead('config'),
    canModifyConfig: canWrite('config'),
    canViewProjects: canRead('project'),
    canModifyProjects: canWrite('project'),
    canViewComponents: canRead('component'),
    canModifyComponents: canWrite('component'),

    // Overall admin status
    isAdmin: isAdmin(),
    isAuthenticated: isAuthenticated,
  }
}

/**
 * Higher-order component for permission-based rendering
 */
export function withAdminPermission<T extends object>(
  Component: React.ComponentType<T>,
  requiredPermission: string,
  fallback?: React.ReactNode
) {
  return function PermissionWrappedComponent(props: T) {
    const permissions = useAdminPermissions()

    if (!permissions.canPerform(requiredPermission)) {
      if (fallback) {
        return <>{fallback}</>
      }

      return (
        <div className="rounded-md bg-yellow-50 p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                <path
                  fillRule="evenodd"
                  d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                  clipRule="evenodd"
                />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-yellow-800">Permission Required</h3>
              <div className="mt-2 text-sm text-yellow-700">
                {permissions.getErrorMessage(requiredPermission)}
              </div>
            </div>
          </div>
        </div>
      )
    }

    return <Component {...props} />
  }
}

/**
 * Hook for permission-based conditional rendering
 */
export function usePermissionGuard() {
  const permissions = useAdminPermissions()

  return {
    canRender: (requiredPermission: string): boolean => {
      return permissions.canPerform(requiredPermission)
    },

    renderWithPermission: (
      requiredPermission: string,
      component: React.ReactNode,
      fallback?: React.ReactNode
    ): React.ReactNode => {
      if (permissions.canPerform(requiredPermission)) {
        return component
      }

      return (
        fallback || (
          <div className="text-sm italic text-gray-500">
            {permissions.getErrorMessage(requiredPermission)}
          </div>
        )
      )
    },

    permissions,
  }
}
