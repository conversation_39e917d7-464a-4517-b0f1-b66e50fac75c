{"Path": "D:\\Projects\\ultimate-electrical-designer", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\cad-integrator-service", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\cad-integrator-service\\src", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\cad-integrator-service\\src\\Controllers", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\cad-integrator-service\\src\\Controllers\\CadController.cs", "SizeKB": 0.0, "Extension": ".cs", "Type": "File", "Name": "CadController.cs"}], "Type": "Folder", "Name": "Controllers"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\cad-integrator-service\\src\\Services", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\cad-integrator-service\\src\\Services\\AutoCADService.cs", "SizeKB": 0.0, "Extension": ".cs", "Type": "File", "Name": "AutoCADService.cs"}], "Type": "Folder", "Name": "Services"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\cad-integrator-service\\src\\ultimate_electrical_designer.CadIntegrator.csproj", "SizeKB": 0.0, "Extension": ".c<PERSON><PERSON>j", "Type": "File", "Name": "ultimate_electrical_designer.CadIntegrator.csproj"}], "Type": "Folder", "Name": "src"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\cad-integrator-service\\Dockerfile", "SizeKB": 0.0, "Extension": "", "Type": "File", "Name": "Dockerfile"}], "Type": "Folder", "Name": "cad-integrator-service"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\docs", "Children": [], "Type": "Folder", "Name": "docs"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\scripts", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\scripts\\generate-failing-report.js", "SizeKB": 6.43, "Extension": ".js", "Type": "File", "Name": "generate-failing-report.js"}], "Type": "Folder", "Name": "scripts"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\app", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\app\\(auth)", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\app\\(auth)\\login", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\app\\(auth)\\login\\page.tsx", "SizeKB": 3.04, "Extension": ".tsx", "Type": "File", "Name": "page.tsx"}], "Type": "Folder", "Name": "login"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\app\\(auth)\\register", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\app\\(auth)\\register\\page.tsx", "SizeKB": 4.2, "Extension": ".tsx", "Type": "File", "Name": "page.tsx"}], "Type": "Folder", "Name": "register"}], "Type": "Folder", "Name": "(auth)"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\app\\admin", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\app\\admin\\users", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\app\\admin\\users\\page.tsx", "SizeKB": 0.43, "Extension": ".tsx", "Type": "File", "Name": "page.tsx"}], "Type": "Folder", "Name": "users"}], "Type": "Folder", "Name": "admin"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\app\\components", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\app\\components\\[id]", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\app\\components\\[id]\\edit", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\app\\components\\[id]\\edit\\page.tsx", "SizeKB": 4.93, "Extension": ".tsx", "Type": "File", "Name": "page.tsx"}], "Type": "Folder", "Name": "edit"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\app\\components\\[id]\\page.tsx", "SizeKB": 7.61, "Extension": ".tsx", "Type": "File", "Name": "page.tsx"}], "Type": "Folder", "Name": "[id]"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\app\\components\\new", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\app\\components\\new\\page.tsx", "SizeKB": 2.11, "Extension": ".tsx", "Type": "File", "Name": "page.tsx"}], "Type": "Folder", "Name": "new"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\app\\components\\page.tsx", "SizeKB": 7.7, "Extension": ".tsx", "Type": "File", "Name": "page.tsx"}], "Type": "Folder", "Name": "components"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\app\\dashboard", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\app\\dashboard\\page.tsx", "SizeKB": 3.74, "Extension": ".tsx", "Type": "File", "Name": "page.tsx"}], "Type": "Folder", "Name": "dashboard"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\app\\profile", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\app\\profile\\page.tsx", "SizeKB": 0.4, "Extension": ".tsx", "Type": "File", "Name": "page.tsx"}], "Type": "Folder", "Name": "profile"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\app\\globals.css", "SizeKB": 11.07, "Extension": ".css", "Type": "File", "Name": "globals.css"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\app\\layout.tsx", "SizeKB": 4.0, "Extension": ".tsx", "Type": "File", "Name": "layout.tsx"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\app\\page.tsx", "SizeKB": 0.12, "Extension": ".tsx", "Type": "File", "Name": "page.tsx"}], "Type": "Folder", "Name": "app"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\components", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\components\\__tests__", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\components\\__tests__\\theme-provider.test.tsx", "SizeKB": 5.14, "Extension": ".tsx", "Type": "File", "Name": "theme-provider.test.tsx"}], "Type": "Folder", "Name": "__tests__"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\components\\admin", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\components\\admin\\AdminDashboard.tsx", "SizeKB": 0.44, "Extension": ".tsx", "Type": "File", "Name": "AdminDashboard.tsx"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\components\\admin\\UserManagement.tsx", "SizeKB": 13.9, "Extension": ".tsx", "Type": "File", "Name": "UserManagement.tsx"}], "Type": "Folder", "Name": "admin"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\components\\audit", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\components\\audit\\ActivityLogViewer.tsx", "SizeKB": 13.16, "Extension": ".tsx", "Type": "File", "Name": "ActivityLogViewer.tsx"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\components\\audit\\AuditTrailViewer.tsx", "SizeKB": 17.98, "Extension": ".tsx", "Type": "File", "Name": "AuditTrailViewer.tsx"}], "Type": "Folder", "Name": "audit"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\components\\auth", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\components\\auth\\__tests__", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\components\\auth\\__tests__\\LoginForm.test.tsx", "SizeKB": 5.2, "Extension": ".tsx", "Type": "File", "Name": "LoginForm.test.tsx"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\components\\auth\\__tests__\\RegisterForm.test.tsx", "SizeKB": 10.11, "Extension": ".tsx", "Type": "File", "Name": "RegisterForm.test.tsx"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\components\\auth\\__tests__\\RouteGuard.test.tsx", "SizeKB": 10.16, "Extension": ".tsx", "Type": "File", "Name": "RouteGuard.test.tsx"}], "Type": "Folder", "Name": "__tests__"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\components\\auth\\forms", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\components\\auth\\forms\\shared", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\components\\auth\\forms\\shared\\FormField.tsx", "SizeKB": 8.29, "Extension": ".tsx", "Type": "File", "Name": "FormField.tsx"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\components\\auth\\forms\\shared\\index.ts", "SizeKB": 0.37, "Extension": ".ts", "Type": "File", "Name": "index.ts"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\components\\auth\\forms\\shared\\PasswordInput.tsx", "SizeKB": 8.04, "Extension": ".tsx", "Type": "File", "Name": "PasswordInput.tsx"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\components\\auth\\forms\\shared\\PasswordStrength.tsx", "SizeKB": 5.88, "Extension": ".tsx", "Type": "File", "Name": "PasswordStrength.tsx"}], "Type": "Folder", "Name": "shared"}], "Type": "Folder", "Name": "forms"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\components\\auth\\layouts", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\components\\auth\\layouts\\AuthLayout.tsx", "SizeKB": 8.91, "Extension": ".tsx", "Type": "File", "Name": "AuthLayout.tsx"}], "Type": "Folder", "Name": "layouts"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\components\\auth\\utils", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\components\\auth\\utils\\__tests__", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\components\\auth\\utils\\__tests__\\authHelpers.test.ts", "SizeKB": 9.69, "Extension": ".ts", "Type": "File", "Name": "authHelpers.test.ts"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\components\\auth\\utils\\__tests__\\validation.test.ts", "SizeKB": 8.02, "Extension": ".ts", "Type": "File", "Name": "validation.test.ts"}], "Type": "Folder", "Name": "__tests__"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\components\\auth\\utils\\authHelpers.ts", "SizeKB": 8.9, "Extension": ".ts", "Type": "File", "Name": "authHelpers.ts"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\components\\auth\\utils\\validation.ts", "SizeKB": 8.98, "Extension": ".ts", "Type": "File", "Name": "validation.ts"}], "Type": "Folder", "Name": "utils"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\components\\auth\\LoginForm.tsx", "SizeKB": 9.68, "Extension": ".tsx", "Type": "File", "Name": "LoginForm.tsx"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\components\\auth\\RegisterForm.tsx", "SizeKB": 10.69, "Extension": ".tsx", "Type": "File", "Name": "RegisterForm.tsx"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\components\\auth\\RoleManagement.tsx", "SizeKB": 16.5, "Extension": ".tsx", "Type": "File", "Name": "RoleManagement.tsx"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\components\\auth\\RouteGuard.tsx", "SizeKB": 4.81, "Extension": ".tsx", "Type": "File", "Name": "RouteGuard.tsx"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\components\\auth\\UserProfile.tsx", "SizeKB": 11.69, "Extension": ".tsx", "Type": "File", "Name": "UserProfile.tsx"}], "Type": "Folder", "Name": "auth"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\components\\common", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\components\\common\\Footer.tsx", "SizeKB": 2.34, "Extension": ".tsx", "Type": "File", "Name": "Footer.tsx"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\components\\common\\Header.tsx", "SizeKB": 15.29, "Extension": ".tsx", "Type": "File", "Name": "Header.tsx"}], "Type": "Folder", "Name": "common"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\components\\data-display", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\components\\data-display\\DataTable.tsx", "SizeKB": 24.53, "Extension": ".tsx", "Type": "File", "Name": "DataTable.tsx"}], "Type": "Folder", "Name": "data-display"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\components\\layout", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\components\\layout\\DashboardLayout.tsx", "SizeKB": 2.14, "Extension": ".tsx", "Type": "File", "Name": "DashboardLayout.tsx"}], "Type": "Folder", "Name": "layout"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\components\\navigation", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\components\\navigation\\Breadcrumbs.tsx", "SizeKB": 3.01, "Extension": ".tsx", "Type": "File", "Name": "Breadcrumbs.tsx"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\components\\navigation\\Sidebar.tsx", "SizeKB": 7.23, "Extension": ".tsx", "Type": "File", "Name": "Sidebar.tsx"}], "Type": "Folder", "Name": "navigation"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\components\\ui", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\components\\ui\\__tests__", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\components\\ui\\__tests__\\theme-toggle.test.tsx", "SizeKB": 6.87, "Extension": ".tsx", "Type": "File", "Name": "theme-toggle.test.tsx"}], "Type": "Folder", "Name": "__tests__"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\components\\ui\\accordion.tsx", "SizeKB": 2.03, "Extension": ".tsx", "Type": "File", "Name": "accordion.tsx"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\components\\ui\\alert-dialog.tsx", "SizeKB": 3.72, "Extension": ".tsx", "Type": "File", "Name": "alert-dialog.tsx"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\components\\ui\\alert.tsx", "SizeKB": 1.55, "Extension": ".tsx", "Type": "File", "Name": "alert.tsx"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\components\\ui\\aspect-ratio.tsx", "SizeKB": 0.27, "Extension": ".tsx", "Type": "File", "Name": "aspect-ratio.tsx"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\components\\ui\\avatar.tsx", "SizeKB": 1.05, "Extension": ".tsx", "Type": "File", "Name": "avatar.tsx"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\components\\ui\\badge.tsx", "SizeKB": 1.41, "Extension": ".tsx", "Type": "File", "Name": "badge.tsx"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\components\\ui\\breadcrumb.tsx", "SizeKB": 2.27, "Extension": ".tsx", "Type": "File", "Name": "breadcrumb.tsx"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\components\\ui\\button.tsx", "SizeKB": 1.8, "Extension": ".tsx", "Type": "File", "Name": "button.tsx"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\components\\ui\\calendar-rac.tsx", "SizeKB": 4.6, "Extension": ".tsx", "Type": "File", "Name": "calendar-rac.tsx"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\components\\ui\\calendar.tsx", "SizeKB": 3.79, "Extension": ".tsx", "Type": "File", "Name": "calendar.tsx"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\components\\ui\\card.tsx", "SizeKB": 1.78, "Extension": ".tsx", "Type": "File", "Name": "card.tsx"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\components\\ui\\carousel.tsx", "SizeKB": 5.35, "Extension": ".tsx", "Type": "File", "Name": "carousel.tsx"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\components\\ui\\chart.tsx", "SizeKB": 9.41, "Extension": ".tsx", "Type": "File", "Name": "chart.tsx"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\components\\ui\\checkbox-tree.tsx", "SizeKB": 2.71, "Extension": ".tsx", "Type": "File", "Name": "checkbox-tree.tsx"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\components\\ui\\checkbox.tsx", "SizeKB": 2.27, "Extension": ".tsx", "Type": "File", "Name": "checkbox.tsx"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\components\\ui\\collapsible.tsx", "SizeKB": 0.72, "Extension": ".tsx", "Type": "File", "Name": "collapsible.tsx"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\components\\ui\\command.tsx", "SizeKB": 4.39, "Extension": ".tsx", "Type": "File", "Name": "command.tsx"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\components\\ui\\context-menu.tsx", "SizeKB": 7.92, "Extension": ".tsx", "Type": "File", "Name": "context-menu.tsx"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\components\\ui\\cropper.tsx", "SizeKB": 1.55, "Extension": ".tsx", "Type": "File", "Name": "cropper.tsx"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\components\\ui\\datefield-rac.tsx", "SizeKB": 2.72, "Extension": ".tsx", "Type": "File", "Name": "datefield-rac.tsx"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\components\\ui\\dialog.tsx", "SizeKB": 3.66, "Extension": ".tsx", "Type": "File", "Name": "dialog.tsx"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\components\\ui\\drawer.tsx", "SizeKB": 4.14, "Extension": ".tsx", "Type": "File", "Name": "drawer.tsx"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\components\\ui\\dropdown-menu.tsx", "SizeKB": 9.0, "Extension": ".tsx", "Type": "File", "Name": "dropdown-menu.tsx"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\components\\ui\\form.tsx", "SizeKB": 3.6, "Extension": ".tsx", "Type": "File", "Name": "form.tsx"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\components\\ui\\hover-card.tsx", "SizeKB": 1.55, "Extension": ".tsx", "Type": "File", "Name": "hover-card.tsx"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\components\\ui\\input-otp.tsx", "SizeKB": 2.16, "Extension": ".tsx", "Type": "File", "Name": "input-otp.tsx"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\components\\ui\\input.tsx", "SizeKB": 1.36, "Extension": ".tsx", "Type": "File", "Name": "input.tsx"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\components\\ui\\label.tsx", "SizeKB": 0.58, "Extension": ".tsx", "Type": "File", "Name": "label.tsx"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\components\\ui\\menubar.tsx", "SizeKB": 8.12, "Extension": ".tsx", "Type": "File", "Name": "menubar.tsx"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\components\\ui\\multiselect.tsx", "SizeKB": 18.94, "Extension": ".tsx", "Type": "File", "Name": "multiselect.tsx"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\components\\ui\\navigation-menu.tsx", "SizeKB": 6.4, "Extension": ".tsx", "Type": "File", "Name": "navigation-menu.tsx"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\components\\ui\\pagination.tsx", "SizeKB": 2.55, "Extension": ".tsx", "Type": "File", "Name": "pagination.tsx"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\components\\ui\\popover.tsx", "SizeKB": 1.78, "Extension": ".tsx", "Type": "File", "Name": "popover.tsx"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\components\\ui\\progress.tsx", "SizeKB": 0.69, "Extension": ".tsx", "Type": "File", "Name": "progress.tsx"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\components\\ui\\radio-group.tsx", "SizeKB": 1.51, "Extension": ".tsx", "Type": "File", "Name": "radio-group.tsx"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\components\\ui\\resizable.tsx", "SizeKB": 1.88, "Extension": ".tsx", "Type": "File", "Name": "resizable.tsx"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\components\\ui\\scroll-area.tsx", "SizeKB": 1.45, "Extension": ".tsx", "Type": "File", "Name": "scroll-area.tsx"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\components\\ui\\select-native.tsx", "SizeKB": 1.34, "Extension": ".tsx", "Type": "File", "Name": "select-native.tsx"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\components\\ui\\select.tsx", "SizeKB": 5.66, "Extension": ".tsx", "Type": "File", "Name": "select.tsx"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\components\\ui\\separator.tsx", "SizeKB": 0.68, "Extension": ".tsx", "Type": "File", "Name": "separator.tsx"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\components\\ui\\sheet.tsx", "SizeKB": 3.98, "Extension": ".tsx", "Type": "File", "Name": "sheet.tsx"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\components\\ui\\sidebar.tsx", "SizeKB": 21.13, "Extension": ".tsx", "Type": "File", "Name": "sidebar.tsx"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\components\\ui\\skeleton.tsx", "SizeKB": 0.27, "Extension": ".tsx", "Type": "File", "Name": "skeleton.tsx"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\components\\ui\\slider.tsx", "SizeKB": 3.62, "Extension": ".tsx", "Type": "File", "Name": "slider.tsx"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\components\\ui\\sonner.tsx", "SizeKB": 0.66, "Extension": ".tsx", "Type": "File", "Name": "sonner.tsx"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\components\\ui\\stepper.tsx", "SizeKB": 6.82, "Extension": ".tsx", "Type": "File", "Name": "stepper.tsx"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\components\\ui\\switch.tsx", "SizeKB": 1.01, "Extension": ".tsx", "Type": "File", "Name": "switch.tsx"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\components\\ui\\table.tsx", "SizeKB": 2.13, "Extension": ".tsx", "Type": "File", "Name": "table.tsx"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\components\\ui\\tabs.tsx", "SizeKB": 1.63, "Extension": ".tsx", "Type": "File", "Name": "tabs.tsx"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\components\\ui\\textarea.tsx", "SizeKB": 0.73, "Extension": ".tsx", "Type": "File", "Name": "textarea.tsx"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\components\\ui\\theme-toggle.tsx", "SizeKB": 6.47, "Extension": ".tsx", "Type": "File", "Name": "theme-toggle.tsx"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\components\\ui\\timeline.tsx", "SizeKB": 5.38, "Extension": ".tsx", "Type": "File", "Name": "timeline.tsx"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\components\\ui\\toast.tsx", "SizeKB": 4.25, "Extension": ".tsx", "Type": "File", "Name": "toast.tsx"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\components\\ui\\toaster.tsx", "SizeKB": 0.96, "Extension": ".tsx", "Type": "File", "Name": "toaster.tsx"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\components\\ui\\toggle-group.tsx", "SizeKB": 1.85, "Extension": ".tsx", "Type": "File", "Name": "toggle-group.tsx"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\components\\ui\\toggle.tsx", "SizeKB": 1.48, "Extension": ".tsx", "Type": "File", "Name": "toggle.tsx"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\components\\ui\\tooltip.tsx", "SizeKB": 1.84, "Extension": ".tsx", "Type": "File", "Name": "tooltip.tsx"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\components\\ui\\tree.tsx", "SizeKB": 5.06, "Extension": ".tsx", "Type": "File", "Name": "tree.tsx"}], "Type": "Folder", "Name": "ui"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\components\\theme-provider.tsx", "SizeKB": 8.73, "Extension": ".tsx", "Type": "File", "Name": "theme-provider.tsx"}], "Type": "Folder", "Name": "components"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\hooks", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\hooks\\__tests__", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\hooks\\__tests__\\useAuth.test.tsx", "SizeKB": 5.12, "Extension": ".tsx", "Type": "File", "Name": "useAuth.test.tsx"}], "Type": "Folder", "Name": "__tests__"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\hooks\\api", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\hooks\\api\\__tests__", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\hooks\\api\\__tests__\\useAudit.test.tsx", "SizeKB": 15.13, "Extension": ".tsx", "Type": "File", "Name": "useAudit.test.tsx"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\hooks\\api\\__tests__\\useRbac.test.tsx", "SizeKB": 11.51, "Extension": ".tsx", "Type": "File", "Name": "useRbac.test.tsx"}], "Type": "Folder", "Name": "__tests__"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\hooks\\api\\useAudit.ts", "SizeKB": 8.5, "Extension": ".ts", "Type": "File", "Name": "useAudit.ts"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\hooks\\api\\useAuth.ts", "SizeKB": 3.95, "Extension": ".ts", "Type": "File", "Name": "useAuth.ts"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\hooks\\api\\useRbac.ts", "SizeKB": 9.32, "Extension": ".ts", "Type": "File", "Name": "useRbac.ts"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\hooks\\api\\useUsers.ts", "SizeKB": 3.31, "Extension": ".ts", "Type": "File", "Name": "useUsers.ts"}], "Type": "Folder", "Name": "api"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\hooks\\useAuth.ts", "SizeKB": 4.31, "Extension": ".ts", "Type": "File", "Name": "useAuth.ts"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\hooks\\useRbacAuth.ts", "SizeKB": 12.24, "Extension": ".ts", "Type": "File", "Name": "useRbacAuth.ts"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\hooks\\useToast.ts", "SizeKB": 3.84, "Extension": ".ts", "Type": "File", "Name": "useToast.ts"}], "Type": "Folder", "Name": "hooks"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\lib", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\lib\\api", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\lib\\api\\__tests__", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\lib\\api\\__tests__\\client.test.ts", "SizeKB": 7.85, "Extension": ".ts", "Type": "File", "Name": "client.test.ts"}], "Type": "Folder", "Name": "__tests__"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\lib\\api\\audit.ts", "SizeKB": 10.1, "Extension": ".ts", "Type": "File", "Name": "audit.ts"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\lib\\api\\client.ts", "SizeKB": 7.3, "Extension": ".ts", "Type": "File", "Name": "client.ts"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\lib\\api\\rbac.ts", "SizeKB": 7.49, "Extension": ".ts", "Type": "File", "Name": "rbac.ts"}], "Type": "Folder", "Name": "api"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\lib\\auth", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\lib\\auth\\__tests__", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\lib\\auth\\__tests__\\tokenManager.test.ts", "SizeKB": 4.74, "Extension": ".ts", "Type": "File", "Name": "tokenManager.test.ts"}], "Type": "Folder", "Name": "__tests__"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\lib\\auth\\tokenManager.ts", "SizeKB": 4.55, "Extension": ".ts", "Type": "File", "Name": "tokenManager.ts"}], "Type": "Folder", "Name": "auth"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\lib\\react-query.tsx", "SizeKB": 1.46, "Extension": ".tsx", "Type": "File", "Name": "react-query.tsx"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\lib\\utils.ts", "SizeKB": 0.16, "Extension": ".ts", "Type": "File", "Name": "utils.ts"}], "Type": "Folder", "Name": "lib"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\admin-dashboard", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\admin-dashboard\\__tests__", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\admin-dashboard\\__tests__\\components", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\admin-dashboard\\__tests__\\components\\AdminDashboardOverview.test.tsx", "SizeKB": 8.49, "Extension": ".tsx", "Type": "File", "Name": "AdminDashboardOverview.test.tsx"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\admin-dashboard\\__tests__\\components\\SystemMetricsWidget.test.tsx", "SizeKB": 9.65, "Extension": ".tsx", "Type": "File", "Name": "SystemMetricsWidget.test.tsx"}], "Type": "Folder", "Name": "components"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\admin-dashboard\\__tests__\\hooks", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\admin-dashboard\\__tests__\\hooks\\useAdminDashboardStore.test.ts", "SizeKB": 11.51, "Extension": ".ts", "Type": "File", "Name": "useAdminDashboardStore.test.ts"}], "Type": "Folder", "Name": "hooks"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\admin-dashboard\\__tests__\\utils.test.ts", "SizeKB": 10.92, "Extension": ".ts", "Type": "File", "Name": "utils.test.ts"}], "Type": "Folder", "Name": "__tests__"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\admin-dashboard\\api", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\admin-dashboard\\api\\adminDashboardApi.ts", "SizeKB": 10.46, "Extension": ".ts", "Type": "File", "Name": "adminDashboardApi.ts"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\admin-dashboard\\api\\index.ts", "SizeKB": 0.1, "Extension": ".ts", "Type": "File", "Name": "index.ts"}], "Type": "Folder", "Name": "api"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\admin-dashboard\\components", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\admin-dashboard\\components\\AdminDashboardOverview.tsx", "SizeKB": 10.7, "Extension": ".tsx", "Type": "File", "Name": "AdminDashboardOverview.tsx"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\admin-dashboard\\components\\AuditLogsWidget.tsx", "SizeKB": 11.5, "Extension": ".tsx", "Type": "File", "Name": "AuditLogsWidget.tsx"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\admin-dashboard\\components\\ComponentLibraryWidget.tsx", "SizeKB": 11.18, "Extension": ".tsx", "Type": "File", "Name": "ComponentLibraryWidget.tsx"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\admin-dashboard\\components\\index.ts", "SizeKB": 0.64, "Extension": ".ts", "Type": "File", "Name": "index.ts"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\admin-dashboard\\components\\ProjectOversightWidget.tsx", "SizeKB": 11.45, "Extension": ".tsx", "Type": "File", "Name": "ProjectOversightWidget.tsx"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\admin-dashboard\\components\\QuickAdminActionsWidget.tsx", "SizeKB": 12.59, "Extension": ".tsx", "Type": "File", "Name": "QuickAdminActionsWidget.tsx"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\admin-dashboard\\components\\SecurityMonitoringWidget.tsx", "SizeKB": 13.69, "Extension": ".tsx", "Type": "File", "Name": "SecurityMonitoringWidget.tsx"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\admin-dashboard\\components\\SystemConfigurationWidget.tsx", "SizeKB": 12.3, "Extension": ".tsx", "Type": "File", "Name": "SystemConfigurationWidget.tsx"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\admin-dashboard\\components\\SystemMetricsWidget.tsx", "SizeKB": 12.24, "Extension": ".tsx", "Type": "File", "Name": "SystemMetricsWidget.tsx"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\admin-dashboard\\components\\UserManagementWidget.tsx", "SizeKB": 12.25, "Extension": ".tsx", "Type": "File", "Name": "UserManagementWidget.tsx"}], "Type": "Folder", "Name": "components"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\admin-dashboard\\hooks", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\admin-dashboard\\hooks\\index.ts", "SizeKB": 0.18, "Extension": ".ts", "Type": "File", "Name": "index.ts"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\admin-dashboard\\hooks\\useAdminDashboardData.ts", "SizeKB": 10.06, "Extension": ".ts", "Type": "File", "Name": "useAdminDashboardData.ts"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\admin-dashboard\\hooks\\useAdminDashboardStore.ts", "SizeKB": 10.79, "Extension": ".ts", "Type": "File", "Name": "useAdminDashboardStore.ts"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\admin-dashboard\\hooks\\useAdminPermissions.tsx", "SizeKB": 4.71, "Extension": ".tsx", "Type": "File", "Name": "useAdminPermissions.tsx"}], "Type": "Folder", "Name": "hooks"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\admin-dashboard\\index.ts", "SizeKB": 0.57, "Extension": ".ts", "Type": "File", "Name": "index.ts"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\admin-dashboard\\types.ts", "SizeKB": 10.72, "Extension": ".ts", "Type": "File", "Name": "types.ts"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\admin-dashboard\\utils.ts", "SizeKB": 15.31, "Extension": ".ts", "Type": "File", "Name": "utils.ts"}], "Type": "Folder", "Name": "admin-dashboard"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\cable_sizing", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\cable_sizing\\api", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\cable_sizing\\api\\index.ts.txt", "SizeKB": 0.22, "Extension": ".txt", "Type": "File", "Name": "index.ts.txt"}], "Type": "Folder", "Name": "api"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\cable_sizing\\components", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\cable_sizing\\components\\index.ts.txt", "SizeKB": 0.2, "Extension": ".txt", "Type": "File", "Name": "index.ts.txt"}], "Type": "Folder", "Name": "components"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\cable_sizing\\hooks", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\cable_sizing\\hooks\\index.ts.txt", "SizeKB": 0.15, "Extension": ".txt", "Type": "File", "Name": "index.ts.txt"}], "Type": "Folder", "Name": "hooks"}], "Type": "Folder", "Name": "cable_sizing"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\circuits", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\circuits\\api", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\circuits\\api\\index.ts.txt", "SizeKB": 0.22, "Extension": ".txt", "Type": "File", "Name": "index.ts.txt"}], "Type": "Folder", "Name": "api"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\circuits\\components", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\circuits\\components\\index.ts.txt", "SizeKB": 0.2, "Extension": ".txt", "Type": "File", "Name": "index.ts.txt"}], "Type": "Folder", "Name": "components"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\circuits\\hooks", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\circuits\\hooks\\index.ts.txt", "SizeKB": 0.15, "Extension": ".txt", "Type": "File", "Name": "index.ts.txt"}], "Type": "Folder", "Name": "hooks"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\circuits\\index.ts.txt", "SizeKB": 0.42, "Extension": ".txt", "Type": "File", "Name": "index.ts.txt"}], "Type": "Folder", "Name": "circuits"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\components", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\components\\__tests__", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\components\\__tests__\\api", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\components\\__tests__\\api\\componentApi.test.ts", "SizeKB": 13.82, "Extension": ".ts", "Type": "File", "Name": "componentApi.test.ts"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\components\\__tests__\\api\\componentMutations.test.tsx", "SizeKB": 27.79, "Extension": ".tsx", "Type": "File", "Name": "componentMutations.test.tsx"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\components\\__tests__\\api\\componentQueries.test.tsx", "SizeKB": 17.25, "Extension": ".tsx", "Type": "File", "Name": "componentQueries.test.tsx"}], "Type": "Folder", "Name": "api"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\components\\__tests__\\components", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\components\\__tests__\\components\\atoms", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\components\\__tests__\\components\\atoms\\ComponentBadge.test.tsx", "SizeKB": 9.42, "Extension": ".tsx", "Type": "File", "Name": "ComponentBadge.test.tsx"}], "Type": "Folder", "Name": "atoms"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\components\\__tests__\\components\\BulkOperations.test.tsx", "SizeKB": 23.49, "Extension": ".tsx", "Type": "File", "Name": "BulkOperations.test.tsx"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\components\\__tests__\\components\\ComponentCard.test.tsx", "SizeKB": 9.53, "Extension": ".tsx", "Type": "File", "Name": "ComponentCard.test.tsx"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\components\\__tests__\\components\\ComponentDetails.test.tsx", "SizeKB": 20.12, "Extension": ".tsx", "Type": "File", "Name": "ComponentDetails.test.tsx"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\components\\__tests__\\components\\ComponentFilters.test.tsx", "SizeKB": 10.86, "Extension": ".tsx", "Type": "File", "Name": "ComponentFilters.test.tsx"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\components\\__tests__\\components\\ComponentForm.test.tsx", "SizeKB": 19.19, "Extension": ".tsx", "Type": "File", "Name": "ComponentForm.test.tsx"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\components\\__tests__\\components\\ComponentList.test.tsx", "SizeKB": 11.5, "Extension": ".tsx", "Type": "File", "Name": "ComponentList.test.tsx"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\components\\__tests__\\components\\ComponentSearch.test.tsx", "SizeKB": 5.05, "Extension": ".tsx", "Type": "File", "Name": "ComponentSearch.test.tsx"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\components\\__tests__\\components\\ComponentStats.test.tsx", "SizeKB": 25.1, "Extension": ".tsx", "Type": "File", "Name": "ComponentStats.test.tsx"}], "Type": "Folder", "Name": "components"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\components\\__tests__\\e2e", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\components\\__tests__\\e2e\\enhanced-component-management.spec.ts", "SizeKB": 17.6, "Extension": ".ts", "Type": "File", "Name": "enhanced-component-management.spec.ts"}], "Type": "Folder", "Name": "e2e"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\components\\__tests__\\hooks", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\components\\__tests__\\hooks\\useComponentForm.test.tsx", "SizeKB": 13.58, "Extension": ".tsx", "Type": "File", "Name": "useComponentForm.test.tsx"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\components\\__tests__\\hooks\\useComponentStore.test.tsx", "SizeKB": 14.18, "Extension": ".tsx", "Type": "File", "Name": "useComponentStore.test.tsx"}], "Type": "Folder", "Name": "hooks"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\components\\__tests__\\integration", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\components\\__tests__\\integration\\ComponentManagement.test.tsx", "SizeKB": 13.72, "Extension": ".tsx", "Type": "File", "Name": "ComponentManagement.test.tsx"}], "Type": "Folder", "Name": "integration"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\components\\__tests__\\setup", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\components\\__tests__\\setup\\test-config.ts", "SizeKB": 12.43, "Extension": ".ts", "Type": "File", "Name": "test-config.ts"}], "Type": "Folder", "Name": "setup"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\components\\__tests__\\utils.test.ts", "SizeKB": 13.43, "Extension": ".ts", "Type": "File", "Name": "utils.test.ts"}], "Type": "Folder", "Name": "__tests__"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\components\\api", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\components\\api\\componentApi.ts", "SizeKB": 9.74, "Extension": ".ts", "Type": "File", "Name": "componentApi.ts"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\components\\api\\componentMutations.ts", "SizeKB": 7.55, "Extension": ".ts", "Type": "File", "Name": "componentMutations.ts"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\components\\api\\componentQueries.ts", "SizeKB": 7.01, "Extension": ".ts", "Type": "File", "Name": "componentQueries.ts"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\components\\api\\index.ts", "SizeKB": 0.28, "Extension": ".ts", "Type": "File", "Name": "index.ts"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\components\\api\\index.ts.txt", "SizeKB": 0.29, "Extension": ".txt", "Type": "File", "Name": "index.ts.txt"}], "Type": "Folder", "Name": "api"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\components\\components", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\components\\components\\atoms", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\components\\components\\atoms\\ComponentBadge.tsx", "SizeKB": 5.33, "Extension": ".tsx", "Type": "File", "Name": "ComponentBadge.tsx"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\components\\components\\atoms\\ComponentIcon.tsx", "SizeKB": 5.48, "Extension": ".tsx", "Type": "File", "Name": "ComponentIcon.tsx"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\components\\components\\atoms\\index.ts", "SizeKB": 0.88, "Extension": ".ts", "Type": "File", "Name": "index.ts"}], "Type": "Folder", "Name": "atoms"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\components\\components\\molecules", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\components\\components\\molecules\\ComponentCard.tsx", "SizeKB": 12.08, "Extension": ".tsx", "Type": "File", "Name": "ComponentCard.tsx"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\components\\components\\molecules\\ComponentFilters.tsx", "SizeKB": 15.54, "Extension": ".tsx", "Type": "File", "Name": "ComponentFilters.tsx"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\components\\components\\molecules\\ComponentSearchBar.tsx", "SizeKB": 10.49, "Extension": ".tsx", "Type": "File", "Name": "ComponentSearchBar.tsx"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\components\\components\\molecules\\index.ts", "SizeKB": 0.33, "Extension": ".ts", "Type": "File", "Name": "index.ts"}], "Type": "Folder", "Name": "molecules"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\components\\components\\organisms", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\components\\components\\organisms\\BulkOperationsPanel.tsx", "SizeKB": 13.88, "Extension": ".tsx", "Type": "File", "Name": "BulkOperationsPanel.tsx"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\components\\components\\organisms\\ComponentList.tsx", "SizeKB": 12.67, "Extension": ".tsx", "Type": "File", "Name": "ComponentList.tsx"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\components\\components\\organisms\\index.ts", "SizeKB": 0.25, "Extension": ".ts", "Type": "File", "Name": "index.ts"}], "Type": "Folder", "Name": "organisms"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\components\\components\\BulkOperations.tsx", "SizeKB": 9.94, "Extension": ".tsx", "Type": "File", "Name": "BulkOperations.tsx"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\components\\components\\ComponentCard.tsx", "SizeKB": 10.91, "Extension": ".tsx", "Type": "File", "Name": "ComponentCard.tsx"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\components\\components\\ComponentDetails.tsx", "SizeKB": 10.07, "Extension": ".tsx", "Type": "File", "Name": "ComponentDetails.tsx"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\components\\components\\ComponentFilters.tsx", "SizeKB": 16.09, "Extension": ".tsx", "Type": "File", "Name": "ComponentFilters.tsx"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\components\\components\\ComponentForm.tsx", "SizeKB": 15.85, "Extension": ".tsx", "Type": "File", "Name": "ComponentForm.tsx"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\components\\components\\ComponentList.tsx", "SizeKB": 13.16, "Extension": ".tsx", "Type": "File", "Name": "ComponentList.tsx"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\components\\components\\ComponentSearch.tsx", "SizeKB": 8.08, "Extension": ".tsx", "Type": "File", "Name": "ComponentSearch.tsx"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\components\\components\\ComponentStats.tsx", "SizeKB": 7.86, "Extension": ".tsx", "Type": "File", "Name": "ComponentStats.tsx"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\components\\components\\index.ts", "SizeKB": 0.87, "Extension": ".ts", "Type": "File", "Name": "index.ts"}], "Type": "Folder", "Name": "components"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\components\\docs", "Children": [], "Type": "Folder", "Name": "docs"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\components\\hooks", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\components\\hooks\\index.ts", "SizeKB": 0.24, "Extension": ".ts", "Type": "File", "Name": "index.ts"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\components\\hooks\\index.ts.txt", "SizeKB": 0.24, "Extension": ".txt", "Type": "File", "Name": "index.ts.txt"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\components\\hooks\\useComponentForm.ts", "SizeKB": 6.26, "Extension": ".ts", "Type": "File", "Name": "useComponentForm.ts"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\components\\hooks\\useComponentStore.ts", "SizeKB": 8.54, "Extension": ".ts", "Type": "File", "Name": "useComponentStore.ts"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\components\\hooks\\useComponentStoreEnhanced.ts", "SizeKB": 15.77, "Extension": ".ts", "Type": "File", "Name": "useComponentStoreEnhanced.ts"}], "Type": "Folder", "Name": "hooks"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\components\\schemas", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\components\\schemas\\componentSchemas.ts", "SizeKB": 8.15, "Extension": ".ts", "Type": "File", "Name": "componentSchemas.ts"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\components\\schemas\\index.ts", "SizeKB": 1.7, "Extension": ".ts", "Type": "File", "Name": "index.ts"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\components\\schemas\\searchSchemas.ts", "SizeKB": 8.71, "Extension": ".ts", "Type": "File", "Name": "searchSchemas.ts"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\components\\schemas\\uiSchemas.ts", "SizeKB": 9.05, "Extension": ".ts", "Type": "File", "Name": "uiSchemas.ts"}], "Type": "Folder", "Name": "schemas"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\components\\utils", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\components\\utils\\calculations.ts", "SizeKB": 10.26, "Extension": ".ts", "Type": "File", "Name": "calculations.ts"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\components\\utils\\formatting.ts", "SizeKB": 11.58, "Extension": ".ts", "Type": "File", "Name": "formatting.ts"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\components\\utils\\index.ts", "SizeKB": 7.56, "Extension": ".ts", "Type": "File", "Name": "index.ts"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\components\\utils\\validation.ts", "SizeKB": 10.29, "Extension": ".ts", "Type": "File", "Name": "validation.ts"}], "Type": "Folder", "Name": "utils"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\components\\index.ts", "SizeKB": 1.97, "Extension": ".ts", "Type": "File", "Name": "index.ts"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\components\\index.ts.txt", "SizeKB": 0.45, "Extension": ".txt", "Type": "File", "Name": "index.ts.txt"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\components\\types.ts", "SizeKB": 4.63, "Extension": ".ts", "Type": "File", "Name": "types.ts"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\components\\utils.ts", "SizeKB": 14.89, "Extension": ".ts", "Type": "File", "Name": "utils.ts"}], "Type": "Folder", "Name": "components"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\dashboard", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\dashboard\\__tests__", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\dashboard\\__tests__\\components", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\dashboard\\__tests__\\components\\MetricsWidget.test.tsx", "SizeKB": 3.18, "Extension": ".tsx", "Type": "File", "Name": "MetricsWidget.test.tsx"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\dashboard\\__tests__\\components\\ProjectsWidget.test.tsx", "SizeKB": 5.6, "Extension": ".tsx", "Type": "File", "Name": "ProjectsWidget.test.tsx"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\dashboard\\__tests__\\components\\QuickActionsWidget.test.tsx", "SizeKB": 6.2, "Extension": ".tsx", "Type": "File", "Name": "QuickActionsWidget.test.tsx"}], "Type": "Folder", "Name": "components"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\dashboard\\__tests__\\hooks", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\dashboard\\__tests__\\hooks\\useDashboardStore.test.ts", "SizeKB": 8.88, "Extension": ".ts", "Type": "File", "Name": "useDashboardStore.test.ts"}], "Type": "Folder", "Name": "hooks"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\dashboard\\__tests__\\utils.test.ts", "SizeKB": 8.48, "Extension": ".ts", "Type": "File", "Name": "utils.test.ts"}], "Type": "Folder", "Name": "__tests__"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\dashboard\\api", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\dashboard\\api\\dashboardApi.ts", "SizeKB": 2.67, "Extension": ".ts", "Type": "File", "Name": "dashboardApi.ts"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\dashboard\\api\\index.ts", "SizeKB": 0.07, "Extension": ".ts", "Type": "File", "Name": "index.ts"}], "Type": "Folder", "Name": "api"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\dashboard\\components", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\dashboard\\components\\DashboardOverview.tsx", "SizeKB": 7.18, "Extension": ".tsx", "Type": "File", "Name": "DashboardOverview.tsx"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\dashboard\\components\\index.ts", "SizeKB": 0.22, "Extension": ".ts", "Type": "File", "Name": "index.ts"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\dashboard\\components\\MetricsWidget.tsx", "SizeKB": 4.81, "Extension": ".tsx", "Type": "File", "Name": "MetricsWidget.tsx"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\dashboard\\components\\ProjectsWidget.tsx", "SizeKB": 7.85, "Extension": ".tsx", "Type": "File", "Name": "ProjectsWidget.tsx"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\dashboard\\components\\QuickActionsWidget.tsx", "SizeKB": 8.62, "Extension": ".tsx", "Type": "File", "Name": "QuickActionsWidget.tsx"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\dashboard\\components\\RecentCalculationsWidget.tsx", "SizeKB": 8.16, "Extension": ".tsx", "Type": "File", "Name": "RecentCalculationsWidget.tsx"}], "Type": "Folder", "Name": "components"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\dashboard\\hooks", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\dashboard\\hooks\\index.ts", "SizeKB": 0.11, "Extension": ".ts", "Type": "File", "Name": "index.ts"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\dashboard\\hooks\\useDashboardData.ts", "SizeKB": 10.17, "Extension": ".ts", "Type": "File", "Name": "useDashboardData.ts"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\dashboard\\hooks\\useDashboardStore.ts", "SizeKB": 7.82, "Extension": ".ts", "Type": "File", "Name": "useDashboardStore.ts"}], "Type": "Folder", "Name": "hooks"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\dashboard\\index.ts", "SizeKB": 0.44, "Extension": ".ts", "Type": "File", "Name": "index.ts"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\dashboard\\types.ts", "SizeKB": 6.95, "Extension": ".ts", "Type": "File", "Name": "types.ts"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\dashboard\\utils.ts", "SizeKB": 11.53, "Extension": ".ts", "Type": "File", "Name": "utils.ts"}], "Type": "Folder", "Name": "dashboard"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\heat_tracing", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\heat_tracing\\api", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\heat_tracing\\api\\index.ts.txt", "SizeKB": 0.22, "Extension": ".txt", "Type": "File", "Name": "index.ts.txt"}], "Type": "Folder", "Name": "api"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\heat_tracing\\components", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\heat_tracing\\components\\index.ts.txt", "SizeKB": 0.2, "Extension": ".txt", "Type": "File", "Name": "index.ts.txt"}], "Type": "Folder", "Name": "components"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\heat_tracing\\hooks", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\heat_tracing\\hooks\\index.ts.txt", "SizeKB": 0.15, "Extension": ".txt", "Type": "File", "Name": "index.ts.txt"}], "Type": "Folder", "Name": "hooks"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\heat_tracing\\index.ts.txt", "SizeKB": 0.42, "Extension": ".txt", "Type": "File", "Name": "index.ts.txt"}], "Type": "Folder", "Name": "heat_tracing"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\landing", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\landing\\__tests__", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\landing\\__tests__\\components", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\landing\\__tests__\\components\\FeaturesSection.test.tsx", "SizeKB": 5.4, "Extension": ".tsx", "Type": "File", "Name": "FeaturesSection.test.tsx"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\landing\\__tests__\\components\\HeroSection.test.tsx", "SizeKB": 5.12, "Extension": ".tsx", "Type": "File", "Name": "HeroSection.test.tsx"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\landing\\__tests__\\components\\LandingPage.test.tsx", "SizeKB": 9.4, "Extension": ".tsx", "Type": "File", "Name": "LandingPage.test.tsx"}], "Type": "Folder", "Name": "components"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\landing\\__tests__\\hooks", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\landing\\__tests__\\hooks\\useLandingPageData.test.tsx", "SizeKB": 4.5, "Extension": ".tsx", "Type": "File", "Name": "useLandingPageData.test.tsx"}], "Type": "Folder", "Name": "hooks"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\landing\\__tests__\\utils.test.ts", "SizeKB": 7.04, "Extension": ".ts", "Type": "File", "Name": "utils.test.ts"}], "Type": "Folder", "Name": "__tests__"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\landing\\api", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\landing\\api\\index.ts", "SizeKB": 0.1, "Extension": ".ts", "Type": "File", "Name": "index.ts"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\landing\\api\\landingApi.ts", "SizeKB": 1.13, "Extension": ".ts", "Type": "File", "Name": "landingApi.ts"}], "Type": "Folder", "Name": "api"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\landing\\components", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\landing\\components\\CTASection.tsx", "SizeKB": 11.56, "Extension": ".tsx", "Type": "File", "Name": "CTASection.tsx"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\landing\\components\\FeaturesSection.tsx", "SizeKB": 7.29, "Extension": ".tsx", "Type": "File", "Name": "FeaturesSection.tsx"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\landing\\components\\HeroSection.tsx", "SizeKB": 10.48, "Extension": ".tsx", "Type": "File", "Name": "HeroSection.tsx"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\landing\\components\\index.ts", "SizeKB": 0.22, "Extension": ".ts", "Type": "File", "Name": "index.ts"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\landing\\components\\LandingPage.tsx", "SizeKB": 4.49, "Extension": ".tsx", "Type": "File", "Name": "LandingPage.tsx"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\landing\\components\\TrustIndicators.tsx", "SizeKB": 7.91, "Extension": ".tsx", "Type": "File", "Name": "TrustIndicators.tsx"}], "Type": "Folder", "Name": "components"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\landing\\hooks", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\landing\\hooks\\index.ts", "SizeKB": 0.13, "Extension": ".ts", "Type": "File", "Name": "index.ts"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\landing\\hooks\\useLandingPageData.ts", "SizeKB": 0.92, "Extension": ".ts", "Type": "File", "Name": "useLandingPageData.ts"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\landing\\hooks\\useLandingPageStore.ts", "SizeKB": 1.32, "Extension": ".ts", "Type": "File", "Name": "useLandingPageStore.ts"}], "Type": "Folder", "Name": "hooks"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\landing\\index.ts", "SizeKB": 0.28, "Extension": ".ts", "Type": "File", "Name": "index.ts"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\landing\\types.ts", "SizeKB": 1.57, "Extension": ".ts", "Type": "File", "Name": "types.ts"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\landing\\utils.ts", "SizeKB": 5.06, "Extension": ".ts", "Type": "File", "Name": "utils.ts"}], "Type": "Folder", "Name": "landing"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\load_calculations", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\load_calculations\\api", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\load_calculations\\api\\index.ts.txt", "SizeKB": 0.22, "Extension": ".txt", "Type": "File", "Name": "index.ts.txt"}], "Type": "Folder", "Name": "api"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\load_calculations\\components", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\load_calculations\\components\\index.ts.txt", "SizeKB": 0.2, "Extension": ".txt", "Type": "File", "Name": "index.ts.txt"}], "Type": "Folder", "Name": "components"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\load_calculations\\hooks", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\load_calculations\\hooks\\index.ts.txt", "SizeKB": 0.15, "Extension": ".txt", "Type": "File", "Name": "index.ts.txt"}], "Type": "Folder", "Name": "hooks"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\load_calculations\\index.ts.txt", "SizeKB": 0.42, "Extension": ".txt", "Type": "File", "Name": "index.ts.txt"}], "Type": "Folder", "Name": "load_calculations"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\projects", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\projects\\api", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\projects\\api\\index.ts.txt", "SizeKB": 0.22, "Extension": ".txt", "Type": "File", "Name": "index.ts.txt"}], "Type": "Folder", "Name": "api"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\projects\\components", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\projects\\components\\index.ts.txt", "SizeKB": 0.2, "Extension": ".txt", "Type": "File", "Name": "index.ts.txt"}], "Type": "Folder", "Name": "components"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\projects\\hooks", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\projects\\hooks\\index.ts.txt", "SizeKB": 0.15, "Extension": ".txt", "Type": "File", "Name": "index.ts.txt"}], "Type": "Folder", "Name": "hooks"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\projects\\index.ts.txt", "SizeKB": 0.42, "Extension": ".txt", "Type": "File", "Name": "index.ts.txt"}], "Type": "Folder", "Name": "projects"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\settings", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\settings\\__tests__", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\settings\\__tests__\\components", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\settings\\__tests__\\components\\SettingsPanel.test.tsx", "SizeKB": 13.79, "Extension": ".tsx", "Type": "File", "Name": "SettingsPanel.test.tsx"}], "Type": "Folder", "Name": "components"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\settings\\__tests__\\integration", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\settings\\__tests__\\integration\\settingsFlow.test.tsx", "SizeKB": 11.51, "Extension": ".tsx", "Type": "File", "Name": "settingsFlow.test.tsx"}], "Type": "Folder", "Name": "integration"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\settings\\__tests__\\stores", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\settings\\__tests__\\stores\\settingsStore.test.ts", "SizeKB": 10.25, "Extension": ".ts", "Type": "File", "Name": "settingsStore.test.ts"}], "Type": "Folder", "Name": "stores"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\settings\\__tests__\\utils.test.ts", "SizeKB": 10.77, "Extension": ".ts", "Type": "File", "Name": "utils.test.ts"}], "Type": "Folder", "Name": "__tests__"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\settings\\api", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\settings\\api\\index.ts", "SizeKB": 0.37, "Extension": ".ts", "Type": "File", "Name": "index.ts"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\settings\\api\\settingsApi.ts", "SizeKB": 9.74, "Extension": ".ts", "Type": "File", "Name": "settingsApi.ts"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\settings\\api\\settingsQueries.ts", "SizeKB": 9.88, "Extension": ".ts", "Type": "File", "Name": "settingsQueries.ts"}], "Type": "Folder", "Name": "api"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\settings\\components", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\settings\\components\\__tests__", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\settings\\components\\__tests__\\ThemeField.test.tsx", "SizeKB": 8.37, "Extension": ".tsx", "Type": "File", "Name": "ThemeField.test.tsx"}], "Type": "Folder", "Name": "__tests__"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\settings\\components\\ConfigurationManager.tsx", "SizeKB": 16.1, "Extension": ".tsx", "Type": "File", "Name": "ConfigurationManager.tsx"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\settings\\components\\ImportExportDialog.tsx", "SizeKB": 10.74, "Extension": ".tsx", "Type": "File", "Name": "ImportExportDialog.tsx"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\settings\\components\\index.ts", "SizeKB": 0.86, "Extension": ".ts", "Type": "File", "Name": "index.ts"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\settings\\components\\PreferencesForm.tsx", "SizeKB": 12.11, "Extension": ".tsx", "Type": "File", "Name": "PreferencesForm.tsx"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\settings\\components\\SettingsPanel.tsx", "SizeKB": 8.25, "Extension": ".tsx", "Type": "File", "Name": "SettingsPanel.tsx"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\settings\\components\\SettingsReset.tsx", "SizeKB": 12.58, "Extension": ".tsx", "Type": "File", "Name": "SettingsReset.tsx"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\settings\\components\\SettingsSearch.tsx", "SizeKB": 12.83, "Extension": ".tsx", "Type": "File", "Name": "SettingsSearch.tsx"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\settings\\components\\ThemeField.tsx", "SizeKB": 4.02, "Extension": ".tsx", "Type": "File", "Name": "ThemeField.tsx"}], "Type": "Folder", "Name": "components"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\settings\\hooks", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\settings\\hooks\\index.ts", "SizeKB": 0.59, "Extension": ".ts", "Type": "File", "Name": "index.ts"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\settings\\hooks\\useSettings.ts", "SizeKB": 10.49, "Extension": ".ts", "Type": "File", "Name": "useSettings.ts"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\settings\\hooks\\useSettingsExport.ts", "SizeKB": 11.52, "Extension": ".ts", "Type": "File", "Name": "useSettingsExport.ts"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\settings\\hooks\\useSettingsSync.ts", "SizeKB": 10.32, "Extension": ".ts", "Type": "File", "Name": "useSettingsSync.ts"}], "Type": "Folder", "Name": "hooks"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\settings\\schemas", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\settings\\schemas\\index.ts", "SizeKB": 0.17, "Extension": ".ts", "Type": "File", "Name": "index.ts"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\settings\\schemas\\settingsSchemas.ts", "SizeKB": 12.75, "Extension": ".ts", "Type": "File", "Name": "settingsSchemas.ts"}], "Type": "Folder", "Name": "schemas"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\settings\\stores", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\settings\\stores\\index.ts", "SizeKB": 0.16, "Extension": ".ts", "Type": "File", "Name": "index.ts"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\settings\\stores\\settingsStore.ts", "SizeKB": 10.99, "Extension": ".ts", "Type": "File", "Name": "settingsStore.ts"}], "Type": "Folder", "Name": "stores"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\settings\\constants.ts", "SizeKB": 14.51, "Extension": ".ts", "Type": "File", "Name": "constants.ts"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\settings\\index.ts", "SizeKB": 1.33, "Extension": ".ts", "Type": "File", "Name": "index.ts"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\settings\\types.ts", "SizeKB": 6.83, "Extension": ".ts", "Type": "File", "Name": "types.ts"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\settings\\utils.ts", "SizeKB": 9.31, "Extension": ".ts", "Type": "File", "Name": "utils.ts"}], "Type": "Folder", "Name": "settings"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\users", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\users\\api", "Children": [], "Type": "Folder", "Name": "api"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\users\\components", "Children": [], "Type": "Folder", "Name": "components"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\users\\hooks", "Children": [], "Type": "Folder", "Name": "hooks"}], "Type": "Folder", "Name": "users"}], "Type": "Folder", "Name": "modules"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\services", "Children": [], "Type": "Folder", "Name": "services"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\stores", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\stores\\authStore.ts", "SizeKB": 3.33, "Extension": ".ts", "Type": "File", "Name": "authStore.ts"}], "Type": "Folder", "Name": "stores"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\test", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\test\\e2e", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\test\\e2e\\rbac-audit-workflow.spec.ts", "SizeKB": 13.86, "Extension": ".ts", "Type": "File", "Name": "rbac-audit-workflow.spec.ts"}], "Type": "Folder", "Name": "e2e"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\test\\factories", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\test\\factories\\componentFactories.ts", "SizeKB": 8.99, "Extension": ".ts", "Type": "File", "Name": "componentFactories.ts"}], "Type": "Folder", "Name": "factories"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\test\\integration", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\test\\integration\\auth-integration.test.tsx", "SizeKB": 16.4, "Extension": ".tsx", "Type": "File", "Name": "auth-integration.test.tsx"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\test\\integration\\component-management-integration.test.tsx", "SizeKB": 18.07, "Extension": ".tsx", "Type": "File", "Name": "component-management-integration.test.tsx"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\test\\integration\\rbac-workflow.test.tsx", "SizeKB": 13.88, "Extension": ".tsx", "Type": "File", "Name": "rbac-workflow.test.tsx"}], "Type": "Folder", "Name": "integration"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\test\\setup.ts", "SizeKB": 2.58, "Extension": ".ts", "Type": "File", "Name": "setup.ts"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\test\\utils.tsx", "SizeKB": 3.01, "Extension": ".tsx", "Type": "File", "Name": "utils.tsx"}], "Type": "Folder", "Name": "test"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\types", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\types\\api.ts", "SizeKB": 22.85, "Extension": ".ts", "Type": "File", "Name": "api.ts"}], "Type": "Folder", "Name": "types"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\utils", "Children": [], "Type": "Folder", "Name": "utils"}], "Type": "Folder", "Name": "src"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\.prettierrc", "SizeKB": 0.15, "Extension": ".prettier<PERSON>", "Type": "File", "Name": ".prettier<PERSON>"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\debug-component-list.png", "SizeKB": 172.06, "Extension": ".png", "Type": "File", "Name": "debug-component-list.png"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\next-env.d.ts", "SizeKB": 0.21, "Extension": ".ts", "Type": "File", "Name": "next-env.d.ts"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\playwright.config.ts", "SizeKB": 1.98, "Extension": ".ts", "Type": "File", "Name": "playwright.config.ts"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\tailwind.config.ts", "SizeKB": 3.69, "Extension": ".ts", "Type": "File", "Name": "tailwind.config.ts"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\tsconfig.tsbuildinfo", "SizeKB": 541.09, "Extension": ".tsbuildinfo", "Type": "File", "Name": "tsconfig.tsbuildinfo"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\vitest.config.ts", "SizeKB": 0.82, "Extension": ".ts", "Type": "File", "Name": "vitest.config.ts"}], "Type": "Folder", "Name": "client"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\computation-engine-service", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\computation-engine-service\\src", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\computation-engine-service\\src\\Controllers", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\computation-engine-service\\src\\Controllers\\ComputationController.cs", "SizeKB": 0.0, "Extension": ".cs", "Type": "File", "Name": "ComputationController.cs"}], "Type": "Folder", "Name": "Controllers"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\computation-engine-service\\src\\Services", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\computation-engine-service\\src\\Services\\PowerFlowSolver.cs", "SizeKB": 0.0, "Extension": ".cs", "Type": "File", "Name": "PowerFlowSolver.cs"}], "Type": "Folder", "Name": "Services"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\computation-engine-service\\src\\ultimate_electrical_designer.ComputationEngine.csproj", "SizeKB": 0.0, "Extension": ".c<PERSON><PERSON>j", "Type": "File", "Name": "ultimate_electrical_designer.ComputationEngine.csproj"}], "Type": "Folder", "Name": "src"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\computation-engine-service\\Dockerfile", "SizeKB": 0.0, "Extension": "", "Type": "File", "Name": "Dockerfile"}], "Type": "Folder", "Name": "computation-engine-service"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\docs", "Children": [], "Type": "Folder", "Name": "docs"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\memory-bank", "Children": [], "Type": "Folder", "Name": "memory-bank"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\data", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\data\\app_dev.db", "SizeKB": 156.0, "Extension": ".db", "Type": "File", "Name": "app_dev.db"}], "Type": "Folder", "Name": "data"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\docs", "Children": [], "Type": "Folder", "Name": "docs"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\alembic", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\alembic\\env.py", "SizeKB": 4.89, "Extension": ".py", "Type": "File", "Name": "env.py"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\alembic\\script.py.mako", "SizeKB": 0.64, "Extension": ".mako", "Type": "File", "Name": "script.py.mako"}], "Type": "Folder", "Name": "alembic"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\api", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\api\\v1", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\api\\v1\\auth_routes.py", "SizeKB": 14.66, "Extension": ".py", "Type": "File", "Name": "auth_routes.py"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\api\\v1\\component_category_routes.py", "SizeKB": 19.64, "Extension": ".py", "Type": "File", "Name": "component_category_routes.py"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\api\\v1\\component_routes.py", "SizeKB": 70.61, "Extension": ".py", "Type": "File", "Name": "component_routes.py"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\api\\v1\\component_type_routes.py", "SizeKB": 13.64, "Extension": ".py", "Type": "File", "Name": "component_type_routes.py"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\api\\v1\\health_routes.py", "SizeKB": 11.92, "Extension": ".py", "Type": "File", "Name": "health_routes.py"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\api\\v1\\router.py", "SizeKB": 6.04, "Extension": ".py", "Type": "File", "Name": "router.py"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\api\\v1\\user_preferences_routes.py", "SizeKB": 11.28, "Extension": ".py", "Type": "File", "Name": "user_preferences_routes.py"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\api\\v1\\user_routes.py", "SizeKB": 17.54, "Extension": ".py", "Type": "File", "Name": "user_routes.py"}], "Type": "Folder", "Name": "v1"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\api\\main_router.py", "SizeKB": 1.35, "Extension": ".py", "Type": "File", "Name": "main_router.py"}], "Type": "Folder", "Name": "api"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\config", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\config\\logging_config.py", "SizeKB": 6.73, "Extension": ".py", "Type": "File", "Name": "logging_config.py"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\config\\settings.py", "SizeKB": 7.08, "Extension": ".py", "Type": "File", "Name": "settings.py"}], "Type": "Folder", "Name": "config"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\auth", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\auth\\dependencies.py", "SizeKB": 0.65, "Extension": ".py", "Type": "File", "Name": "dependencies.py"}], "Type": "Folder", "Name": "auth"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\calculations", "Children": [], "Type": "Folder", "Name": "calculations"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\database", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\database\\dependencies.py", "SizeKB": 1.04, "Extension": ".py", "Type": "File", "Name": "dependencies.py"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\database\\engine.py", "SizeKB": 10.42, "Extension": ".py", "Type": "File", "Name": "engine.py"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\database\\initialization.py", "SizeKB": 6.08, "Extension": ".py", "Type": "File", "Name": "initialization.py"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\database\\session.py", "SizeKB": 14.23, "Extension": ".py", "Type": "File", "Name": "session.py"}], "Type": "Folder", "Name": "database"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\enums", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\enums\\calculation_enums.py", "SizeKB": 2.59, "Extension": ".py", "Type": "File", "Name": "calculation_enums.py"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\enums\\common_enums.py", "SizeKB": 2.3, "Extension": ".py", "Type": "File", "Name": "common_enums.py"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\enums\\data_io_enums.py", "SizeKB": 6.07, "Extension": ".py", "Type": "File", "Name": "data_io_enums.py"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\enums\\electrical_enums.py", "SizeKB": 24.67, "Extension": ".py", "Type": "File", "Name": "electrical_enums.py"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\enums\\heat_tracing_enums.py", "SizeKB": 2.66, "Extension": ".py", "Type": "File", "Name": "heat_tracing_enums.py"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\enums\\mechanical_enums.py", "SizeKB": 4.06, "Extension": ".py", "Type": "File", "Name": "mechanical_enums.py"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\enums\\project_management_enums.py", "SizeKB": 4.8, "Extension": ".py", "Type": "File", "Name": "project_management_enums.py"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\enums\\standards_enums.py", "SizeKB": 2.85, "Extension": ".py", "Type": "File", "Name": "standards_enums.py"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\enums\\system_enums.py", "SizeKB": 4.78, "Extension": ".py", "Type": "File", "Name": "system_enums.py"}], "Type": "Folder", "Name": "enums"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\errors", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\errors\\exceptions.py", "SizeKB": 12.23, "Extension": ".py", "Type": "File", "Name": "exceptions.py"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\errors\\unified_error_handler.py", "SizeKB": 41.07, "Extension": ".py", "Type": "File", "Name": "unified_error_handler.py"}], "Type": "Folder", "Name": "errors"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\integrations", "Children": [], "Type": "Folder", "Name": "integrations"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\models", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\models\\general", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\models\\general\\activity_log.py", "SizeKB": 5.79, "Extension": ".py", "Type": "File", "Name": "activity_log.py"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\models\\general\\component_category.py", "SizeKB": 8.17, "Extension": ".py", "Type": "File", "Name": "component_category.py"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\models\\general\\component_type.py", "SizeKB": 10.45, "Extension": ".py", "Type": "File", "Name": "component_type.py"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\models\\general\\component.py", "SizeKB": 12.71, "Extension": ".py", "Type": "File", "Name": "component.py"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\models\\general\\project.py", "SizeKB": 15.4, "Extension": ".py", "Type": "File", "Name": "project.py"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\models\\general\\user_role.py", "SizeKB": 4.71, "Extension": ".py", "Type": "File", "Name": "user_role.py"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\models\\general\\user.py", "SizeKB": 4.14, "Extension": ".py", "Type": "File", "Name": "user.py"}], "Type": "Folder", "Name": "general"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\models\\base.py", "SizeKB": 7.47, "Extension": ".py", "Type": "File", "Name": "base.py"}], "Type": "Folder", "Name": "models"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\monitoring", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\monitoring\\performance_monitor.py", "SizeKB": 1.4, "Extension": ".py", "Type": "File", "Name": "performance_monitor.py"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\monitoring\\unified_performance_monitor.py", "SizeKB": 32.21, "Extension": ".py", "Type": "File", "Name": "unified_performance_monitor.py"}], "Type": "Folder", "Name": "monitoring"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\repositories", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\repositories\\general", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\repositories\\general\\component_category_repository.py", "SizeKB": 15.18, "Extension": ".py", "Type": "File", "Name": "component_category_repository.py"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\repositories\\general\\component_repository.py", "SizeKB": 67.63, "Extension": ".py", "Type": "File", "Name": "component_repository.py"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\repositories\\general\\component_type_repository.py", "SizeKB": 14.0, "Extension": ".py", "Type": "File", "Name": "component_type_repository.py"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\repositories\\general\\project_repository.py", "SizeKB": 10.32, "Extension": ".py", "Type": "File", "Name": "project_repository.py"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\repositories\\general\\user_preference_repository.py", "SizeKB": 5.51, "Extension": ".py", "Type": "File", "Name": "user_preference_repository.py"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\repositories\\general\\user_repository.py", "SizeKB": 8.26, "Extension": ".py", "Type": "File", "Name": "user_repository.py"}], "Type": "Folder", "Name": "general"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\repositories\\base_repository.py", "SizeKB": 10.25, "Extension": ".py", "Type": "File", "Name": "base_repository.py"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\repositories\\repository_dependencies.py", "SizeKB": 2.97, "Extension": ".py", "Type": "File", "Name": "repository_dependencies.py"}], "Type": "Folder", "Name": "repositories"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\schemas", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\schemas\\general", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\schemas\\general\\audit_trail_schemas.py", "SizeKB": 10.58, "Extension": ".py", "Type": "File", "Name": "audit_trail_schemas.py"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\schemas\\general\\component_category_schemas.py", "SizeKB": 10.88, "Extension": ".py", "Type": "File", "Name": "component_category_schemas.py"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\schemas\\general\\component_schemas.py", "SizeKB": 31.24, "Extension": ".py", "Type": "File", "Name": "component_schemas.py"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\schemas\\general\\component_type_schemas.py", "SizeKB": 12.76, "Extension": ".py", "Type": "File", "Name": "component_type_schemas.py"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\schemas\\general\\error_schemas.py", "SizeKB": 0.45, "Extension": ".py", "Type": "File", "Name": "error_schemas.py"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\schemas\\general\\project_schemas.py", "SizeKB": 4.04, "Extension": ".py", "Type": "File", "Name": "project_schemas.py"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\schemas\\general\\user_role_schemas.py", "SizeKB": 6.68, "Extension": ".py", "Type": "File", "Name": "user_role_schemas.py"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\schemas\\general\\user_schemas.py", "SizeKB": 9.71, "Extension": ".py", "Type": "File", "Name": "user_schemas.py"}], "Type": "Folder", "Name": "general"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\schemas\\base.py", "SizeKB": 1.79, "Extension": ".py", "Type": "File", "Name": "base.py"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\schemas\\error.py", "SizeKB": 3.27, "Extension": ".py", "Type": "File", "Name": "error.py"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\schemas\\health.py", "SizeKB": 4.78, "Extension": ".py", "Type": "File", "Name": "health.py"}], "Type": "Folder", "Name": "schemas"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\security", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\security\\enhanced_dependencies.py", "SizeKB": 11.58, "Extension": ".py", "Type": "File", "Name": "enhanced_dependencies.py"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\security\\input_validators.py", "SizeKB": 43.63, "Extension": ".py", "Type": "File", "Name": "input_validators.py"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\security\\password_handler.py", "SizeKB": 2.64, "Extension": ".py", "Type": "File", "Name": "password_handler.py"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\security\\unified_security_validator.py", "SizeKB": 40.9, "Extension": ".py", "Type": "File", "Name": "unified_security_validator.py"}], "Type": "Folder", "Name": "security"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\services", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\services\\general", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\services\\general\\audit_trail_service.py", "SizeKB": 14.37, "Extension": ".py", "Type": "File", "Name": "audit_trail_service.py"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\services\\general\\component_category_service.py", "SizeKB": 18.18, "Extension": ".py", "Type": "File", "Name": "component_category_service.py"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\services\\general\\component_service.py", "SizeKB": 85.12, "Extension": ".py", "Type": "File", "Name": "component_service.py"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\services\\general\\component_type_service.py", "SizeKB": 15.74, "Extension": ".py", "Type": "File", "Name": "component_type_service.py"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\services\\general\\health_service.py", "SizeKB": 11.65, "Extension": ".py", "Type": "File", "Name": "health_service.py"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\services\\general\\project_service.py", "SizeKB": 16.42, "Extension": ".py", "Type": "File", "Name": "project_service.py"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\services\\general\\user_service.py", "SizeKB": 28.25, "Extension": ".py", "Type": "File", "Name": "user_service.py"}], "Type": "Folder", "Name": "general"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\services\\dependencies.py", "SizeKB": 3.17, "Extension": ".py", "Type": "File", "Name": "dependencies.py"}], "Type": "Folder", "Name": "services"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\standards", "Children": [], "Type": "Folder", "Name": "standards"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\utils", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\utils\\advanced_cache_manager.py", "SizeKB": 12.23, "Extension": ".py", "Type": "File", "Name": "advanced_cache_manager.py"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\utils\\crud_endpoint_factory.py", "SizeKB": 27.96, "Extension": ".py", "Type": "File", "Name": "crud_endpoint_factory.py"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\utils\\datetime_utils.py", "SizeKB": 8.26, "Extension": ".py", "Type": "File", "Name": "datetime_utils.py"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\utils\\file_io_utils.py", "SizeKB": 14.81, "Extension": ".py", "Type": "File", "Name": "file_io_utils.py"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\utils\\json_validation.py", "SizeKB": 12.45, "Extension": ".py", "Type": "File", "Name": "json_validation.py"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\utils\\logger.py", "SizeKB": 0.38, "Extension": ".py", "Type": "File", "Name": "logger.py"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\utils\\memory_manager.py", "SizeKB": 15.13, "Extension": ".py", "Type": "File", "Name": "memory_manager.py"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\utils\\pagination_utils.py", "SizeKB": 9.81, "Extension": ".py", "Type": "File", "Name": "pagination_utils.py"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\utils\\performance_optimizer.py", "SizeKB": 17.25, "Extension": ".py", "Type": "File", "Name": "performance_optimizer.py"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\utils\\performance_utils.py", "SizeKB": 12.44, "Extension": ".py", "Type": "File", "Name": "performance_utils.py"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\utils\\query_optimizer.py", "SizeKB": 14.8, "Extension": ".py", "Type": "File", "Name": "query_optimizer.py"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\utils\\query_utils.py", "SizeKB": 15.33, "Extension": ".py", "Type": "File", "Name": "query_utils.py"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\utils\\search_query_builder.py", "SizeKB": 17.14, "Extension": ".py", "Type": "File", "Name": "search_query_builder.py"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\utils\\security.py", "SizeKB": 7.02, "Extension": ".py", "Type": "File", "Name": "security.py"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\utils\\string_utils.py", "SizeKB": 8.55, "Extension": ".py", "Type": "File", "Name": "string_utils.py"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\utils\\uuid_utils.py", "SizeKB": 5.45, "Extension": ".py", "Type": "File", "Name": "uuid_utils.py"}], "Type": "Folder", "Name": "utils"}], "Type": "Folder", "Name": "core"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\middleware", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\middleware\\caching_middleware.py", "SizeKB": 18.57, "Extension": ".py", "Type": "File", "Name": "caching_middleware.py"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\middleware\\context_middleware.py", "SizeKB": 8.55, "Extension": ".py", "Type": "File", "Name": "context_middleware.py"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\middleware\\logging_middleware.py", "SizeKB": 11.25, "Extension": ".py", "Type": "File", "Name": "logging_middleware.py"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\middleware\\rate_limiting_middleware.py", "SizeKB": 15.11, "Extension": ".py", "Type": "File", "Name": "rate_limiting_middleware.py"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\middleware\\security_middleware.py", "SizeKB": 26.14, "Extension": ".py", "Type": "File", "Name": "security_middleware.py"}], "Type": "Folder", "Name": "middleware"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\alembic.ini", "SizeKB": 3.11, "Extension": ".ini", "Type": "File", "Name": "alembic.ini"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\app.py", "SizeKB": 7.01, "Extension": ".py", "Type": "File", "Name": "app.py"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\main.py", "SizeKB": 12.61, "Extension": ".py", "Type": "File", "Name": "main.py"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\test_app.db", "SizeKB": 0.0, "Extension": ".db", "Type": "File", "Name": "test_app.db"}], "Type": "Folder", "Name": "src"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\.safety-project.ini", "SizeKB": 0.14, "Extension": ".ini", "Type": "File", "Name": ".safety-project.ini"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\mypy.ini", "SizeKB": 2.97, "Extension": ".ini", "Type": "File", "Name": "mypy.ini"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\pyproject.toml", "SizeKB": 14.65, "Extension": ".toml", "Type": "File", "Name": "pyproject.toml"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\test_app.db", "SizeKB": 1144.0, "Extension": ".db", "Type": "File", "Name": "test_app.db"}], "Type": "Folder", "Name": "server"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\.pre-commit-config.yaml", "SizeKB": 3.35, "Extension": ".yaml", "Type": "File", "Name": ".pre-commit-config.yaml"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\docker-compose.yml", "SizeKB": 0.37, "Extension": ".yml", "Type": "File", "Name": "docker-compose.yml"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\Makefile", "SizeKB": 13.88, "Extension": "", "Type": "File", "Name": "<PERSON><PERSON><PERSON>"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\test_app.db", "SizeKB": 220.0, "Extension": ".db", "Type": "File", "Name": "test_app.db"}], "Type": "Folder", "Name": "ultimate-electrical-designer"}