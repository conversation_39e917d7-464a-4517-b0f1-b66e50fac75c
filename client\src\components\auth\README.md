# Authentication Components

This directory contains all authentication-related components for the Ultimate Electrical Designer client application.

## Overview

The authentication system follows a modular, component-based architecture with comprehensive validation, accessibility features, and robust error handling. All components are built with TypeScript, React, and Tailwind CSS.

## Architecture

### Component Structure

```
auth/
├── components/           # React components
│   ├── LoginForm.tsx    # Login form component
│   ├── RegisterForm.tsx # Registration form component
│   └── index.ts         # Component exports
├── hooks/               # Custom React hooks
│   ├── useAuth.ts       # Authentication state management
│   └── index.ts         # Hook exports
├── types/               # TypeScript type definitions
│   ├── auth.ts          # Authentication types
│   └── index.ts         # Type exports
├── utils/               # Utility functions
│   ├── validation.ts    # Form validation logic
│   └── index.ts         # Utility exports
├── __tests__/           # Component tests
│   ├── LoginForm.test.tsx
│   ├── RegisterForm.test.tsx
│   └── utils/
│       └── validation.test.ts
└── README.md           # This file
```

## Components

### LoginForm

A comprehensive login form component with validation and accessibility features.

**Features:**

- Email and password validation
- Show/hide password toggle
- Loading states
- Error handling
- WCAG 2.1 AA compliance
- Responsive design

**Usage:**

```tsx
import { LoginForm } from '@/components/auth'

function LoginPage() {
  const handleLogin = async (credentials: LoginCredentials) => {
    // Handle login logic
  }

  return <LoginForm onSubmit={handleLogin} isLoading={false} error={null} />
}
```

**Props:**

- `onSubmit: (credentials: LoginCredentials) => Promise<void>` - Login handler
- `isLoading?: boolean` - Loading state
- `error?: string | null` - Error message to display
- `className?: string` - Additional CSS classes

### RegisterForm

A registration form component with comprehensive validation.

**Features:**

- Email, password, and confirm password validation
- Password strength indicator
- Real-time validation feedback
- Accessibility features
- Terms and conditions acceptance

**Usage:**

```tsx
import { RegisterForm } from '@/components/auth'

function RegisterPage() {
  const handleRegister = async (data: RegisterData) => {
    // Handle registration logic
  }

  return <RegisterForm onSubmit={handleRegister} isLoading={false} error={null} />
}
```

**Props:**

- `onSubmit: (data: RegisterData) => Promise<void>` - Registration handler
- `isLoading?: boolean` - Loading state
- `error?: string | null` - Error message to display
- `className?: string` - Additional CSS classes

## Hooks

### useAuth

Custom hook for managing authentication state and operations.

**Features:**

- Login/logout functionality
- User state management
- Token management
- Automatic token refresh
- Loading states

**Usage:**

```tsx
import { useAuth } from '@/components/auth/hooks'

function MyComponent() {
  const { user, isAuthenticated, isLoading, login, logout, register } = useAuth()

  // Use authentication state and methods
}
```

## Types

### Core Types

```typescript
// User information
interface User {
  id: string
  email: string
  firstName: string
  lastName: string
  role: UserRole
  createdAt: string
  updatedAt: string
}

// Login credentials
interface LoginCredentials {
  email: string
  password: string
}

// Registration data
interface RegisterData {
  email: string
  password: string
  confirmPassword: string
  firstName: string
  lastName: string
  acceptTerms: boolean
}

// Authentication response
interface AuthResponse {
  user: User
  token: string
  refreshToken: string
}
```

## Validation

The validation system uses Zod for schema validation with comprehensive rules:

### Email Validation

- Valid email format
- Required field
- Trimmed whitespace

### Password Validation

- Minimum 8 characters
- At least one uppercase letter
- At least one lowercase letter
- At least one number
- At least one special character

### Password Strength Levels

- **Weak**: Basic requirements met
- **Medium**: Additional complexity
- **Strong**: High complexity with mixed characters

## Accessibility

All components follow WCAG 2.1 AA guidelines:

- **Keyboard Navigation**: Full keyboard support
- **Screen Readers**: Proper ARIA labels and descriptions
- **Focus Management**: Visible focus indicators
- **Error Announcements**: Screen reader accessible error messages
- **Color Contrast**: Meets AA contrast requirements

## Testing

### Test Coverage

- Unit tests for all components
- Integration tests for authentication flows
- Accessibility tests
- Validation tests

### Running Tests

```bash
# Run all auth tests
npm test src/components/auth

# Run specific component tests
npm test LoginForm.test.tsx

# Run with coverage
npm test -- --coverage src/components/auth
```

## Security Features

- **Input Sanitization**: All inputs are validated and sanitized
- **XSS Protection**: Proper escaping of user inputs
- **CSRF Protection**: Token-based authentication
- **Secure Storage**: Tokens stored securely
- **Password Security**: Strong password requirements

## Styling

Components use Tailwind CSS with:

- Responsive design patterns
- Dark mode support (future)
- Consistent spacing and typography
- Brand color integration
- Hover and focus states

## Error Handling

Comprehensive error handling includes:

- Form validation errors
- Network errors
- Authentication errors
- User-friendly error messages
- Error recovery mechanisms

## Performance

- **Code Splitting**: Components are lazy-loaded
- **Memoization**: Expensive operations are memoized
- **Debounced Validation**: Real-time validation is debounced
- **Optimized Renders**: Minimal re-renders

## Browser Support

- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

## Contributing

When contributing to authentication components:

1. Follow TypeScript strict mode
2. Add comprehensive tests
3. Ensure accessibility compliance
4. Update documentation
5. Follow existing patterns
6. Test across browsers

## Future Enhancements

- Two-factor authentication
- Social login integration
- Biometric authentication
- Advanced password policies
- Session management improvements
