/**
 * Settings Search Component
 * Ultimate Electrical Designer - Settings & User Preferences
 */

'use client'

import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { ScrollArea } from '@/components/ui/scroll-area'
import {
  Bell,
  Calculator,
  ChevronRight,
  Palette,
  Search,
  Settings as SettingsIcon,
  Shield,
  User,
  X,
} from 'lucide-react'
import React, { useCallback, useMemo, useState } from 'react'
import { useSettingsSearch } from '../hooks/useSettings'
import { useSettingsStore } from '../stores/settingsStore'
import type { SettingsCategory } from '../types'
import { searchSettings } from '../utils'

// Category icons mapping
const CATEGORY_ICONS = {
  account: User,
  appearance: Palette,
  notifications: Bell,
  privacy: Shield,
  advanced: SettingsIcon,
  engineering: Calculator,
} as const

interface SettingsSearchProps {
  className?: string
  placeholder?: string
  showResults?: boolean
  onResultClick?: (categoryId: SettingsCategory, fieldId?: string) => void
}

/**
 * Settings Search Component
 */
export function SettingsSearch({
  className = '',
  placeholder = 'Search settings...',
  showResults = true,
  onResultClick,
}: SettingsSearchProps) {
  const { searchQuery, setSearchQuery, clearSearch } = useSettingsSearch()
  const { setActiveCategory } = useSettingsStore()
  const [isFocused, setIsFocused] = useState(false)
  const [inputValue, setInputValue] = useState(searchQuery)

  // Search results
  const searchResults = useMemo(() => {
    if (!searchQuery || searchQuery.length < 2) {
      return { categories: [], matches: [] }
    }
    return searchSettings(searchQuery)
  }, [searchQuery])

  // Handle input change with debouncing
  const handleInputChange = useCallback(
    (value: string) => {
      setInputValue(value)
      setSearchQuery(value)
    },
    [setSearchQuery]
  )

  // Handle clear search
  const handleClear = useCallback(() => {
    setInputValue('')
    clearSearch()
    setIsFocused(false)
  }, [clearSearch])

  // Handle result click
  const handleResultClick = useCallback(
    (categoryId: SettingsCategory, fieldId?: string) => {
      setActiveCategory(categoryId)
      setIsFocused(false)

      if (onResultClick) {
        onResultClick(categoryId, fieldId)
      }

      // Scroll to field if specified
      if (fieldId) {
        setTimeout(() => {
          const element = document.getElementById(`setting-field-${fieldId}`)
          if (element) {
            element.scrollIntoView({ behavior: 'smooth', block: 'center' })
            element.focus()
          }
        }, 100)
      }
    },
    [setActiveCategory, onResultClick]
  )

  // Handle keyboard navigation
  const handleKeyDown = useCallback(
    (event: React.KeyboardEvent) => {
      if (event.key === 'Escape') {
        setIsFocused(false)
        handleClear()
      }
    },
    [handleClear]
  )

  const hasResults = searchResults.matches.length > 0 || searchResults.categories.length > 0
  const showResultsPanel = showResults && isFocused && searchQuery.length >= 2

  return (
    <div className={`settings-search relative ${className}`}>
      {/* Search Input */}
      <div className="relative">
        <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 transform text-muted-foreground" />
        <Input
          type="text"
          placeholder={placeholder}
          value={inputValue}
          onChange={(e) => handleInputChange(e.target.value)}
          onFocus={() => setIsFocused(true)}
          onKeyDown={handleKeyDown}
          className="pl-10 pr-10"
          aria-label="Search settings"
          aria-expanded={showResultsPanel}
          aria-haspopup="listbox"
          role="combobox"
        />

        {/* Clear button */}
        {inputValue && (
          <Button
            variant="ghost"
            size="sm"
            onClick={handleClear}
            className="absolute right-1 top-1/2 h-8 w-8 -translate-y-1/2 transform p-0"
            aria-label="Clear search"
          >
            <X className="h-4 w-4" />
          </Button>
        )}
      </div>

      {/* Search Results */}
      {showResultsPanel && (
        <Card className="absolute left-0 right-0 top-full z-50 mt-2 shadow-lg">
          <CardContent className="p-0">
            <ScrollArea className="max-h-96">
              {hasResults ? (
                <div className="p-2">
                  {/* Direct field matches */}
                  {searchResults.matches.length > 0 && (
                    <div className="mb-4">
                      <div className="px-2 py-1 text-xs font-medium uppercase tracking-wide text-muted-foreground">
                        Settings
                      </div>
                      <div className="space-y-1">
                        {searchResults.matches.map((match, index) => {
                          const Icon = CATEGORY_ICONS[match.categoryId]
                          return (
                            <button
                              key={`${match.categoryId}-${match.fieldId}-${index}`}
                              onClick={() => handleResultClick(match.categoryId, match.fieldId)}
                              className="flex w-full items-center gap-3 rounded-md px-2 py-2 text-left transition-colors hover:bg-accent hover:text-accent-foreground"
                              role="option"
                              aria-selected="false"
                            >
                              <Icon className="h-4 w-4 text-muted-foreground" />
                              <div className="min-w-0 flex-1">
                                <div className="text-sm font-medium">{match.label}</div>
                                {match.description && (
                                  <div className="truncate text-xs text-muted-foreground">
                                    {match.description}
                                  </div>
                                )}
                              </div>
                              <Badge variant="secondary" className="text-xs">
                                {match.categoryId}
                              </Badge>
                              <ChevronRight className="h-4 w-4 text-muted-foreground" />
                            </button>
                          )
                        })}
                      </div>
                    </div>
                  )}

                  {/* Category matches */}
                  {searchResults.categories.length > 0 && (
                    <div>
                      <div className="px-2 py-1 text-xs font-medium uppercase tracking-wide text-muted-foreground">
                        Categories
                      </div>
                      <div className="space-y-1">
                        {searchResults.categories.map((category) => {
                          const Icon = CATEGORY_ICONS[category.id]
                          return (
                            <button
                              key={category.id}
                              onClick={() => handleResultClick(category.id)}
                              className="flex w-full items-center gap-3 rounded-md px-2 py-2 text-left transition-colors hover:bg-accent hover:text-accent-foreground"
                              role="option"
                              aria-selected="false"
                            >
                              <Icon className="h-4 w-4 text-muted-foreground" />
                              <div className="min-w-0 flex-1">
                                <div className="text-sm font-medium">{category.label}</div>
                                <div className="truncate text-xs text-muted-foreground">
                                  {category.description}
                                </div>
                              </div>
                              <ChevronRight className="h-4 w-4 text-muted-foreground" />
                            </button>
                          )
                        })}
                      </div>
                    </div>
                  )}
                </div>
              ) : (
                <div className="p-4 text-center text-muted-foreground">
                  <Search className="mx-auto mb-2 h-8 w-8 opacity-50" />
                  <div className="text-sm">No settings found</div>
                  <div className="text-xs">Try a different search term</div>
                </div>
              )}
            </ScrollArea>
          </CardContent>
        </Card>
      )}

      {/* Backdrop to close results */}
      {showResultsPanel && (
        <div
          className="fixed inset-0 z-40"
          onClick={() => setIsFocused(false)}
          aria-hidden="true"
        />
      )}
    </div>
  )
}

/**
 * Compact Search Component for smaller spaces
 */
export function CompactSettingsSearch({
  className = '',
  onResultClick,
}: {
  className?: string
  onResultClick?: (categoryId: SettingsCategory, fieldId?: string) => void
}) {
  return (
    <SettingsSearch
      className={`compact ${className}`}
      placeholder="Search..."
      showResults={true}
      onResultClick={onResultClick}
    />
  )
}

/**
 * Search Results Only Component
 */
export function SettingsSearchResults({
  query,
  onResultClick,
  className = '',
}: {
  query: string
  onResultClick?: (categoryId: SettingsCategory, fieldId?: string) => void
  className?: string
}) {
  const searchResults = useMemo(() => {
    if (!query || query.length < 2) {
      return { categories: [], matches: [] }
    }
    return searchSettings(query)
  }, [query])

  const hasResults = searchResults.matches.length > 0 || searchResults.categories.length > 0

  if (!hasResults) {
    return (
      <div className={`p-4 text-center text-muted-foreground ${className}`}>
        <Search className="mx-auto mb-2 h-8 w-8 opacity-50" />
        <div className="text-sm">No settings found for &quot;{query}&quot;</div>
      </div>
    )
  }

  return (
    <div className={`settings-search-results ${className}`}>
      {/* Direct field matches */}
      {searchResults.matches.length > 0 && (
        <div className="mb-6">
          <h3 className="mb-3 text-sm font-medium uppercase tracking-wide text-muted-foreground">
            Settings ({searchResults.matches.length})
          </h3>
          <div className="grid gap-2">
            {searchResults.matches.map((match, index) => {
              const Icon = CATEGORY_ICONS[match.categoryId]
              return (
                <Card
                  key={`${match.categoryId}-${match.fieldId}-${index}`}
                  className="cursor-pointer transition-colors hover:bg-accent"
                  onClick={() => onResultClick?.(match.categoryId, match.fieldId)}
                >
                  <CardContent className="p-3">
                    <div className="flex items-center gap-3">
                      <Icon className="h-4 w-4 text-muted-foreground" />
                      <div className="min-w-0 flex-1">
                        <div className="text-sm font-medium">{match.label}</div>
                        {match.description && (
                          <div className="text-xs text-muted-foreground">{match.description}</div>
                        )}
                      </div>
                      <Badge variant="secondary" className="text-xs">
                        {match.categoryId}
                      </Badge>
                    </div>
                  </CardContent>
                </Card>
              )
            })}
          </div>
        </div>
      )}

      {/* Category matches */}
      {searchResults.categories.length > 0 && (
        <div>
          <h3 className="mb-3 text-sm font-medium uppercase tracking-wide text-muted-foreground">
            Categories ({searchResults.categories.length})
          </h3>
          <div className="grid gap-2">
            {searchResults.categories.map((category) => {
              const Icon = CATEGORY_ICONS[category.id]
              return (
                <Card
                  key={category.id}
                  className="cursor-pointer transition-colors hover:bg-accent"
                  onClick={() => onResultClick?.(category.id)}
                >
                  <CardContent className="p-3">
                    <div className="flex items-center gap-3">
                      <Icon className="h-4 w-4 text-muted-foreground" />
                      <div className="min-w-0 flex-1">
                        <div className="text-sm font-medium">{category.label}</div>
                        <div className="text-xs text-muted-foreground">{category.description}</div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )
            })}
          </div>
        </div>
      )}
    </div>
  )
}

export default SettingsSearch
