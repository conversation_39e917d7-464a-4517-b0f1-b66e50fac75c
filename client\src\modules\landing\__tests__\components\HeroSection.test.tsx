/**
 * @vitest-environment jsdom
 */

import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { render, screen } from '@testing-library/react'
import { beforeEach, describe, expect, it, vi } from 'vitest'
import { HeroSection } from '../../components/HeroSection'
import { defaultLandingPageData } from '../../utils'

// Mock the useAuth hook
vi.mock('@/hooks/useAuth', () => ({
  useAuth: vi.fn(),
}))

// Mock Next.js Link component
vi.mock('next/link', () => ({
  default: ({ children, href, ...props }: any) => (
    <a href={href} {...props}>
      {children}
    </a>
  ),
}))

// Mock Intersection Observer
const mockIntersectionObserver = vi.fn()
mockIntersectionObserver.mockReturnValue({
  observe: () => null,
  unobserve: () => null,
  disconnect: () => null,
})
window.IntersectionObserver = mockIntersectionObserver

const createTestQueryClient = () =>
  new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
      },
    },
  })

const renderWithProviders = (component: React.ReactElement) => {
  const queryClient = createTestQueryClient()
  return render(<QueryClientProvider client={queryClient}>{component}</QueryClientProvider>)
}

const createMockAuthReturn = (overrides = {}) => ({
  isAuthenticated: false,
  user: null,
  token: null,
  login: vi.fn(),
  logout: vi.fn(),
  isLoading: false,
  hasRole: vi.fn(),
  isAdmin: false,
  requireAuth: vi.fn(),
  isLoginPending: false,
  isLogoutPending: false,
  ...overrides,
})

describe('HeroSection', () => {
  let mockUseAuth: any

  beforeEach(async () => {
    vi.clearAllMocks()
    const authModule = await import('@/hooks/useAuth')
    mockUseAuth = vi.mocked(authModule.useAuth)
  })

  it('renders hero section with default content', () => {
    mockUseAuth.mockReturnValue(createMockAuthReturn())

    renderWithProviders(<HeroSection hero={defaultLandingPageData.hero} />)

    expect(screen.getByText('Engineering Grade Software')).toBeInTheDocument()
    expect(screen.getByText('Ultimate Electrical')).toBeInTheDocument()
    expect(screen.getByText('Designer')).toBeInTheDocument()
    expect(screen.getByText(/Design heat tracing systems/)).toBeInTheDocument()
  })

  it('shows authenticated user content when user is logged in', () => {
    mockUseAuth.mockReturnValue(
      createMockAuthReturn({
        isAuthenticated: true,
        user: { name: 'John Doe', id: 1, email: '<EMAIL>' },
        token: 'mock-token',
      })
    )

    renderWithProviders(<HeroSection hero={defaultLandingPageData.hero} />)

    expect(screen.getByText('Go to Dashboard')).toBeInTheDocument()
    expect(screen.getByText('Welcome back, John Doe!')).toBeInTheDocument()
  })

  it('shows unauthenticated user content when user is not logged in', () => {
    mockUseAuth.mockReturnValue(createMockAuthReturn())

    renderWithProviders(<HeroSection hero={defaultLandingPageData.hero} />)

    expect(screen.getByText('Start Designing')).toBeInTheDocument()
    expect(screen.getByText('View Demo')).toBeInTheDocument()
  })

  it('renders trust indicators', () => {
    mockUseAuth.mockReturnValue(createMockAuthReturn())

    renderWithProviders(<HeroSection hero={defaultLandingPageData.hero} />)

    expect(screen.getByText('Enterprise Security')).toBeInTheDocument()
    expect(screen.getByText('Code Compliant')).toBeInTheDocument()
    expect(screen.getByText('Industry Standard')).toBeInTheDocument()
  })

  it('renders background pattern when enabled', () => {
    mockUseAuth.mockReturnValue(createMockAuthReturn())

    const heroWithPattern = {
      ...defaultLandingPageData.hero,
      backgroundPattern: true,
    }

    const { container } = renderWithProviders(<HeroSection hero={heroWithPattern} />)

    const patternElement = container.querySelector('pattern#hero-grid')
    expect(patternElement).toBeInTheDocument()
  })

  it('renders floating elements when enabled', () => {
    mockUseAuth.mockReturnValue(createMockAuthReturn())

    const heroWithFloatingElements = {
      ...defaultLandingPageData.hero,
      floatingElements: true,
    }

    const { container } = renderWithProviders(<HeroSection hero={heroWithFloatingElements} />)

    const floatingElements = container.querySelectorAll('.animate-pulse')
    expect(floatingElements.length).toBeGreaterThan(0)
  })

  it('has proper accessibility attributes', () => {
    mockUseAuth.mockReturnValue(createMockAuthReturn())

    renderWithProviders(<HeroSection hero={defaultLandingPageData.hero} />)

    // Check for proper heading hierarchy
    const mainHeading = screen.getByRole('heading', { level: 1 })
    expect(mainHeading).toBeInTheDocument()

    // Check for aria-hidden on decorative elements
    const decorativeElements = screen.getAllByLabelText('', { exact: false })
    expect(decorativeElements.length).toBeGreaterThanOrEqual(0)
  })

  it('applies custom className when provided', () => {
    mockUseAuth.mockReturnValue(createMockAuthReturn())

    const { container } = renderWithProviders(
      <HeroSection hero={defaultLandingPageData.hero} className="custom-class" />
    )

    const sectionElement = container.querySelector('section')
    expect(sectionElement).toHaveClass('custom-class')
  })
})
