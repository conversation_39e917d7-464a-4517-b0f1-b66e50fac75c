'use client'

import { But<PERSON> } from '@/components/ui/button'
import { useAuth } from '@/hooks/useAuth'
import { useEffect, useRef, useState } from 'react'
import { FormField, PasswordInput } from './forms/shared'
import { focusUtils, generateA11yAnnouncement, type FormState } from './utils/authHelpers'
import {
  loginSchema,
  parseAuthError,
  sanitizeLoginData,
  validateForm,
  type LoginFormData,
} from './utils/validation'

interface LoginFormProps {
  onSuccess?: () => void
  className?: string
  autoFocus?: boolean
}

export function LoginForm({ onSuccess, className = '', autoFocus = true }: LoginFormProps) {
  const { login, isLoading, loginError } = useAuth()
  const formRef = useRef<HTMLFormElement>(null)
  const [formState, setFormState] = useState<FormState>('idle')

  const [formData, setFormData] = useState<LoginFormData>({
    username: '',
    password: '',
  })
  const [errors, setErrors] = useState<Record<string, string>>({})

  // Auto-focus first field on mount
  useEffect(() => {
    if (autoFocus && formRef.current) {
      focusUtils.focusFirstField(formRef.current)
    }
  }, [autoFocus])

  // Handle form state changes for accessibility
  useEffect(() => {
    if (formState !== 'idle') {
      const announcement = generateA11yAnnouncement(formState, 'login', loginError?.message)
      if (announcement) {
        // Announce to screen readers
        const announcer = document.createElement('div')
        announcer.setAttribute('aria-live', 'polite')
        announcer.setAttribute('aria-atomic', 'true')
        announcer.className = 'sr-only'
        announcer.textContent = announcement
        document.body.appendChild(announcer)
        setTimeout(() => document.body.removeChild(announcer), 1000)
      }
    }
  }, [formState, loginError])

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }))

    // Clear field error when user starts typing
    if (errors[name]) {
      setErrors((prev) => ({
        ...prev,
        [name]: '',
      }))
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setFormState('submitting')

    // Validate form data
    const validation = validateForm(loginSchema, formData)
    if (!validation.success) {
      setErrors(validation.errors)
      setFormState('error')

      // Focus first error field
      if (formRef.current) {
        focusUtils.focusFirstError(formRef.current)
      }
      return
    }

    // Clear any existing errors
    setErrors({})

    try {
      // Sanitize and submit form data
      const sanitizedData = sanitizeLoginData(formData)
      await login(sanitizedData)

      setFormState('success')
      onSuccess?.()
    } catch (error) {
      setFormState('error')
      const authError = parseAuthError(error)

      // Set field-specific error if available
      if (authError.field) {
        setErrors({ [authError.field]: authError.message })
      }

      console.error('Login failed:', error)
    }
  }

  return (
    <form ref={formRef} className={`space-y-6 ${className}`} onSubmit={handleSubmit}>
      {/* Username/Email Field */}
      <FormField
        id="username"
        name="username"
        type="text"
        label="Username or Email"
        placeholder="Enter your username or email"
        value={formData.username}
        onChange={handleInputChange}
        error={errors.username}
        required
        autoComplete="username"
        icon={
          <svg fill="none" stroke="currentColor" viewBox="0 0 24 24" className="h-full w-full">
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
            />
          </svg>
        }
      />

      {/* Password Field */}
      <PasswordInput
        id="password"
        name="password"
        label="Password"
        placeholder="Enter your password"
        value={formData.password}
        onChange={handleInputChange}
        error={errors.password}
        required
        autoComplete="current-password"
      />

      {/* Global Error Display */}
      {loginError && formState === 'error' && (
        <div
          className="rounded-lg border border-error/20 bg-error/5 p-4"
          role="alert"
          aria-live="polite"
        >
          <div className="flex items-start">
            <div className="flex-shrink-0">
              <svg
                className="h-5 w-5 text-error"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                aria-hidden="true"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
                />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-semibold text-error">Authentication Failed</h3>
              <div className="mt-1 text-sm text-error/80">{parseAuthError(loginError).message}</div>
            </div>
          </div>
        </div>
      )}

      {/* Success Message */}
      {formState === 'success' && (
        <div
          className="rounded-lg border border-success/20 bg-success/5 p-4"
          role="alert"
          aria-live="polite"
        >
          <div className="flex items-start">
            <div className="flex-shrink-0">
              <svg
                className="h-5 w-5 text-success"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                aria-hidden="true"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-semibold text-success">Sign In Successful</h3>
              <div className="mt-1 text-sm text-success/80">Redirecting to your dashboard...</div>
            </div>
          </div>
        </div>
      )}

      {/* Remember Me Option */}
      <div className="flex items-center justify-between">
        <div className="flex items-center">
          <input
            id="remember-me"
            name="remember-me"
            type="checkbox"
            className="h-4 w-4 rounded border-neutral-300 text-brand-secondary focus:ring-brand-secondary"
          />
          <label htmlFor="remember-me" className="ml-2 block text-sm text-neutral-700">
            Remember me
          </label>
        </div>
        <div className="text-sm">
          <a
            href="#"
            className="font-medium text-brand-secondary transition-colors hover:text-brand-secondary/80"
          >
            Forgot your password?
          </a>
        </div>
      </div>

      {/* Submit Button */}
      <div className="space-y-4">
        <Button
          type="submit"
          disabled={isLoading || formState === 'submitting'}
          className="h-12 w-full text-base font-semibold"
          aria-describedby={formState === 'error' ? 'login-error' : undefined}
        >
          {isLoading || formState === 'submitting' ? (
            <>
              <svg
                className="-ml-1 mr-3 h-5 w-5 animate-spin text-white"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                aria-hidden="true"
              >
                <circle
                  className="opacity-25"
                  cx="12"
                  cy="12"
                  r="10"
                  stroke="currentColor"
                  strokeWidth="4"
                ></circle>
                <path
                  className="opacity-75"
                  fill="currentColor"
                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                ></path>
              </svg>
              Signing in...
            </>
          ) : (
            <>
              <svg
                className="mr-2 h-5 w-5"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                aria-hidden="true"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1"
                />
              </svg>
              Sign in to your workspace
            </>
          )}
        </Button>

        {/* Security Info */}
        <div className="text-center">
          <p className="flex items-center justify-center space-x-1 text-xs text-neutral-500">
            <svg
              className="h-3 w-3"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              aria-hidden="true"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"
              />
            </svg>
            <span>Your connection is secured with enterprise-grade encryption</span>
          </p>
        </div>
      </div>
    </form>
  )
}
