import { describe, it, expect } from 'vitest'
import {
  defaultLandingPageData,
  getFeatureById,
  getFeaturesByColor,
  getTrustIndicatorById,
  formatFeatureDescription,
  generateFeatureAriaLabel,
  getIconComponent,
  getBrandColorClass,
} from '../utils'
import type { FeatureItem, TrustIndicator } from '../types'

describe('Landing Page Utils', () => {
  describe('defaultLandingPageData', () => {
    it('has all required sections', () => {
      expect(defaultLandingPageData).toHaveProperty('hero')
      expect(defaultLandingPageData).toHaveProperty('features')
      expect(defaultLandingPageData).toHaveProperty('trustIndicators')
      expect(defaultLandingPageData).toHaveProperty('cta')
    })

    it('has valid hero section', () => {
      const { hero } = defaultLandingPageData
      expect(hero).toHaveProperty('badge')
      expect(hero).toHaveProperty('title')
      expect(hero).toHaveProperty('subtitle')
      expect(hero).toHaveProperty('description')
      expect(hero.badge).toHaveProperty('icon')
      expect(hero.badge).toHaveProperty('text')
    })

    it('has valid features array', () => {
      const { features } = defaultLandingPageData
      expect(Array.isArray(features)).toBe(true)
      expect(features.length).toBeGreaterThan(0)

      features.forEach((feature) => {
        expect(feature).toHaveProperty('id')
        expect(feature).toHaveProperty('title')
        expect(feature).toHaveProperty('description')
        expect(feature).toHaveProperty('icon')
        expect(feature).toHaveProperty('color')
        expect(['primary', 'secondary', 'accent', 'dark']).toContain(feature.color)
      })
    })

    it('has valid trust indicators array', () => {
      const { trustIndicators } = defaultLandingPageData
      expect(Array.isArray(trustIndicators)).toBe(true)
      expect(trustIndicators.length).toBeGreaterThan(0)

      trustIndicators.forEach((indicator) => {
        expect(indicator).toHaveProperty('id')
        expect(indicator).toHaveProperty('icon')
        expect(indicator).toHaveProperty('label')
        expect(indicator).toHaveProperty('value')
      })
    })

    it('has valid CTA section', () => {
      const { cta } = defaultLandingPageData
      expect(cta).toHaveProperty('title')
      expect(cta).toHaveProperty('subtitle')
      expect(cta).toHaveProperty('description')
      expect(cta).toHaveProperty('primaryAction')
      expect(cta).toHaveProperty('secondaryAction')
      expect(cta.primaryAction).toHaveProperty('label')
      expect(cta.primaryAction).toHaveProperty('href')
    })
  })

  describe('getFeatureById', () => {
    const testFeatures: FeatureItem[] = [
      { id: 'test1', title: 'Test 1', description: 'Desc 1', icon: 'icon1', color: 'primary' },
      { id: 'test2', title: 'Test 2', description: 'Desc 2', icon: 'icon2', color: 'secondary' },
    ]

    it('returns feature when found', () => {
      const result = getFeatureById(testFeatures, 'test1')
      expect(result).toEqual(testFeatures[0])
    })

    it('returns undefined when not found', () => {
      const result = getFeatureById(testFeatures, 'nonexistent')
      expect(result).toBeUndefined()
    })

    it('handles empty array', () => {
      const result = getFeatureById([], 'test1')
      expect(result).toBeUndefined()
    })
  })

  describe('getFeaturesByColor', () => {
    const testFeatures: FeatureItem[] = [
      { id: 'test1', title: 'Test 1', description: 'Desc 1', icon: 'icon1', color: 'primary' },
      { id: 'test2', title: 'Test 2', description: 'Desc 2', icon: 'icon2', color: 'primary' },
      { id: 'test3', title: 'Test 3', description: 'Desc 3', icon: 'icon3', color: 'secondary' },
    ]

    it('returns features with matching color', () => {
      const result = getFeaturesByColor(testFeatures, 'primary')
      expect(result).toHaveLength(2)
      expect(result[0].id).toBe('test1')
      expect(result[1].id).toBe('test2')
    })

    it('returns empty array when no matches', () => {
      const result = getFeaturesByColor(testFeatures, 'accent')
      expect(result).toHaveLength(0)
    })

    it('handles empty array', () => {
      const result = getFeaturesByColor([], 'primary')
      expect(result).toHaveLength(0)
    })
  })

  describe('getTrustIndicatorById', () => {
    const testIndicators: TrustIndicator[] = [
      { id: 'test1', icon: 'icon1', label: 'Label 1', value: 'Value 1' },
      { id: 'test2', icon: 'icon2', label: 'Label 2', value: 'Value 2' },
    ]

    it('returns indicator when found', () => {
      const result = getTrustIndicatorById(testIndicators, 'test1')
      expect(result).toEqual(testIndicators[0])
    })

    it('returns undefined when not found', () => {
      const result = getTrustIndicatorById(testIndicators, 'nonexistent')
      expect(result).toBeUndefined()
    })
  })

  describe('formatFeatureDescription', () => {
    it('formats feature description correctly', () => {
      const feature: FeatureItem = {
        id: 'test',
        title: 'Test Feature',
        description: 'This is a test description',
        icon: 'icon',
        color: 'primary',
      }

      const result = formatFeatureDescription(feature)
      expect(result).toBe('Test Feature: This is a test description')
    })
  })

  describe('generateFeatureAriaLabel', () => {
    it('generates correct aria label', () => {
      const feature: FeatureItem = {
        id: 'test',
        title: 'Test Feature',
        description: 'Description',
        icon: 'icon',
        color: 'primary',
      }

      const result = generateFeatureAriaLabel(feature)
      expect(result).toBe('Learn more about Test Feature feature')
    })
  })

  describe('getIconComponent', () => {
    it('converts kebab-case to PascalCase', () => {
      expect(getIconComponent('check-circle')).toBe('CheckCircle')
      expect(getIconComponent('user-plus')).toBe('UserPlus')
      expect(getIconComponent('single-word')).toBe('SingleWord')
    })

    it('handles single words', () => {
      expect(getIconComponent('user')).toBe('User')
      expect(getIconComponent('home')).toBe('Home')
    })

    it('handles empty string', () => {
      expect(getIconComponent('')).toBe('')
    })
  })

  describe('getBrandColorClass', () => {
    it('returns correct background classes', () => {
      expect(getBrandColorClass('primary', 'bg')).toBe('bg-brand-primary')
      expect(getBrandColorClass('secondary', 'bg')).toBe('bg-brand-secondary')
      expect(getBrandColorClass('accent', 'bg')).toBe('bg-brand-accent')
      expect(getBrandColorClass('dark', 'bg')).toBe('bg-brand-dark')
    })

    it('returns correct text classes', () => {
      expect(getBrandColorClass('primary', 'text')).toBe('text-brand-primary')
      expect(getBrandColorClass('secondary', 'text')).toBe('text-brand-secondary')
    })

    it('returns correct border classes', () => {
      expect(getBrandColorClass('primary', 'border')).toBe('border-brand-primary')
      expect(getBrandColorClass('accent', 'border')).toBe('border-brand-accent')
    })

    it('defaults to bg type when not specified', () => {
      expect(getBrandColorClass('primary')).toBe('bg-brand-primary')
    })
  })
})
