/**
 * Settings Synchronization Hook
 * Ultimate Electrical Designer - Settings & User Preferences
 */

import { useQueryClient } from '@tanstack/react-query'
import React, { useCallback, useEffect, useRef } from 'react'
import { SYNC_CHANNELS, UI_CONSTANTS } from '../constants'
import { useSettingsStore } from '../stores/settingsStore'
import type { UserPreferences, UserPreferencesUpdate } from '../types'
import { SettingsQueryKeys } from '../types'

interface SyncMessage {
  type: 'SETTINGS_UPDATE' | 'SETTINGS_RESET' | 'SETTINGS_IMPORT' | 'SYNC_REQUEST'
  data?: UserPreferencesUpdate
  timestamp: number
  tabId: string
  source: 'user' | 'auto' | 'import'
}

interface UseSettingsSyncOptions {
  enabled?: boolean
  syncInterval?: number
  conflictResolution?: 'latest' | 'manual' | 'merge'
}

/**
 * Hook for cross-tab settings synchronization
 */
export function useSettingsSync(options: UseSettingsSyncOptions = {}) {
  const {
    enabled = true,
    syncInterval = UI_CONSTANTS.SYNC_INTERVAL_MS,
    conflictResolution = 'latest',
  } = options

  const queryClient = useQueryClient()
  const { updateFormData, syncEnabled, lastSyncTime } = useSettingsStore()
  const channelRef = useRef<BroadcastChannel | null>(null)
  const tabIdRef = useRef<string>(generateTabId())
  const lastBroadcastRef = useRef<number>(0)

  // Initialize broadcast channel
  useEffect(() => {
    if (
      !enabled ||
      !syncEnabled ||
      typeof window === 'undefined' ||
      !('BroadcastChannel' in window)
    ) {
      return
    }

    try {
      channelRef.current = new BroadcastChannel(SYNC_CHANNELS.SETTINGS)

      channelRef.current.addEventListener('message', handleSyncMessage)

      // Send sync request to get latest state from other tabs
      broadcastMessage({
        type: 'SYNC_REQUEST',
        timestamp: Date.now(),
        tabId: tabIdRef.current,
        source: 'user',
      })

      return () => {
        if (channelRef.current) {
          channelRef.current.removeEventListener('message', handleSyncMessage)
          channelRef.current.close()
          channelRef.current = null
        }
      }
    } catch (error) {
      console.warn('Failed to initialize settings sync:', error)
    }
  }, [enabled, syncEnabled, broadcastMessage, handleSyncMessage])

  // Handle incoming sync messages
  const handleSyncMessage = useCallback(
    (event: MessageEvent<SyncMessage>) => {
      const { type, data, timestamp, tabId, source } = event.data

      // Ignore messages from the same tab
      if (tabId === tabIdRef.current) {
        return
      }

      // Ignore old messages
      if (lastSyncTime && timestamp < new Date(lastSyncTime).getTime()) {
        return
      }

      switch (type) {
        case 'SETTINGS_UPDATE':
          if (data) {
            handleRemoteUpdate(data, timestamp, source)
          }
          break

        case 'SETTINGS_RESET':
          handleRemoteReset(timestamp)
          break

        case 'SETTINGS_IMPORT':
          if (data) {
            handleRemoteImport(data, timestamp)
          }
          break

        case 'SYNC_REQUEST':
          // Respond with current state if we have recent changes
          const currentPreferences = queryClient.getQueryData<UserPreferences>(
            SettingsQueryKeys.preferences()
          )
          if (currentPreferences && lastBroadcastRef.current > timestamp) {
            broadcastMessage({
              type: 'SETTINGS_UPDATE',
              data: currentPreferences,
              timestamp: Date.now(),
              tabId: tabIdRef.current,
              source: 'user',
            })
          }
          break
      }
    },
    [
      lastSyncTime,
      queryClient,
      broadcastMessage,
      handleRemoteImport,
      handleRemoteReset,
      handleRemoteUpdate,
    ]
  )

  // Handle remote settings update
  const handleRemoteUpdate = useCallback(
    (data: UserPreferencesUpdate, timestamp: number, source: 'user' | 'auto' | 'import') => {
      if (conflictResolution === 'latest') {
        // Apply remote changes immediately
        updateFormData(data)
        useSettingsStore.setState({ lastSyncTime: new Date(timestamp).toISOString() })
      } else if (conflictResolution === 'merge') {
        // Merge with local changes
        const currentFormData = useSettingsStore.getState().formData
        const mergedData = { ...currentFormData, ...data }
        updateFormData(mergedData)
        useSettingsStore.setState({ lastSyncTime: new Date(timestamp).toISOString() })
      }
      // For 'manual' resolution, we would show a conflict dialog (not implemented here)
    },
    [conflictResolution, updateFormData]
  )

  // Handle remote reset
  const handleRemoteReset = useCallback(
    (timestamp: number) => {
      // Invalidate local cache and refetch
      queryClient.invalidateQueries({ queryKey: SettingsQueryKeys.preferences() })
      useSettingsStore.getState().resetFormData()
      useSettingsStore.setState({ lastSyncTime: new Date(timestamp).toISOString() })
    },
    [queryClient]
  )

  // Handle remote import
  const handleRemoteImport = useCallback(
    (data: UserPreferencesUpdate, timestamp: number) => {
      // Similar to update but with import source
      updateFormData(data)
      queryClient.invalidateQueries({ queryKey: SettingsQueryKeys.preferences() })
      useSettingsStore.setState({ lastSyncTime: new Date(timestamp).toISOString() })
    },
    [updateFormData, queryClient]
  )

  // Broadcast message to other tabs
  const broadcastMessage = useCallback(
    (message: Omit<SyncMessage, 'timestamp' | 'tabId'>) => {
      if (!channelRef.current || !syncEnabled) {
        return
      }

      try {
        const fullMessage: SyncMessage = {
          ...message,
          timestamp: Date.now(),
          tabId: tabIdRef.current,
        }

        channelRef.current.postMessage(fullMessage)
        lastBroadcastRef.current = fullMessage.timestamp
      } catch (error) {
        console.warn('Failed to broadcast sync message:', error)
      }
    },
    [syncEnabled]
  )

  // Broadcast settings update
  const broadcastUpdate = useCallback(
    (data: UserPreferencesUpdate, source: 'user' | 'auto' | 'import' = 'user') => {
      broadcastMessage({
        type: 'SETTINGS_UPDATE',
        data,
        source,
      })
    },
    [broadcastMessage]
  )

  // Broadcast settings reset
  const broadcastReset = useCallback(() => {
    broadcastMessage({
      type: 'SETTINGS_RESET',
      source: 'user',
    })
  }, [broadcastMessage])

  // Broadcast settings import
  const broadcastImport = useCallback(
    (data: UserPreferencesUpdate) => {
      broadcastMessage({
        type: 'SETTINGS_IMPORT',
        data,
        source: 'import',
      })
    },
    [broadcastMessage]
  )

  // Enable/disable sync
  const enableSync = useCallback(() => {
    useSettingsStore.getState().enableSync()
  }, [])

  const disableSync = useCallback(() => {
    useSettingsStore.getState().disableSync()
  }, [])

  // Get sync status
  const getSyncStatus = useCallback(() => {
    return {
      enabled: syncEnabled,
      connected: channelRef.current !== null,
      lastSync: lastSyncTime,
      tabId: tabIdRef.current,
    }
  }, [syncEnabled, lastSyncTime])

  return {
    // Status
    isEnabled: enabled && syncEnabled,
    isConnected: channelRef.current !== null,
    lastSyncTime,
    tabId: tabIdRef.current,

    // Actions
    broadcastUpdate,
    broadcastReset,
    broadcastImport,
    enableSync,
    disableSync,
    getSyncStatus,

    // Utilities
    conflictResolution,
  }
}

/**
 * Hook for monitoring sync conflicts
 */
export function useSettingsSyncConflicts() {
  const [conflicts, setConflicts] = React.useState<
    Array<{
      field: string
      localValue: any
      remoteValue: any
      timestamp: number
    }>
  >([])

  const resolveConflict = useCallback((field: string, resolution: 'local' | 'remote' | 'merge') => {
    setConflicts((prev) => prev.filter((conflict) => conflict.field !== field))
    // Implementation would depend on the specific resolution strategy
  }, [])

  const resolveAllConflicts = useCallback((resolution: 'local' | 'remote' | 'merge') => {
    setConflicts([])
    // Implementation would resolve all conflicts with the chosen strategy
  }, [])

  return {
    conflicts,
    hasConflicts: conflicts.length > 0,
    resolveConflict,
    resolveAllConflicts,
  }
}

/**
 * Hook for settings backup and restore
 */
export function useSettingsBackup() {
  const queryClient = useQueryClient()

  const createBackup = useCallback(async () => {
    const preferences = queryClient.getQueryData<UserPreferences>(SettingsQueryKeys.preferences())
    if (!preferences) {
      throw new Error('No preferences data available for backup')
    }

    const backup = {
      preferences,
      timestamp: new Date().toISOString(),
      version: '1.0',
      metadata: {
        userAgent: navigator.userAgent,
        url: window.location.href,
      },
    }

    // Store in localStorage as emergency backup
    try {
      localStorage.setItem('ued-settings-backup', JSON.stringify(backup))
    } catch (error) {
      console.warn('Failed to store emergency backup:', error)
    }

    return backup
  }, [queryClient])

  const restoreBackup = useCallback(
    async (backup: any) => {
      if (!backup.preferences) {
        throw new Error('Invalid backup format')
      }

      // Validate backup data
      const { preferences } = backup

      // Update query cache
      queryClient.setQueryData(SettingsQueryKeys.preferences(), preferences)

      // Update form data
      useSettingsStore.getState().updateFormData(preferences)

      return preferences
    },
    [queryClient]
  )

  const getEmergencyBackup = useCallback(() => {
    try {
      const backup = localStorage.getItem('ued-settings-backup')
      return backup ? JSON.parse(backup) : null
    } catch (error) {
      console.warn('Failed to retrieve emergency backup:', error)
      return null
    }
  }, [])

  const clearEmergencyBackup = useCallback(() => {
    try {
      localStorage.removeItem('ued-settings-backup')
    } catch (error) {
      console.warn('Failed to clear emergency backup:', error)
    }
  }, [])

  return {
    createBackup,
    restoreBackup,
    getEmergencyBackup,
    clearEmergencyBackup,
  }
}

// Utility functions
function generateTabId(): string {
  return `tab-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
}

export default useSettingsSync
