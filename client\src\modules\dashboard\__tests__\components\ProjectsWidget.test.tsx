/**
 * Unit tests for ProjectsWidget component
 */

import { render, screen, fireEvent } from '@testing-library/react'
import { describe, it, expect, vi } from 'vitest'
import { ProjectsWidget } from '../../components/ProjectsWidget'
import type { ProjectSummary } from '../../types'

const mockProjects: ProjectSummary[] = [
  {
    id: '1',
    name: 'Industrial Heat Tracing System',
    description: 'Heat tracing design for chemical processing plant',
    status: 'active',
    progress: 75,
    lastModified: new Date('2024-12-19T09:00:00Z').toISOString(),
    createdAt: new Date('2024-12-12T09:00:00Z').toISOString(),
    owner: '<PERSON>',
    type: 'heat_tracing',
    priority: 'high',
  },
  {
    id: '2',
    name: 'Power Distribution Analysis',
    description: 'Load calculation for manufacturing facility',
    status: 'completed',
    progress: 100,
    lastModified: new Date('2024-12-18T14:30:00Z').toISOString(),
    createdAt: new Date('2024-12-10T10:00:00Z').toISOString(),
    owner: '<PERSON>',
    type: 'load_calculation',
    priority: 'medium',
  },
]

describe('ProjectsWidget', () => {
  it('renders projects correctly', () => {
    render(<ProjectsWidget projects={mockProjects} />)

    expect(screen.getByText('Recent Projects')).toBeInTheDocument()
    expect(screen.getByText('Industrial Heat Tracing System')).toBeInTheDocument()
    expect(screen.getByText('Power Distribution Analysis')).toBeInTheDocument()
    expect(
      screen.getByText('Heat tracing design for chemical processing plant')
    ).toBeInTheDocument()
    expect(screen.getByText('Load calculation for manufacturing facility')).toBeInTheDocument()
  })

  it('displays project status and priority badges', () => {
    render(<ProjectsWidget projects={mockProjects} />)

    expect(screen.getByText('Active')).toBeInTheDocument()
    expect(screen.getByText('Completed')).toBeInTheDocument()
    expect(screen.getByText('High')).toBeInTheDocument()
    expect(screen.getByText('Medium')).toBeInTheDocument()
  })

  it('shows project progress bars', () => {
    render(<ProjectsWidget projects={mockProjects} />)

    expect(screen.getByText('75%')).toBeInTheDocument()
    expect(screen.getByText('100%')).toBeInTheDocument()
  })

  it('displays project owners and last modified times', () => {
    render(<ProjectsWidget projects={mockProjects} />)

    expect(screen.getByText('by John Smith')).toBeInTheDocument()
    expect(screen.getByText('by Sarah Johnson')).toBeInTheDocument()
  })

  it('calls onProjectClick when project is clicked', () => {
    const onProjectClick = vi.fn()
    render(<ProjectsWidget projects={mockProjects} onProjectClick={onProjectClick} />)

    const projectCard = screen.getByText('Industrial Heat Tracing System').closest('div')
    fireEvent.click(projectCard!)

    expect(onProjectClick).toHaveBeenCalledWith(mockProjects[0])
  })

  it('calls onCreateProject when new project button is clicked', () => {
    const onCreateProject = vi.fn()
    render(<ProjectsWidget projects={mockProjects} onCreateProject={onCreateProject} />)

    const newProjectButton = screen.getByText('New Project')
    fireEvent.click(newProjectButton)

    expect(onCreateProject).toHaveBeenCalled()
  })

  it('displays loading state correctly', () => {
    render(<ProjectsWidget projects={[]} isLoading={true} />)

    const loadingElements = screen.getAllByRole('generic')
    const animatedElements = loadingElements.filter((el) => el.className.includes('animate-pulse'))

    expect(animatedElements.length).toBeGreaterThan(0)
  })

  it('shows empty state when no projects', () => {
    render(<ProjectsWidget projects={[]} />)

    expect(screen.getByText('No projects')).toBeInTheDocument()
    expect(screen.getByText('Get started by creating a new project.')).toBeInTheDocument()
  })

  it('shows "View all" button when more than 5 projects', () => {
    const manyProjects = Array.from({ length: 7 }, (_, i) => ({
      ...mockProjects[0],
      id: `project-${i}`,
      name: `Project ${i + 1}`,
    }))

    render(<ProjectsWidget projects={manyProjects} />)

    expect(screen.getByText('View all 7 projects')).toBeInTheDocument()
  })

  it('limits display to 5 projects', () => {
    const manyProjects = Array.from({ length: 7 }, (_, i) => ({
      ...mockProjects[0],
      id: `project-${i}`,
      name: `Project ${i + 1}`,
    }))

    render(<ProjectsWidget projects={manyProjects} />)

    // Should only show first 5 projects
    expect(screen.getByText('Project 1')).toBeInTheDocument()
    expect(screen.getByText('Project 5')).toBeInTheDocument()
    expect(screen.queryByText('Project 6')).not.toBeInTheDocument()
    expect(screen.queryByText('Project 7')).not.toBeInTheDocument()
  })

  it('applies custom className', () => {
    const { container } = render(
      <ProjectsWidget projects={mockProjects} className="custom-class" />
    )

    expect(container.firstChild).toHaveClass('custom-class')
  })

  it('has proper accessibility attributes', () => {
    render(<ProjectsWidget projects={mockProjects} />)

    expect(screen.getByRole('heading', { name: 'Recent Projects' })).toBeInTheDocument()

    // Check for proper button accessibility
    const newProjectButton = screen.getByRole('button', { name: /New Project/ })
    expect(newProjectButton).toBeInTheDocument()
  })

  it('shows hover effects on project cards', () => {
    render(<ProjectsWidget projects={mockProjects} onProjectClick={vi.fn()} />)

    const projectCards = screen
      .getAllByRole('generic')
      .filter(
        (el) => el.className.includes('cursor-pointer') && el.className.includes('hover:bg-gray-50')
      )

    expect(projectCards.length).toBeGreaterThan(0)
  })
})
