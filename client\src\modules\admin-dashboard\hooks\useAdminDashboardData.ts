/**
 * React Query hooks for admin dashboard data fetching
 */

import { useAuth } from '@/hooks/useAuth'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import {
  bulkUserOperation,
  executeQuickAdminAction,
  exportAuditLogs,
  fetchAdminDashboardData,
  fetchAuditLogs,
  fetchComponentLibraryStats,
  fetchProjectSummaries,
  fetchQuickAdminActions,
  fetchSecurityAlerts,
  fetchSystemConfiguration,
  fetchSystemMetrics,
  fetchUserSummaries,
  getSystemHealth,
  resolveSecurityAlert,
  updateSystemConfiguration,
  updateUserRole,
  updateUserStatus,
} from '../api'
import type {
  AuditLogsApiParams,
  ProjectsApiParams,
  SecurityAlertsApiParams,
  UsersApiParams,
} from '../types'

// Query keys
export const adminDashboardKeys = {
  all: ['admin-dashboard'] as const,
  data: () => [...adminDashboardKeys.all, 'data'] as const,
  metrics: () => [...adminDashboardKeys.all, 'metrics'] as const,
  users: (params?: UsersApiParams) => [...adminDashboardKeys.all, 'users', params] as const,
  projects: (params?: ProjectsApiParams) =>
    [...adminDashboardKeys.all, 'projects', params] as const,
  auditLogs: (params?: AuditLogsApiParams) =>
    [...adminDashboardKeys.all, 'audit-logs', params] as const,
  securityAlerts: (params?: SecurityAlertsApiParams) =>
    [...adminDashboardKeys.all, 'security-alerts', params] as const,
  componentStats: () => [...adminDashboardKeys.all, 'component-stats'] as const,
  systemConfig: () => [...adminDashboardKeys.all, 'system-config'] as const,
  quickActions: () => [...adminDashboardKeys.all, 'quick-actions'] as const,
  systemHealth: () => [...adminDashboardKeys.all, 'system-health'] as const,
}

/**
 * Hook to fetch complete admin dashboard data
 */
export function useAdminDashboardData() {
  const { isAdmin, isAuthenticated } = useAuth()

  return useQuery({
    queryKey: adminDashboardKeys.data(),
    queryFn: fetchAdminDashboardData,
    enabled: isAuthenticated && isAdmin(),
    staleTime: 2 * 60 * 1000, // 2 minutes
    refetchInterval: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: true,
    retry: (failureCount, error: any) => {
      // Don't retry on authentication/authorization errors
      if (error?.response?.status === 401 || error?.response?.status === 403) {
        return false
      }
      return failureCount < 3
    },
  })
}

/**
 * Hook to fetch system metrics
 */
export function useSystemMetrics() {
  const { isAdmin, isAuthenticated } = useAuth()

  return useQuery({
    queryKey: adminDashboardKeys.metrics(),
    queryFn: fetchSystemMetrics,
    enabled: isAuthenticated && isAdmin(),
    staleTime: 30 * 1000, // 30 seconds
    refetchInterval: 60 * 1000, // 1 minute
    refetchOnWindowFocus: true,
    retry: (failureCount, error: any) => {
      if (error?.response?.status === 401 || error?.response?.status === 403) {
        return false
      }
      return failureCount < 3
    },
  })
}

/**
 * Hook to fetch user summaries
 */
export function useUserSummaries(params?: UsersApiParams) {
  const { isAdmin } = useAuth()

  return useQuery({
    queryKey: adminDashboardKeys.users(params),
    queryFn: () => fetchUserSummaries(params),
    enabled: isAdmin(),
    staleTime: 2 * 60 * 1000, // 2 minutes
    refetchOnWindowFocus: true,
    retry: 3,
  })
}

/**
 * Hook to fetch project summaries
 */
export function useProjectSummaries(params?: ProjectsApiParams) {
  const { isAdmin } = useAuth()

  return useQuery({
    queryKey: adminDashboardKeys.projects(params),
    queryFn: () => fetchProjectSummaries(params),
    enabled: isAdmin(),
    staleTime: 2 * 60 * 1000, // 2 minutes
    refetchOnWindowFocus: true,
    retry: 3,
  })
}

/**
 * Hook to fetch audit logs
 */
export function useAuditLogs(params?: AuditLogsApiParams) {
  const { isAdmin } = useAuth()

  return useQuery({
    queryKey: adminDashboardKeys.auditLogs(params),
    queryFn: () => fetchAuditLogs(params),
    enabled: isAdmin(),
    staleTime: 1 * 60 * 1000, // 1 minute
    refetchOnWindowFocus: true,
    retry: 3,
  })
}

/**
 * Hook to fetch security alerts
 */
export function useSecurityAlerts(params?: SecurityAlertsApiParams) {
  const { isAdmin } = useAuth()

  return useQuery({
    queryKey: adminDashboardKeys.securityAlerts(params),
    queryFn: () => fetchSecurityAlerts(params),
    enabled: isAdmin(),
    staleTime: 30 * 1000, // 30 seconds
    refetchInterval: 60 * 1000, // 1 minute
    refetchOnWindowFocus: true,
    retry: 3,
  })
}

/**
 * Hook to fetch component library statistics
 */
export function useComponentLibraryStats() {
  const { isAdmin } = useAuth()

  return useQuery({
    queryKey: adminDashboardKeys.componentStats(),
    queryFn: fetchComponentLibraryStats,
    enabled: isAdmin(),
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: true,
    retry: 3,
  })
}

/**
 * Hook to fetch system configuration
 */
export function useSystemConfiguration() {
  const { isAdmin } = useAuth()

  return useQuery({
    queryKey: adminDashboardKeys.systemConfig(),
    queryFn: fetchSystemConfiguration,
    enabled: isAdmin(),
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: true,
    retry: 3,
  })
}

/**
 * Hook to fetch quick admin actions
 */
export function useQuickAdminActions() {
  const { isAdmin } = useAuth()

  return useQuery({
    queryKey: adminDashboardKeys.quickActions(),
    queryFn: fetchQuickAdminActions,
    enabled: isAdmin(),
    staleTime: 10 * 60 * 1000, // 10 minutes
    retry: 3,
  })
}

/**
 * Hook to fetch system health
 */
export function useSystemHealth() {
  const { isAdmin } = useAuth()

  return useQuery({
    queryKey: adminDashboardKeys.systemHealth(),
    queryFn: getSystemHealth,
    enabled: isAdmin(),
    staleTime: 30 * 1000, // 30 seconds
    refetchInterval: 60 * 1000, // 1 minute
    refetchOnWindowFocus: true,
    retry: 3,
  })
}

/**
 * Mutation hook to update user status
 */
export function useUpdateUserStatus() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ userId, isActive }: { userId: string; isActive: boolean }) =>
      updateUserStatus(userId, isActive),
    onSuccess: () => {
      // Invalidate and refetch user-related queries
      queryClient.invalidateQueries({ queryKey: adminDashboardKeys.users() })
      queryClient.invalidateQueries({ queryKey: adminDashboardKeys.data() })
      queryClient.invalidateQueries({ queryKey: adminDashboardKeys.metrics() })
    },
  })
}

/**
 * Mutation hook to update user role
 */
export function useUpdateUserRole() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ userId, role }: { userId: string; role: string }) =>
      updateUserRole(userId, role),
    onSuccess: () => {
      // Invalidate and refetch user-related queries
      queryClient.invalidateQueries({ queryKey: adminDashboardKeys.users() })
      queryClient.invalidateQueries({ queryKey: adminDashboardKeys.data() })
      queryClient.invalidateQueries({ queryKey: adminDashboardKeys.metrics() })
    },
  })
}

/**
 * Mutation hook to resolve security alert
 */
export function useResolveSecurityAlert() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ alertId, resolvedBy }: { alertId: string; resolvedBy: string }) =>
      resolveSecurityAlert(alertId, resolvedBy),
    onSuccess: () => {
      // Invalidate and refetch security-related queries
      queryClient.invalidateQueries({ queryKey: adminDashboardKeys.securityAlerts() })
      queryClient.invalidateQueries({ queryKey: adminDashboardKeys.data() })
    },
  })
}

/**
 * Mutation hook to update system configuration
 */
export function useUpdateSystemConfiguration() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({
      configId,
      value,
      modifiedBy,
    }: {
      configId: string
      value: string
      modifiedBy: string
    }) => updateSystemConfiguration(configId, value, modifiedBy),
    onSuccess: () => {
      // Invalidate and refetch configuration queries
      queryClient.invalidateQueries({ queryKey: adminDashboardKeys.systemConfig() })
      queryClient.invalidateQueries({ queryKey: adminDashboardKeys.data() })
    },
  })
}

/**
 * Mutation hook to execute quick admin action
 */
export function useExecuteQuickAdminAction() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ actionId, params }: { actionId: string; params?: Record<string, any> }) =>
      executeQuickAdminAction(actionId, params),
    onSuccess: () => {
      // Invalidate all queries as admin actions can affect any data
      queryClient.invalidateQueries({ queryKey: adminDashboardKeys.all })
    },
  })
}

/**
 * Mutation hook to export audit logs
 */
export function useExportAuditLogs() {
  return useMutation({
    mutationFn: (params?: AuditLogsApiParams) => exportAuditLogs(params),
    onSuccess: (blob) => {
      // Create download link
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `audit-logs-${new Date().toISOString().split('T')[0]}.csv`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)
    },
  })
}

/**
 * Mutation hook for bulk user operations
 */
export function useBulkUserOperation() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({
      operation,
      userIds,
      params,
    }: {
      operation: 'activate' | 'deactivate' | 'delete' | 'change_role'
      userIds: string[]
      params?: { role?: string }
    }) => bulkUserOperation(operation, userIds, params),
    onSuccess: () => {
      // Invalidate and refetch user-related queries
      queryClient.invalidateQueries({ queryKey: adminDashboardKeys.users() })
      queryClient.invalidateQueries({ queryKey: adminDashboardKeys.data() })
      queryClient.invalidateQueries({ queryKey: adminDashboardKeys.metrics() })
    },
  })
}

/**
 * Hook to refresh all admin dashboard data
 */
export function useRefreshAdminDashboard() {
  const queryClient = useQueryClient()

  return () => {
    queryClient.invalidateQueries({ queryKey: adminDashboardKeys.all })
  }
}
