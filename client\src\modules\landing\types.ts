/**
 * Type definitions for the landing page domain
 */

export interface FeatureItem {
  id: string
  title: string
  description: string
  icon: string
  color: 'primary' | 'secondary' | 'accent' | 'dark'
  href?: string
}

export interface TrustIndicator {
  id: string
  icon: string
  label: string
  value: string
}

export interface TestimonialItem {
  id: string
  name: string
  role: string
  company: string
  content: string
  avatar?: string
  rating: number
}

export interface CTASectionData {
  title: string
  subtitle: string
  description: string
  primaryAction: {
    label: string
    href: string
  }
  secondaryAction?: {
    label: string
    href: string
  }
}

export interface HeroSectionData {
  badge: {
    icon: string
    text: string
  }
  title: string
  subtitle: string
  description: string
  backgroundPattern?: boolean
  floatingElements?: boolean
}

export interface LandingPageData {
  hero: HeroSectionData
  features: FeatureItem[]
  trustIndicators: TrustIndicator[]
  testimonials?: TestimonialItem[]
  cta: CTASectionData
}

export interface LandingPageProps {
  data?: Partial<LandingPageData>
  className?: string
}

export interface FeatureCardProps {
  feature: FeatureItem
  index: number
  className?: string
}

export interface TrustIndicatorProps {
  indicators: TrustIndicator[]
  className?: string
}

export interface HeroSectionProps {
  hero: HeroSectionData
  isAuthenticated: boolean
  user?: { name: string } | null
  className?: string
}

export interface CTASectionProps {
  cta: CTASectionData
  isAuthenticated: boolean
  className?: string
}
