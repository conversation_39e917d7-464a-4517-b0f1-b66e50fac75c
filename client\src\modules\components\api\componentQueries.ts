/**
 * Enhanced React Query hooks for component management queries
 * Includes caching strategies, error handling, and performance optimizations
 */

import type {
  ComponentAdvancedSearch,
  ComponentAdvancedSearchResponse,
  ComponentPaginatedResponse,
  ComponentRead,
  ComponentSearch,
  ComponentStats,
  ComponentSummary,
} from '@/types/api'
import { QueryKeys } from '@/types/api'
import { useQuery, UseQueryOptions } from '@tanstack/react-query'
import { useEffect, useState } from 'react'
import { componentApi } from './componentApi'

// Hook for listing components with pagination and filtering
export function useComponents(
  params: {
    page?: number
    size?: number
    search_term?: string
    component_category_id?: number
    component_type_id?: number
    manufacturer?: string
    is_preferred?: boolean
    is_active?: boolean
  } = {},
  options?: Omit<UseQueryOptions<ComponentPaginatedResponse>, 'queryKey' | 'queryFn'>
) {
  return useQuery({
    queryKey: QueryKeys.componentsList(params),
    queryFn: async () => {
      const response = await componentApi.list(params)
      if (response.error) {
        throw new Error(response.error.detail)
      }
      return response.data!
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    ...options,
  })
}

// Hook for getting a single component by ID
export function useComponent(
  id: number,
  options?: Omit<UseQueryOptions<ComponentRead>, 'queryKey' | 'queryFn'>
) {
  return useQuery({
    queryKey: QueryKeys.component(id),
    queryFn: async () => {
      const response = await componentApi.getById(id)
      if (response.error) {
        throw new Error(response.error.detail)
      }
      return response.data!
    },
    enabled: !!id,
    staleTime: 10 * 60 * 1000, // 10 minutes
    ...options,
  })
}

// Hook for component search
export function useComponentSearch(
  searchParams: ComponentSearch,
  pagination: { page?: number; size?: number } = {},
  options?: Omit<UseQueryOptions<ComponentPaginatedResponse>, 'queryKey' | 'queryFn'>
) {
  return useQuery({
    queryKey: QueryKeys.componentsSearch({ ...searchParams, ...pagination }),
    queryFn: async () => {
      const response = await componentApi.search(searchParams, pagination)
      if (response.error) {
        throw new Error(response.error.detail)
      }
      return response.data!
    },
    enabled: !!(
      searchParams.search_term ||
      searchParams.component_category_id ||
      searchParams.component_type_id ||
      searchParams.manufacturer
    ),
    staleTime: 2 * 60 * 1000, // 2 minutes
    ...options,
  })
}

// Hook for advanced component search
export function useComponentAdvancedSearch(
  searchParams: ComponentAdvancedSearch,
  pagination: { page?: number; size?: number } = {},
  options?: Omit<UseQueryOptions<ComponentAdvancedSearchResponse>, 'queryKey' | 'queryFn'>
) {
  return useQuery({
    queryKey: QueryKeys.componentsAdvancedSearch({ ...searchParams, ...pagination }),
    queryFn: async () => {
      const response = await componentApi.advancedSearch(searchParams, pagination)
      if (response.error) {
        throw new Error(response.error.detail)
      }
      return response.data!
    },
    enabled: !!(
      searchParams.search_term ||
      searchParams.basic_filters?.length ||
      searchParams.specification_filters?.length ||
      searchParams.range_filters?.length
    ),
    staleTime: 2 * 60 * 1000, // 2 minutes
    ...options,
  })
}

// Hook for search suggestions
export function useComponentSuggestions(
  query: string,
  field: string = 'name',
  limit: number = 10,
  options?: Omit<UseQueryOptions<string[]>, 'queryKey' | 'queryFn'>
) {
  const [debouncedQuery, setDebouncedQuery] = useState(query)

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedQuery(query)
    }, 300) // Debounce delay

    return () => {
      clearTimeout(handler)
    }
  }, [query])

  return useQuery({
    queryKey: QueryKeys.componentsSuggestions(debouncedQuery, field),
    queryFn: async () => {
      const response = await componentApi.getSuggestions(debouncedQuery, field, limit)
      if (response.error) {
        throw new Error(response.error.detail)
      }
      return response.data!
    },
    enabled: debouncedQuery.length >= 2,
    staleTime: 5 * 60 * 1000, // 5 minutes
    ...options,
  })
}

// Hook for preferred components
export function usePreferredComponents(
  params: { skip?: number; limit?: number } = {},
  options?: Omit<UseQueryOptions<ComponentSummary[]>, 'queryKey' | 'queryFn'>
) {
  return useQuery({
    queryKey: QueryKeys.componentsPreferred(params),
    queryFn: async () => {
      const response = await componentApi.getPreferred(params)
      if (response.error) {
        throw new Error(response.error.detail)
      }
      return response.data!
    },
    staleTime: 10 * 60 * 1000, // 10 minutes
    ...options,
  })
}

// Hook for component statistics
export function useComponentStats(
  options?: Omit<UseQueryOptions<ComponentStats>, 'queryKey' | 'queryFn'>
) {
  return useQuery({
    queryKey: QueryKeys.componentsStats,
    queryFn: async () => {
      const response = await componentApi.getStats()
      if (response.error) {
        throw new Error(response.error.detail)
      }
      return response.data!
    },
    staleTime: 15 * 60 * 1000, // 15 minutes
    ...options,
  })
}

// Hook for component categories
export function useComponentCategories(
  options?: Omit<UseQueryOptions<Array<{ name: string; value: string }>>, 'queryKey' | 'queryFn'>
) {
  return useQuery({
    queryKey: QueryKeys.componentsCategories,
    queryFn: async () => {
      const response = await componentApi.getCategories()
      if (response.error) {
        throw new Error(response.error.detail)
      }
      return response.data!
    },
    staleTime: 60 * 60 * 1000, // 1 hour
    ...options,
  })
}

// Hook for component types
export function useComponentTypes(
  categoryId?: number,
  options?: Omit<UseQueryOptions<Array<{ name: string; value: string }>>, 'queryKey' | 'queryFn'>
) {
  return useQuery({
    queryKey: QueryKeys.componentsTypes(categoryId),
    queryFn: async () => {
      const response = await componentApi.getTypes(categoryId)
      if (response.error) {
        throw new Error(response.error.detail)
      }
      return response.data!
    },
    staleTime: 60 * 60 * 1000, // 1 hour
    ...options,
  })
}

// Hook for searching components by specifications
export function useComponentSpecificationSearch(
  specifications: Record<string, any>,
  pagination: { page?: number; size?: number } = {},
  options?: Omit<UseQueryOptions<ComponentPaginatedResponse>, 'queryKey' | 'queryFn'>
) {
  return useQuery({
    queryKey: ['components', 'search-specifications', specifications, pagination],
    queryFn: async () => {
      const response = await componentApi.searchBySpecifications(specifications, pagination)
      if (response.error) {
        throw new Error(response.error.detail)
      }
      return response.data!
    },
    enabled: Object.keys(specifications).length > 0,
    staleTime: 2 * 60 * 1000, // 2 minutes
    ...options,
  })
}
