/**
 * Import/Export Dialog Component
 * Ultimate Electrical Designer - Settings & User Preferences
 */

'use client'

import React, { useState, useCallback } from 'react'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Checkbox } from '@/components/ui/checkbox'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Separator } from '@/components/ui/separator'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Upload, Download, FileText, AlertTriangle, CheckCircle, Info, X } from 'lucide-react'
import { useSettings } from '../hooks/useSettings'
import { useValidateImportFile } from '../api/settingsQueries'
import { SETTINGS_CATEGORIES } from '../constants'
import { parseImportFile, generateExportFilename } from '../utils'
import type { SettingsCategory, SettingsImport } from '../types'

/**
 * Import/Export Dialog Component
 */
export function ImportExportDialog() {
  const settings = useSettings()
  const { ui, actions } = settings
  const validateImportMutation = useValidateImportFile()

  // Import state
  const [importFile, setImportFile] = useState<File | null>(null)
  const [importData, setImportData] = useState<SettingsImport | null>(null)
  const [importPreview, setImportPreview] = useState<string>('')
  const [mergeMode, setMergeMode] = useState(true)

  // Export state
  const [selectedCategories, setSelectedCategories] = useState<SettingsCategory[]>([])
  const [includeMetadata, setIncludeMetadata] = useState(true)

  // Handle import file selection
  const handleImportFileSelect = useCallback(
    async (event: React.ChangeEvent<HTMLInputElement>) => {
      const file = event.target.files?.[0]
      if (!file) return

      setImportFile(file)
      setImportData(null)
      setImportPreview('')

      try {
        // Parse and validate file
        const data = await parseImportFile(file)
        const validationResult = await validateImportMutation.mutateAsync(file)

        setImportData(validationResult)
        setImportPreview(JSON.stringify(data, null, 2))
      } catch (error) {
        console.error('Import file validation failed:', error)
      }
    },
    [validateImportMutation]
  )

  // Handle import
  const handleImport = useCallback(async () => {
    if (!importData) return

    try {
      await actions.importPreferences({
        ...importData,
        merge: mergeMode,
      })

      // Reset state
      setImportFile(null)
      setImportData(null)
      setImportPreview('')
    } catch (error) {
      console.error('Import failed:', error)
    }
  }, [importData, mergeMode, actions])

  // Handle export
  const handleExport = useCallback(async () => {
    try {
      await actions.exportPreferences()
    } catch (error) {
      console.error('Export failed:', error)
    }
  }, [actions])

  // Handle category selection for export
  const handleCategoryToggle = useCallback((categoryId: SettingsCategory) => {
    setSelectedCategories((prev) =>
      prev.includes(categoryId) ? prev.filter((id) => id !== categoryId) : [...prev, categoryId]
    )
  }, [])

  // Clear import state
  const clearImportState = useCallback(() => {
    setImportFile(null)
    setImportData(null)
    setImportPreview('')
  }, [])

  return (
    <>
      {/* Import Dialog */}
      <Dialog
        open={ui.showImportDialog}
        onOpenChange={(open) => !open && actions.closeImportDialog()}
      >
        <DialogContent className="max-h-[80vh] max-w-2xl">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Upload className="h-5 w-5" />
              Import Settings
            </DialogTitle>
            <DialogDescription>
              Import settings from a previously exported file. You can choose to merge with existing
              settings or replace them completely.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            {/* File Selection */}
            <div className="space-y-2">
              <Label htmlFor="import-file">Select Settings File</Label>
              <Input
                id="import-file"
                type="file"
                accept=".json"
                onChange={handleImportFileSelect}
                className="cursor-pointer"
              />
              <p className="text-xs text-muted-foreground">
                Select a JSON file exported from Ultimate Electrical Designer
              </p>
            </div>

            {/* Import Options */}
            {importFile && (
              <div className="space-y-3">
                <Separator />

                <div className="flex items-center space-x-2">
                  <Checkbox id="merge-mode" checked={mergeMode} onCheckedChange={setMergeMode} />
                  <Label htmlFor="merge-mode" className="text-sm">
                    Merge with existing settings (recommended)
                  </Label>
                </div>
                <p className="ml-6 text-xs text-muted-foreground">
                  When enabled, only specified settings will be updated. When disabled, all settings
                  will be replaced.
                </p>
              </div>
            )}

            {/* Import Preview */}
            {importData && (
              <div className="space-y-3">
                <Separator />

                <div className="space-y-2">
                  <Label>Import Preview</Label>
                  <Alert>
                    <CheckCircle className="h-4 w-4" />
                    <AlertDescription>
                      File validated successfully. The following settings will be imported:
                    </AlertDescription>
                  </Alert>

                  <ScrollArea className="h-32 w-full rounded-md border p-3">
                    <pre className="text-xs text-muted-foreground">{importPreview}</pre>
                  </ScrollArea>
                </div>
              </div>
            )}

            {/* Validation Errors */}
            {validateImportMutation.error && (
              <Alert variant="destructive">
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>{validateImportMutation.error.message}</AlertDescription>
              </Alert>
            )}
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={actions.closeImportDialog}>
              Cancel
            </Button>
            <Button variant="outline" onClick={clearImportState} disabled={!importFile}>
              Clear
            </Button>
            <Button
              onClick={handleImport}
              disabled={!importData || validateImportMutation.isPending}
            >
              {validateImportMutation.isPending ? 'Validating...' : 'Import Settings'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Export Dialog */}
      <Dialog
        open={ui.showExportDialog}
        onOpenChange={(open) => !open && actions.closeExportDialog()}
      >
        <DialogContent className="max-w-lg">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Download className="h-5 w-5" />
              Export Settings
            </DialogTitle>
            <DialogDescription>
              Export your current settings to a JSON file for backup or migration to another device.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            {/* Export Options */}
            <div className="space-y-3">
              <Label>Export Options</Label>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="include-metadata"
                  checked={includeMetadata}
                  onCheckedChange={setIncludeMetadata}
                />
                <Label htmlFor="include-metadata" className="text-sm">
                  Include metadata (export date, version)
                </Label>
              </div>
            </div>

            {/* Category Selection */}
            <div className="space-y-3">
              <Label>Categories to Export</Label>
              <p className="text-xs text-muted-foreground">
                Leave all unchecked to export all settings
              </p>

              <div className="grid grid-cols-2 gap-2">
                {SETTINGS_CATEGORIES.map((category) => (
                  <div key={category.id} className="flex items-center space-x-2">
                    <Checkbox
                      id={`export-${category.id}`}
                      checked={selectedCategories.includes(category.id)}
                      onCheckedChange={() => handleCategoryToggle(category.id)}
                    />
                    <Label htmlFor={`export-${category.id}`} className="text-sm">
                      {category.label}
                    </Label>
                  </div>
                ))}
              </div>
            </div>

            {/* Export Info */}
            <Alert>
              <Info className="h-4 w-4" />
              <AlertDescription>
                The exported file will be named: {generateExportFilename()}
              </AlertDescription>
            </Alert>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={actions.closeExportDialog}>
              Cancel
            </Button>
            <Button onClick={handleExport}>
              <Download className="mr-2 h-4 w-4" />
              Export Settings
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  )
}

/**
 * Quick Export Button Component
 */
export function QuickExportButton({
  variant = 'outline',
  size = 'sm',
  className = '',
}: {
  variant?: 'default' | 'outline' | 'ghost'
  size?: 'sm' | 'default' | 'lg'
  className?: string
}) {
  const { actions } = useSettings()

  return (
    <Button variant={variant} size={size} onClick={actions.openExportDialog} className={className}>
      <Download className="mr-2 h-4 w-4" />
      Export
    </Button>
  )
}

/**
 * Quick Import Button Component
 */
export function QuickImportButton({
  variant = 'outline',
  size = 'sm',
  className = '',
}: {
  variant?: 'default' | 'outline' | 'ghost'
  size?: 'sm' | 'default' | 'lg'
  className?: string
}) {
  const { actions } = useSettings()

  return (
    <Button variant={variant} size={size} onClick={actions.openImportDialog} className={className}>
      <Upload className="mr-2 h-4 w-4" />
      Import
    </Button>
  )
}

export default ImportExportDialog
