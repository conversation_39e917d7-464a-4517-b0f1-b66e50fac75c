/**
 * useComponentStore Hook Tests
 * Tests the Zustand store for component management state
 */

import { act, renderHook } from '@testing-library/react'
import { mockComponentFilters } from '@/test/utils'
import { beforeEach, describe, expect, it } from 'vitest'
import { useComponentStore } from '../../hooks/useComponentStore'
import { ComponentCategoryType } from '../../types'

describe('useComponentStore', () => {
  beforeEach(() => {
    // Clear localStorage and reset store state before each test
    localStorage.clear()
    act(() => {
      useComponentStore.getState().reset()
    })
  })

  describe('List State Management', () => {
    it('initializes with default list state', () => {
      const { result } = renderHook(() => useComponentStore())

      expect(result.current.listState).toEqual({
        filters: {
          search_term: '',
          category: null,
          component_type: null,
          manufacturer: '',
          is_preferred: null,
          is_active: null,
          min_price: null,
          max_price: null,
          currency: 'EUR',
          stock_status: '',
        },
        sortBy: 'name',
        sortOrder: 'asc',
        page: 1,
        pageSize: 20,
        viewMode: 'grid',
        selectedComponents: [],
      })
    })

    it('updates list state', () => {
      const { result } = renderHook(() => useComponentStore())

      act(() => {
        result.current.setListState({
          page: 2,
          pageSize: 50,
          viewMode: 'list',
        })
      })

      expect(result.current.listState.page).toBe(2)
      expect(result.current.listState.pageSize).toBe(50)
      expect(result.current.listState.viewMode).toBe('list')
      // Other properties should remain unchanged
      expect(result.current.listState.sortBy).toBe('name')
    })

    it('updates filters', () => {
      const { result } = renderHook(() => useComponentStore())

      act(() => {
        result.current.updateFilters({
          category: 'Power Distribution' as ComponentCategoryType,
          manufacturer: 'Test Electronics',
        })
      })

      expect(result.current.listState.filters.category).toBe('Power Distribution')
      expect(result.current.listState.filters.manufacturer).toBe('Test Electronics')
    })

    it('merges filters with existing ones', () => {
      const { result } = renderHook(() => useComponentStore())

      act(() => {
        result.current.updateFilters({
          category: 'Power Distribution' as ComponentCategoryType,
          manufacturer: 'Test Electronics',
        })
      })

      act(() => {
        result.current.updateFilters({
          is_preferred: true,
        })
      })

      expect(result.current.listState.filters).toEqual({
        search_term: '',
        category: 'Power Distribution',
        component_type: null,
        manufacturer: 'Test Electronics',
        is_preferred: true,
        is_active: null,
        min_price: null,
        max_price: null,
        currency: 'EUR',
        stock_status: '',
      })
    })

    it('clears all filters', () => {
      const { result } = renderHook(() => useComponentStore())

      act(() => {
        result.current.updateFilters(mockComponentFilters)
      })

      expect(Object.keys(result.current.listState.filters)).toHaveLength(10)

      act(() => {
        result.current.clearFilters()
      })

      expect(result.current.listState.filters).toEqual({
        search_term: '',
        category: null,
        component_type: null,
        manufacturer: '',
        is_preferred: null,
        is_active: null,
        min_price: null,
        max_price: null,
        currency: 'EUR',
        stock_status: '',
      })
    })
  })

  describe('Search State Management', () => {
    it('initializes with default search state', () => {
      const { result } = renderHook(() => useComponentStore())

      expect(result.current.searchState).toEqual({
        query: '',
        field: 'name',
        suggestions: [],
        recentSearches: [],
        isSearching: false,
        searchHistory: [],
        isAdvancedMode: false,
        savedSearches: [],
      })
    })

    it('updates search state', () => {
      const { result } = renderHook(() => useComponentStore())

      act(() => {
        result.current.setSearchState({
          query: 'resistor',
          field: 'part_number',
          isSearching: true,
        })
      })

      expect(result.current.searchState.query).toBe('resistor')
      expect(result.current.searchState.field).toBe('part_number')
      expect(result.current.searchState.isSearching).toBe(true)
    })

    it('adds to search history', () => {
      const { result } = renderHook(() => useComponentStore())

      act(() => {
        result.current.addToSearchHistory('resistor')
      })

      act(() => {
        result.current.addToSearchHistory('capacitor')
      })

      expect(result.current.searchState.recentSearches).toEqual([
        { query: 'capacitor', timestamp: expect.any(String) },
        { query: 'resistor', timestamp: expect.any(String) },
      ])
      expect(result.current.searchState.searchHistory).toHaveLength(2)
    })

    it('limits search history to maximum entries', () => {
      const { result } = renderHook(() => useComponentStore())

      // Add more than the maximum allowed entries
      act(() => {
        for (let i = 0; i < 15; i++) {
          result.current.addToSearchHistory(`search-${i}`)
        }
      })

      expect(result.current.searchState.recentSearches).toHaveLength(10) // Assuming max is 10
    })

    it('prevents duplicate entries in search history', () => {
      const { result } = renderHook(() => useComponentStore())

      act(() => {
        result.current.addToSearchHistory('resistor')
        result.current.addToSearchHistory('capacitor')
        result.current.addToSearchHistory('resistor') // Duplicate
      })

      expect(result.current.searchState.recentSearches).toEqual([
        { query: 'resistor', timestamp: expect.any(String) },
        { query: 'capacitor', timestamp: expect.any(String) },
      ])
    })

    it('clears search history', () => {
      const { result } = renderHook(() => useComponentStore())

      act(() => {
        result.current.addToSearchHistory('resistor')
        result.current.addToSearchHistory('capacitor')
      })

      expect(result.current.searchState.recentSearches).toHaveLength(2)

      act(() => {
        result.current.clearSearchHistory()
      })

      expect(result.current.searchState.recentSearches).toEqual([])
      expect(result.current.searchState.searchHistory).toEqual([])
    })
  })

  describe('Bulk Operations State', () => {
    it('initializes with default bulk state', () => {
      const { result } = renderHook(() => useComponentStore())

      expect(result.current.bulkState).toEqual({
        selectedIds: [],
        operation: null,
        isProcessing: false,
        progress: 0,
        results: [],
        errors: [],
      })
    })

    it('updates bulk state', () => {
      const { result } = renderHook(() => useComponentStore())

      act(() => {
        result.current.setBulkState({
          operation: 'update',
          isProcessing: true,
          progress: 50,
        })
      })

      expect(result.current.bulkState.operation).toBe('update')
      expect(result.current.bulkState.isProcessing).toBe(true)
      expect(result.current.bulkState.progress).toBe(50)
    })

    it('selects individual component', () => {
      const { result } = renderHook(() => useComponentStore())

      act(() => {
        result.current.selectComponent(1)
      })

      expect(result.current.bulkState.selectedIds).toEqual([1])

      act(() => {
        result.current.selectComponent(2)
      })

      expect(result.current.bulkState.selectedIds).toEqual([1, 2])
    })

    it('prevents duplicate selections', () => {
      const { result } = renderHook(() => useComponentStore())

      act(() => {
        result.current.selectComponent(1)
        result.current.selectComponent(1) // Duplicate
      })

      expect(result.current.bulkState.selectedIds).toEqual([1])
    })

    it('deselects individual component', () => {
      const { result } = renderHook(() => useComponentStore())

      act(() => {
        result.current.selectComponent(1)
        result.current.selectComponent(2)
        result.current.selectComponent(3)
      })

      act(() => {
        result.current.deselectComponent(2)
      })

      expect(result.current.bulkState.selectedIds).toEqual([1, 3])
    })

    it('selects all components', () => {
      const { result } = renderHook(() => useComponentStore())

      const allIds = [1, 2, 3, 4, 5]

      act(() => {
        result.current.selectAll(allIds)
      })

      expect(result.current.bulkState.selectedIds).toEqual(allIds)
    })

    it('clears all selections', () => {
      const { result } = renderHook(() => useComponentStore())

      act(() => {
        result.current.selectComponent(1)
        result.current.selectComponent(2)
        result.current.selectComponent(3)
      })

      expect(result.current.bulkState.selectedIds).toHaveLength(3)

      act(() => {
        result.current.clearSelection()
      })

      expect(result.current.bulkState.selectedIds).toEqual([])
    })
  })

  describe('Display Options', () => {
    it('manages display preferences', () => {
      const { result } = renderHook(() => useComponentStore())

      act(() => {
        result.current.setDisplayOptions({
          showImages: false,
          showPrices: true,
          compactMode: true,
        })
      })

      expect(result.current.displayOptions.showImages).toBe(false)
      expect(result.current.displayOptions.showPrices).toBe(true)
      expect(result.current.displayOptions.compactMode).toBe(true)
    })

    it('toggles individual display options', () => {
      const { result } = renderHook(() => useComponentStore())

      act(() => {
        result.current.toggleDisplayOption('showImages')
      })

      expect(result.current.displayOptions.showImages).toBe(false)

      act(() => {
        result.current.toggleDisplayOption('showImages')
      })

      expect(result.current.displayOptions.showImages).toBe(true)
    })
  })

  describe('Persistence', () => {
    it('persists state to localStorage', () => {
      const { result } = renderHook(() => useComponentStore())

      act(() => {
        result.current.updateFilters({
          category: 'Power Distribution' as ComponentCategoryType,
          manufacturer: 'Test Electronics',
        })
        result.current.setListState({
          viewMode: 'list',
          pageSize: 50,
        })
      })

      // Create a new hook instance to test persistence
      const { result: newResult } = renderHook(() => useComponentStore())

      expect(newResult.current.listState.filters.category).toBe('Power Distribution')
      expect(newResult.current.listState.viewMode).toBe('list')
      expect(newResult.current.listState.pageSize).toBe(50)
    })

    it('handles corrupted localStorage data gracefully', () => {
      // Simulate corrupted localStorage
      localStorage.setItem('component-store', 'invalid-json')

      const { result } = renderHook(() => useComponentStore())

      // Should fall back to default state
      expect(result.current.listState.sortBy).toBe('name')
      expect(result.current.listState.viewMode).toBe('grid')
    })
  })

  describe('Computed Values', () => {
    it('calculates active filter count', () => {
      const { result } = renderHook(() => useComponentStore())

      expect(result.current.getActiveFilterCount()).toBe(0)

      act(() => {
        result.current.updateFilters({
          category: 'Power Distribution' as ComponentCategoryType,
          manufacturer: 'Test Electronics',
          is_preferred: true,
        })
      })

      expect(result.current.getActiveFilterCount()).toBe(3)
    })

    it('calculates selected component count', () => {
      const { result } = renderHook(() => useComponentStore())

      expect(result.current.getSelectedCount()).toBe(0)

      act(() => {
        result.current.selectComponent(1)
        result.current.selectComponent(2)
        result.current.selectComponent(3)
      })

      expect(result.current.getSelectedCount()).toBe(3)
    })

    it('determines if component is selected', () => {
      const { result } = renderHook(() => useComponentStore())

      act(() => {
        result.current.selectComponent(1)
        result.current.selectComponent(3)
      })

      expect(result.current.isComponentSelected(1)).toBe(true)
      expect(result.current.isComponentSelected(2)).toBe(false)
      expect(result.current.isComponentSelected(3)).toBe(true)
    })
  })

  describe('Edge Cases', () => {
    it('handles invalid component IDs gracefully', () => {
      const { result } = renderHook(() => useComponentStore())

      act(() => {
        result.current.selectComponent(-1)
        result.current.selectComponent(0)
        result.current.selectComponent(null as any)
        result.current.selectComponent(undefined as any)
      })

      // Should only include valid IDs
      expect(result.current.bulkState.selectedIds).toEqual([])
    })

    it('handles large selection sets efficiently', () => {
      const { result } = renderHook(() => useComponentStore())

      const largeIdSet = Array.from({ length: 10000 }, (_, i) => i + 1)

      act(() => {
        result.current.selectAll(largeIdSet)
      })

      expect(result.current.bulkState.selectedIds).toHaveLength(10000)
      expect(result.current.getSelectedCount()).toBe(10000)
    })

    it('handles concurrent state updates', () => {
      const { result } = renderHook(() => useComponentStore())

      act(() => {
        // Simulate concurrent updates
        result.current.updateFilters({ category: 'Power Distribution' as ComponentCategoryType })
        result.current.selectComponent(1)
        result.current.setListState({ page: 2 })
        result.current.updateFilters({ manufacturer: 'Test' })
      })

      expect(result.current.listState.filters.category).toBe('Power Distribution')
      expect(result.current.listState.filters.manufacturer).toBe('Test')
      expect(result.current.listState.page).toBe(1) // Page resets to 1 when filters change
      expect(result.current.bulkState.selectedIds).toEqual([1])
    })
  })
})
