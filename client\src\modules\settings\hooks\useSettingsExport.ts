/**
 * Settings Export/Import Hook
 * Ultimate Electrical Designer - Settings & User Preferences
 */

import { toast } from '@/hooks/useToast'
import { useQueryClient } from '@tanstack/react-query'
import { useCallback, useState } from 'react'
import { settingsApi } from '../api/settingsApi'
import { validateSettingsExport, validateSettingsImport } from '../schemas/settingsSchemas'
import type { SettingsCategory, SettingsExport, SettingsImport } from '../types'
import { generateExportFilename, parseImportFile } from '../utils'
import { useSettings } from './useSettings'

interface ExportOptions {
  categories?: SettingsCategory[]
  includeMetadata?: boolean
  format?: 'json'
  filename?: string
}

interface ImportOptions {
  validate?: boolean
  merge?: boolean
  backup?: boolean
}

interface UseSettingsExportReturn {
  // Export functionality
  exportSettings: (options?: ExportOptions) => Promise<void>
  exportToFile: (data: SettingsExport, filename?: string) => void
  exportToClipboard: (data: SettingsExport) => Promise<void>
  exportToUrl: (data: SettingsExport) => string

  // Import functionality
  importSettings: (file: File, options?: ImportOptions) => Promise<void>
  importFromClipboard: (options?: ImportOptions) => Promise<void>
  importFromUrl: (url: string, options?: ImportOptions) => Promise<void>
  validateImportData: (data: any) => { isValid: boolean; errors: string[] }

  // Batch operations
  exportMultipleCategories: (categories: SettingsCategory[]) => Promise<void>
  importWithPreview: (file: File) => Promise<{ data: SettingsImport; preview: string }>

  // State
  isExporting: boolean
  isImporting: boolean
  exportProgress: number
  importProgress: number
  lastExport?: SettingsExport
  lastImport?: SettingsImport
}

/**
 * Hook for settings export and import functionality
 */
export function useSettingsExport(): UseSettingsExportReturn {
  const settings = useSettings()
  const queryClient = useQueryClient()

  const [isExporting, setIsExporting] = useState(false)
  const [isImporting, setIsImporting] = useState(false)
  const [exportProgress, setExportProgress] = useState(0)
  const [importProgress, setImportProgress] = useState(0)
  const [lastExport, setLastExport] = useState<SettingsExport>()
  const [lastImport, setLastImport] = useState<SettingsImport>()

  // Export settings with options
  const exportSettings = useCallback(
    async (options: ExportOptions = {}) => {
      const { categories, includeMetadata = true, format = 'json', filename } = options

      setIsExporting(true)
      setExportProgress(0)

      try {
        // Get current preferences
        const preferences = settings.preferences
        if (!preferences) {
          throw new Error('No preferences data available')
        }

        setExportProgress(25)

        // Filter by categories if specified
        let exportData = preferences
        if (categories && categories.length > 0) {
          // This would require mapping fields to categories
          // For now, export all data
          exportData = preferences
        }

        setExportProgress(50)

        // Create export object
        const exportObject: SettingsExport = {
          preferences: settingsApi.sanitizeForExport(exportData),
          exported_at: new Date().toISOString(),
          version: '1.0',
        }

        if (includeMetadata) {
          exportObject.metadata = {
            export_type: categories ? 'partial' : 'full',
            categories,
          }
        }

        setExportProgress(75)

        // Validate export data
        const validatedExport = validateSettingsExport(exportObject)
        setLastExport(validatedExport)

        setExportProgress(90)

        // Download file
        const exportFilename = filename || generateExportFilename()
        settingsApi.downloadExportFile(validatedExport, exportFilename)

        setExportProgress(100)

        toast({
          title: 'Export Successful',
          description: `Settings exported to ${exportFilename}`,
          variant: 'default',
        })
      } catch (error: any) {
        console.error('Export failed:', error)
        toast({
          title: 'Export Failed',
          description: error.message || 'Failed to export settings',
          variant: 'destructive',
        })
      } finally {
        setIsExporting(false)
        setTimeout(() => setExportProgress(0), 1000)
      }
    },
    [settings.preferences]
  )

  // Export to file
  const exportToFile = useCallback((data: SettingsExport, filename?: string) => {
    try {
      const exportFilename = filename || generateExportFilename()
      settingsApi.downloadExportFile(data, exportFilename)

      toast({
        title: 'Export Successful',
        description: `Settings exported to ${exportFilename}`,
        variant: 'default',
      })
    } catch (error: any) {
      toast({
        title: 'Export Failed',
        description: error.message || 'Failed to export to file',
        variant: 'destructive',
      })
    }
  }, [])

  // Export to clipboard
  const exportToClipboard = useCallback(async (data: SettingsExport) => {
    try {
      const jsonString = JSON.stringify(data, null, 2)
      await navigator.clipboard.writeText(jsonString)

      toast({
        title: 'Copied to Clipboard',
        description: 'Settings data copied to clipboard',
        variant: 'default',
      })
    } catch (error: any) {
      toast({
        title: 'Copy Failed',
        description: error.message || 'Failed to copy to clipboard',
        variant: 'destructive',
      })
    }
  }, [])

  // Export to URL (base64 encoded)
  const exportToUrl = useCallback((data: SettingsExport): string => {
    try {
      const jsonString = JSON.stringify(data)
      const encoded = btoa(jsonString)
      const url = `${window.location.origin}${window.location.pathname}?import=${encoded}`
      return url
    } catch (error: any) {
      console.error('Failed to create export URL:', error)
      return ''
    }
  }, [])

  // Validate import data
  const validateImportData = useCallback((data: any): { isValid: boolean; errors: string[] } => {
    const errors: string[] = []

    try {
      // Basic structure validation
      if (!data) {
        errors.push('No data provided')
        return { isValid: false, errors }
      }

      if (typeof data !== 'object') {
        errors.push('Data must be an object')
        return { isValid: false, errors }
      }

      // Check for preferences
      if (!data.preferences && !data.theme) {
        errors.push('No preferences data found')
      }

      // Validate using Zod schema
      const result = validateSettingsImport(data)

      return { isValid: errors.length === 0, errors }
    } catch (error: any) {
      errors.push(error.message || 'Validation error')
      return { isValid: false, errors }
    }
  }, [])

  // Import settings from file
  const importSettings = useCallback(
    async (file: File, options: ImportOptions = {}) => {
      const { validate = true, merge = true, backup = true } = options

      setIsImporting(true)
      setImportProgress(0)

      try {
        // Create backup if requested
        if (backup) {
          // This would create a backup of current settings
          setImportProgress(10)
        }

        // Parse file
        const data = await parseImportFile(file)
        setImportProgress(30)

        // Validate import data
        if (validate) {
          const validation = validateImportData(data)
          if (!validation.isValid) {
            throw new Error(`Validation failed: ${validation.errors.join(', ')}`)
          }
        }

        setImportProgress(50)

        // Prepare import data
        const importData: SettingsImport = {
          preferences: data.preferences || data,
          version: data.version,
          validate,
          merge,
        }

        setImportProgress(70)

        // Import settings
        await settings.actions.importPreferences(importData)
        setLastImport(importData)

        setImportProgress(100)

        toast({
          title: 'Import Successful',
          description: 'Settings imported successfully',
          variant: 'default',
        })
      } catch (error: any) {
        console.error('Import failed:', error)
        toast({
          title: 'Import Failed',
          description: error.message || 'Failed to import settings',
          variant: 'destructive',
        })
      } finally {
        setIsImporting(false)
        setTimeout(() => setImportProgress(0), 1000)
      }
    },
    [settings.actions, validateImportData]
  )

  // Import from clipboard
  const importFromClipboard = useCallback(
    async (options: ImportOptions = {}) => {
      try {
        const text = await navigator.clipboard.readText()
        const data = JSON.parse(text)

        // Create a temporary file-like object
        const blob = new Blob([text], { type: 'application/json' })
        const file = new File([blob], 'clipboard-import.json', { type: 'application/json' })

        await importSettings(file, options)
      } catch (error: any) {
        toast({
          title: 'Import Failed',
          description: error.message || 'Failed to import from clipboard',
          variant: 'destructive',
        })
      }
    },
    [importSettings]
  )

  // Import from URL
  const importFromUrl = useCallback(
    async (url: string, options: ImportOptions = {}) => {
      try {
        // Extract import data from URL
        const urlObj = new URL(url)
        const importParam = urlObj.searchParams.get('import')

        if (!importParam) {
          throw new Error('No import data found in URL')
        }

        // Decode base64 data
        const jsonString = atob(importParam)
        const data = JSON.parse(jsonString)

        // Create a temporary file-like object
        const blob = new Blob([jsonString], { type: 'application/json' })
        const file = new File([blob], 'url-import.json', { type: 'application/json' })

        await importSettings(file, options)
      } catch (error: any) {
        toast({
          title: 'Import Failed',
          description: error.message || 'Failed to import from URL',
          variant: 'destructive',
        })
      }
    },
    [importSettings]
  )

  // Export multiple categories
  const exportMultipleCategories = useCallback(
    async (categories: SettingsCategory[]) => {
      await exportSettings({
        categories,
        includeMetadata: true,
        filename: `ued-settings-${categories.join('-')}-${new Date().toISOString().split('T')[0]}.json`,
      })
    },
    [exportSettings]
  )

  // Import with preview
  const importWithPreview = useCallback(
    async (file: File): Promise<{ data: SettingsImport; preview: string }> => {
      try {
        const data = await parseImportFile(file)
        const preview = JSON.stringify(data, null, 2)

        const importData: SettingsImport = {
          preferences: data.preferences || data,
          version: data.version,
          validate: true,
          merge: true,
        }

        return { data: importData, preview }
      } catch (error: any) {
        throw new Error(`Failed to preview import: ${error.message}`)
      }
    },
    []
  )

  return {
    // Export functionality
    exportSettings,
    exportToFile,
    exportToClipboard,
    exportToUrl,

    // Import functionality
    importSettings,
    importFromClipboard,
    importFromUrl,
    validateImportData,

    // Batch operations
    exportMultipleCategories,
    importWithPreview,

    // State
    isExporting,
    isImporting,
    exportProgress,
    importProgress,
    lastExport,
    lastImport,
  }
}

export default useSettingsExport
