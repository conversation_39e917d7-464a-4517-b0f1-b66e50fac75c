---
type: "manual"
---

# Project Structure Specification
## Ultimate Electrical Designer

**Document Version:** 1.0  
**Last Updated:** July 2025

---

## Project Overview

The Ultimate Electrical Designer follows a monorepo structure with clear separation between backend (Python FastAPI), frontend (Next.js React), and documentation components. The architecture implements a 5-layer pattern with unified error handling, ensuring engineering-grade quality and maintainability.

---

## Root Directory Structure

```
ultimate-electrical-designer/
├── server/                     # Backend Python FastAPI application
├── client/                     # Frontend Next.js React application  
├── docs/                       # Comprehensive project documentation
|   ├── product.md              # Product specification (this document series)
|   ├── structure.md            # Project structure documentation
|   ├── tech.md                 # Technology stack specification
|   ├── rules.md                # Development standards and rules
|   ├── requirements.md         # Functional and non-functional requirements
|   ├── design.md               # Technical architecture and design
|   └── tasks.md                # Implementation task breakdown
├── Makefile                    # Global development commands
├── README.md                   # Project overview and quick start
└── CLAUDE.md                   # AI assistant guidance document
```

---

## Backend Structure (server/)

### 5-Layer Architecture Implementation

```
server/
├── src/                        # Source code root
│   ├── api/                    # Layer 1: API Routes (FastAPI endpoints)
│   │   ├── v1/                 # Versioned API routes
│   │   │   ├── auth_routes.py  # Authentication endpoints
│   │   │   ├── component_routes.py # Component management
│   │   │   ├── user_routes.py  # User management
│   │   │   └── router.py       # Main API router
│   │   └── main_router.py      # Application router
│   ├── core/                   # Layers 2-4: Business Logic
│   │   ├── models/             # Layer 2: Data Models (SQLAlchemy)
│   │   │   ├── auth/           # Authentication models
│   │   │   ├── general/        # General domain models
│   │   │   └── base.py         # Base model classes
│   │   ├── schemas/            # Layer 3: Validation Schemas (Pydantic)
│   │   │   ├── auth/           # Authentication schemas
│   │   │   ├── general/        # General domain schemas
│   │   │   └── base.py         # Base schema classes
│   │   ├── services/           # Layer 4: Business Logic Services
│   │   │   ├── auth/           # Authentication services
│   │   │   ├── general/        # General domain services
│   │   │   └── dependencies.py # Service dependencies
│   │   ├── repositories/       # Layer 5: Data Access Layer
│   │   │   ├── auth/           # Authentication repositories
│   │   │   ├── general/        # General domain repositories
│   │   │   └── base.py         # Base repository classes
│   │   ├── database/           # Database configuration
│   │   │   ├── connection.py   # Database connection management
│   │   │   └── session.py      # Session management
│   │   ├── errors/             # Unified Error Handling
│   │   │   ├── unified_error_handler.py # Central error handling
│   │   │   ├── exceptions.py   # Custom exception classes
│   │   │   └── error_responses.py # Standardized error responses
│   │   ├── security/           # Security Components
│   │   │   ├── enhanced_dependencies.py # Security dependencies
│   │   │   ├── unified_security_validator.py # Security validation
│   │   │   └── password_handler.py # Password management
│   │   ├── monitoring/         # Performance Monitoring
│   │   │   ├── performance_monitor.py # Performance tracking
│   │   │   └── metrics.py      # Application metrics
│   │   ├── standards/          # Engineering Standards Compliance
│   │   │   ├── ieee/           # IEEE standards implementation
│   │   │   ├── iec/            # IEC standards implementation
│   │   │   └── en/             # EN standards implementation
│   │   └── utils/              # Utility Functions
│   │       ├── crud_endpoint_factory.py # CRUD endpoint factory
│   │       ├── validation.py   # Input validation utilities
│   │       └── calculations.py # Engineering calculations
│   ├── config/                 # Configuration Management
│   │   ├── settings.py         # Application settings
│   │   ├── logging_config.py   # Logging configuration
│   │   └── database_config.py  # Database configuration
│   ├── middleware/             # Custom Middleware
│   │   ├── security_middleware.py # Security middleware
│   │   ├── performance_middleware.py # Performance monitoring
│   │   └── cors_middleware.py  # CORS configuration
│   ├── app.py                  # FastAPI application instance
│   └── main.py                 # Application entry point and CLI
├── tests/                      # Comprehensive Test Suite
│   ├── unit/                   # Unit tests
│   ├── integration/            # Integration tests
│   ├── api/                    # API endpoint tests
│   ├── conftest.py             # Test configuration
│   └── fixtures/               # Test fixtures
├── data/                       # Database Files
│   └── app_dev.db              # Development SQLite database
├── logs/                       # Application Logs
├── alembic/                    # Database Migration Scripts
├── pyproject.toml              # Python dependencies and configuration
└── README.md                   # Backend documentation
```

---

## Frontend Structure (client/)

### Next.js App Router Architecture

```
client/
├── src/                        # Source code root
│   ├── app/                    # Next.js App Router (pages, layouts, API routes)
│   │   ├── (auth)/             # Route groups for authentication flows
│   │   │   └── login/          # Login page
│   │   │       └── page.tsx
│   │   ├── dashboard/          # Dashboard pages
│   │   │   └── page.tsx
│   │   ├── api/                # Next.js API routes (if needed)
│   │   ├── layout.tsx          # Root layout
│   │   ├── page.tsx            # Home page
│   │   └── globals.css         # Global styles
│   ├── components/             # Reusable UI Components
│   │   ├── ui/                 # Shadcn UI components (design system primitives)
│   │   │   ├── button.tsx
│   │   │   ├── dialog.tsx
│   │   │   └── form.tsx
│   │   ├── common/             # Generic, application-agnostic components
│   │   │   ├── Header.tsx
│   │   │   ├── Footer.tsx
│   │   │   └── Navigation.tsx
│   │   └── domain/             # Domain-specific reusable components
│   │       ├── ComponentCard.tsx
│   │       ├── CalculationForm.tsx
│   │       └── ProjectSummary.tsx
│   ├── modules/                # Feature-based modules (Domain-Driven Design)
│   │   ├── auth/               # Authentication module
│   │   │   ├── components/     # Auth-specific components
│   │   │   ├── hooks/          # Auth-specific hooks
│   │   │   ├── services/       # Auth API services
│   │   │   └── types/          # Auth type definitions
│   │   ├── components/         # Component management module
│   │   │   ├── components/     # Component-specific UI
│   │   │   ├── hooks/          # Component management hooks
│   │   │   ├── services/       # Component API services
│   │   │   └── types/          # Component type definitions
│   │   └── calculations/       # Calculation engine module
│   │       ├── components/     # Calculation UI components
│   │       ├── hooks/          # Calculation hooks
│   │       ├── services/       # Calculation API services
│   │       └── types/          # Calculation type definitions
│   ├── hooks/                  # Global React hooks
│   │   ├── useAuth.ts          # Authentication hook
│   │   ├── useApi.ts           # API client hook
│   │   └── useLocalStorage.ts  # Local storage hook
│   ├── lib/                    # Core libraries and utilities
│   │   ├── api/                # API client configuration
│   │   │   ├── client.ts       # Main API client
│   │   │   ├── auth.ts         # Authentication API
│   │   │   └── components.ts   # Component API
│   │   ├── utils.ts            # Utility functions
│   │   └── constants.ts        # Application constants
│   ├── stores/                 # State Management (Zustand)
│   │   ├── authStore.ts        # Authentication state
│   │   ├── componentStore.ts   # Component management state
│   │   └── uiStore.ts          # UI state management
│   ├── types/                  # Global TypeScript type definitions
│   │   ├── api.ts              # API response types
│   │   ├── auth.ts             # Authentication types
│   │   └── components.ts       # Component types
│   ├── utils/                  # Utility functions
│   │   ├── formatting.ts       # Data formatting utilities
│   │   ├── validation.ts       # Client-side validation
│   │   └── calculations.ts     # Client-side calculations
│   ├── assets/                 # Static assets
│   │   ├── images/             # Image files
│   │   ├── fonts/              # Font files
│   │   └── icons/              # Icon files
│   ├── services/               # External service integrations
│   │   ├── api.ts              # API service layer
│   │   └── storage.ts          # Storage service layer
│   └── test/                   # Test utilities and setup
│       ├── setup.ts            # Test setup configuration
│       ├── mocks/              # Mock data and services
│       └── utils.ts            # Test utility functions
├── tests/                      # Test suites
│   ├── e2e/                    # End-to-end tests (Playwright)
│   │   ├── auth/               # Authentication E2E tests
│   │   ├── components/         # Component management E2E tests
│   │   └── calculations/       # Calculation E2E tests
│   └── mocks/                  # Mock service workers (MSW)
├── public/                     # Static public assets
├── package.json                # Node.js dependencies and scripts
├── tsconfig.json               # TypeScript configuration
├── tailwind.config.ts          # Tailwind CSS configuration
├── vitest.config.ts            # Vitest testing configuration
├── playwright.config.ts        # Playwright E2E testing configuration
└── README.md                   # Frontend documentation
```

---

## Module Relationships

### Backend Layer Dependencies
1. **API Layer** → **Services Layer** → **Repositories Layer** → **Models Layer**
2. **Unified Error Handling** spans all layers
3. **Security Validation** integrated at API and Service layers
4. **Performance Monitoring** integrated at all layers

### Frontend Module Dependencies
1. **App Router** → **Modules** → **Components** → **UI Primitives**
2. **State Management** (Zustand) for client state
3. **React Query** for server state management
4. **API Client** for backend communication

### Cross-System Integration
- **Authentication Flow**: Frontend auth module ↔ Backend auth services
- **Component Management**: Frontend component module ↔ Backend component services
- **Real-time Updates**: WebSocket connections for live collaboration
- **File Uploads**: Direct integration for component specifications and drawings

---

## Architectural Boundaries

### Backend Boundaries
- **API Layer**: HTTP request/response handling, input validation, authentication
- **Service Layer**: Business logic, workflow orchestration, transaction management
- **Repository Layer**: Data access abstraction, query optimization, caching
- **Model Layer**: Data structure definition, relationships, constraints

### Frontend Boundaries
- **Presentation Layer**: UI components, user interactions, visual feedback
- **State Management**: Client state, server state caching, optimistic updates
- **Service Layer**: API communication, data transformation, error handling
- **Utility Layer**: Helper functions, validation, formatting, calculations

### Integration Boundaries
- **API Contract**: RESTful endpoints with OpenAPI specification
- **Authentication**: JWT token-based authentication with role-based access control
- **Data Flow**: Unidirectional data flow with React Query for server state
- **Error Handling**: Unified error responses with consistent error codes

---

This structure specification provides the complete organizational framework for the Ultimate Electrical Designer project, ensuring clear separation of concerns, maintainable code organization, and scalable architecture that supports the engineering-grade quality standards required for professional electrical design software.
