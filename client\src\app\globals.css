@import 'tailwindcss';

/* Define custom design tokens using @theme */
@theme {
  /* Engineering Application Color Palette */
  --color-brand-primary: 14 78% 54%; /* Engineering Orange #E67E22 */
  --color-brand-secondary: 210 100% 56%; /* Professional Blue #1E88E5 */
  --color-brand-accent: 142 76% 36%; /* Technical Green #27AE60 */
  --color-brand-dark: 210 29% 24%; /* Dark Blue-Gray #34495E */

  /* Neutral Grays for Professional Look */
  --color-neutral-50: 210 20% 98%; /* Very Light Gray */
  --color-neutral-100: 210 16% 93%; /* Light Gray */
  --color-neutral-200: 210 13% 87%; /* Medium Light Gray */
  --color-neutral-300: 210 11% 71%; /* Medium Gray */
  --color-neutral-400: 210 9% 53%; /* Dark Medium Gray */
  --color-neutral-500: 210 10% 40%; /* Dark Gray */
  --color-neutral-600: 210 13% 32%; /* Very Dark Gray */
  --color-neutral-700: 210 18% 25%; /* Almost Black */
  --color-neutral-800: 210 24% 16%; /* Very Dark */
  --color-neutral-900: 210 29% 9%; /* Near Black */

  /* Status Colors */
  --color-success: 142 76% 36%; /* Success Green */
  --color-warning: 38 92% 50%; /* Warning Amber */
  --color-error: 0 84% 60%; /* Error Red */
  --color-info: 210 100% 56%; /* Info Blue */

  /* Text Colors */
  --color-text-primary: 210 29% 24%; /* Primary Text */
  --color-text-secondary: 210 10% 40%; /* Secondary Text */
  --color-text-muted: 210 9% 53%; /* Muted Text */
  --color-text-inverse: 0 0% 100%; /* White Text */

  /* Background Colors */
  --color-bg-primary: 0 0% 100%; /* White Background */
  --color-bg-secondary: 210 20% 98%; /* Light Gray Background */
  --color-bg-tertiary: 210 16% 93%; /* Medium Light Background */

  /* Engineering-specific spacing */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  --spacing-2xl: 48px;
  --spacing-3xl: 64px;
  --spacing-4xl: 96px;

  /* Professional shadows */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
}

/* Base styles and CSS variables */
:root {
  --foreground-rgb: 52, 73, 94; /* Professional Dark Blue-Gray */
  --background-start-rgb: 248, 249, 250; /* Very Light Gray */
  --background-end-rgb: 255, 255, 255; /* Pure White */

  /* Engineering Application Variables */
  --header-height: 64px;
  --sidebar-width: 280px;
  --border-radius-sm: 4px;
  --border-radius-md: 8px;
  --border-radius-lg: 12px;
  --border-radius-xl: 16px;
}

/* Landing Page Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInFromLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInFromRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.animate-fadeIn {
  animation: fadeIn 0.6s ease-out forwards;
}

.animate-slideIn {
  animation: slideIn 0.8s ease-out forwards;
}

.animate-slideInFromLeft {
  animation: slideInFromLeft 0.8s ease-out forwards;
}

.animate-slideInFromRight {
  animation: slideInFromRight 0.8s ease-out forwards;
}

.animate-scaleIn {
  animation: scaleIn 0.6s ease-out forwards;
}

@media (prefers-color-scheme: dark) {
  :root {
    --foreground-rgb: 236, 240, 241; /* Light Gray for Dark Mode */
    --background-start-rgb: 44, 62, 80; /* Dark Blue-Gray */
    --background-end-rgb: 52, 73, 94; /* Darker Blue-Gray */
  }
}

body {
  color: rgb(var(--foreground-rgb));
  background: linear-gradient(
    135deg,
    rgb(var(--background-start-rgb)) 0%,
    rgb(var(--background-end-rgb)) 100%
  );
  font-family:
    'Inter',
    -apple-system,
    BlinkMacSystemFont,
    'Segoe UI',
    Roboto,
    sans-serif;
  font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Custom component styles using @layer */
@layer components {
  /* Engineering Application Cards */
  .card-base-style {
    @apply rounded-lg border border-neutral-200 bg-white p-6 shadow-md transition-all duration-200 hover:shadow-lg;
  }

  .card-engineering {
    @apply rounded-xl border border-neutral-200 bg-white p-8 shadow-lg transition-all duration-300 hover:border-brand-secondary/30 hover:shadow-xl;
  }

  .card-feature {
    @apply rounded-lg border border-neutral-100 bg-gradient-to-br from-white to-neutral-50 p-6 shadow-md transition-all duration-200 hover:from-brand-secondary/5 hover:to-brand-secondary/10 hover:shadow-lg;
  }

  /* Professional Button Styles */
  .btn-primary {
    @apply rounded-lg bg-brand-primary px-6 py-3 font-semibold text-white shadow-md transition-all duration-200 hover:bg-brand-primary/90 hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-brand-primary/50 focus:ring-offset-2;
  }

  .btn-secondary {
    @apply rounded-lg bg-brand-secondary px-6 py-3 font-semibold text-white shadow-md transition-all duration-200 hover:bg-brand-secondary/90 hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-brand-secondary/50 focus:ring-offset-2;
  }

  .btn-outline {
    @apply rounded-lg border-2 border-brand-primary bg-transparent px-6 py-3 font-semibold text-brand-primary transition-all duration-200 hover:bg-brand-primary hover:text-white focus:outline-none focus:ring-2 focus:ring-brand-primary/50 focus:ring-offset-2;
  }

  .btn-ghost {
    @apply rounded-lg bg-transparent px-6 py-3 font-medium text-neutral-600 transition-all duration-200 hover:bg-neutral-100 hover:text-neutral-900 focus:outline-none focus:ring-2 focus:ring-neutral-300 focus:ring-offset-2;
  }

  /* Engineering Application Gradients */
  .hero-gradient {
    @apply bg-gradient-to-br from-brand-dark via-brand-secondary to-brand-primary;
  }

  .engineering-gradient {
    @apply bg-gradient-to-r from-brand-secondary via-brand-accent to-brand-primary;
  }

  .professional-gradient {
    @apply bg-gradient-to-br from-neutral-50 via-white to-neutral-100;
  }

  /* Form Styles */
  .form-input {
    @apply w-full rounded-lg border border-neutral-300 bg-white px-4 py-3 text-neutral-900 placeholder-neutral-500 shadow-sm transition-all duration-200 focus:border-brand-secondary focus:outline-none focus:ring-2 focus:ring-brand-secondary/20;
  }

  .form-label {
    @apply mb-2 block text-sm font-semibold text-neutral-700;
  }

  .form-error {
    @apply mt-1 text-sm text-error;
  }

  /* Navigation Styles */
  .nav-link {
    @apply rounded-md px-3 py-2 text-sm font-medium text-neutral-600 transition-all duration-200 hover:bg-neutral-100 hover:text-neutral-900;
  }

  .nav-link-active {
    @apply rounded-md bg-brand-secondary/10 px-3 py-2 text-sm font-medium text-brand-secondary;
  }

  /* Engineering Header */
  .engineering-header {
    @apply border-b border-neutral-200 bg-white/95 shadow-sm backdrop-blur-sm;
  }

  /* Professional Layout */
  .engineering-layout {
    @apply min-h-screen bg-gradient-to-br from-neutral-50 via-white to-neutral-100;
  }

  /* Status Indicators */
  .status-success {
    @apply inline-flex items-center rounded-full bg-success/10 px-3 py-1 text-sm font-medium text-success;
  }

  .status-warning {
    @apply inline-flex items-center rounded-full bg-warning/10 px-3 py-1 text-sm font-medium text-warning;
  }

  .status-error {
    @apply inline-flex items-center rounded-full bg-error/10 px-3 py-1 text-sm font-medium text-error;
  }

  .status-info {
    @apply inline-flex items-center rounded-full bg-info/10 px-3 py-1 text-sm font-medium text-info;
  }

  /* Engineering Typography */
  .heading-primary {
    @apply text-4xl font-bold tracking-tight text-neutral-900 sm:text-5xl;
  }

  .heading-secondary {
    @apply text-3xl font-bold tracking-tight text-neutral-900 sm:text-4xl;
  }

  .heading-tertiary {
    @apply text-2xl font-bold tracking-tight text-neutral-900 sm:text-3xl;
  }

  .text-lead {
    @apply text-xl leading-relaxed text-neutral-600;
  }

  .text-body {
    @apply text-base leading-relaxed text-neutral-700;
  }

  .text-caption {
    @apply text-sm text-neutral-500;
  }
}

:root {
  --radius: 0.625rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.141 0.005 285.823);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.141 0.005 285.823);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.141 0.005 285.823);
  --primary: oklch(0.21 0.006 285.885);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.967 0.001 286.375);
  --secondary-foreground: oklch(0.21 0.006 285.885);
  --muted: oklch(0.967 0.001 286.375);
  --muted-foreground: oklch(0.552 0.016 285.938);
  --accent: oklch(0.967 0.001 286.375);
  --accent-foreground: oklch(0.21 0.006 285.885);
  --destructive: oklch(0.637 0.237 25.331);
  --destructive-foreground: oklch(0.637 0.237 25.331);
  --border: oklch(0.92 0.004 286.32);
  --input: oklch(0.871 0.006 286.286);
  --ring: oklch(0.871 0.006 286.286);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.141 0.005 285.823);
  --sidebar-primary: oklch(0.21 0.006 285.885);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.967 0.001 286.375);
  --sidebar-accent-foreground: oklch(0.21 0.006 285.885);
  --sidebar-border: oklch(0.92 0.004 286.32);
  --sidebar-ring: oklch(0.871 0.006 286.286);
}

.dark {
  --background: oklch(0.141 0.005 285.823);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.141 0.005 285.823);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.141 0.005 285.823);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.985 0 0);
  --primary-foreground: oklch(0.21 0.006 285.885);
  --secondary: oklch(0.274 0.006 286.033);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.21 0.006 285.885);
  --muted-foreground: oklch(0.65 0.01 286);
  --accent: oklch(0.21 0.006 285.885);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.396 0.141 25.723);
  --destructive-foreground: oklch(0.637 0.237 25.331);
  --border: oklch(0.274 0.006 286.033);
  --input: oklch(0.274 0.006 286.033);
  --ring: oklch(0.442 0.017 285.786);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(0.274 0.006 286.033);
  --sidebar-ring: oklch(0.442 0.017 285.786);
}
