/**
 * Unit tests for useDashboardStore hook
 */

import { renderHook, act } from '@testing-library/react'
import { describe, it, expect, beforeEach } from 'vitest'
import { useDashboardStore } from '../../hooks/useDashboardStore'
import type { DashboardData, ProjectSummary, RecentCalculation } from '../../types'
import { defaultDashboardData } from '../../utils'

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
}
Object.defineProperty(window, 'localStorage', {
  value: localStorageMock,
})

describe('useDashboardStore', () => {
  beforeEach(() => {
    // Clear all mocks before each test
    vi.clearAllMocks()

    // Reset store state
    const { result } = renderHook(() => useDashboardStore())
    act(() => {
      result.current.resetState()
    })
  })

  it('initializes with default state', () => {
    const { result } = renderHook(() => useDashboardStore())

    expect(result.current.data).toEqual(defaultDashboardData)
    expect(result.current.isLoading).toBe(false)
    expect(result.current.error).toBe(null)
    expect(result.current.selectedProject).toBe(null)
    expect(result.current.projectFilter).toBe('all')
    expect(result.current.calculationFilter).toBe('all')
    expect(result.current.searchQuery).toBe('')
    expect(result.current.layout).toBe('grid')
  })

  it('sets dashboard data correctly', () => {
    const { result } = renderHook(() => useDashboardStore())

    const testData: DashboardData = {
      ...defaultDashboardData,
      metrics: {
        ...defaultDashboardData.metrics,
        totalProjects: 25,
      },
    }

    act(() => {
      result.current.setData(testData)
    })

    expect(result.current.data).toEqual(testData)
    expect(result.current.lastUpdated).toBeTruthy()
    expect(result.current.error).toBe(null)
    expect(result.current.isLoading).toBe(false)
  })

  it('updates metrics correctly', () => {
    const { result } = renderHook(() => useDashboardStore())

    // First set initial data
    act(() => {
      result.current.setData(defaultDashboardData)
    })

    const metricsUpdate = {
      totalProjects: 50,
      activeProjects: 30,
    }

    act(() => {
      result.current.updateMetrics(metricsUpdate)
    })

    expect(result.current.data?.metrics.totalProjects).toBe(50)
    expect(result.current.data?.metrics.activeProjects).toBe(30)
    expect(result.current.data?.metrics.completedCalculations).toBe(
      defaultDashboardData.metrics.completedCalculations
    )
  })

  it('adds project correctly', () => {
    const { result } = renderHook(() => useDashboardStore())

    // First set initial data
    act(() => {
      result.current.setData(defaultDashboardData)
    })

    const newProject: ProjectSummary = {
      id: 'new-project',
      name: 'New Test Project',
      description: 'Test description',
      status: 'active',
      progress: 0,
      lastModified: new Date().toISOString(),
      createdAt: new Date().toISOString(),
      owner: 'Test User',
      type: 'heat_tracing',
      priority: 'medium',
    }

    act(() => {
      result.current.addProject(newProject)
    })

    expect(result.current.data?.projects).toHaveLength(1)
    expect(result.current.data?.projects[0]).toEqual(newProject)
  })

  it('updates project correctly', () => {
    const { result } = renderHook(() => useDashboardStore())

    const initialProject: ProjectSummary = {
      id: 'test-project',
      name: 'Test Project',
      description: 'Test description',
      status: 'active',
      progress: 50,
      lastModified: new Date().toISOString(),
      createdAt: new Date().toISOString(),
      owner: 'Test User',
      type: 'heat_tracing',
      priority: 'medium',
    }

    const dataWithProject: DashboardData = {
      ...defaultDashboardData,
      projects: [initialProject],
    }

    act(() => {
      result.current.setData(dataWithProject)
    })

    const updates = {
      name: 'Updated Project Name',
      progress: 75,
    }

    act(() => {
      result.current.updateProject('test-project', updates)
    })

    expect(result.current.data?.projects[0].name).toBe('Updated Project Name')
    expect(result.current.data?.projects[0].progress).toBe(75)
    expect(result.current.data?.projects[0].status).toBe('active') // Unchanged
  })

  it('removes project correctly', () => {
    const { result } = renderHook(() => useDashboardStore())

    const project1: ProjectSummary = {
      id: 'project-1',
      name: 'Project 1',
      description: 'Description 1',
      status: 'active',
      progress: 50,
      lastModified: new Date().toISOString(),
      createdAt: new Date().toISOString(),
      owner: 'User 1',
      type: 'heat_tracing',
      priority: 'medium',
    }

    const project2: ProjectSummary = {
      id: 'project-2',
      name: 'Project 2',
      description: 'Description 2',
      status: 'completed',
      progress: 100,
      lastModified: new Date().toISOString(),
      createdAt: new Date().toISOString(),
      owner: 'User 2',
      type: 'load_calculation',
      priority: 'high',
    }

    const dataWithProjects: DashboardData = {
      ...defaultDashboardData,
      projects: [project1, project2],
    }

    act(() => {
      result.current.setData(dataWithProjects)
    })

    act(() => {
      result.current.removeProject('project-1')
    })

    expect(result.current.data?.projects).toHaveLength(1)
    expect(result.current.data?.projects[0].id).toBe('project-2')
  })

  it('adds calculation correctly', () => {
    const { result } = renderHook(() => useDashboardStore())

    // First set initial data
    act(() => {
      result.current.setData(defaultDashboardData)
    })

    const newCalculation: RecentCalculation = {
      id: 'new-calc',
      name: 'New Calculation',
      type: 'heat_tracing',
      projectId: 'project-1',
      projectName: 'Test Project',
      result: 'Test result',
      status: 'completed',
      createdAt: new Date().toISOString(),
      lastModified: new Date().toISOString(),
      calculatedBy: 'Test User',
    }

    act(() => {
      result.current.addCalculation(newCalculation)
    })

    expect(result.current.data?.recentCalculations).toHaveLength(1)
    expect(result.current.data?.recentCalculations[0]).toEqual(newCalculation)
  })

  it('sets filters correctly', () => {
    const { result } = renderHook(() => useDashboardStore())

    act(() => {
      result.current.setProjectFilter('active')
    })
    expect(result.current.projectFilter).toBe('active')

    act(() => {
      result.current.setCalculationFilter('heat_tracing')
    })
    expect(result.current.calculationFilter).toBe('heat_tracing')

    act(() => {
      result.current.setSearchQuery('test query')
    })
    expect(result.current.searchQuery).toBe('test query')
  })

  it('sets UI state correctly', () => {
    const { result } = renderHook(() => useDashboardStore())

    act(() => {
      result.current.setSelectedProject('project-1')
    })
    expect(result.current.selectedProject).toBe('project-1')

    act(() => {
      result.current.setActiveWidget('metrics')
    })
    expect(result.current.activeWidget).toBe('metrics')

    act(() => {
      result.current.setLayout('list')
    })
    expect(result.current.layout).toBe('list')

    act(() => {
      result.current.setSidebarCollapsed(true)
    })
    expect(result.current.sidebarCollapsed).toBe(true)

    act(() => {
      result.current.setShowWelcomeModal(false)
    })
    expect(result.current.showWelcomeModal).toBe(false)
  })

  it('sets loading and error states correctly', () => {
    const { result } = renderHook(() => useDashboardStore())

    act(() => {
      result.current.setLoading(true)
    })
    expect(result.current.isLoading).toBe(true)

    const testError = new Error('Test error')
    act(() => {
      result.current.setError(testError)
    })
    expect(result.current.error).toBe(testError)
    expect(result.current.isLoading).toBe(false)
  })

  it('refreshes data correctly', () => {
    const { result } = renderHook(() => useDashboardStore())

    act(() => {
      result.current.refreshData()
    })

    expect(result.current.isRefreshing).toBe(true)

    // Wait for the timeout to complete
    setTimeout(() => {
      expect(result.current.isRefreshing).toBe(false)
    }, 1100)
  })

  it('resets state correctly', () => {
    const { result } = renderHook(() => useDashboardStore())

    // Set some state
    act(() => {
      result.current.setSelectedProject('project-1')
      result.current.setProjectFilter('active')
      result.current.setSearchQuery('test')
      result.current.setLoading(true)
    })

    // Reset state
    act(() => {
      result.current.resetState()
    })

    expect(result.current.data).toEqual(defaultDashboardData)
    expect(result.current.selectedProject).toBe(null)
    expect(result.current.projectFilter).toBe('all')
    expect(result.current.searchQuery).toBe('')
    expect(result.current.isLoading).toBe(false)
  })
})
