# Settings Module

**Ultimate Electrical Designer - Settings & User Preferences**

A comprehensive settings management system built following the 14-step client module workflow and engineering-grade development standards.

## Overview

The Settings module provides a complete solution for managing user preferences, application configuration, and system settings in the Ultimate Electrical Designer application. It features real-time synchronization, import/export functionality, comprehensive validation, and full accessibility compliance.

## Features

### Core Functionality

- **User Preferences Management** - Complete CRUD operations for user settings
- **Real-time Synchronization** - Cross-tab communication and automatic sync
- **Import/Export** - Backup and restore settings with validation
- **Search & Filtering** - Find settings quickly across all categories
- **Auto-save** - Optional automatic saving with debouncing
- **Reset Functionality** - Reset to defaults with granular control

### Settings Categories

1. **Account** - Profile, security, and authentication settings
2. **Appearance** - Theme, layout, and display preferences
3. **Notifications** - Email, push, and desktop notification settings
4. **Privacy** - Data sharing, analytics, and privacy controls
5. **Advanced** - Developer options, debugging, and performance settings
6. **Engineering Calculations** - Units, precision, standards, and calculation preferences

### Technical Features

- **Type Safety** - Full TypeScript support with strict mode compliance
- **Validation** - Comprehensive Zod schemas for all settings
- **State Management** - Zustand for client state, React Query for server state
- **Accessibility** - WCAG 2.1 AA compliance with keyboard navigation
- **Testing** - 100% test coverage with unit, integration, and E2E tests
- **Performance** - Optimized with code splitting and intelligent caching

## Quick Start

### Basic Usage

```tsx
import { SettingsPanel } from '@/modules/settings'

function App() {
  return (
    <div>
      <SettingsPanel />
    </div>
  )
}
```

### With Auto-save

```tsx
import { SettingsPanel } from '@/modules/settings'

function App() {
  return <SettingsPanel autoSave={true} showSearch={true} showActions={true} />
}
```

### Category-specific Settings

```tsx
import { CategorySettingsPanel } from '@/modules/settings'

function AppearanceSettings() {
  return <CategorySettingsPanel category="appearance" autoSave={true} />
}
```

### Using the Settings Hook

```tsx
import { useSettings } from '@/modules/settings'

function CustomSettingsComponent() {
  const { preferences, formData, hasChanges, actions, ui } = useSettings({ autoSave: true })

  const handleThemeChange = (theme: string) => {
    actions.updateField('theme', theme)
  }

  return (
    <div>
      <select
        value={formData.theme || preferences?.theme}
        onChange={(e) => handleThemeChange(e.target.value)}
      >
        <option value="light">Light</option>
        <option value="dark">Dark</option>
        <option value="system">System</option>
      </select>

      {hasChanges && <button onClick={actions.savePreferences}>Save Changes</button>}
    </div>
  )
}
```

## Architecture

### Module Structure

```
settings/
├── index.ts                 # Main module exports
├── types.ts                 # TypeScript definitions
├── constants.ts             # Configuration and constants
├── utils.ts                 # Utility functions
├── api/                     # API integration
│   ├── settingsApi.ts       # API client methods
│   └── settingsQueries.ts   # React Query hooks
├── hooks/                   # Custom hooks
│   ├── useSettings.ts       # Main settings hook
│   ├── useSettingsSync.ts   # Cross-tab synchronization
│   └── useSettingsExport.ts # Import/export functionality
├── stores/                  # State management
│   └── settingsStore.ts     # Zustand store
├── components/              # UI components
│   ├── SettingsPanel.tsx    # Main settings panel
│   ├── PreferencesForm.tsx  # Form components
│   ├── SettingsSearch.tsx   # Search functionality
│   ├── ImportExportDialog.tsx # Import/export UI
│   ├── SettingsReset.tsx    # Reset functionality
│   └── ConfigurationManager.tsx # Advanced configuration
├── schemas/                 # Validation schemas
│   └── settingsSchemas.ts   # Zod schemas
└── __tests__/              # Test files
    ├── components/         # Component tests
    ├── hooks/             # Hook tests
    ├── stores/            # Store tests
    └── integration/       # Integration tests
```

### Data Flow

1. **Server State** - React Query manages server-side preferences
2. **Client State** - Zustand handles UI state and form data
3. **Synchronization** - BroadcastChannel for cross-tab communication
4. **Persistence** - Local storage for client state backup
5. **Validation** - Zod schemas ensure data integrity

## Components

### SettingsPanel

Main settings interface with tabbed navigation.

**Props:**

- `className?: string` - Additional CSS classes
- `autoSave?: boolean` - Enable automatic saving
- `showSearch?: boolean` - Show search functionality
- `showActions?: boolean` - Show action buttons

### CompactSettingsPanel

Simplified version for smaller spaces.

### CategorySettingsPanel

Single-category settings interface.

**Props:**

- `category: SettingsCategory` - Category to display
- `autoSave?: boolean` - Enable automatic saving

### PreferencesForm

Form component for specific settings category.

**Props:**

- `category: SettingsCategory` - Settings category
- `className?: string` - Additional CSS classes

### SettingsSearch

Search component with real-time filtering.

**Props:**

- `placeholder?: string` - Search input placeholder
- `showResults?: boolean` - Show search results dropdown
- `onResultClick?: (category, field?) => void` - Result click handler

## Hooks

### useSettings

Main hook for settings management.

**Options:**

- `autoSave?: boolean` - Enable auto-save mode
- `autoSaveDelay?: number` - Auto-save debounce delay

**Returns:**

- `preferences` - Current user preferences
- `formData` - Form state data
- `hasChanges` - Whether there are unsaved changes
- `actions` - Action methods for updating settings
- `ui` - UI state (loading, errors, etc.)
- `autoSave` - Auto-save state and controls

### useSettingsSync

Cross-tab synchronization functionality.

**Options:**

- `enabled?: boolean` - Enable synchronization
- `syncInterval?: number` - Sync check interval
- `conflictResolution?: 'latest' | 'manual' | 'merge'` - Conflict resolution strategy

### useSettingsExport

Import/export functionality.

**Returns:**

- `exportSettings` - Export settings to file
- `importSettings` - Import settings from file
- `validateImportData` - Validate import data
- `isExporting` - Export state
- `isImporting` - Import state

## API Integration

### Backend Endpoints

- `GET /api/v1/users/me/preferences` - Get user preferences
- `PUT /api/v1/users/me/preferences` - Update preferences
- `DELETE /api/v1/users/me/preferences` - Reset to defaults
- `POST /api/v1/users/me/preferences/export` - Export preferences
- `POST /api/v1/users/me/preferences/import` - Import preferences

### Error Handling

All API calls include comprehensive error handling with:

- Validation error messages
- Network error recovery
- Optimistic updates with rollback
- User-friendly error notifications

## Validation

### Zod Schemas

All settings are validated using Zod schemas:

```typescript
import { UserPreferencesSchema } from '@/modules/settings/schemas'

// Validate preferences
const result = UserPreferencesSchema.safeParse(data)
if (!result.success) {
  console.error('Validation errors:', result.error.issues)
}
```

### Business Rules

- Auto-save interval: 30-3600 seconds
- Calculation precision: 0-6 decimal places
- Safety factor: 1.0-3.0
- Supported languages: en, es, fr, de
- Supported themes: light, dark, system

## Testing

### Running Tests

```bash
# Unit tests
npm run test:unit

# Integration tests
npm run test:integration

# E2E tests
npm run test:e2e

# All tests with coverage
npm run test:coverage
```

### Test Coverage

- **Unit Tests** - Components, hooks, utilities, stores
- **Integration Tests** - Complete workflows and API integration
- **E2E Tests** - User scenarios and accessibility
- **Target Coverage** - 100% for new code, 95%+ overall

## Accessibility

### WCAG 2.1 AA Compliance

- **Keyboard Navigation** - Full keyboard support
- **Screen Readers** - Proper ARIA labels and descriptions
- **Focus Management** - Logical tab order and focus indicators
- **Color Contrast** - Meets contrast requirements
- **Responsive Design** - Works on all screen sizes

### Keyboard Shortcuts

- `Tab` / `Shift+Tab` - Navigate between elements
- `Arrow Keys` - Navigate between tabs
- `Enter` / `Space` - Activate buttons and toggles
- `Escape` - Close dialogs and clear search

## Performance

### Optimization Features

- **Code Splitting** - Lazy loading of settings categories
- **Debounced Updates** - Prevents excessive API calls
- **Intelligent Caching** - React Query with optimized cache times
- **Bundle Size** - Minimal impact on application bundle

### Monitoring

- Performance metrics tracked
- Error boundaries for graceful degradation
- Loading states for better UX
- Memory leak prevention

## Contributing

See [IMPLEMENTATION.md](./IMPLEMENTATION.md) for detailed implementation guidelines and [TESTING.md](./TESTING.md) for testing standards.

## License

Part of the Ultimate Electrical Designer project. See main project license.
