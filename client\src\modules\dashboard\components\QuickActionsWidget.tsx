'use client'

import { cn } from '@/lib/utils'
import Link from 'next/link'
import { memo } from 'react'
import type { QuickActionsWidgetProps } from '../types'
import { getBrandColorClass } from '../utils'

/**
 * Dashboard quick actions widget component
 */
export const QuickActionsWidget = memo(function QuickActionsWidget({
  actions,
  onActionClick,
  className,
}: QuickActionsWidgetProps) {
  const getActionIcon = (iconName: string) => {
    switch (iconName) {
      case 'zap':
        return (
          <svg className="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M13 10V3L4 14h7v7l9-11h-7z"
            />
          </svg>
        )
      case 'calculator':
        return (
          <svg className="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z"
            />
          </svg>
        )
      case 'cable':
        return (
          <svg className="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"
            />
          </svg>
        )
      case 'plus-circle':
        return (
          <svg className="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 9v3m0 0v3m0-3h3m-3 0H9m12 0a9 9 0 11-18 0 9 9 0 0118 0z"
            />
          </svg>
        )
      case 'folder':
        return (
          <svg className="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"
            />
          </svg>
        )
      case 'file-text':
        return (
          <svg className="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
            />
          </svg>
        )
      case 'shield-check':
        return (
          <svg className="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"
            />
          </svg>
        )
      default:
        return (
          <svg className="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 6v6m0 0v6m0-6h6m-6 0H6"
            />
          </svg>
        )
    }
  }

  const enabledActions = actions.filter((action) => action.isEnabled)

  return (
    <div className={cn('rounded-lg border border-gray-200 bg-white shadow-sm', className)}>
      {/* Header */}
      <div className="border-b border-gray-200 px-6 py-4">
        <h3 className="text-lg font-medium text-gray-900">Quick Actions</h3>
        <p className="mt-1 text-sm text-gray-500">Start your electrical design work</p>
      </div>

      {/* Content */}
      <div className="p-6">
        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
          {enabledActions.map((action) => {
            const ActionComponent = action.href ? Link : 'button'
            const actionProps = action.href
              ? { href: action.href }
              : {
                  onClick: () => onActionClick?.(action),
                  type: 'button' as const,
                }

            return (
              <ActionComponent
                key={action.id}
                {...actionProps}
                className={cn(
                  'group relative rounded-lg border border-gray-200 p-6 transition-all duration-200 hover:border-gray-300 hover:shadow-md focus:outline-none focus:ring-2 focus:ring-brand-primary focus:ring-offset-2',
                  !action.href && 'cursor-pointer'
                )}
              >
                <div className="flex items-start space-x-4">
                  <div
                    className={cn(
                      'flex-shrink-0 rounded-lg p-3 transition-colors duration-200',
                      getBrandColorClass(action.color, 'bg') + '/10',
                      `group-hover:${getBrandColorClass(action.color, 'bg')}/20`
                    )}
                  >
                    <div
                      className={cn(
                        'transition-colors duration-200',
                        getBrandColorClass(action.color, 'text'),
                        `group-hover:${getBrandColorClass(action.color, 'text')}`
                      )}
                    >
                      {getActionIcon(action.icon)}
                    </div>
                  </div>
                  <div className="min-w-0 flex-1">
                    <h4 className="text-base font-medium text-gray-900 transition-colors duration-200 group-hover:text-gray-700">
                      {action.title}
                    </h4>
                    <p className="mt-1 line-clamp-2 text-sm text-gray-500">{action.description}</p>
                    <div className="mt-3 flex items-center">
                      <span
                        className={cn(
                          'inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium',
                          getBrandColorClass(action.color, 'bg') + '/10',
                          getBrandColorClass(action.color, 'text')
                        )}
                      >
                        {action.category.replace('_', ' ')}
                      </span>
                      {action.requiredRole && (
                        <span className="ml-2 inline-flex items-center rounded-full bg-gray-100 px-2.5 py-0.5 text-xs font-medium text-gray-800">
                          {action.requiredRole}
                        </span>
                      )}
                    </div>
                  </div>
                  <div className="flex-shrink-0">
                    <svg
                      className="h-5 w-5 text-gray-400 transition-colors duration-200 group-hover:text-gray-600"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M9 5l7 7-7 7"
                      />
                    </svg>
                  </div>
                </div>
              </ActionComponent>
            )
          })}
        </div>

        {enabledActions.length === 0 && (
          <div className="py-8 text-center">
            <svg
              className="mx-auto h-12 w-12 text-gray-400"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 6v6m0 0v6m0-6h6m-6 0H6"
              />
            </svg>
            <h3 className="mt-2 text-sm font-medium text-gray-900">No actions available</h3>
            <p className="mt-1 text-sm text-gray-500">
              Quick actions will appear here when available.
            </p>
          </div>
        )}
      </div>
    </div>
  )
})

QuickActionsWidget.displayName = 'QuickActionsWidget'
