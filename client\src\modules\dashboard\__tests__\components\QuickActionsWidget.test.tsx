/**
 * Unit tests for QuickActionsWidget component
 */

import { render, screen, fireEvent } from '@testing-library/react'
import { describe, it, expect, vi } from 'vitest'
import { QuickActionsWidget } from '../../components/QuickActionsWidget'
import type { QuickAction } from '../../types'

const mockActions: QuickAction[] = [
  {
    id: 'heat-tracing',
    title: 'Heat Tracing Design',
    description: 'Design and calculate heat tracing systems for industrial applications',
    icon: 'zap',
    color: 'primary',
    href: '/heat-tracing',
    category: 'calculation',
    isEnabled: true,
    requiresAuth: true,
  },
  {
    id: 'load-calculations',
    title: 'Load Calculations',
    description: 'Perform electrical load analysis and sizing calculations',
    icon: 'calculator',
    color: 'secondary',
    href: '/load-calculations',
    category: 'calculation',
    isEnabled: true,
    requiresAuth: true,
  },
  {
    id: 'disabled-action',
    title: 'Disabled Action',
    description: 'This action is disabled',
    icon: 'folder',
    color: 'dark',
    href: '/disabled',
    category: 'project',
    isEnabled: false,
    requiresAuth: true,
  },
]

// Mock Next.js Link component
vi.mock('next/link', () => ({
  default: ({ children, href, ...props }: any) => (
    <a href={href} {...props}>
      {children}
    </a>
  ),
}))

describe('QuickActionsWidget', () => {
  it('renders enabled actions correctly', () => {
    render(<QuickActionsWidget actions={mockActions} />)

    expect(screen.getByText('Quick Actions')).toBeInTheDocument()
    expect(screen.getByText('Start your electrical design work')).toBeInTheDocument()
    expect(screen.getByText('Heat Tracing Design')).toBeInTheDocument()
    expect(screen.getByText('Load Calculations')).toBeInTheDocument()

    // Disabled action should not be rendered
    expect(screen.queryByText('Disabled Action')).not.toBeInTheDocument()
  })

  it('displays action descriptions and categories', () => {
    render(<QuickActionsWidget actions={mockActions} />)

    expect(
      screen.getByText('Design and calculate heat tracing systems for industrial applications')
    ).toBeInTheDocument()
    expect(
      screen.getByText('Perform electrical load analysis and sizing calculations')
    ).toBeInTheDocument()
    expect(screen.getAllByText('calculation')).toHaveLength(2)
  })

  it('renders action icons correctly', () => {
    render(<QuickActionsWidget actions={mockActions} />)

    // Check that SVG icons are present
    const svgElements = screen.getAllByRole('img', { hidden: true })
    expect(svgElements.length).toBeGreaterThan(0)
  })

  it('creates links for actions with href', () => {
    render(<QuickActionsWidget actions={mockActions} />)

    const heatTracingLink = screen.getByText('Heat Tracing Design').closest('a')
    expect(heatTracingLink).toHaveAttribute('href', '/heat-tracing')

    const loadCalcLink = screen.getByText('Load Calculations').closest('a')
    expect(loadCalcLink).toHaveAttribute('href', '/load-calculations')
  })

  it('calls onActionClick for actions without href', () => {
    const onActionClick = vi.fn()
    const actionsWithoutHref = mockActions.map((action) => ({ ...action, href: undefined }))

    render(<QuickActionsWidget actions={actionsWithoutHref} onActionClick={onActionClick} />)

    const heatTracingButton = screen.getByText('Heat Tracing Design').closest('button')
    fireEvent.click(heatTracingButton!)

    expect(onActionClick).toHaveBeenCalledWith(actionsWithoutHref[0])
  })

  it('shows empty state when no enabled actions', () => {
    const disabledActions = mockActions.map((action) => ({ ...action, isEnabled: false }))

    render(<QuickActionsWidget actions={disabledActions} />)

    expect(screen.getByText('No actions available')).toBeInTheDocument()
    expect(screen.getByText('Quick actions will appear here when available.')).toBeInTheDocument()
  })

  it('applies brand color classes correctly', () => {
    render(<QuickActionsWidget actions={mockActions} />)

    // Check that brand color classes are applied
    const actionElements = screen
      .getAllByRole('generic')
      .filter((el) => el.className.includes('bg-brand-') || el.className.includes('text-brand-'))

    expect(actionElements.length).toBeGreaterThan(0)
  })

  it('displays required role badges when present', () => {
    const actionsWithRoles = mockActions.map((action) => ({
      ...action,
      requiredRole: action.id === 'heat-tracing' ? 'ADMIN' : undefined,
    }))

    render(<QuickActionsWidget actions={actionsWithRoles} />)

    expect(screen.getByText('ADMIN')).toBeInTheDocument()
  })

  it('applies custom className', () => {
    const { container } = render(
      <QuickActionsWidget actions={mockActions} className="custom-class" />
    )

    expect(container.firstChild).toHaveClass('custom-class')
  })

  it('has proper accessibility attributes', () => {
    render(<QuickActionsWidget actions={mockActions} />)

    expect(screen.getByRole('heading', { name: 'Quick Actions' })).toBeInTheDocument()

    // Check for proper focus management
    const actionElements = screen.getAllByRole('link')
    actionElements.forEach((element) => {
      expect(element).toHaveClass('focus:outline-none', 'focus:ring-2')
    })
  })

  it('shows hover effects on action cards', () => {
    render(<QuickActionsWidget actions={mockActions} />)

    const actionCards = screen
      .getAllByRole('link')
      .filter(
        (el) =>
          el.className.includes('hover:border-gray-300') && el.className.includes('hover:shadow-md')
      )

    expect(actionCards.length).toBe(2) // Two enabled actions
  })

  it('handles keyboard navigation', () => {
    render(<QuickActionsWidget actions={mockActions} />)

    const firstAction = screen.getByText('Heat Tracing Design').closest('a')
    const secondAction = screen.getByText('Load Calculations').closest('a')

    // Check that elements are focusable
    expect(firstAction).toHaveAttribute('href')
    expect(secondAction).toHaveAttribute('href')
  })

  it('renders correct number of enabled actions', () => {
    render(<QuickActionsWidget actions={mockActions} />)

    // Should only render enabled actions (2 out of 3)
    const actionCards = screen.getAllByRole('link')
    expect(actionCards).toHaveLength(2)
  })
})
