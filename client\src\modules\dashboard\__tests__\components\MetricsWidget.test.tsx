/**
 * Unit tests for MetricsWidget component
 */

import { render, screen } from '@testing-library/react'
import { describe, it, expect } from 'vitest'
import { MetricsWidget } from '../../components/MetricsWidget'
import type { DashboardMetrics } from '../../types'

const mockMetrics: DashboardMetrics = {
  totalProjects: 12,
  activeProjects: 8,
  completedCalculations: 156,
  recentActivity: 24,
  systemUptime: '99.9%',
  lastLogin: new Date('2024-12-19T10:00:00Z').toISOString(),
}

describe('MetricsWidget', () => {
  it('renders metrics correctly', () => {
    render(<MetricsWidget metrics={mockMetrics} />)

    expect(screen.getByText('Total Projects')).toBeInTheDocument()
    expect(screen.getByText('12')).toBeInTheDocument()

    expect(screen.getByText('Active Projects')).toBeInTheDocument()
    expect(screen.getByText('8')).toBeInTheDocument()

    expect(screen.getByText('Completed Calculations')).toBeInTheDocument()
    expect(screen.getByText('156')).toBeInTheDocument()

    expect(screen.getByText('Recent Activity')).toBeInTheDocument()
    expect(screen.getByText('24')).toBeInTheDocument()
  })

  it('displays loading state correctly', () => {
    render(<MetricsWidget metrics={mockMetrics} isLoading={true} />)

    const loadingElements = screen.getAllByRole('generic')
    const animatedElements = loadingElements.filter((el) => el.className.includes('animate-pulse'))

    expect(animatedElements.length).toBeGreaterThan(0)
  })

  it('applies custom className', () => {
    const { container } = render(<MetricsWidget metrics={mockMetrics} className="custom-class" />)

    expect(container.firstChild).toHaveClass('custom-class')
  })

  it('displays last login time', () => {
    render(<MetricsWidget metrics={mockMetrics} />)

    // Should display relative time for last login
    expect(screen.getByText(/Last login/)).toBeInTheDocument()
  })

  it('renders all metric cards with correct icons', () => {
    render(<MetricsWidget metrics={mockMetrics} />)

    // Check that all SVG icons are present
    const svgElements = screen.getAllByRole('img', { hidden: true })
    expect(svgElements.length).toBe(4) // One for each metric card
  })

  it('has proper accessibility attributes', () => {
    render(<MetricsWidget metrics={mockMetrics} />)

    // Check for proper semantic structure
    const descriptions = screen.getAllByRole('term')
    expect(descriptions).toHaveLength(4)

    const values = screen.getAllByRole('definition')
    expect(values).toHaveLength(4)
  })

  it('handles zero values correctly', () => {
    const zeroMetrics: DashboardMetrics = {
      totalProjects: 0,
      activeProjects: 0,
      completedCalculations: 0,
      recentActivity: 0,
      systemUptime: '0%',
      lastLogin: new Date().toISOString(),
    }

    render(<MetricsWidget metrics={zeroMetrics} />)

    expect(screen.getAllByText('0')).toHaveLength(4)
    expect(screen.getByText('0%')).toBeInTheDocument()
  })

  it('displays hover effects on metric cards', () => {
    render(<MetricsWidget metrics={mockMetrics} />)

    const metricCards = screen
      .getAllByRole('generic')
      .filter((el) => el.className.includes('hover:shadow-md'))

    expect(metricCards.length).toBeGreaterThan(0)
  })
})
