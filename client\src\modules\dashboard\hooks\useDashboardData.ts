/**
 * React Query hooks for dashboard data fetching
 */

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { apiClient } from '@/lib/api/client'
import type {
  DashboardData,
  DashboardMetrics,
  ProjectSummary,
  RecentCalculation,
  ProjectsApiParams,
  CalculationsApiParams,
  MetricsApiParams,
  DashboardApiResponse,
} from '../types'
import { defaultDashboardData } from '../utils'
import { useDashboardActions } from './useDashboardStore'

// Query keys
export const dashboardQueryKeys = {
  all: ['dashboard'] as const,
  data: () => [...dashboardQueryKeys.all, 'data'] as const,
  metrics: (params?: MetricsApiParams) => [...dashboardQueryKeys.all, 'metrics', params] as const,
  projects: (params?: ProjectsApiParams) =>
    [...dashboardQueryKeys.all, 'projects', params] as const,
  calculations: (params?: CalculationsApiParams) =>
    [...dashboardQueryKeys.all, 'calculations', params] as const,
  activity: () => [...dashboardQueryKeys.all, 'activity'] as const,
}

/**
 * Hook to fetch complete dashboard data
 */
export function useDashboardData() {
  const { setData, setError, setLoading } = useDashboardActions()

  return useQuery({
    queryKey: dashboardQueryKeys.data(),
    queryFn: async (): Promise<DashboardData> => {
      try {
        setLoading(true)

        // For now, return mock data since the API endpoints may not be fully implemented
        // In a real implementation, this would fetch from the API
        const mockData: DashboardData = {
          ...defaultDashboardData,
          metrics: {
            totalProjects: 12,
            activeProjects: 8,
            completedCalculations: 156,
            recentActivity: 24,
            systemUptime: '99.9%',
            lastLogin: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(), // 2 hours ago
          },
          projects: [
            {
              id: '1',
              name: 'Industrial Heat Tracing System',
              description: 'Heat tracing design for chemical processing plant',
              status: 'active',
              progress: 75,
              lastModified: new Date(Date.now() - 1 * 60 * 60 * 1000).toISOString(),
              createdAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
              owner: 'John Smith',
              type: 'heat_tracing',
              priority: 'high',
            },
            {
              id: '2',
              name: 'Power Distribution Analysis',
              description: 'Load calculation for manufacturing facility',
              status: 'active',
              progress: 45,
              lastModified: new Date(Date.now() - 3 * 60 * 60 * 1000).toISOString(),
              createdAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
              owner: 'Sarah Johnson',
              type: 'load_calculation',
              priority: 'medium',
            },
            {
              id: '3',
              name: 'Cable Sizing Project',
              description: 'Cable sizing for offshore platform',
              status: 'completed',
              progress: 100,
              lastModified: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
              createdAt: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000).toISOString(),
              owner: 'Mike Davis',
              type: 'cable_sizing',
              priority: 'critical',
            },
          ],
          recentCalculations: [
            {
              id: '1',
              name: 'Heat Loss Calculation',
              type: 'heat_tracing',
              projectId: '1',
              projectName: 'Industrial Heat Tracing System',
              result: '2.5 kW/m required',
              status: 'completed',
              createdAt: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
              lastModified: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
              calculatedBy: 'John Smith',
            },
            {
              id: '2',
              name: 'Load Analysis',
              type: 'load_calculation',
              projectId: '2',
              projectName: 'Power Distribution Analysis',
              result: '850 kVA total load',
              status: 'completed',
              createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
              lastModified: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
              calculatedBy: 'Sarah Johnson',
            },
            {
              id: '3',
              name: 'Cable Sizing',
              type: 'cable_sizing',
              projectId: '3',
              projectName: 'Cable Sizing Project',
              result: '185mm² Cu cable',
              status: 'completed',
              createdAt: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(),
              lastModified: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(),
              calculatedBy: 'Mike Davis',
            },
          ],
        }

        setData(mockData)
        return mockData
      } catch (error) {
        setError(error as Error)
        throw error
      } finally {
        setLoading(false)
      }
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    refetchOnWindowFocus: false,
    retry: 2,
  })
}

/**
 * Hook to fetch dashboard metrics
 */
export function useDashboardMetrics(params?: MetricsApiParams) {
  return useQuery({
    queryKey: dashboardQueryKeys.metrics(params),
    queryFn: async (): Promise<DashboardMetrics> => {
      // Mock implementation - replace with actual API call
      return {
        totalProjects: 12,
        activeProjects: 8,
        completedCalculations: 156,
        recentActivity: 24,
        systemUptime: '99.9%',
        lastLogin: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
      }
    },
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 5 * 60 * 1000, // 5 minutes
    refetchInterval: 5 * 60 * 1000, // Refresh every 5 minutes
    enabled: true,
  })
}

/**
 * Hook to fetch projects
 */
export function useDashboardProjects(params?: ProjectsApiParams) {
  return useQuery({
    queryKey: dashboardQueryKeys.projects(params),
    queryFn: async (): Promise<ProjectSummary[]> => {
      // Mock implementation - replace with actual API call
      return [
        {
          id: '1',
          name: 'Industrial Heat Tracing System',
          description: 'Heat tracing design for chemical processing plant',
          status: 'active',
          progress: 75,
          lastModified: new Date(Date.now() - 1 * 60 * 60 * 1000).toISOString(),
          createdAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
          owner: 'John Smith',
          type: 'heat_tracing',
          priority: 'high',
        },
        {
          id: '2',
          name: 'Power Distribution Analysis',
          description: 'Load calculation for manufacturing facility',
          status: 'active',
          progress: 45,
          lastModified: new Date(Date.now() - 3 * 60 * 60 * 1000).toISOString(),
          createdAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
          owner: 'Sarah Johnson',
          type: 'load_calculation',
          priority: 'medium',
        },
      ]
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    enabled: true,
  })
}

/**
 * Hook to fetch recent calculations
 */
export function useDashboardCalculations(params?: CalculationsApiParams) {
  return useQuery({
    queryKey: dashboardQueryKeys.calculations(params),
    queryFn: async (): Promise<RecentCalculation[]> => {
      // Mock implementation - replace with actual API call
      return [
        {
          id: '1',
          name: 'Heat Loss Calculation',
          type: 'heat_tracing',
          projectId: '1',
          projectName: 'Industrial Heat Tracing System',
          result: '2.5 kW/m required',
          status: 'completed',
          createdAt: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
          lastModified: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
          calculatedBy: 'John Smith',
        },
        {
          id: '2',
          name: 'Load Analysis',
          type: 'load_calculation',
          projectId: '2',
          projectName: 'Power Distribution Analysis',
          result: '850 kVA total load',
          status: 'completed',
          createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
          lastModified: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
          calculatedBy: 'Sarah Johnson',
        },
      ]
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    enabled: true,
  })
}

/**
 * Hook to refresh dashboard data
 */
export function useRefreshDashboard() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async () => {
      // Invalidate all dashboard queries to trigger refetch
      await queryClient.invalidateQueries({ queryKey: dashboardQueryKeys.all })
    },
    onSuccess: () => {
      console.log('Dashboard data refreshed successfully')
    },
    onError: (error) => {
      console.error('Failed to refresh dashboard data:', error)
    },
  })
}

/**
 * Hook to create a new project
 */
export function useCreateProject() {
  const queryClient = useQueryClient()
  const { addProject } = useDashboardActions()

  return useMutation({
    mutationFn: async (
      projectData: Omit<ProjectSummary, 'id' | 'createdAt' | 'lastModified'>
    ): Promise<ProjectSummary> => {
      // Mock implementation - replace with actual API call
      const newProject: ProjectSummary = {
        ...projectData,
        id: Date.now().toString(),
        createdAt: new Date().toISOString(),
        lastModified: new Date().toISOString(),
      }

      // Simulate API delay
      await new Promise((resolve) => setTimeout(resolve, 1000))

      return newProject
    },
    onSuccess: (newProject) => {
      // Update the store
      addProject(newProject)

      // Invalidate and refetch projects
      queryClient.invalidateQueries({ queryKey: dashboardQueryKeys.projects() })
      queryClient.invalidateQueries({ queryKey: dashboardQueryKeys.data() })
    },
    onError: (error) => {
      console.error('Failed to create project:', error)
    },
  })
}
