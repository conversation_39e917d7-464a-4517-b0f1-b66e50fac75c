/**
 * Zustand store for dashboard client state management
 */

import { create } from 'zustand'
import { createJSONStorage, persist } from 'zustand/middleware'
import type {
  DashboardData,
  DashboardState,
  ProjectSummary,
  RecentCalculation,
  DashboardMetrics,
} from '../types'
import { defaultDashboardData } from '../utils'

export const useDashboardStore = create<DashboardState>()(
  persist(
    (set, get) => ({
      // Initial state
      data: null,
      isLoading: false,
      error: null,
      lastUpdated: null,

      // UI state
      selectedProject: null,
      activeWidget: null,
      isRefreshing: false,
      showWelcomeModal: true,

      // Filter and search state
      projectFilter: 'all',
      calculationFilter: 'all',
      searchQuery: '',

      // Layout state
      layout: 'grid',
      sidebarCollapsed: false,

      // Actions
      setData: (data: DashboardData) => {
        set({
          data,
          lastUpdated: new Date().toISOString(),
          error: null,
          isLoading: false,
        })
      },

      updateMetrics: (metrics: Partial<DashboardMetrics>) => {
        const currentData = get().data
        if (currentData) {
          set({
            data: {
              ...currentData,
              metrics: {
                ...currentData.metrics,
                ...metrics,
              },
            },
            lastUpdated: new Date().toISOString(),
          })
        }
      },

      addProject: (project: ProjectSummary) => {
        const currentData = get().data
        if (currentData) {
          set({
            data: {
              ...currentData,
              projects: [project, ...currentData.projects],
            },
            lastUpdated: new Date().toISOString(),
          })
        }
      },

      updateProject: (id: string, updates: Partial<ProjectSummary>) => {
        const currentData = get().data
        if (currentData) {
          set({
            data: {
              ...currentData,
              projects: currentData.projects.map((project) =>
                project.id === id ? { ...project, ...updates } : project
              ),
            },
            lastUpdated: new Date().toISOString(),
          })
        }
      },

      removeProject: (id: string) => {
        const currentData = get().data
        if (currentData) {
          set({
            data: {
              ...currentData,
              projects: currentData.projects.filter((project) => project.id !== id),
            },
            lastUpdated: new Date().toISOString(),
          })
        }
      },

      addCalculation: (calculation: RecentCalculation) => {
        const currentData = get().data
        if (currentData) {
          const updatedCalculations = [calculation, ...currentData.recentCalculations]
          // Keep only the most recent calculations based on preferences
          const limit = currentData.preferences.calculationHistoryLimit
          const limitedCalculations = updatedCalculations.slice(0, limit)

          set({
            data: {
              ...currentData,
              recentCalculations: limitedCalculations,
            },
            lastUpdated: new Date().toISOString(),
          })
        }
      },

      setLoading: (loading: boolean) => {
        set({ isLoading: loading })
      },

      setError: (error: Error | null) => {
        set({ error, isLoading: false })
      },

      setSelectedProject: (projectId: string | null) => {
        set({ selectedProject: projectId })
      },

      setActiveWidget: (widgetId: string | null) => {
        set({ activeWidget: widgetId })
      },

      setProjectFilter: (filter: 'all' | 'active' | 'completed' | 'on_hold') => {
        set({ projectFilter: filter })
      },

      setCalculationFilter: (
        filter: 'all' | 'heat_tracing' | 'load_calculation' | 'cable_sizing'
      ) => {
        set({ calculationFilter: filter })
      },

      setSearchQuery: (query: string) => {
        set({ searchQuery: query })
      },

      setLayout: (layout: 'grid' | 'list' | 'compact') => {
        set({ layout })

        // Update preferences in data if available
        const currentData = get().data
        if (currentData) {
          set({
            data: {
              ...currentData,
              preferences: {
                ...currentData.preferences,
                layout,
              },
            },
          })
        }
      },

      setSidebarCollapsed: (collapsed: boolean) => {
        set({ sidebarCollapsed: collapsed })
      },

      setShowWelcomeModal: (show: boolean) => {
        set({ showWelcomeModal: show })

        // Update preferences in data if available
        const currentData = get().data
        if (currentData) {
          set({
            data: {
              ...currentData,
              preferences: {
                ...currentData.preferences,
                showWelcomeMessage: show,
              },
            },
          })
        }
      },

      refreshData: () => {
        set({ isRefreshing: true })
        // This will trigger a refetch in the React Query hooks
        setTimeout(() => {
          set({ isRefreshing: false })
        }, 1000)
      },

      resetState: () => {
        set({
          data: defaultDashboardData,
          isLoading: false,
          error: null,
          lastUpdated: null,
          selectedProject: null,
          activeWidget: null,
          isRefreshing: false,
          showWelcomeModal: true,
          projectFilter: 'all',
          calculationFilter: 'all',
          searchQuery: '',
          layout: 'grid',
          sidebarCollapsed: false,
        })
      },
    }),
    {
      name: 'dashboard-store',
      storage: createJSONStorage(() => localStorage),
      partialize: (state) => ({
        // Only persist UI preferences, not data
        layout: state.layout,
        sidebarCollapsed: state.sidebarCollapsed,
        showWelcomeModal: state.showWelcomeModal,
        projectFilter: state.projectFilter,
        calculationFilter: state.calculationFilter,
      }),
    }
  )
)

// Selector hooks for better performance
export const useDashboardData = () => useDashboardStore((state) => state.data)
export const useDashboardLoading = () => useDashboardStore((state) => state.isLoading)
export const useDashboardError = () => useDashboardStore((state) => state.error)
export const useSelectedProject = () => useDashboardStore((state) => state.selectedProject)
export const useActiveWidget = () => useDashboardStore((state) => state.activeWidget)
export const useProjectFilter = () => useDashboardStore((state) => state.projectFilter)
export const useCalculationFilter = () => useDashboardStore((state) => state.calculationFilter)
export const useSearchQuery = () => useDashboardStore((state) => state.searchQuery)
export const useDashboardLayout = () => useDashboardStore((state) => state.layout)
export const useSidebarCollapsed = () => useDashboardStore((state) => state.sidebarCollapsed)
export const useShowWelcomeModal = () => useDashboardStore((state) => state.showWelcomeModal)

// Action hooks
export const useDashboardActions = () =>
  useDashboardStore((state) => ({
    setData: state.setData,
    updateMetrics: state.updateMetrics,
    addProject: state.addProject,
    updateProject: state.updateProject,
    removeProject: state.removeProject,
    addCalculation: state.addCalculation,
    setLoading: state.setLoading,
    setError: state.setError,
    setSelectedProject: state.setSelectedProject,
    setActiveWidget: state.setActiveWidget,
    setProjectFilter: state.setProjectFilter,
    setCalculationFilter: state.setCalculationFilter,
    setSearchQuery: state.setSearchQuery,
    setLayout: state.setLayout,
    setSidebarCollapsed: state.setSidebarCollapsed,
    setShowWelcomeModal: state.setShowWelcomeModal,
    refreshData: state.refreshData,
    resetState: state.resetState,
  }))
