/**
 * Enhanced Utilities Index
 * Central export point for all component utility functions
 */

// Validation utilities
export * from './validation'
export {
  ComponentApiError,
  formatValidationErrors,
  getFieldError,
  getFieldErrors,
  hasFieldError,
  validateAdvancedSearch,
  validateBulkComponents,
  validateComponent,
  validateField,
  validateFilter,
  validationHelpers,
} from './validation'

// Formatting utilities
export * from './formatting'
export {
  formatComponentDescription,
  formatComponentName,
  formatComponentStatus,
  formatDate,
  formatDimensions,
  formatFileSize,
  formatNumber,
  formatPercentage,
  formatPrice,
  formatPriceRange,
  formatSpecification,
  formatWeight,
  highlightSearchTerms,
  truncateText,
} from './formatting'

// Calculation utilities
export * from './calculations'
export {
  calculateAvailabilityScore,
  calculateBulkPrice,
  calculateCompatibilityScore,
  calculateComponentRating,
  calculateComponentTotalPrice,
  calculateDensity,
  calculateEnergyEfficiency,
  calculateHeatDissipation,
  calculateLifespan,
  calculatePaybackPeriod,
  calculatePowerConsumption,
  calculatePriceComparison,
  calculatePricePerWeight,
  calculateROI,
  calculateShippingDimensions,
  calculateShippingWeight,
  calculateTCO,
  calculateTotalPrice,
  calculateVolume,
} from './calculations'

// Re-export commonly used types for convenience
export type { ValidationError, ValidationResult } from './validation'

// Utility helper functions
export const componentUtils = {
  // Validation helpers
  validation: {
    validateComponent,
    validateFilter,
    validateAdvancedSearch,
    validateField,
    validateBulkComponents,
    formatValidationErrors,
    getFieldError,
    hasFieldError,
    getFieldErrors,
    ...validationHelpers,
  },

  // Formatting helpers
  formatting: {
    formatPrice,
    formatPriceRange,
    formatWeight,
    formatDimensions,
    formatComponentName,
    formatComponentDescription,
    formatComponentStatus,
    formatDate,
    formatNumber,
    formatPercentage,
    formatFileSize,
    formatSpecification,
    truncateText,
    highlightSearchTerms,
  },

  // Calculation helpers
  calculations: {
    calculateTotalPrice,
    calculateComponentTotalPrice,
    calculateBulkPrice,
    calculateShippingWeight,
    calculateShippingDimensions,
    calculatePricePerWeight,
    calculatePriceComparison,
    calculateAvailabilityScore,
    calculateCompatibilityScore,
    calculateComponentRating,
    calculateVolume,
    calculateDensity,
    calculatePowerConsumption,
    calculateEnergyEfficiency,
    calculateHeatDissipation,
    calculateLifespan,
    calculateTCO,
    calculateROI,
    calculatePaybackPeriod,
  },
}

// Common utility functions
export const createComponentId = (manufacturer: string, modelNumber: string): string => {
  return `${manufacturer.toLowerCase().replace(/\s+/g, '-')}-${modelNumber.toLowerCase().replace(/\s+/g, '-')}`
}

export const parseComponentId = (
  id: string
): { manufacturer: string; modelNumber: string } | null => {
  const parts = id.split('-')
  if (parts.length < 2) return null

  const manufacturer = parts[0].replace(/-/g, ' ')
  const modelNumber = parts.slice(1).join('-').replace(/-/g, ' ')

  return { manufacturer, modelNumber }
}

export const generateComponentSlug = (name: string): string => {
  return name
    .toLowerCase()
    .replace(/[^a-z0-9\s-]/g, '')
    .replace(/\s+/g, '-')
    .replace(/-+/g, '-')
    .trim()
}

export const isValidComponentId = (id: number | string): boolean => {
  if (typeof id === 'number') {
    return Number.isInteger(id) && id > 0
  }

  if (typeof id === 'string') {
    const numId = parseInt(id, 10)
    return !isNaN(numId) && numId > 0
  }

  return false
}

export const sanitizeSearchQuery = (query: string): string => {
  return query
    .trim()
    .replace(/[<>]/g, '') // Remove potential HTML tags
    .replace(/['"]/g, '') // Remove quotes that could break queries
    .substring(0, 200) // Limit length
}

export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) => {
  let timeout: NodeJS.Timeout | null = null

  return (...args: Parameters<T>) => {
    if (timeout) {
      clearTimeout(timeout)
    }

    timeout = setTimeout(() => {
      func(...args)
    }, wait)
  }
}

export const throttle = <T extends (...args: any[]) => any>(
  func: T,
  limit: number
): ((...args: Parameters<T>) => void) => {
  let inThrottle: boolean = false

  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args)
      inThrottle = true
      setTimeout(() => {
        inThrottle = false
      }, limit)
    }
  }
}

export const deepClone = <T>(obj: T): T => {
  if (obj === null || typeof obj !== 'object') {
    return obj
  }

  if (obj instanceof Date) {
    return new Date(obj.getTime()) as T
  }

  if (obj instanceof Array) {
    return obj.map((item) => deepClone(item)) as T
  }

  if (typeof obj === 'object') {
    const cloned = {} as T
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        cloned[key] = deepClone(obj[key])
      }
    }
    return cloned
  }

  return obj
}

export const isEqual = (a: any, b: any): boolean => {
  if (a === b) return true

  if (a === null || b === null) return false

  if (Array.isArray(a) && Array.isArray(b)) {
    if (a.length !== b.length) return false
    for (let i = 0; i < a.length; i++) {
      if (!isEqual(a[i], b[i])) return false
    }
    return true
  }

  if (typeof a === 'object' && typeof b === 'object') {
    const keysA = Object.keys(a)
    const keysB = Object.keys(b)

    if (keysA.length !== keysB.length) return false

    for (const key of keysA) {
      if (!keysB.includes(key)) return false
      if (!isEqual(a[key], b[key])) return false
    }

    return true
  }

  return false
}

export const omit = <T extends Record<string, any>, K extends keyof T>(
  obj: T,
  keys: K[]
): Omit<T, K> => {
  const result = { ...obj }
  keys.forEach((key) => {
    delete result[key]
  })
  return result
}

export const pick = <T extends Record<string, any>, K extends keyof T>(
  obj: T,
  keys: K[]
): Pick<T, K> => {
  const result = {} as Pick<T, K>
  keys.forEach((key) => {
    if (key in obj) {
      result[key] = obj[key]
    }
  })
  return result
}

export const groupBy = <T, K extends string | number | symbol>(
  array: T[],
  keyFn: (item: T) => K
): Record<K, T[]> => {
  return array.reduce(
    (groups, item) => {
      const key = keyFn(item)
      if (!groups[key]) {
        groups[key] = []
      }
      groups[key].push(item)
      return groups
    },
    {} as Record<K, T[]>
  )
}

export const sortBy = <T>(
  array: T[],
  keyFn: (item: T) => any,
  direction: 'asc' | 'desc' = 'asc'
): T[] => {
  return [...array].sort((a, b) => {
    const aVal = keyFn(a)
    const bVal = keyFn(b)

    if (aVal < bVal) return direction === 'asc' ? -1 : 1
    if (aVal > bVal) return direction === 'asc' ? 1 : -1
    return 0
  })
}

export const unique = <T>(array: T[], keyFn?: (item: T) => any): T[] => {
  if (!keyFn) {
    return [...new Set(array)]
  }

  const seen = new Set()
  return array.filter((item) => {
    const key = keyFn(item)
    if (seen.has(key)) {
      return false
    }
    seen.add(key)
    return true
  })
}

export const chunk = <T>(array: T[], size: number): T[][] => {
  const chunks: T[][] = []
  for (let i = 0; i < array.length; i += size) {
    chunks.push(array.slice(i, i + size))
  }
  return chunks
}

export const flatten = <T>(array: (T | T[])[]): T[] => {
  return array.reduce<T[]>((flat, item) => {
    return flat.concat(Array.isArray(item) ? flatten(item) : item)
  }, [])
}
