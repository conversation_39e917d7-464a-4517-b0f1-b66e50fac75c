# tests/api/conftest.py - Fixtures for API layer tests
import uuid
from typing import Dict, Any, List
from decimal import Decimal
from unittest.mock import Mock, MagicMock
from datetime import datetime
from fastapi import Request, Response
from fastapi.testclient import TestClient
import pytest

from src.core.services.general.user_service import UserService
from src.core.enums.project_management_enums import UserRole
from src.core.schemas.general.user_schemas import UserCreateSchema

from src.core.enums.electrical_enums import ComponentCategoryType, ComponentType
from src.core.schemas.general.component_schemas import (
    ComponentAdvancedSearchResponseSchema,
    ComponentReadSchema,
    ComponentSearchResultSchema,
    ComponentDimensionsSchema,
)
from src.core.schemas.base import PaginationSchema

@pytest.fixture
def mock_app():
    """Create a mock ASGI app for testing."""
    return Mock()


@pytest.fixture
def mock_request():
    """Create a mock request object with proper attributes."""
    request = Mock(spec=Request)
    request.method = "GET"
    request.url = Mock()
    request.url.path = "/api/v1/test"
    request.url.query = ""
    request.query_params = {}
    request.headers = {}
    request.client.host = "127.0.0.1"
    return request


@pytest.fixture
def mock_response():
    """Create a mock response object."""
    response = Mock(spec=Response)
    response.status_code = 200
    response.headers = {}
    response.body = b'{"result": "success"}'
    return response


@pytest.fixture
def mock_call_next():
    """Create a mock call_next function that returns a response."""

    async def call_next(request):
        # Directly create a mock response within the fixture
        response = Mock(spec=Response)
        response.status_code = 200
        response.headers = {}
        # Add a default body that can be overridden in tests if needed
        response.body = b'{"message": "test"}'
        return response

    return call_next

@pytest.fixture
def mock_advanced_search_request() -> Dict[str, Any]:
    """Sample advanced search request data."""
    return {
        "search_term": "circuit breaker",
        "fuzzy_search": False,
        "basic_filters": [
            {
                "field": "manufacturer",
                "operator": "eq",
                "value": "Schneider Electric",
                "logical_operator": "and"
            }
        ],
        "specification_filters": [
            {
                "path": "electrical.voltage_rating",
                "operator": "gte",
                "value": 120,
                "data_type": "number",
                "logical_operator": "and"
            }
        ],
        "range_filters": [
            {
                "field": "unit_price",
                "min_value": 10.0,
                "max_value": 100.0,
                "include_min": True,
                "include_max": True
            }
        ],
        "sort_by": "name",
        "sort_order": "asc",
        "include_inactive": False
    }

@pytest.fixture
def mock_search_response() -> ComponentAdvancedSearchResponseSchema:
    """Sample search response data."""
    component_schema = ComponentReadSchema(
        id=1,
        manufacturer="Schneider Electric",
        model_number="QO120",
        name="Circuit Breaker 20A",
        description="Single pole circuit breaker",
        component_category_id=1,
        component_type_id=1,
        category=ComponentCategoryType.PROTECTION_DEVICES,
        component_type=ComponentType.CIRCUIT_BREAKER,
        unit_price=Decimal("25.99"),
        currency="USD",
        supplier="Test Supplier",
        part_number="QO120-TS",
        weight_kg=0.5,
        dimensions=ComponentDimensionsSchema(length=10, width=20, height=30, diameter=None, unit="mm"),
        specifications={
            "electrical": {
                "voltage_rating": 120,
                "current_rating": 20
            }
        },
        is_active=True,
        is_preferred=False,
        stock_status="in_stock",
        version="1.0",
        metadata={},
        full_name="Schneider Electric QO120",
        display_name="Circuit Breaker 20A (QO120)",
        created_at=datetime.now(),
        updated_at=datetime.now()
    )
    
    search_result = ComponentSearchResultSchema(
        component=component_schema,
        relevance_score=0.95,
        matched_fields=["name", "manufacturer"]
    )
    
    return ComponentAdvancedSearchResponseSchema(
        items=[search_result],
        pagination=PaginationSchema(
            page=1,
            size=20,
            total=1,
            pages=1
        ),
        search_metadata={
            "query_time": "< 1ms",
            "total_filters_applied": 3,
            "search_type": "advanced_builder",
            "fuzzy_search_enabled": False
        },
        suggestions=[]
    )

@pytest.fixture
def mock_bulk_create_request() -> List[Dict[str, Any]]:
    """Sample bulk create request data."""
    return [
        {
            "manufacturer": "Schneider Electric",
            "model_number": "QO120",
            "name": "Circuit Breaker 20A",
            "description": "Single pole circuit breaker",
            "category": "PROTECTION",
            "component_type": "CIRCUIT_BREAKER",
            "unit_price": 25.99,
            "specifications": {
                "electrical": {
                    "voltage_rating": 120,
                    "current_rating": 20
                }
            }
        },
        {
            "manufacturer": "ABB",
            "model_number": "S201-B16",
            "name": "MCB 16A Type B",
            "description": "Miniature circuit breaker",
            "category": "PROTECTION",
            "component_type": "CIRCUIT_BREAKER",
            "unit_price": 18.50,
            "specifications": {
                "electrical": {
                    "voltage_rating": 230,
                    "current_rating": 16
                }
            }
        }
    ]

@pytest.fixture
def mock_bulk_update_request() -> List[Dict[str, Any]]:
    """Sample bulk update request data."""
    return [
        {
            "id": 1,
            "name": "Updated Circuit Breaker 20A",
            "unit_price": 28.99,
            "specifications": {
                "electrical": {
                    "voltage_rating": 120,
                    "current_rating": 20
                }
            }
        },
        {
            "id": 2,
            "name": "Updated MCB 16A Type B",
            "unit_price": 19.50,
            "specifications": {
                "electrical": {
                    "voltage_rating": 230,
                    "current_rating": 16
                }
            }
        }
    ]

@pytest.fixture
def mock_component_service():
    """Create a mock component service for testing."""
    service = MagicMock()
    service.create_component.return_value = Mock()
    service.get_component.return_value = Mock()
    service.update_component.return_value = Mock()
    service.delete_component.return_value = True
    service.search_components.return_value = Mock()
    service.get_components_by_category.return_value = []
    service.get_components_by_type.return_value = []
    service.get_preferred_components.return_value = []
    service.get_component_stats.return_value = Mock()
    service.search_components_with_builder.return_value = Mock()
    service.search_components_with_relevance.return_value = Mock()
    service.bulk_create_with_validation.return_value = Mock()
    service.bulk_update_selective.return_value = Mock()
    service.bulk_delete_components.return_value = Mock()
    service.get_performance_metrics.return_value = Mock()
    service.optimize_system_performance.return_value = Mock()
    service.invalidate_component_cache.return_value = Mock()
    return service

@pytest.fixture
def mock_auth_user() -> Dict[str, Any]:
    """Create a mock authenticated user for testing."""
    return {
        "id": 1,
        "username": "testuser",
        "email": "<EMAIL>",
        "is_active": True,
        "is_admin": False,
        "roles": ["user"]
    }

@pytest.fixture
def mock_search_result() -> Dict[str, Any]:
    """Create a mock search result for testing."""
    from datetime import datetime

    # Create timestamp for consistency
    now = datetime.now()

    return {
        "items": [
            {
                "id": 1,
                "name": "Test Circuit Breaker",
                "manufacturer": "ABB",
                "model_number": "S203-C16",
                "description": "Test circuit breaker for electrical protection",
                "component_type": "Circuit Breaker",
                "category": "Protection Devices",
                "specifications": {},
                "unit_price": 25.50,
                "currency": "EUR",
                "supplier": "Test Supplier",
                "part_number": "TS-CB-001",
                "weight_kg": 0.5,
                "dimensions": {"length": 100, "width": 50, "height": 75},
                "is_active": True,
                "is_preferred": False,
                "stock_status": "available",
                "version": "1.0",
                "created_at": now.isoformat(),
                "updated_at": now.isoformat()
            },
            {
                "id": 2,
                "name": "Test MCB",
                "manufacturer": "Schneider Electric",
                "model_number": "C60N-C16",
                "description": "Test miniature circuit breaker",
                "component_type": "Circuit Breaker",
                "category": "Protection Devices",
                "specifications": {},
                "unit_price": 22.75,
                "currency": "EUR",
                "supplier": "Test Supplier 2",
                "part_number": "TS-MCB-002",
                "weight_kg": 0.3,
                "dimensions": {"length": 80, "width": 40, "height": 60},
                "is_active": True,
                "is_preferred": True,
                "stock_status": "available",
                "version": "1.0",
                "created_at": now.isoformat(),
                "updated_at": now.isoformat()
            }
        ],
        "total": 2,
        "page": 1,
        "size": 10,
        "pages": 1
    }

@pytest.fixture
def sample_category_id(authenticated_client: TestClient) -> int:
    """Create a sample category and return its ID."""
    category_data = {
        "name": "API Test Category",
        "description": "API test category description",
        "is_active": True,
    }

    response = authenticated_client.post(
        "/api/v1/component-categories/",
        json=category_data,
    )
    return response.json()["id"]

# --------------------------------Admin User Fixtures--------------------------------#

unique_suffix = str(uuid.uuid4())[:8]


@pytest.fixture
def admin_user_data():
    """Create admin user data."""
    return {
        "name": f"Test Admin {unique_suffix}",
        "email": f"admin.{unique_suffix}@example.com",
        "password": "SecurePass123",
        "role": UserRole.ADMIN,
        "is_active": True,
    }


@pytest.fixture
def test_admin_user(db_session, admin_user_data):
    """Create an admin user in the database."""

    user_service = UserService(db_session)

    user_create = UserCreateSchema(**admin_user_data)
    user = user_service.create_user(user_create)
    return user


# --------------------------------Regular User Fixtures--------------------------------#


@pytest.fixture
def test_user_data():
    """Create test user data."""
    return {
        "name": f"Test Viewer {unique_suffix}",
        "email": f"viewer.{unique_suffix}@example.com",
        "password": "SecurePass123",
        "role": UserRole.VIEWER,
        "is_active": True,
    }


@pytest.fixture
def test_user(db_session, test_user_data):
    """Create a test user in the database."""

    # UserService expects a database session, not a repository
    user_service = UserService(db_session)

    user_create = UserCreateSchema(**test_user_data)
    user = user_service.create_user(user_create)
    return user


# --------------------------------Inactive User Fixtures--------------------------------#


@pytest.fixture
def test_inactive_user_data():
    """Create test user data."""
    return {
        "name": f"Test Inactive {unique_suffix}",
        "email": f"inactive.{unique_suffix}@example.com",
        "password": "SecurePass123",
        "role": UserRole.VIEWER,
        "is_active": False,
    }


@pytest.fixture
def test_inactive_user(db_session, test_inactive_user_data):
    """Create a test inactive user in the database."""

    # UserService expects a database session, not a repository
    user_service = UserService(db_session)

    user_create = UserCreateSchema(**test_inactive_user_data)
    user = user_service.create_user(user_create)
    return user


# --------------------------------Authentication Token Fixtures--------------------------------#


@pytest.fixture
def admin_token(client: TestClient, test_admin_user):
    """Get admin authentication token."""
    login_data = {
        "username": test_admin_user.email,
        "password": "SecurePass123",
    }
    response = client.post("/api/v1/auth/login", json=login_data)
    response.raise_for_status()
    return response.json()["access_token"]


@pytest.fixture
def user_token(client: TestClient, test_user):
    """Get regular user authentication token."""
    login_data = {
        "username": test_user.email,
        "password": "SecurePass123",
    }
    response = client.post("/api/v1/auth/login", json=login_data)
    response.raise_for_status()
    return response.json()["access_token"]


# Alias fixtures for backward compatibility
@pytest.fixture
def sample_advanced_search_request(mock_advanced_search_request):
    """Alias for mock_advanced_search_request."""
    return mock_advanced_search_request

@pytest.fixture
def sample_search_response(mock_search_response):
    """Alias for mock_search_response."""
    return mock_search_response

@pytest.fixture
def sample_bulk_create_request(mock_bulk_create_request):
    """Alias for mock_bulk_create_request."""
    return mock_bulk_create_request

@pytest.fixture
def sample_component_data() -> Dict[str, Any]:
    """Sample component data for testing."""
    return {
        "name": "Circuit Breaker 16A",
        "manufacturer": "ABB",
        "model_number": "S203-C16",
        "description": "3-pole miniature circuit breaker, 16A, C-curve",
        "component_type": ComponentType.CIRCUIT_BREAKER.value,
        "category": ComponentCategoryType.PROTECTION_DEVICES.value,
        "specifications": {
            "rated_current": 16,
            "rated_voltage": 230,
            "breaking_capacity": 6000,
            "curve_type": "C",
            "poles": 3
        },
        "unit_price": 125.99,
        "currency": "USD",
        "supplier": "Electrical Supply Co",
        "part_number": "ESC-S203-C16",
        "weight_kg": 0.5,
        "dimensions": {
            "length": 54.0,
            "width": 18.0,
            "height": 85.0,
            "unit": "mm"
        },
        "is_active": True,
        "is_preferred": False,
        "stock_status": "available",
        "version": "1.0"
    }