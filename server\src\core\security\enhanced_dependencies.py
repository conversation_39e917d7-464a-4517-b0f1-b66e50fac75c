# src/core/security/enhanced_dependencies.py
"""Enhanced Security Dependencies.

This module provides enhanced security dependencies that use the unified
security validation system. These dependencies replace the scattered
security validation approaches with a consistent, centralized system.
"""

from typing import Any, Dict, Optional

from fastapi import Depends, HTTPException, Request, status
from fastapi.security import OAuth2<PERSON>asswordBearer
from jose import JWTError, jwt

from src.config.logging_config import logger
from src.config.settings import settings
from src.core.errors.unified_error_handler import handle_security_errors
from src.core.schemas.general.user_schemas import UserReadSchema
from src.core.security.unified_security_validator import (
    SecurityContext,
    SecurityLevel,
    UnifiedSecurityValidator,
    ValidationResult,
    get_unified_security_validator,
    require_admin_privileges,
    require_authentication,
)
from src.core.services.dependencies import get_user_service
from src.core.services.general.user_service import UserService

# OAuth2 scheme for token-based authentication
oauth2_scheme = OAuth2PasswordBearer(
    tokenUrl="/api/v1/auth/token",
    auto_error=False,  # Allow optional authentication
)


async def get_security_context(
    request: Request,
    token: Optional[str] = Depends(oauth2_scheme),
    validator: UnifiedSecurityValidator = Depends(get_unified_security_validator),
    user_service: UserService = Depends(get_user_service),
) -> SecurityContext:
    """Create security context from request and token.

    This dependency extracts security information from the request
    and creates a comprehensive security context for validation.

    Args:
        request: FastAPI request object
        token: Optional JWT token from OAuth2 scheme
        validator: Unified security validator instance
        user_service: User service dependency

    Returns:
        SecurityContext: Comprehensive security context

    """
    user_data = None
    if token:
        try:
            payload = jwt.decode(
                token, settings.SECRET_KEY, algorithms=[settings.JWT_ALGORITHM]
            )
            email = payload.get("sub")
            if email is None or not isinstance(email, str):
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Invalid authentication credentials",
                    headers={"WWW-Authenticate": "Bearer"},
                )
        except JWTError:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid authentication credentials",
                headers={"WWW-Authenticate": "Bearer"},
            )

        user = user_service.user_repo.get_by_email(email)
        if user is None:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid authentication credentials",
                headers={"WWW-Authenticate": "Bearer"},
            )
        user_data = UserReadSchema.from_orm(user).model_dump()

    context = validator.create_security_context(request, user_data)

    logger.debug(
        f"Security context created for {context.endpoint}: authenticated={context.is_authenticated}"
    )
    logger.debug(f"get_security_context returning context: {context}")
    return context  # type: ignore


@handle_security_errors("request_security_validation")
async def validate_request_security(
    request: Request,
    context: SecurityContext = Depends(get_security_context),
    validator: UnifiedSecurityValidator = Depends(get_unified_security_validator),
    level: SecurityLevel = SecurityLevel.STANDARD,
) -> SecurityContext:
    """Validate request security using unified validation system.

    This dependency performs comprehensive security validation
    including authentication, authorization, and input validation.

    Args:
        request: FastAPI request object
        context: Security context
        validator: Unified security validator instance
        level: Security validation level

    Returns:
        SecurityContext: Validated security context

    Raises:
        HTTPException: If security validation fails

    """
    # Get request body for validation
    body = await request.body()
    data = None
    if body:
        import json

        try:
            data = json.loads(body)
        except json.JSONDecodeError:
            # Not JSON data, skip data validation
            pass

    # Perform comprehensive security validation
    result = validator.validate_comprehensive(data, context, level)

    if not result.is_valid():
        logger.warning(f"Security validation failed: {result.message}")

        # Map validation results to appropriate HTTP status codes
        status_code = status.HTTP_400_BAD_REQUEST
        if result.result == ValidationResult.BLOCKED:
            status_code = status.HTTP_403_FORBIDDEN
        elif "authentication" in result.message.lower():
            status_code = status.HTTP_401_UNAUTHORIZED
        elif "authorization" in result.message.lower():
            status_code = status.HTTP_403_FORBIDDEN
        elif "rate limit" in result.message.lower():
            status_code = status.HTTP_429_TOO_MANY_REQUESTS

        raise HTTPException(
            status_code=status_code,
            detail=result.message,
            headers={"X-Security-Validation": "failed"},
        )

    logger.debug(f"Security validation passed for {context.endpoint}")
    return context


async def get_current_user(
    context: SecurityContext = Depends(get_security_context),
) -> Dict[str, Any]:
    """Get current authenticated user.

    This dependency ensures the user is authenticated and returns
    user information from the security context.

    Args:
        context: Security context

    Returns:
        Dict containing user information

    Raises:
        HTTPException: If authentication is required but not provided

    """
    logger.debug("=== get_current_user ENTRY ===")
    try:
        logger.debug(f"get_current_user called with context: authenticated={context.is_authenticated}, user_id={context.user_id}, user_roles={context.user_roles}, is_admin={context.is_admin}")

        logger.debug("About to call require_authentication")
        require_authentication(context)
        logger.debug("require_authentication completed successfully")

        logger.debug(f"Authentication check passed, building user dict")

        user_dict = {
            "id": context.user_id,
            "roles": context.user_roles or [],
            "is_authenticated": context.is_authenticated,
            "is_admin": context.is_admin,
        }

        logger.debug(f"get_current_user returning user dict: {user_dict}")
        logger.debug("=== get_current_user SUCCESS EXIT ===")
        return user_dict
    except Exception as e:
        logger.error(f"=== get_current_user EXCEPTION: {e} ===", exc_info=True)
        logger.debug("=== get_current_user ERROR EXIT ===")
        raise


@handle_security_errors("admin_user_retrieval")
async def get_admin_user(
    context: SecurityContext = Depends(get_security_context),
) -> Dict[str, Any]:
    """Get current authenticated admin user.

    This dependency ensures the user is authenticated and has
    admin privileges.

    Args:
        context: Security context

    Returns:
        Dict containing admin user information

    Raises:
        HTTPException: If admin privileges are required but not provided

    """
    require_admin_privileges(context)

    return {
        "id": context.user_id,
        "roles": context.user_roles or [],
        "is_authenticated": context.is_authenticated,
        "is_admin": context.is_admin,
    }


@handle_security_errors("active_user_retrieval")
async def get_active_user(
    current_user: Dict[str, Any] = Depends(get_current_user),
) -> Dict[str, Any]:
    """Get current active user.

    This dependency ensures the user is authenticated and active.

    Args:
        current_user: Current authenticated user

    Returns:
        Dict containing active user information

    Raises:
        HTTPException: If user account is not active

    """
    # In a real implementation, you would check user.is_active from database
    # For now, we assume all authenticated users are active
    if not current_user.get("is_authenticated", False):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN, detail="Active user account required"
        )

    return current_user


# Security level dependencies for different validation levels
@handle_security_errors("basic_security_context")
async def get_basic_security_context(
    request: Request,
    context: SecurityContext = Depends(get_security_context),
    validator: UnifiedSecurityValidator = Depends(get_unified_security_validator),
) -> SecurityContext:
    """Security validation with BASIC level."""
    return await validate_request_security(  # type: ignore
        request, context, validator, SecurityLevel.BASIC
    )


@handle_security_errors("standard_security_context")
async def get_standard_security_context(
    request: Request,
    context: SecurityContext = Depends(get_security_context),
    validator: UnifiedSecurityValidator = Depends(get_unified_security_validator),
) -> SecurityContext:
    """Security validation with STANDARD level."""
    return await validate_request_security(  # type: ignore
        request, context, validator, SecurityLevel.STANDARD
    )


@handle_security_errors("strict_security_context")
async def get_strict_security_context(
    request: Request,
    context: SecurityContext = Depends(get_security_context),
    validator: UnifiedSecurityValidator = Depends(get_unified_security_validator),
) -> SecurityContext:
    """Security validation with STRICT level."""
    return await validate_request_security(  # type: ignore
        request, context, validator, SecurityLevel.STRICT
    )


# Convenience dependencies for common use cases
async def require_authenticated_user(
    current_user: Dict[str, Any] = Depends(get_current_user),
) -> Dict[str, Any]:
    """Convenience dependency that requires authentication."""
    logger.debug("=== require_authenticated_user ENTRY ===")
    try:
        logger.debug(f"require_authenticated_user called, current_user: {current_user}")
        logger.debug(f"require_authenticated_user returning: {current_user}")
        logger.debug("=== require_authenticated_user SUCCESS EXIT ===")
        return current_user
    except Exception as e:
        logger.error(f"=== require_authenticated_user EXCEPTION: {e} ===", exc_info=True)
        logger.debug("=== require_authenticated_user ERROR EXIT ===")
        raise


@handle_security_errors("require_admin_user")
async def require_admin_user(
    admin_user: Dict[str, Any] = Depends(get_admin_user),
) -> Dict[str, Any]:
    """Convenience dependency that requires admin privileges."""
    return admin_user


# Optional authentication dependency
@handle_security_errors("optional_user_retrieval")
async def get_optional_user(
    context: SecurityContext = Depends(get_security_context),
) -> Optional[Dict[str, Any]]:
    """Get current user if authenticated, None otherwise.

    This dependency allows optional authentication - it returns
    user information if available but doesn't require authentication.

    Args:
        context: Security context

    Returns:
        Dict containing user information if authenticated, None otherwise

    """
    if not context.is_authenticated:
        return None

    return {
        "id": context.user_id,
        "roles": context.user_roles or [],
        "is_authenticated": context.is_authenticated,
        "is_admin": context.is_admin,
    }
