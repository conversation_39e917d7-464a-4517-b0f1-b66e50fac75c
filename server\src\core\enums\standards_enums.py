# src/core/enums/standards_enums.py
"""This module defines enumeration types related to engineering standards,
regulations, and compliance aspects relevant to electrical design,
particularly focusing on hazardous area classifications.
It includes classifications for temperature classes, industry standards bodies,
compliance levels, and ATEX directives (Zones, Gas Groups, Protection Concepts).
These enums are essential for ensuring design adherence to international and local
regulatory requirements.
"""

from enum import Enum


class TemperatureClass(Enum):
    """Temperature classes for hazardous area equipment as per IEC/NEC standards."""

    T1 = "T1 (<450°C)"
    T2 = "T2 (<300°C)"
    T3 = "T3 (<200°C)"
    T4 = "T4 (<135°C)"
    T5 = "T5 (<100°C)"
    T6 = "T6 (<85°C)"
    NA = "Not Applicable"  # For non-hazardous area equipment


class EngineeringStandard(Enum):
    """Relevant engineering standards and codes."""

    IEC = "International Electrotechnical Commission (IEC)"
    IEEE = "Institute of Electrical and Electronics Engineers (IEEE)"
    EN = "European Norms (EN)"
    ATEX = "ATEX Directives (EU)"
    SFS = "Finnish Standard Association (SFS)"


class ComplianceLevel(Enum):
    """Status of compliance with a standard or regulation."""

    COMPLIANT = "Compliant"
    NON_COMPLIANT = "Non-Compliant"
    WARNING = "Warning (Partial Compliance / Deviation)"
    NOT_APPLICABLE = "Not Applicable"
    UNKNOWN = "Unknown"  # Not yet assessed or data missing
    PENDING_REVIEW = "Pending Review"  # New: for review by checker


class ATEXZone(Enum):
    """ATEX classifications for explosive atmospheres (gas/vapor/mist)."""

    ZONE_0 = "Zone 0 (Gas: continuous/long periods)"
    ZONE_1 = "Zone 1 (Gas: likely during normal operation)"
    ZONE_2 = "Zone 2 (Gas: unlikely/short periods)"
    ZONE_20 = "Zone 20 (Dust: continuous/long periods)"  # Dust classifications
    ZONE_21 = "Zone 21 (Dust: likely during normal operation)"
    ZONE_22 = "Zone 22 (Dust: unlikely/short periods)"
    NON_HAZARDOUS = "Non-Hazardous Area"  # For clarity


class ATEXGasGroup(Enum):
    """ATEX gas groups based on explosion characteristics."""

    IIA = "Group IIA (e.g., Propane)"
    IIB = "Group IIB (e.g., Ethylene)"
    IIC = "Group IIC (e.g., Hydrogen, Acetylene)"
    NA = "Not Applicable"


class ATEXProtectionConcept(Enum):
    """Common ATEX protection concepts for equipment."""

    EX_D = "Ex 'd' - Flameproof Enclosure"
    EX_E = "Ex 'e' - Increased Safety"
    EX_I = "Ex 'i' - Intrinsic Safety"
    EX_P = "Ex 'p' - Pressurized Enclosure"
    EX_M = "Ex 'm' - Encapsulation"
    EX_O = "Ex 'o' - Oil Immersion"
    EX_Q = "Ex 'q' - Powder Filling"
    EX_N = "Ex 'n' - Non-Sparking (Zone 2/22 only)"
    EX_T = "Ex 't' - Protection by Enclosure (Dust)"  # For dust
    NA = "Not Applicable"
