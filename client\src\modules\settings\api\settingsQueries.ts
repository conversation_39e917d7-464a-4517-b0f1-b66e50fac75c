/**
 * React Query hooks for the Settings module
 * Ultimate Electrical Designer - Settings & User Preferences
 */

import { toast } from '@/hooks/useToast'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import React from 'react'
import type { SettingsImport, UserPreferences, UserPreferencesUpdate } from '../types'
import { SettingsMutationKeys, SettingsQueryKeys } from '../types'
import { settingsApi } from './settingsApi'

/**
 * Hook for getting user preferences
 */
export function useUserPreferences() {
  return useQuery({
    queryKey: SettingsQueryKeys.preferences(),
    queryFn: async () => {
      const result = await settingsApi.getUserPreferences()
      if (!result.success) {
        throw new Error(result.error?.message || 'Failed to fetch preferences')
      }
      return result.data!
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    retry: (failureCount, error) => {
      // Don't retry on validation errors
      if (error.message.includes('validation') || error.message.includes('Invalid')) {
        return false
      }
      return failureCount < 3
    },
  })
}

/**
 * Hook for updating user preferences
 */
export function useUpdateUserPreferences() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationKey: [SettingsMutationKeys.updatePreferences],
    mutationFn: async (preferences: UserPreferencesUpdate) => {
      // Validate business rules before sending
      const validation = settingsApi.validateBusinessRules(preferences)
      if (!validation.isValid) {
        throw new Error(`Validation failed: ${validation.errors.join(', ')}`)
      }

      const result = await settingsApi.updateUserPreferences(preferences)
      if (!result.success) {
        throw new Error(result.error?.message || 'Failed to update preferences')
      }
      return result.data!
    },
    onMutate: async (newPreferences) => {
      // Cancel any outgoing refetches
      await queryClient.cancelQueries({ queryKey: SettingsQueryKeys.preferences() })

      // Snapshot the previous value
      const previousPreferences = queryClient.getQueryData<UserPreferences>(
        SettingsQueryKeys.preferences()
      )

      // Optimistically update to the new value
      if (previousPreferences) {
        queryClient.setQueryData<UserPreferences>(SettingsQueryKeys.preferences(), {
          ...previousPreferences,
          ...newPreferences,
        })
      }

      // Return a context object with the snapshotted value
      return { previousPreferences }
    },
    onError: (error, newPreferences, context) => {
      // If the mutation fails, use the context returned from onMutate to roll back
      if (context?.previousPreferences) {
        queryClient.setQueryData(SettingsQueryKeys.preferences(), context.previousPreferences)
      }

      toast({
        title: 'Error',
        description: error.message || 'Failed to update preferences',
        variant: 'destructive',
      })
    },
    onSuccess: (data) => {
      // Invalidate and refetch preferences
      queryClient.invalidateQueries({ queryKey: SettingsQueryKeys.preferences() })

      toast({
        title: 'Success',
        description: 'Preferences updated successfully',
        variant: 'default',
      })
    },
    onSettled: () => {
      // Always refetch after error or success
      queryClient.invalidateQueries({ queryKey: SettingsQueryKeys.preferences() })
    },
  })
}

/**
 * Hook for resetting user preferences
 */
export function useResetUserPreferences() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationKey: [SettingsMutationKeys.resetPreferences],
    mutationFn: async () => {
      const result = await settingsApi.resetUserPreferences()
      if (!result.success) {
        throw new Error(result.error?.message || 'Failed to reset preferences')
      }
      return result.data!
    },
    onSuccess: () => {
      // Invalidate and refetch preferences to get the defaults
      queryClient.invalidateQueries({ queryKey: SettingsQueryKeys.preferences() })

      toast({
        title: 'Success',
        description: 'Preferences reset to defaults successfully',
        variant: 'default',
      })
    },
    onError: (error) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to reset preferences',
        variant: 'destructive',
      })
    },
  })
}

/**
 * Hook for exporting user preferences
 */
export function useExportUserPreferences() {
  return useMutation({
    mutationFn: async () => {
      const result = await settingsApi.exportUserPreferences()
      if (!result.success) {
        throw new Error(result.error?.message || 'Failed to export preferences')
      }
      return result.data!
    },
    onSuccess: (exportData) => {
      // Automatically download the export file
      settingsApi.downloadExportFile(exportData)

      toast({
        title: 'Success',
        description: 'Preferences exported successfully',
        variant: 'default',
      })
    },
    onError: (error) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to export preferences',
        variant: 'destructive',
      })
    },
  })
}

/**
 * Hook for importing user preferences
 */
export function useImportUserPreferences() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationKey: [SettingsMutationKeys.importPreferences],
    mutationFn: async (importData: SettingsImport) => {
      const result = await settingsApi.importUserPreferences(importData)
      if (!result.success) {
        throw new Error(result.error?.message || 'Failed to import preferences')
      }
      return result.data!
    },
    onSuccess: () => {
      // Invalidate and refetch preferences
      queryClient.invalidateQueries({ queryKey: SettingsQueryKeys.preferences() })

      toast({
        title: 'Success',
        description: 'Preferences imported successfully',
        variant: 'default',
      })
    },
    onError: (error) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to import preferences',
        variant: 'destructive',
      })
    },
  })
}

/**
 * Hook for validating import file
 */
export function useValidateImportFile() {
  return useMutation({
    mutationFn: async (file: File) => {
      const result = await settingsApi.validateImportFile(file)
      if (!result.success) {
        throw new Error(result.error?.message || 'Invalid import file')
      }
      return result.data!
    },
    onError: (error) => {
      toast({
        title: 'Invalid File',
        description: error.message || 'The selected file is not a valid settings export',
        variant: 'destructive',
      })
    },
  })
}

/**
 * Hook for batch updating multiple preference fields
 */
export function useBatchUpdatePreferences() {
  const updateMutation = useUpdateUserPreferences()

  return useMutation({
    mutationFn: async (updates: UserPreferencesUpdate[]) => {
      // Merge all updates into a single object
      const mergedUpdates = updates.reduce((acc, update) => ({ ...acc, ...update }), {})

      return updateMutation.mutateAsync(mergedUpdates)
    },
    onSuccess: () => {
      toast({
        title: 'Success',
        description: 'All preferences updated successfully',
        variant: 'default',
      })
    },
    onError: (error) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to update preferences',
        variant: 'destructive',
      })
    },
  })
}

/**
 * Hook for getting preferences with defaults merged
 */
export function useUserPreferencesWithDefaults() {
  const { data: preferences, ...queryResult } = useUserPreferences()

  // Import defaults here to avoid circular dependency
  const DEFAULT_PREFERENCES: UserPreferences = {
    theme: 'system',
    language: 'en',
    timezone: 'UTC',
    date_format: 'YYYY-MM-DD',
    time_format: '24h',
    units_system: 'metric',
    notifications_enabled: true,
    email_notifications: true,
    auto_save_interval: 300,
    dashboard_layout: 'default',
    calculation_precision: 2,
    auto_save_enabled: true,
  }

  const preferencesWithDefaults = preferences
    ? settingsApi.mergeWithDefaults(preferences, DEFAULT_PREFERENCES)
    : DEFAULT_PREFERENCES

  return {
    ...queryResult,
    data: preferencesWithDefaults,
  }
}

/**
 * Hook for checking if preferences have unsaved changes
 */
export function usePreferencesChanges(formData: Partial<UserPreferences>) {
  const { data: currentPreferences } = useUserPreferences()

  if (!currentPreferences) {
    return { hasChanges: false, changes: {} }
  }

  const hasChanges = settingsApi.comparePreferences(currentPreferences, formData)
  const changes = settingsApi.getPreferencesDiff(currentPreferences, formData)

  return { hasChanges, changes }
}

/**
 * Hook for auto-saving preferences with debouncing
 */
export function useAutoSavePreferences(
  formData: Partial<UserPreferences>,
  enabled: boolean = true,
  debounceMs: number = 2000
) {
  const updateMutation = useUpdateUserPreferences()
  const { hasChanges } = usePreferencesChanges(formData)

  // Use a ref to store the timeout
  const timeoutRef = React.useRef<NodeJS.Timeout>()

  React.useEffect(() => {
    if (!enabled || !hasChanges || Object.keys(formData).length === 0) {
      return
    }

    // Clear existing timeout
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current)
    }

    // Set new timeout for auto-save
    timeoutRef.current = setTimeout(() => {
      updateMutation.mutate(formData)
    }, debounceMs)

    // Cleanup timeout on unmount
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
      }
    }
  }, [formData, hasChanges, enabled, debounceMs, updateMutation])

  return {
    isAutoSaving: updateMutation.isPending,
    autoSaveError: updateMutation.error,
  }
}

// Re-export query keys for external use
export { SettingsMutationKeys, SettingsQueryKeys }
