/**
 * Audit logs widget for admin dashboard
 */

'use client'

import { useState } from 'react'
import { AuditLogsWidgetProps } from '../types'
import { formatAuditAction, formatRelativeTime, getAuditActionColor } from '../utils'

export function AuditLogsWidget({
  logs,
  isLoading,
  onViewDetails,
  onExportLogs,
  onFilterChange,
  className = '',
}: AuditLogsWidgetProps) {
  const [filter, setFilter] = useState<
    'all' | 'login' | 'create' | 'update' | 'delete' | 'admin_action'
  >('all')
  const [showFilters, setShowFilters] = useState(false)

  // Filter logs based on selected filter
  const filteredLogs = filter === 'all' ? logs : logs.filter((log) => log.action === filter)

  // Sort logs by timestamp (most recent first)
  const sortedLogs = [...filteredLogs].sort(
    (a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
  )

  // Calculate statistics
  const stats = {
    total: logs.length,
    successful: logs.filter((log) => log.success).length,
    failed: logs.filter((log) => !log.success).length,
    today: logs.filter((log) => {
      const today = new Date()
      const logDate = new Date(log.timestamp)
      return logDate.toDateString() === today.toDateString()
    }).length,
  }

  if (isLoading) {
    return (
      <div className={`overflow-hidden rounded-lg bg-white shadow ${className}`}>
        <div className="p-6">
          <div className="animate-pulse">
            <div className="mb-4 h-6 w-1/2 rounded bg-gray-200"></div>
            <div className="space-y-3">
              {Array.from({ length: 8 }).map((_, i) => (
                <div key={i} className="flex items-center space-x-3">
                  <div className="h-4 w-1/4 rounded bg-gray-200"></div>
                  <div className="h-4 w-1/3 rounded bg-gray-200"></div>
                  <div className="h-4 w-1/5 rounded bg-gray-200"></div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className={`overflow-hidden rounded-lg bg-white shadow ${className}`}>
      <div className="p-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-medium text-gray-900">Audit Logs</h3>
          <div className="flex items-center space-x-2">
            <button
              onClick={() => setShowFilters(!showFilters)}
              className="inline-flex items-center rounded-md bg-gray-100 px-3 py-2 text-sm font-medium text-gray-700 hover:bg-gray-200"
            >
              <svg className="mr-1 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.414A1 1 0 013 6.707V4z"
                />
              </svg>
              Filters
            </button>
            {onExportLogs && (
              <button
                onClick={onExportLogs}
                className="inline-flex items-center rounded-md bg-blue-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-blue-500"
              >
                <svg className="mr-1 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                  />
                </svg>
                Export
              </button>
            )}
          </div>
        </div>

        {/* Statistics */}
        <div className="mt-6 grid grid-cols-4 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-gray-900">{stats.total}</div>
            <div className="text-sm text-gray-500">Total Logs</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">{stats.successful}</div>
            <div className="text-sm text-gray-500">Successful</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-red-600">{stats.failed}</div>
            <div className="text-sm text-gray-500">Failed</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">{stats.today}</div>
            <div className="text-sm text-gray-500">Today</div>
          </div>
        </div>

        {/* Filters */}
        {showFilters && (
          <div className="mt-6 rounded-lg bg-gray-50 p-4">
            <div className="flex flex-wrap gap-2">
              {[
                { key: 'all', label: 'All Actions' },
                { key: 'login', label: 'Logins' },
                { key: 'create', label: 'Creates' },
                { key: 'update', label: 'Updates' },
                { key: 'delete', label: 'Deletes' },
                { key: 'admin_action', label: 'Admin Actions' },
              ].map((filterOption) => (
                <button
                  key={filterOption.key}
                  onClick={() => setFilter(filterOption.key as any)}
                  className={`rounded-md px-3 py-2 text-sm font-medium ${
                    filter === filterOption.key
                      ? 'bg-blue-600 text-white'
                      : 'bg-white text-gray-700 hover:bg-gray-100'
                  }`}
                >
                  {filterOption.label}
                </button>
              ))}
            </div>
          </div>
        )}

        {/* Logs List */}
        <div className="mt-6">
          <div className="max-h-96 space-y-3 overflow-y-auto">
            {sortedLogs.slice(0, 20).map((log) => (
              <div
                key={log.id}
                className={`flex items-center justify-between rounded-lg border p-3 transition-colors ${
                  log.success
                    ? 'border-gray-200 hover:bg-gray-50'
                    : 'border-red-200 bg-red-50 hover:bg-red-100'
                }`}
              >
                <div className="min-w-0 flex-1">
                  <div className="flex items-center space-x-2">
                    <span
                      className={`inline-flex items-center rounded-full px-2 py-1 text-xs font-medium ${getAuditActionColor(log.action)}`}
                    >
                      {formatAuditAction(log.action)}
                    </span>
                    <span
                      className={`inline-flex items-center rounded-full px-2 py-1 text-xs font-medium ${
                        log.success ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                      }`}
                    >
                      {log.success ? 'Success' : 'Failed'}
                    </span>
                    <span className="text-sm font-medium text-gray-900">{log.userName}</span>
                  </div>

                  <p className="mt-1 text-sm text-gray-600">{log.details}</p>

                  <div className="mt-1 flex items-center space-x-4 text-xs text-gray-500">
                    <span>Resource: {log.resource}</span>
                    <span>IP: {log.ipAddress}</span>
                    <span>{formatRelativeTime(log.timestamp)}</span>
                  </div>

                  {!log.success && log.errorMessage && (
                    <p className="mt-1 text-xs text-red-600">{log.errorMessage}</p>
                  )}
                </div>

                {onViewDetails && (
                  <button
                    onClick={() => onViewDetails(log)}
                    className="ml-4 rounded-md p-1 text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    aria-label="View log details"
                  >
                    <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M9 5l7 7-7 7"
                      />
                    </svg>
                  </button>
                )}
              </div>
            ))}
          </div>

          {sortedLogs.length > 20 && (
            <div className="mt-3 text-center">
              <button className="text-sm text-blue-600 hover:text-blue-500">
                View all {sortedLogs.length} logs
              </button>
            </div>
          )}

          {sortedLogs.length === 0 && (
            <div className="py-6 text-center">
              <svg
                className="mx-auto h-12 w-12 text-gray-400"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                />
              </svg>
              <h3 className="mt-2 text-sm font-medium text-gray-900">No audit logs found</h3>
              <p className="mt-1 text-sm text-gray-500">
                {filter === 'all'
                  ? 'No audit logs have been recorded yet.'
                  : `No logs found for action type "${filter}".`}
              </p>
            </div>
          )}
        </div>

        {/* Recent Activity Summary */}
        <div className="mt-6 rounded-lg bg-gray-50 p-4">
          <h4 className="mb-2 text-sm font-medium text-gray-900">Recent Activity Summary</h4>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-gray-500">Most Active User:</span>
              <span className="ml-2 font-medium text-gray-900">
                {logs.length > 0
                  ? (() => {
                      const userCounts = logs.reduce(
                        (acc, log) => {
                          acc[log.userName] = (acc[log.userName] || 0) + 1
                          return acc
                        },
                        {} as Record<string, number>
                      )
                      const entries = Object.entries(userCounts)
                      return entries.sort(([, a], [, b]) => b - a)[0]?.[0] || 'N/A'
                    })()
                  : 'N/A'}
              </span>
            </div>
            <div>
              <span className="text-gray-500">Most Common Action:</span>
              <span className="ml-2 font-medium text-gray-900">
                {logs.length > 0
                  ? (() => {
                      const actionCounts = logs.reduce(
                        (acc, log) => {
                          acc[log.action] = (acc[log.action] || 0) + 1
                          return acc
                        },
                        {} as Record<string, number>
                      )
                      const entries = Object.entries(actionCounts)
                      const mostCommon = entries.sort(([, a], [, b]) => b - a)[0]?.[0]
                      return mostCommon ? formatAuditAction(mostCommon as any) : 'N/A'
                    })()
                  : 'N/A'}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
