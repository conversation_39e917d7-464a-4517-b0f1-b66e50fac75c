/**
 * Zustand store for admin dashboard client state management
 */

import { create } from 'zustand'
import { devtools } from 'zustand/middleware'
import type {
  AdminDashboardData,
  AdminDashboardMetrics,
  AdminDashboardState,
  AuditLogEntry,
  AuditLogFilters,
  ProjectOversightSummary,
  SecurityAlert,
  UserManagementSummary,
} from '../types'

const initialState = {
  // Data state
  data: null,
  isLoading: false,
  error: null,
  lastUpdated: null,

  // UI state
  selectedUser: null,
  selectedProject: null,
  activeWidget: null,
  isRefreshing: false,
  showUserModal: false,
  showConfigModal: false,

  // Filter and search state
  userFilter: 'all' as const,
  projectFilter: 'all' as const,
  auditLogFilter: {} as AuditLogFilters,
  securityAlertFilter: 'all' as const,
  searchQuery: '',

  // Layout state
  layout: 'grid' as const,
  sidebarCollapsed: false,
}

export const useAdminDashboardStore = create<AdminDashboardState>()(
  devtools(
    (set, get) => ({
      ...initialState,

      // Data actions
      setData: (data: AdminDashboardData) =>
        set(
          {
            data,
            lastUpdated: new Date().toISOString(),
            error: null,
          },
          false,
          'setData'
        ),

      updateMetrics: (metrics: Partial<AdminDashboardMetrics>) =>
        set(
          (state) => ({
            data: state.data
              ? {
                  ...state.data,
                  metrics: { ...state.data.metrics, ...metrics },
                }
              : null,
            lastUpdated: new Date().toISOString(),
          }),
          false,
          'updateMetrics'
        ),

      addUser: (user: UserManagementSummary) =>
        set(
          (state) => ({
            data: state.data
              ? {
                  ...state.data,
                  userSummaries: [...state.data.userSummaries, user],
                }
              : null,
            lastUpdated: new Date().toISOString(),
          }),
          false,
          'addUser'
        ),

      updateUser: (id: string, updates: Partial<UserManagementSummary>) =>
        set(
          (state) => ({
            data: state.data
              ? {
                  ...state.data,
                  userSummaries: state.data.userSummaries.map((user) =>
                    user.id === id ? { ...user, ...updates } : user
                  ),
                }
              : null,
            lastUpdated: new Date().toISOString(),
          }),
          false,
          'updateUser'
        ),

      removeUser: (id: string) =>
        set(
          (state) => ({
            data: state.data
              ? {
                  ...state.data,
                  userSummaries: state.data.userSummaries.filter((user) => user.id !== id),
                }
              : null,
            lastUpdated: new Date().toISOString(),
          }),
          false,
          'removeUser'
        ),

      addProject: (project: ProjectOversightSummary) =>
        set(
          (state) => ({
            data: state.data
              ? {
                  ...state.data,
                  projectSummaries: [...state.data.projectSummaries, project],
                }
              : null,
            lastUpdated: new Date().toISOString(),
          }),
          false,
          'addProject'
        ),

      updateProject: (id: string, updates: Partial<ProjectOversightSummary>) =>
        set(
          (state) => ({
            data: state.data
              ? {
                  ...state.data,
                  projectSummaries: state.data.projectSummaries.map((project) =>
                    project.id === id ? { ...project, ...updates } : project
                  ),
                }
              : null,
            lastUpdated: new Date().toISOString(),
          }),
          false,
          'updateProject'
        ),

      addAuditLog: (log: AuditLogEntry) =>
        set(
          (state) => ({
            data: state.data
              ? {
                  ...state.data,
                  auditLogs: [log, ...state.data.auditLogs].slice(0, 100), // Keep only latest 100
                }
              : null,
            lastUpdated: new Date().toISOString(),
          }),
          false,
          'addAuditLog'
        ),

      addSecurityAlert: (alert: SecurityAlert) =>
        set(
          (state) => ({
            data: state.data
              ? {
                  ...state.data,
                  securityAlerts: [alert, ...state.data.securityAlerts],
                }
              : null,
            lastUpdated: new Date().toISOString(),
          }),
          false,
          'addSecurityAlert'
        ),

      resolveSecurityAlert: (alertId: string, resolvedBy: string) =>
        set(
          (state) => ({
            data: state.data
              ? {
                  ...state.data,
                  securityAlerts: state.data.securityAlerts.map((alert) =>
                    alert.id === alertId
                      ? {
                          ...alert,
                          resolved: true,
                          resolvedBy,
                          resolvedAt: new Date().toISOString(),
                        }
                      : alert
                  ),
                }
              : null,
            lastUpdated: new Date().toISOString(),
          }),
          false,
          'resolveSecurityAlert'
        ),

      updateConfiguration: (id: string, value: string, modifiedBy: string) =>
        set(
          (state) => ({
            data: state.data
              ? {
                  ...state.data,
                  systemConfiguration: state.data.systemConfiguration.map((config) =>
                    config.id === id
                      ? {
                          ...config,
                          value,
                          lastModified: new Date().toISOString(),
                          modifiedBy,
                        }
                      : config
                  ),
                }
              : null,
            lastUpdated: new Date().toISOString(),
          }),
          false,
          'updateConfiguration'
        ),

      // UI state actions
      setLoading: (loading: boolean) => set({ isLoading: loading }, false, 'setLoading'),

      setError: (error: Error | null) => set({ error }, false, 'setError'),

      setSelectedUser: (userId: string | null) =>
        set({ selectedUser: userId }, false, 'setSelectedUser'),

      setSelectedProject: (projectId: string | null) =>
        set({ selectedProject: projectId }, false, 'setSelectedProject'),

      setActiveWidget: (widgetId: string | null) =>
        set({ activeWidget: widgetId }, false, 'setActiveWidget'),

      // Filter actions
      setUserFilter: (filter) => set({ userFilter: filter }, false, 'setUserFilter'),

      setProjectFilter: (filter) => set({ projectFilter: filter }, false, 'setProjectFilter'),

      setAuditLogFilter: (filter: AuditLogFilters) =>
        set({ auditLogFilter: filter }, false, 'setAuditLogFilter'),

      setSecurityAlertFilter: (filter) =>
        set({ securityAlertFilter: filter }, false, 'setSecurityAlertFilter'),

      setSearchQuery: (query: string) => set({ searchQuery: query }, false, 'setSearchQuery'),

      // Layout actions
      setLayout: (layout) => set({ layout }, false, 'setLayout'),

      setSidebarCollapsed: (collapsed: boolean) =>
        set({ sidebarCollapsed: collapsed }, false, 'setSidebarCollapsed'),

      setShowUserModal: (show: boolean) => set({ showUserModal: show }, false, 'setShowUserModal'),

      setShowConfigModal: (show: boolean) =>
        set({ showConfigModal: show }, false, 'setShowConfigModal'),

      // Utility actions
      refreshData: () => set({ isRefreshing: true }, false, 'refreshData'),

      resetState: () => set(initialState, false, 'resetState'),
    }),
    {
      name: 'admin-dashboard-store',
      partialize: (state) => ({
        // Persist only UI preferences, not data
        layout: state.layout,
        sidebarCollapsed: state.sidebarCollapsed,
        userFilter: state.userFilter,
        projectFilter: state.projectFilter,
        securityAlertFilter: state.securityAlertFilter,
      }),
    }
  )
)

// Selectors for computed values
export const useAdminDashboardSelectors = () => {
  const store = useAdminDashboardStore()

  return {
    // Filtered data selectors
    filteredUsers:
      store.data?.userSummaries.filter((user) => {
        if (store.userFilter === 'all') return true
        if (store.userFilter === 'active') return user.isActive
        if (store.userFilter === 'inactive') return !user.isActive
        return user.role === store.userFilter.toUpperCase()
      }) || [],

    filteredProjects:
      store.data?.projectSummaries.filter((project) => {
        if (store.projectFilter === 'all') return true
        return project.status === store.projectFilter
      }) || [],

    filteredSecurityAlerts:
      store.data?.securityAlerts.filter((alert) => {
        if (store.securityAlertFilter === 'all') return true
        if (store.securityAlertFilter === 'unresolved') return !alert.resolved
        return alert.severity === store.securityAlertFilter
      }) || [],

    // Summary statistics
    userStats: {
      total: store.data?.userSummaries.length || 0,
      active: store.data?.userSummaries.filter((u) => u.isActive).length || 0,
      inactive: store.data?.userSummaries.filter((u) => !u.isActive).length || 0,
      admins: store.data?.userSummaries.filter((u) => u.role === 'ADMIN').length || 0,
      editors: store.data?.userSummaries.filter((u) => u.role === 'EDITOR').length || 0,
      viewers: store.data?.userSummaries.filter((u) => u.role === 'VIEWER').length || 0,
    },

    projectStats: {
      total: store.data?.projectSummaries.length || 0,
      active: store.data?.projectSummaries.filter((p) => p.status === 'active').length || 0,
      completed: store.data?.projectSummaries.filter((p) => p.status === 'completed').length || 0,
      onHold: store.data?.projectSummaries.filter((p) => p.status === 'on_hold').length || 0,
      draft: store.data?.projectSummaries.filter((p) => p.status === 'draft').length || 0,
    },

    securityStats: {
      total: store.data?.securityAlerts.length || 0,
      unresolved: store.data?.securityAlerts.filter((a) => !a.resolved).length || 0,
      critical: store.data?.securityAlerts.filter((a) => a.severity === 'critical').length || 0,
      high: store.data?.securityAlerts.filter((a) => a.severity === 'high').length || 0,
      medium: store.data?.securityAlerts.filter((a) => a.severity === 'medium').length || 0,
      low: store.data?.securityAlerts.filter((a) => a.severity === 'low').length || 0,
    },

    // UI state
    hasData: !!store.data,
    isDataStale: store.lastUpdated
      ? Date.now() - new Date(store.lastUpdated).getTime() > 5 * 60 * 1000 // 5 minutes
      : true,
  }
}
