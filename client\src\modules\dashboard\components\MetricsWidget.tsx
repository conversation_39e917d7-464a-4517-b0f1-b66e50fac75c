'use client'

import { cn } from '@/lib/utils'
import { memo } from 'react'
import type { MetricsWidgetProps } from '../types'
import { formatRelativeTime } from '../utils'

/**
 * Dashboard metrics widget component
 */
export const MetricsWidget = memo(function MetricsWidget({
  metrics,
  isLoading = false,
  className,
}: MetricsWidgetProps) {
  if (isLoading) {
    return (
      <div className={cn('grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4', className)}>
        {Array.from({ length: 4 }).map((_, index) => (
          <div
            key={index}
            className="animate-pulse rounded-lg border border-gray-200 bg-white p-6 shadow-sm"
          >
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="h-8 w-8 rounded-lg bg-gray-200"></div>
              </div>
              <div className="ml-5 w-0 flex-1">
                <div className="mb-2 h-4 w-3/4 rounded bg-gray-200"></div>
                <div className="h-6 w-1/2 rounded bg-gray-200"></div>
              </div>
            </div>
          </div>
        ))}
      </div>
    )
  }

  const metricCards = [
    {
      id: 'total-projects',
      title: 'Total Projects',
      value: metrics.totalProjects.toString(),
      icon: (
        <svg
          className="h-6 w-6 text-brand-primary"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"
          />
        </svg>
      ),
      bgColor: 'bg-brand-primary/10',
      textColor: 'text-brand-primary',
    },
    {
      id: 'active-projects',
      title: 'Active Projects',
      value: metrics.activeProjects.toString(),
      icon: (
        <svg
          className="h-6 w-6 text-brand-secondary"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M13 10V3L4 14h7v7l9-11h-7z"
          />
        </svg>
      ),
      bgColor: 'bg-brand-secondary/10',
      textColor: 'text-brand-secondary',
    },
    {
      id: 'completed-calculations',
      title: 'Completed Calculations',
      value: metrics.completedCalculations.toString(),
      icon: (
        <svg
          className="h-6 w-6 text-brand-accent"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z"
          />
        </svg>
      ),
      bgColor: 'bg-brand-accent/10',
      textColor: 'text-brand-accent',
    },
    {
      id: 'recent-activity',
      title: 'Recent Activity',
      value: metrics.recentActivity.toString(),
      subtitle: `Last login ${formatRelativeTime(metrics.lastLogin)}`,
      icon: (
        <svg
          className="h-6 w-6 text-brand-dark"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
          />
        </svg>
      ),
      bgColor: 'bg-brand-dark/10',
      textColor: 'text-brand-dark',
    },
  ]

  return (
    <div className={cn('grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4', className)}>
      {metricCards.map((card) => (
        <div
          key={card.id}
          className="rounded-lg border border-gray-200 bg-white p-6 shadow-sm transition-shadow duration-200 hover:shadow-md"
        >
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div
                className={cn(
                  'flex h-10 w-10 items-center justify-center rounded-lg',
                  card.bgColor
                )}
              >
                {card.icon}
              </div>
            </div>
            <div className="ml-5 w-0 flex-1">
              <dl>
                <dt className="truncate text-sm font-medium text-gray-500">{card.title}</dt>
                <dd className={cn('text-2xl font-semibold', card.textColor)}>{card.value}</dd>
                {card.subtitle && <dd className="mt-1 text-xs text-gray-400">{card.subtitle}</dd>}
              </dl>
            </div>
          </div>
        </div>
      ))}
    </div>
  )
})

MetricsWidget.displayName = 'MetricsWidget'
