# Settings Module Testing Guide

**Ultimate Electrical Designer - Settings & User Preferences**

Comprehensive testing documentation for the Settings module, covering unit tests, integration tests, E2E tests, and accessibility testing.

## Testing Strategy

### Testing Pyramid

```
    E2E Tests (10%)
   ┌─────────────────┐
   │ User Scenarios  │
   │ Accessibility   │
   │ Cross-browser   │
   └─────────────────┘

  Integration Tests (20%)
 ┌───────────────────────┐
 │ Component Integration │
 │ API Integration       │
 │ State Management      │
 └───────────────────────┘

     Unit Tests (70%)
┌─────────────────────────────┐
│ Components, Hooks, Utils    │
│ Stores, Schemas, API        │
│ Business Logic              │
└─────────────────────────────┘
```

### Coverage Targets

- **Unit Tests**: 100% for new code
- **Integration Tests**: 95% of critical workflows
- **E2E Tests**: 90% of user scenarios
- **Overall Coverage**: 95%+ with zero tolerance for critical paths

## Test Setup

### Dependencies

```json
{
  "devDependencies": {
    "@testing-library/react": "^14.0.0",
    "@testing-library/jest-dom": "^6.0.0",
    "@testing-library/user-event": "^14.0.0",
    "@playwright/test": "^1.40.0",
    "vitest": "^1.0.0",
    "jsdom": "^23.0.0",
    "msw": "^2.0.0"
  }
}
```

### Configuration

**Vitest Config (vitest.config.ts)**

```typescript
export default defineConfig({
  test: {
    environment: 'jsdom',
    setupFiles: ['./src/test/setup.ts'],
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json', 'html'],
      exclude: [
        'node_modules/',
        'src/test/',
        '**/*.d.ts',
        '**/*.test.{ts,tsx}',
        '**/*.spec.{ts,tsx}',
      ],
      thresholds: {
        global: {
          branches: 95,
          functions: 95,
          lines: 95,
          statements: 95,
        },
      },
    },
  },
})
```

**Test Setup (src/test/setup.ts)**

```typescript
import '@testing-library/jest-dom'
import { vi } from 'vitest'

// Mock IntersectionObserver
global.IntersectionObserver = vi.fn(() => ({
  observe: vi.fn(),
  disconnect: vi.fn(),
  unobserve: vi.fn(),
}))

// Mock BroadcastChannel
global.BroadcastChannel = vi.fn(() => ({
  postMessage: vi.fn(),
  close: vi.fn(),
  addEventListener: vi.fn(),
  removeEventListener: vi.fn(),
}))

// Mock localStorage
Object.defineProperty(window, 'localStorage', {
  value: {
    getItem: vi.fn(),
    setItem: vi.fn(),
    removeItem: vi.fn(),
    clear: vi.fn(),
  },
})
```

## Unit Testing

### Component Testing

**Testing Pattern**

```typescript
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { SettingsPanel } from '../SettingsPanel'

const TestWrapper = ({ children }: { children: React.ReactNode }) => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  })

  return (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  )
}

describe('SettingsPanel', () => {
  it('should render with default props', () => {
    render(
      <TestWrapper>
        <SettingsPanel />
      </TestWrapper>
    )

    expect(screen.getByText('Settings')).toBeInTheDocument()
  })
})
```

**Component Test Categories**

1. **Rendering Tests** - Component renders correctly
2. **Interaction Tests** - User interactions work as expected
3. **State Tests** - Component state updates correctly
4. **Props Tests** - Props are handled properly
5. **Error Tests** - Error states are handled gracefully

### Hook Testing

**Hook Testing Pattern**

```typescript
import { renderHook, act } from '@testing-library/react'
import { useSettings } from '../useSettings'

describe('useSettings', () => {
  it('should initialize with default state', () => {
    const { result } = renderHook(() => useSettings())

    expect(result.current.hasChanges).toBe(false)
    expect(result.current.formData).toEqual({})
  })

  it('should update form data', () => {
    const { result } = renderHook(() => useSettings())

    act(() => {
      result.current.actions.updateField('theme', 'dark')
    })

    expect(result.current.formData.theme).toBe('dark')
    expect(result.current.hasChanges).toBe(true)
  })
})
```

### Store Testing

**Zustand Store Testing**

```typescript
import { useSettingsStore } from '../settingsStore'

describe('settingsStore', () => {
  beforeEach(() => {
    useSettingsStore.getState().reset()
  })

  it('should update active category', () => {
    const { setActiveCategory } = useSettingsStore.getState()

    act(() => {
      setActiveCategory('appearance')
    })

    expect(useSettingsStore.getState().ui.activeCategory).toBe('appearance')
  })
})
```

### Utility Testing

**Utility Function Testing**

```typescript
import { validatePreferenceField, formatPreferenceValue } from '../utils'

describe('utils', () => {
  describe('validatePreferenceField', () => {
    it('should validate auto_save_interval correctly', () => {
      expect(validatePreferenceField('auto_save_interval', 300).isValid).toBe(true)
      expect(validatePreferenceField('auto_save_interval', 10).isValid).toBe(false)
    })
  })

  describe('formatPreferenceValue', () => {
    it('should format theme values', () => {
      expect(formatPreferenceValue('theme', 'light')).toBe('Light')
      expect(formatPreferenceValue('theme', 'system')).toBe('System Default')
    })
  })
})
```

## Integration Testing

### API Integration

**MSW Setup**

```typescript
import { setupServer } from 'msw/node'
import { http, HttpResponse } from 'msw'

const server = setupServer(
  http.get('/api/v1/users/me/preferences', () => {
    return HttpResponse.json({
      theme: 'light',
      language: 'en',
      // ... other preferences
    })
  }),

  http.put('/api/v1/users/me/preferences', async ({ request }) => {
    const body = await request.json()
    return HttpResponse.json(body)
  })
)

beforeAll(() => server.listen())
afterEach(() => server.resetHandlers())
afterAll(() => server.close())
```

**Integration Test Example**

```typescript
describe('Settings Integration', () => {
  it('should load and update preferences', async () => {
    render(
      <TestWrapper>
        <SettingsPanel />
      </TestWrapper>
    )

    // Wait for preferences to load
    await waitFor(() => {
      expect(screen.getByDisplayValue('light')).toBeInTheDocument()
    })

    // Update theme
    fireEvent.change(screen.getByLabelText('Theme'), {
      target: { value: 'dark' }
    })

    // Save changes
    fireEvent.click(screen.getByText('Save Changes'))

    // Verify API call
    await waitFor(() => {
      expect(screen.getByText('Preferences updated successfully')).toBeInTheDocument()
    })
  })
})
```

### State Management Integration

**Store + Hook Integration**

```typescript
describe('Settings State Integration', () => {
  it('should sync store and hook state', () => {
    const { result } = renderHook(() => useSettings())

    // Update through hook
    act(() => {
      result.current.actions.updateField('theme', 'dark')
    })

    // Verify store is updated
    expect(useSettingsStore.getState().formData.theme).toBe('dark')

    // Update through store
    act(() => {
      useSettingsStore.getState().updateFormData({ language: 'es' })
    })

    // Verify hook reflects change
    expect(result.current.formData.language).toBe('es')
  })
})
```

## E2E Testing

### Playwright Configuration

**playwright.config.ts**

```typescript
export default defineConfig({
  testDir: './tests/e2e',
  fullyParallel: true,
  forbidOnly: !!process.env.CI,
  retries: process.env.CI ? 2 : 0,
  workers: process.env.CI ? 1 : undefined,
  reporter: 'html',
  use: {
    baseURL: 'http://localhost:3000',
    trace: 'on-first-retry',
  },
  projects: [
    {
      name: 'chromium',
      use: { ...devices['Desktop Chrome'] },
    },
    {
      name: 'firefox',
      use: { ...devices['Desktop Firefox'] },
    },
    {
      name: 'webkit',
      use: { ...devices['Desktop Safari'] },
    },
  ],
})
```

### E2E Test Patterns

**Page Object Model**

```typescript
class SettingsPage {
  constructor(private page: Page) {}

  async navigateToCategory(category: string) {
    await this.page.getByRole('tab', { name: category }).click()
  }

  async updateTheme(theme: string) {
    await this.page.getByLabel('Color Theme').selectOption(theme)
  }

  async saveChanges() {
    await this.page.getByRole('button', { name: 'Save Changes' }).click()
  }

  async expectSaveSuccess() {
    await expect(this.page.getByText('Preferences updated successfully')).toBeVisible()
  }
}
```

**User Scenario Tests**

```typescript
test('complete settings workflow', async ({ page }) => {
  const settingsPage = new SettingsPage(page)

  await page.goto('/settings')

  // Navigate to appearance
  await settingsPage.navigateToCategory('Appearance')

  // Update theme
  await settingsPage.updateTheme('dark')

  // Save changes
  await settingsPage.saveChanges()

  // Verify success
  await settingsPage.expectSaveSuccess()
})
```

## Accessibility Testing

### Automated Testing

**axe-core Integration**

```typescript
import { injectAxe, checkA11y } from 'axe-playwright'

test('should be accessible', async ({ page }) => {
  await page.goto('/settings')
  await injectAxe(page)
  await checkA11y(page)
})
```

### Manual Testing Checklist

**Keyboard Navigation**

- [ ] Tab order is logical
- [ ] All interactive elements are focusable
- [ ] Focus indicators are visible
- [ ] Escape key closes dialogs
- [ ] Arrow keys navigate tabs

**Screen Reader Testing**

- [ ] All content is announced
- [ ] Form labels are associated
- [ ] Error messages are announced
- [ ] Status updates are announced
- [ ] Landmarks are properly defined

**Visual Testing**

- [ ] High contrast mode works
- [ ] Zoom to 200% is usable
- [ ] Color is not the only indicator
- [ ] Text meets contrast requirements
- [ ] Focus indicators are visible

## Performance Testing

### Load Testing

**Component Performance**

```typescript
import { performance } from 'perf_hooks'

test('settings panel renders within performance budget', async () => {
  const start = performance.now()

  render(
    <TestWrapper>
      <SettingsPanel />
    </TestWrapper>
  )

  const end = performance.now()
  expect(end - start).toBeLessThan(100) // 100ms budget
})
```

### Memory Testing

**Memory Leak Detection**

```typescript
test('should not leak memory on unmount', () => {
  const { unmount } = render(
    <TestWrapper>
      <SettingsPanel />
    </TestWrapper>
  )

  // Simulate multiple mount/unmount cycles
  for (let i = 0; i < 10; i++) {
    unmount()
    render(
      <TestWrapper>
        <SettingsPanel />
      </TestWrapper>
    )
  }

  // Check for memory leaks (implementation depends on testing environment)
})
```

## Test Data Management

### Test Fixtures

**Mock Data**

```typescript
export const mockUserPreferences: UserPreferences = {
  theme: 'light',
  language: 'en',
  timezone: 'UTC',
  date_format: 'YYYY-MM-DD',
  time_format: '24h',
  units_system: 'metric',
  notifications_enabled: true,
  email_notifications: true,
  auto_save_interval: 300,
  dashboard_layout: 'default',
  calculation_precision: 2,
  auto_save_enabled: true,
}

export const mockSettingsExport: SettingsExport = {
  preferences: mockUserPreferences,
  exported_at: '2025-07-17T10:30:00Z',
  version: '1.0',
}
```

### Test Utilities

**Custom Render Function**

```typescript
export function renderWithProviders(
  ui: React.ReactElement,
  options?: {
    preloadedState?: Partial<SettingsStoreState>
    queryClient?: QueryClient
  }
) {
  const { preloadedState, queryClient = new QueryClient() } = options || {}

  if (preloadedState) {
    useSettingsStore.setState(preloadedState)
  }

  function Wrapper({ children }: { children: React.ReactNode }) {
    return (
      <QueryClientProvider client={queryClient}>
        {children}
      </QueryClientProvider>
    )
  }

  return render(ui, { wrapper: Wrapper })
}
```

## Continuous Integration

### GitHub Actions

```yaml
name: Settings Module Tests

on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'

      - run: npm ci
      - run: npm run test:unit -- --coverage
      - run: npm run test:integration
      - run: npm run test:e2e

      - name: Upload coverage
        uses: codecov/codecov-action@v3
        with:
          file: ./coverage/lcov.info
```

### Quality Gates

**Pre-commit Hooks**

```json
{
  "husky": {
    "hooks": {
      "pre-commit": "lint-staged"
    }
  },
  "lint-staged": {
    "src/modules/settings/**/*.{ts,tsx}": [
      "eslint --fix",
      "prettier --write",
      "npm run test:unit -- --passWithNoTests"
    ]
  }
}
```

## Debugging Tests

### Common Issues

**Async Testing**

```typescript
// ❌ Wrong - not waiting for async operations
test('should update preferences', () => {
  fireEvent.click(saveButton)
  expect(screen.getByText('Success')).toBeInTheDocument()
})

// ✅ Correct - waiting for async operations
test('should update preferences', async () => {
  fireEvent.click(saveButton)
  await waitFor(() => {
    expect(screen.getByText('Success')).toBeInTheDocument()
  })
})
```

**Mock Cleanup**

```typescript
// ❌ Wrong - mocks persist between tests
beforeEach(() => {
  vi.mocked(settingsApi.getUserPreferences).mockResolvedValue(mockData)
})

// ✅ Correct - clean mocks between tests
afterEach(() => {
  vi.clearAllMocks()
})
```

### Test Debugging Tools

**Debug Utilities**

```typescript
import { screen } from '@testing-library/react'

// Debug rendered output
screen.debug()

// Debug specific element
screen.debug(screen.getByRole('button'))

// Log queries
screen.logTestingPlaygroundURL()
```

## Best Practices

### Test Organization

1. **Group related tests** using `describe` blocks
2. **Use descriptive test names** that explain the expected behavior
3. **Follow AAA pattern** (Arrange, Act, Assert)
4. **Keep tests focused** on single behaviors
5. **Use proper cleanup** to prevent test interference

### Mocking Strategy

1. **Mock external dependencies** (APIs, third-party libraries)
2. **Don't mock internal modules** unless necessary
3. **Use realistic mock data** that matches production
4. **Reset mocks between tests** to ensure isolation
5. **Verify mock calls** when testing integrations

### Performance

1. **Minimize test setup** to reduce execution time
2. **Use parallel execution** where possible
3. **Share expensive setup** across related tests
4. **Profile slow tests** and optimize bottlenecks
5. **Use appropriate test levels** (unit vs integration vs E2E)
