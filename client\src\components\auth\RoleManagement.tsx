/**
 * Role Management Component
 * Provides comprehensive role-based access control management interface
 */

import React, { useState } from 'react'
import { useRoles, useCreateRole, useUpdateRole, useDeleteRole, useRoleHierarchy } from '@/hooks/api/useRbac'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Switch } from '@/components/ui/switch'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Label } from '@/components/ui/label'
import { 
  PlusIcon, 
  EditIcon, 
  TrashIcon, 
  RefreshCwIcon, 
  ShieldIcon, 
  UsersIcon,
  HierarchyIcon,
  SettingsIcon
} from 'lucide-react'
import { useToast } from '@/hooks/useToast'
import type { UserRoleModel, UserRoleCreate, UserRoleUpdate } from '@/types/api'

interface RoleManagementProps {
  className?: string
}

export function RoleManagement({ className = '' }: RoleManagementProps) {
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [selectedRole, setSelectedRole] = useState<UserRoleModel | null>(null)
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize] = useState(10)
  const { toast } = useToast()

  const { data: roles, isLoading, error, refetch } = useRoles({
    skip: (currentPage - 1) * pageSize,
    limit: pageSize,
  })
  
  const { data: roleHierarchy, isLoading: isHierarchyLoading } = useRoleHierarchy()
  
  const createRoleMutation = useCreateRole()
  const updateRoleMutation = useUpdateRole()
  const deleteRoleMutation = useDeleteRole()

  const handleCreateRole = async (data: UserRoleCreate) => {
    try {
      await createRoleMutation.mutateAsync(data)
      setIsCreateDialogOpen(false)
      toast({
        title: 'Role Created',
        description: 'Role has been created successfully.',
      })
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to create role. Please try again.',
        variant: 'destructive',
      })
    }
  }

  const handleUpdateRole = async (id: number, data: UserRoleUpdate) => {
    try {
      await updateRoleMutation.mutateAsync({ id, data })
      setIsEditDialogOpen(false)
      setSelectedRole(null)
      toast({
        title: 'Role Updated',
        description: 'Role has been updated successfully.',
      })
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to update role. Please try again.',
        variant: 'destructive',
      })
    }
  }

  const handleDeleteRole = async (id: number) => {
    if (!window.confirm('Are you sure you want to delete this role?')) return
    
    try {
      await deleteRoleMutation.mutateAsync(id)
      toast({
        title: 'Role Deleted',
        description: 'Role has been deleted successfully.',
      })
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to delete role. Please try again.',
        variant: 'destructive',
      })
    }
  }

  const openEditDialog = (role: UserRoleModel) => {
    setSelectedRole(role)
    setIsEditDialogOpen(true)
  }

  if (error) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="text-red-600">Error Loading Roles</CardTitle>
          <CardDescription>
            {error.message || 'Failed to load roles'}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Button onClick={() => refetch()} variant="outline">
            <RefreshCwIcon className="h-4 w-4 mr-2" />
            Retry
          </Button>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <ShieldIcon className="h-5 w-5" />
            <span>Role Management</span>
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => refetch()}
              disabled={isLoading}
            >
              <RefreshCwIcon className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
            </Button>
            <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
              <DialogTrigger asChild>
                <Button size="sm">
                  <PlusIcon className="h-4 w-4 mr-2" />
                  Create Role
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-2xl">
                <DialogHeader>
                  <DialogTitle>Create New Role</DialogTitle>
                </DialogHeader>
                <RoleForm onSubmit={handleCreateRole} />
              </DialogContent>
            </Dialog>
          </div>
        </CardTitle>
        <CardDescription>
          Manage user roles and permissions
        </CardDescription>
      </CardHeader>
      
      <CardContent>
        <Tabs defaultValue="roles" className="w-full">
          <TabsList>
            <TabsTrigger value="roles">Roles</TabsTrigger>
            <TabsTrigger value="hierarchy">Hierarchy</TabsTrigger>
          </TabsList>
          
          <TabsContent value="roles" className="space-y-4">
            <div className="border rounded-lg overflow-hidden">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Name</TableHead>
                    <TableHead>Description</TableHead>
                    <TableHead>Priority</TableHead>
                    <TableHead>Type</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {isLoading ? (
                    <TableRow>
                      <TableCell colSpan={6} className="text-center py-8">
                        <div className="flex items-center justify-center">
                          <RefreshCwIcon className="h-4 w-4 animate-spin mr-2" />
                          Loading roles...
                        </div>
                      </TableCell>
                    </TableRow>
                  ) : roles?.items?.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={6} className="text-center py-8 text-gray-500">
                        No roles found
                      </TableCell>
                    </TableRow>
                  ) : (
                    roles?.items?.map((role) => (
                      <TableRow key={role.id}>
                        <TableCell className="font-medium">{role.name}</TableCell>
                        <TableCell className="max-w-md truncate">
                          {role.description || '-'}
                        </TableCell>
                        <TableCell>
                          <Badge variant="outline">{role.priority}</Badge>
                        </TableCell>
                        <TableCell>
                          <Badge variant={role.is_system_role ? 'default' : 'secondary'}>
                            {role.is_system_role ? 'System' : 'Custom'}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <Badge variant={role.is_active ? 'default' : 'secondary'}>
                            {role.is_active ? 'Active' : 'Inactive'}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center space-x-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => openEditDialog(role)}
                            >
                              <EditIcon className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleDeleteRole(role.id)}
                              disabled={role.is_system_role}
                            >
                              <TrashIcon className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>

            {/* Pagination */}
            {roles && roles.pages > 1 && (
              <div className="mt-4 flex items-center justify-between">
                <div className="text-sm text-gray-600">
                  Showing {((currentPage - 1) * pageSize) + 1} to {Math.min(currentPage * pageSize, roles.total)} of {roles.total} roles
                </div>
                <div className="flex items-center space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                    disabled={currentPage === 1}
                  >
                    Previous
                  </Button>
                  <span className="text-sm">
                    Page {currentPage} of {roles.pages}
                  </span>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setCurrentPage(prev => Math.min(roles.pages, prev + 1))}
                    disabled={currentPage === roles.pages}
                  >
                    Next
                  </Button>
                </div>
              </div>
            )}
          </TabsContent>
          
          <TabsContent value="hierarchy" className="space-y-4">
            {isHierarchyLoading ? (
              <div className="flex items-center justify-center py-8">
                <RefreshCwIcon className="h-4 w-4 animate-spin mr-2" />
                Loading role hierarchy...
              </div>
            ) : (
              <div className="space-y-4">
                <div className="flex items-center space-x-2">
                  <HierarchyIcon className="h-5 w-5" />
                  <span className="text-lg font-medium">Role Hierarchy</span>
                </div>
                {roleHierarchy?.map((role) => (
                  <RoleHierarchyTree key={role.id} role={role} level={0} />
                ))}
              </div>
            )}
          </TabsContent>
        </Tabs>

        {/* Edit Role Dialog */}
        <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>Edit Role</DialogTitle>
            </DialogHeader>
            {selectedRole && (
              <RoleForm
                initialData={selectedRole}
                onSubmit={(data) => handleUpdateRole(selectedRole.id, data)}
              />
            )}
          </DialogContent>
        </Dialog>
      </CardContent>
    </Card>
  )
}

// Role Form Component
interface RoleFormProps {
  initialData?: UserRoleModel
  onSubmit: (data: UserRoleCreate | UserRoleUpdate) => void
}

function RoleForm({ initialData, onSubmit }: RoleFormProps) {
  const [formData, setFormData] = useState({
    name: initialData?.name || '',
    description: initialData?.description || '',
    is_system_role: initialData?.is_system_role || false,
    is_active: initialData?.is_active ?? true,
    permissions: initialData?.permissions || '',
    parent_role_id: initialData?.parent_role_id || undefined,
    priority: initialData?.priority || 0,
    notes: initialData?.notes || '',
  })

  const { data: roles } = useRoles()

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    onSubmit(formData)
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div>
        <Label htmlFor="name">Role Name</Label>
        <Input
          id="name"
          value={formData.name}
          onChange={(e) => setFormData({ ...formData, name: e.target.value })}
          required
        />
      </div>

      <div>
        <Label htmlFor="description">Description</Label>
        <Textarea
          id="description"
          value={formData.description}
          onChange={(e) => setFormData({ ...formData, description: e.target.value })}
          rows={3}
        />
      </div>

      <div>
        <Label htmlFor="permissions">Permissions (JSON)</Label>
        <Textarea
          id="permissions"
          value={formData.permissions}
          onChange={(e) => setFormData({ ...formData, permissions: e.target.value })}
          placeholder='["read", "write", "delete"]'
          rows={3}
        />
      </div>

      <div>
        <Label htmlFor="parent_role_id">Parent Role</Label>
        <Select
          value={formData.parent_role_id?.toString() || ''}
          onValueChange={(value) => setFormData({ ...formData, parent_role_id: value ? parseInt(value) : undefined })}
        >
          <SelectTrigger>
            <SelectValue placeholder="Select parent role" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="">No parent</SelectItem>
            {roles?.items?.map((role) => (
              <SelectItem key={role.id} value={role.id.toString()}>
                {role.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <div>
        <Label htmlFor="priority">Priority</Label>
        <Input
          id="priority"
          type="number"
          value={formData.priority}
          onChange={(e) => setFormData({ ...formData, priority: parseInt(e.target.value) || 0 })}
          min={0}
          max={100}
        />
      </div>

      <div>
        <Label htmlFor="notes">Notes</Label>
        <Textarea
          id="notes"
          value={formData.notes}
          onChange={(e) => setFormData({ ...formData, notes: e.target.value })}
          rows={2}
        />
      </div>

      <div className="flex items-center space-x-4">
        <div className="flex items-center space-x-2">
          <Switch
            id="is_system_role"
            checked={formData.is_system_role}
            onCheckedChange={(checked) => setFormData({ ...formData, is_system_role: checked })}
          />
          <Label htmlFor="is_system_role">System Role</Label>
        </div>

        <div className="flex items-center space-x-2">
          <Switch
            id="is_active"
            checked={formData.is_active}
            onCheckedChange={(checked) => setFormData({ ...formData, is_active: checked })}
          />
          <Label htmlFor="is_active">Active</Label>
        </div>
      </div>

      <div className="flex justify-end space-x-2 pt-4">
        <Button type="submit">
          {initialData ? 'Update Role' : 'Create Role'}
        </Button>
      </div>
    </form>
  )
}

// Role Hierarchy Tree Component
interface RoleHierarchyTreeProps {
  role: any
  level: number
}

function RoleHierarchyTree({ role, level }: RoleHierarchyTreeProps) {
  return (
    <div className={`ml-${level * 4} border-l-2 pl-4 py-2`}>
      <div className="flex items-center space-x-2">
        <Badge variant="outline">{role.priority}</Badge>
        <span className="font-medium">{role.name}</span>
        {role.description && (
          <span className="text-sm text-gray-500">- {role.description}</span>
        )}
      </div>
      {role.child_roles?.map((child: any) => (
        <RoleHierarchyTree key={child.id} role={child} level={level + 1} />
      ))}
    </div>
  )
}