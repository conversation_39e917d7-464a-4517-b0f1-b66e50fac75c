/**
 * UI-Specific Zod Validation Schemas
 * Validation schemas for UI state, display options, and user interactions
 */

import { z } from 'zod'

// View mode schema
export const ViewModeSchema = z.enum(['grid', 'list', 'table', 'cards'])

// Sort configuration schema
export const SortConfigSchema = z.object({
  field: z.string().min(1, 'Sort field is required'),
  order: z.enum(['asc', 'desc']).default('asc'),
  label: z.string().optional(),
})

// Pagination configuration schema
export const PaginationConfigSchema = z.object({
  page: z.number().positive().default(1),
  size: z.number().positive().min(1).max(100).default(20),
  total: z.number().nonnegative().default(0),
  show_size_selector: z.boolean().default(true),
  size_options: z.array(z.number().positive()).default([10, 20, 50, 100]),
})

// Display options schema
export const ComponentDisplayOptionsSchema = z.object({
  show_images: z.boolean().default(true),
  show_specifications: z.boolean().default(true),
  show_pricing: z.boolean().default(true),
  show_availability: z.boolean().default(true),
  show_actions: z.boolean().default(true),
  show_selection: z.boolean().default(false),
  compact_mode: z.boolean().default(false),
  highlight_preferred: z.boolean().default(true),
  show_inactive: z.boolean().default(false),
})

// List state schema
export const ComponentListStateSchema = z.object({
  view_mode: ViewModeSchema.default('grid'),
  sort_config: SortConfigSchema.default({ field: 'name', order: 'asc' }),
  pagination: PaginationConfigSchema.default({}),
  display_options: ComponentDisplayOptionsSchema.default({}),
  selected_ids: z.array(z.number().positive()).default([]),
  loading: z.boolean().default(false),
  error: z.string().optional(),
})

// Search state schema
export const SearchStateSchema = z.object({
  query: z.string().default(''),
  field: z
    .enum(['name', 'manufacturer', 'model_number', 'description', 'part_number', 'all'])
    .default('all'),
  is_searching: z.boolean().default(false),
  is_advanced_mode: z.boolean().default(false),
  search_history: z.array(z.string()).default([]),
  recent_searches: z.array(z.string()).default([]),
  saved_searches: z.array(z.string()).default([]),
  suggestions: z.array(z.string()).default([]),
  show_suggestions: z.boolean().default(true),
})

// Filter state schema
export const FilterStateSchema = z.object({
  active_filters: z.record(z.string(), z.any()).default({}),
  filter_count: z.number().nonnegative().default(0),
  is_filtering: z.boolean().default(false),
  show_advanced_filters: z.boolean().default(false),
  preset_filters: z
    .array(
      z.object({
        id: z.string(),
        name: z.string(),
        filters: z.record(z.string(), z.any()),
      })
    )
    .default([]),
})

// Bulk operation state schema
export const BulkOperationStateSchema = z.object({
  selected_ids: z.array(z.number().positive()).default([]),
  operation: z.enum(['update', 'delete', 'export', 'duplicate']).optional(),
  is_processing: z.boolean().default(false),
  progress: z.number().min(0).max(100).default(0),
  results: z
    .array(
      z.object({
        id: z.number().positive(),
        success: z.boolean(),
        message: z.string().optional(),
      })
    )
    .default([]),
  errors: z.array(z.string()).default([]),
})

// Form state schema
export const ComponentFormStateSchema = z.object({
  data: z.record(z.string(), z.any()).default({}),
  errors: z.record(z.string(), z.string()).default({}),
  touched: z.record(z.string(), z.boolean()).default({}),
  is_dirty: z.boolean().default(false),
  is_submitting: z.boolean().default(false),
  is_valid: z.boolean().default(false),
  mode: z.enum(['create', 'edit', 'view']).default('create'),
})

// Sidebar state schema
export const SidebarStateSchema = z.object({
  is_open: z.boolean().default(true),
  width: z.number().positive().default(300),
  collapsed: z.boolean().default(false),
  active_section: z.enum(['filters', 'search', 'stats', 'help']).default('filters'),
})

// Modal state schema
export const ModalStateSchema = z.object({
  is_open: z.boolean().default(false),
  type: z.enum(['create', 'edit', 'delete', 'bulk', 'import', 'export', 'details']).optional(),
  data: z.any().optional(),
  loading: z.boolean().default(false),
  error: z.string().optional(),
})

// Notification schema
export const NotificationSchema = z.object({
  id: z.string(),
  type: z.enum(['success', 'error', 'warning', 'info']),
  title: z.string(),
  message: z.string(),
  duration: z.number().positive().default(5000),
  dismissible: z.boolean().default(true),
  actions: z
    .array(
      z.object({
        label: z.string(),
        action: z.function().optional(),
      })
    )
    .optional(),
})

// Toast notification schema
export const ToastSchema = z.object({
  id: z.string(),
  type: z.enum(['success', 'error', 'warning', 'info']),
  message: z.string(),
  duration: z.number().positive().default(3000),
  position: z.enum(['top-right', 'top-left', 'bottom-right', 'bottom-left']).default('top-right'),
})

// Theme configuration schema
export const ThemeConfigSchema = z.object({
  mode: z.enum(['light', 'dark', 'system']).default('system'),
  primary_color: z.string().default('#3b82f6'),
  accent_color: z.string().default('#10b981'),
  border_radius: z.enum(['none', 'sm', 'md', 'lg', 'xl']).default('md'),
  font_size: z.enum(['sm', 'md', 'lg']).default('md'),
})

// Accessibility preferences schema
export const AccessibilityPreferencesSchema = z.object({
  reduce_motion: z.boolean().default(false),
  high_contrast: z.boolean().default(false),
  large_text: z.boolean().default(false),
  keyboard_navigation: z.boolean().default(true),
  screen_reader_mode: z.boolean().default(false),
})

// User preferences schema
export const UserPreferencesSchema = z.object({
  display_options: ComponentDisplayOptionsSchema.default({}),
  theme: ThemeConfigSchema.default({}),
  accessibility: AccessibilityPreferencesSchema.default({}),
  default_view_mode: ViewModeSchema.default('grid'),
  default_page_size: z.number().positive().default(20),
  auto_save: z.boolean().default(true),
  show_tooltips: z.boolean().default(true),
  enable_animations: z.boolean().default(true),
})

// Component store state schema
export const ComponentStoreStateSchema = z.object({
  list_state: ComponentListStateSchema.default({}),
  search_state: SearchStateSchema.default({}),
  filter_state: FilterStateSchema.default({}),
  bulk_state: BulkOperationStateSchema.default({}),
  form_state: ComponentFormStateSchema.default({}),
  sidebar_state: SidebarStateSchema.default({}),
  modal_state: ModalStateSchema.default({}),
  notifications: z.array(NotificationSchema).default([]),
  user_preferences: UserPreferencesSchema.default({}),
})

// Export type definitions
export type ViewMode = z.infer<typeof ViewModeSchema>
export type SortConfig = z.infer<typeof SortConfigSchema>
export type PaginationConfig = z.infer<typeof PaginationConfigSchema>
export type ComponentDisplayOptions = z.infer<typeof ComponentDisplayOptionsSchema>
export type ComponentListState = z.infer<typeof ComponentListStateSchema>
export type SearchState = z.infer<typeof SearchStateSchema>
export type FilterState = z.infer<typeof FilterStateSchema>
export type BulkOperationState = z.infer<typeof BulkOperationStateSchema>
export type ComponentFormState = z.infer<typeof ComponentFormStateSchema>
export type SidebarState = z.infer<typeof SidebarStateSchema>
export type ModalState = z.infer<typeof ModalStateSchema>
export type Notification = z.infer<typeof NotificationSchema>
export type Toast = z.infer<typeof ToastSchema>
export type ThemeConfig = z.infer<typeof ThemeConfigSchema>
export type AccessibilityPreferences = z.infer<typeof AccessibilityPreferencesSchema>
export type UserPreferences = z.infer<typeof UserPreferencesSchema>
export type ComponentStoreState = z.infer<typeof ComponentStoreStateSchema>

// Validation helper functions
export const validateComponentListState = (data: unknown): ComponentListState => {
  return ComponentListStateSchema.parse(data)
}

export const validateSearchState = (data: unknown): SearchState => {
  return SearchStateSchema.parse(data)
}

export const validateFilterState = (data: unknown): FilterState => {
  return FilterStateSchema.parse(data)
}

export const validateUserPreferences = (data: unknown): UserPreferences => {
  return UserPreferencesSchema.parse(data)
}

// Safe validation functions
export const safeValidateComponentListState = (data: unknown) => {
  return ComponentListStateSchema.safeParse(data)
}

export const safeValidateSearchState = (data: unknown) => {
  return SearchStateSchema.safeParse(data)
}

export const safeValidateFilterState = (data: unknown) => {
  return FilterStateSchema.safeParse(data)
}

// Default state creators
export const createDefaultListState = (): ComponentListState => {
  return ComponentListStateSchema.parse({})
}

export const createDefaultSearchState = (): SearchState => {
  return SearchStateSchema.parse({})
}

export const createDefaultFilterState = (): FilterState => {
  return FilterStateSchema.parse({})
}

export const createDefaultUserPreferences = (): UserPreferences => {
  return UserPreferencesSchema.parse({})
}
