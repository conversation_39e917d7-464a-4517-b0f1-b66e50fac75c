/**
 * Test Configuration
 * Comprehensive test setup and configuration for the components module
 */

import { beforeAll, afterAll, beforeEach, afterEach } from 'vitest'
import { cleanup } from '@testing-library/react'
import { setupServer } from 'msw/node'
import { rest } from 'msw'
import '@testing-library/jest-dom'

// Mock data for testing
export const mockComponents = [
  {
    id: 1,
    name: 'Test Resistor',
    manufacturer: 'Test Corp',
    model_number: 'TR-001',
    component_type: 'resistor',
    category: 'RESISTOR',
    unit_price: 0.25,
    currency: 'EUR',
    is_active: true,
    is_preferred: false,
    stock_status: 'available',
    weight_kg: 0.001,
    dimensions: {
      length: 10,
      width: 5,
      height: 2,
      unit: 'mm',
    },
    specifications: {
      resistance: '1kΩ',
      tolerance: '5%',
      power: '0.25W',
      temperature_coefficient: '100ppm/°C',
    },
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
  },
  {
    id: 2,
    name: 'Test Capacitor',
    manufacturer: 'Test Corp',
    model_number: 'TC-002',
    component_type: 'capacitor',
    category: 'CAPACITOR',
    unit_price: 1.5,
    currency: 'EUR',
    is_active: true,
    is_preferred: true,
    stock_status: 'limited',
    weight_kg: 0.005,
    dimensions: {
      diameter: 8,
      height: 12,
      unit: 'mm',
    },
    specifications: {
      capacitance: '100µF',
      voltage: '25V',
      tolerance: '20%',
      type: 'Electrolytic',
    },
    created_at: '2024-01-02T00:00:00Z',
    updated_at: '2024-01-02T00:00:00Z',
  },
  {
    id: 3,
    name: 'Test Switch',
    manufacturer: 'Switch Corp',
    model_number: 'TS-003',
    component_type: 'switch',
    category: 'SWITCH',
    unit_price: 5.99,
    currency: 'EUR',
    is_active: false,
    is_preferred: false,
    stock_status: 'discontinued',
    weight_kg: 0.025,
    dimensions: {
      length: 20,
      width: 15,
      height: 10,
      unit: 'mm',
    },
    specifications: {
      type: 'Momentary',
      contacts: 'SPST',
      current_rating: '1A',
      voltage_rating: '250V',
    },
    created_at: '2024-01-03T00:00:00Z',
    updated_at: '2024-01-03T00:00:00Z',
  },
]

// MSW server for API mocking
export const server = setupServer(
  // Get components
  rest.get('/api/v1/components', (req, res, ctx) => {
    const page = parseInt(req.url.searchParams.get('page') || '1')
    const size = parseInt(req.url.searchParams.get('size') || '20')
    const search = req.url.searchParams.get('search')
    const manufacturer = req.url.searchParams.get('manufacturer')
    const category = req.url.searchParams.get('category')
    const isActive = req.url.searchParams.get('is_active')
    const isPreferred = req.url.searchParams.get('is_preferred')
    const stockStatus = req.url.searchParams.get('stock_status')

    let filteredComponents = [...mockComponents]

    // Apply filters
    if (search) {
      filteredComponents = filteredComponents.filter(
        (c) =>
          c.name.toLowerCase().includes(search.toLowerCase()) ||
          c.manufacturer.toLowerCase().includes(search.toLowerCase()) ||
          c.model_number.toLowerCase().includes(search.toLowerCase())
      )
    }

    if (manufacturer) {
      filteredComponents = filteredComponents.filter(
        (c) => c.manufacturer.toLowerCase() === manufacturer.toLowerCase()
      )
    }

    if (category) {
      filteredComponents = filteredComponents.filter((c) => c.category === category)
    }

    if (isActive !== null) {
      filteredComponents = filteredComponents.filter((c) => c.is_active === (isActive === 'true'))
    }

    if (isPreferred !== null) {
      filteredComponents = filteredComponents.filter(
        (c) => c.is_preferred === (isPreferred === 'true')
      )
    }

    if (stockStatus) {
      filteredComponents = filteredComponents.filter((c) => c.stock_status === stockStatus)
    }

    // Apply pagination
    const startIndex = (page - 1) * size
    const endIndex = startIndex + size
    const paginatedComponents = filteredComponents.slice(startIndex, endIndex)

    return res(
      ctx.json({
        items: paginatedComponents,
        total: filteredComponents.length,
        page,
        size,
        pages: Math.ceil(filteredComponents.length / size),
        has_next: endIndex < filteredComponents.length,
        has_prev: page > 1,
      })
    )
  }),

  // Get component by ID
  rest.get('/api/v1/components/:id', (req, res, ctx) => {
    const id = parseInt(req.params.id as string)
    const component = mockComponents.find((c) => c.id === id)

    if (!component) {
      return res(ctx.status(404), ctx.json({ detail: 'Component not found' }))
    }

    return res(ctx.json(component))
  }),

  // Create component
  rest.post('/api/v1/components', (req, res, ctx) => {
    const newComponent = {
      id: mockComponents.length + 1,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      ...req.body,
    }

    mockComponents.push(newComponent as any)

    return res(ctx.status(201), ctx.json(newComponent))
  }),

  // Update component
  rest.put('/api/v1/components/:id', (req, res, ctx) => {
    const id = parseInt(req.params.id as string)
    const componentIndex = mockComponents.findIndex((c) => c.id === id)

    if (componentIndex === -1) {
      return res(ctx.status(404), ctx.json({ detail: 'Component not found' }))
    }

    const updatedComponent = {
      ...mockComponents[componentIndex],
      ...req.body,
      updated_at: new Date().toISOString(),
    }

    mockComponents[componentIndex] = updatedComponent as any

    return res(ctx.json(updatedComponent))
  }),

  // Delete component
  rest.delete('/api/v1/components/:id', (req, res, ctx) => {
    const id = parseInt(req.params.id as string)
    const componentIndex = mockComponents.findIndex((c) => c.id === id)

    if (componentIndex === -1) {
      return res(ctx.status(404), ctx.json({ detail: 'Component not found' }))
    }

    mockComponents.splice(componentIndex, 1)

    return res(ctx.status(204))
  }),

  // Bulk operations
  rest.put('/api/v1/components/bulk', (req, res, ctx) => {
    const { component_ids, updates } = req.body as any

    const results = component_ids.map((id: number) => {
      const componentIndex = mockComponents.findIndex((c) => c.id === id)
      if (componentIndex !== -1) {
        mockComponents[componentIndex] = {
          ...mockComponents[componentIndex],
          ...updates,
          updated_at: new Date().toISOString(),
        } as any
        return { id, success: true }
      }
      return { id, success: false, message: 'Component not found' }
    })

    return res(
      ctx.json({
        success: true,
        updated_count: results.filter((r) => r.success).length,
        results,
      })
    )
  }),

  rest.delete('/api/v1/components/bulk', (req, res, ctx) => {
    const { component_ids } = req.body as any

    const results = component_ids.map((id: number) => {
      const componentIndex = mockComponents.findIndex((c) => c.id === id)
      if (componentIndex !== -1) {
        mockComponents.splice(componentIndex, 1)
        return { id, success: true }
      }
      return { id, success: false, message: 'Component not found' }
    })

    return res(
      ctx.json({
        success: true,
        deleted_count: results.filter((r) => r.success).length,
        results,
      })
    )
  }),

  // Component statistics
  rest.get('/api/v1/components/stats', (req, res, ctx) => {
    const activeComponents = mockComponents.filter((c) => c.is_active)
    const preferredComponents = mockComponents.filter((c) => c.is_preferred)

    const byCategory = mockComponents.reduce(
      (acc, component) => {
        acc[component.category] = (acc[component.category] || 0) + 1
        return acc
      },
      {} as Record<string, number>
    )

    const byManufacturer = mockComponents.reduce(
      (acc, component) => {
        acc[component.manufacturer] = (acc[component.manufacturer] || 0) + 1
        return acc
      },
      {} as Record<string, number>
    )

    const prices = mockComponents.filter((c) => c.unit_price).map((c) => c.unit_price as number)

    return res(
      ctx.json({
        total_components: mockComponents.length,
        active_components: activeComponents.length,
        inactive_components: mockComponents.length - activeComponents.length,
        preferred_components: preferredComponents.length,
        by_category: byCategory,
        by_manufacturer: byManufacturer,
        by_type: {},
        price_range: {
          min: Math.min(...prices),
          max: Math.max(...prices),
          average: prices.reduce((sum, price) => sum + price, 0) / prices.length,
        },
        last_updated: new Date().toISOString(),
      })
    )
  }),

  // Search suggestions
  rest.get('/api/v1/components/suggestions', (req, res, ctx) => {
    const query = req.url.searchParams.get('q') || ''

    const suggestions = [
      'Siemens contactors',
      'ABB circuit breakers',
      'Schneider switches',
      'Phoenix connectors',
      'Weidmuller terminals',
    ].filter((suggestion) => suggestion.toLowerCase().includes(query.toLowerCase()))

    return res(
      ctx.json({
        query,
        suggestions: suggestions.map((text) => ({
          text,
          type: 'general',
          count: Math.floor(Math.random() * 100),
        })),
        max_suggestions: 10,
      })
    )
  })
)

// Test setup and teardown
beforeAll(() => {
  server.listen({ onUnhandledRequest: 'error' })
})

afterAll(() => {
  server.close()
})

beforeEach(() => {
  // Reset mock data before each test
  mockComponents.length = 0
  mockComponents.push(
    {
      id: 1,
      name: 'Test Resistor',
      manufacturer: 'Test Corp',
      model_number: 'TR-001',
      component_type: 'resistor',
      category: 'RESISTOR',
      unit_price: 0.25,
      currency: 'EUR',
      is_active: true,
      is_preferred: false,
      stock_status: 'available',
      weight_kg: 0.001,
      dimensions: {
        length: 10,
        width: 5,
        height: 2,
        unit: 'mm',
      },
      specifications: {
        resistance: '1kΩ',
        tolerance: '5%',
        power: '0.25W',
        temperature_coefficient: '100ppm/°C',
      },
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-01T00:00:00Z',
    },
    {
      id: 2,
      name: 'Test Capacitor',
      manufacturer: 'Test Corp',
      model_number: 'TC-002',
      component_type: 'capacitor',
      category: 'CAPACITOR',
      unit_price: 1.5,
      currency: 'EUR',
      is_active: true,
      is_preferred: true,
      stock_status: 'limited',
      weight_kg: 0.005,
      dimensions: {
        diameter: 8,
        height: 12,
        unit: 'mm',
      },
      specifications: {
        capacitance: '100µF',
        voltage: '25V',
        tolerance: '20%',
        type: 'Electrolytic',
      },
      created_at: '2024-01-02T00:00:00Z',
      updated_at: '2024-01-02T00:00:00Z',
    },
    {
      id: 3,
      name: 'Test Switch',
      manufacturer: 'Switch Corp',
      model_number: 'TS-003',
      component_type: 'switch',
      category: 'SWITCH',
      unit_price: 5.99,
      currency: 'EUR',
      is_active: false,
      is_preferred: false,
      stock_status: 'discontinued',
      weight_kg: 0.025,
      dimensions: {
        length: 20,
        width: 15,
        height: 10,
        unit: 'mm',
      },
      specifications: {
        type: 'Momentary',
        contacts: 'SPST',
        current_rating: '1A',
        voltage_rating: '250V',
      },
      created_at: '2024-01-03T00:00:00Z',
      updated_at: '2024-01-03T00:00:00Z',
    }
  )
})

afterEach(() => {
  cleanup()
  server.resetHandlers()
})

// Test utilities
export const createMockComponent = (overrides = {}) => ({
  id: Math.floor(Math.random() * 1000),
  name: 'Mock Component',
  manufacturer: 'Mock Corp',
  model_number: 'MC-001',
  component_type: 'resistor',
  category: 'RESISTOR',
  unit_price: 1.0,
  currency: 'EUR',
  is_active: true,
  is_preferred: false,
  stock_status: 'available',
  created_at: new Date().toISOString(),
  updated_at: new Date().toISOString(),
  ...overrides,
})

export const createMockComponentList = (count: number) =>
  Array.from({ length: count }, (_, i) =>
    createMockComponent({ id: i + 1, name: `Mock Component ${i + 1}` })
  )

// Custom render function with providers
export { render, screen, waitFor, within } from '@testing-library/react'
export { userEvent } from '@testing-library/user-event'
export { default as userEvent } from '@testing-library/user-event'
