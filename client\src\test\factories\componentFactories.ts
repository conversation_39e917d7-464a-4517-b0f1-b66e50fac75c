/**
 * Test data factories for Component Management module
 * Provides mock data for testing component-related functionality
 */

import { vi } from 'vitest'
import type {
  BulkOperationState,
  ComponentCategoryEntity,
  ComponentCreate,
  ComponentFilterState,
  ComponentListState,
  ComponentPaginatedResponse,
  ComponentRead,
  ComponentSearchState,
  ComponentStats,
  ComponentTypeEntity,
  ComponentUpdate,
} from '@/modules/components/types'

// Mock component data
export const mockComponent: ComponentRead = {
  id: 1,
  name: 'Test Resistor',
  full_name: 'Test Electronics 1K Ohm Resistor',
  display_name: 'Test 1K Resistor',
  manufacturer: 'Test Electronics',
  part_number: 'TR-1K-001',
  model_number: 'TR1K',
  component_category_id: 1,
  component_type_id: 1,
  component_category_entity: {
    id: 1,
    name: 'Electronic Components',
    description: 'Basic electronic components',
    is_active: true,
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
  },
  component_type_entity: {
    id: 1,
    name: 'Resistor',
    description: 'Fixed value resistors',
    category_id: 1,
    is_active: true,
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
  },
  description: 'High precision 1K ohm resistor for testing',
  specifications: {
    resistance: '1000',
    tolerance: '±1%',
    power_rating: '0.25W',
    temperature_coefficient: '±100ppm/°C',
  },
  dimensions: {
    length: 6.3,
    width: 2.5,
    height: 2.5,
    unit: 'mm',
  },
  unit_price: '0.15',
  currency: 'EUR',
  is_preferred: false,
  is_active: true,
  created_at: '2024-01-01T00:00:00Z',
  updated_at: '2024-01-01T00:00:00Z',
  weight_kg: 0.001, // Add missing weight_kg property
  supplier: 'Test Supplier', // Add missing supplier property
  metadata: {}, // Add missing metadata property
}

export const mockCapacitor: ComponentRead = {
  id: 2,
  name: 'Test Capacitor',
  full_name: 'Test Electronics 100uF Electrolytic Capacitor',
  display_name: 'Test 100uF Cap',
  manufacturer: 'Test Electronics',
  part_number: 'TC-100UF-001',
  model_number: 'TC100UF',
  component_category_id: 2,
  component_type_id: 2,
  component_category_entity: {
    id: 2,
    name: 'Passive Components',
    description: 'Passive electronic components',
    is_active: true,
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
  },
  component_type_entity: {
    id: 2,
    name: 'Capacitor',
    description: 'Energy storage capacitors',
    category_id: 2,
    is_active: true,
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
  },
  description: 'High quality 100uF electrolytic capacitor',
  specifications: {
    capacitance: '100uF',
    voltage_rating: '25V',
    tolerance: '±20%',
    esr: '0.5Ω',
  },
  dimensions: {
    length: 8.0,
    width: 8.0,
    height: 11.5,
    unit: 'mm',
  },
  unit_price: '0.45',
  currency: 'EUR',
  is_preferred: true,
  is_active: true,
  created_at: '2024-01-01T00:00:00Z',
  updated_at: '2024-01-01T00:00:00Z',
  weight_kg: 0.002,
  supplier: 'Test Supplier',
  metadata: {},
}

export const mockInactiveComponent: ComponentRead = {
  ...mockComponent,
  id: 3,
  name: 'Inactive Component',
  is_active: false,
}

// Component creation data
export const mockComponentCreate: ComponentCreate = {
  name: 'New Test Component',
  manufacturer: 'Test Manufacturer',
  part_number: 'TEST-001',
  model_number: 'TM001',
  component_category_id: 1,
  component_type_id: 1,
  description: 'A new test component for testing',
  specifications: {
    resistance: '2200',
    tolerance: '±5%',
    power_rating: '0.5W',
  },
  dimensions: {
    length: 7.0,
    width: 3.0,
    height: 3.0,
    unit: 'mm',
  },
  unit_price: '0.25',
  currency: 'EUR',
  is_preferred: false,
  is_active: true,
}

// Component update data
export const mockComponentUpdate: ComponentUpdate = {
  name: 'Updated Test Component',
  description: 'Updated description for testing',
  unit_price: '0.30',
  is_preferred: true,
  is_active: true,
}

// Paginated response
export const mockComponentPaginatedResponse: ComponentPaginatedResponse = {
  items: [mockComponent, mockCapacitor, mockInactiveComponent],
  total: 3,
  page: 1,
  size: 10,
  pages: 1,
}

// Component stats
export const mockComponentStats: ComponentStats = {
  total_components: 150,
  active_components: 142,
  preferred_components: 25,
  inactive_components: 8,
  by_category: {
    RESISTOR: 45,
    CAPACITOR: 38,
    INDUCTOR: 22,
    DIODE: 18,
    TRANSISTOR: 15,
    IC: 12,
  },
  by_manufacturer: {
    'Test Electronics': 45,
    'Quality Components': 38,
    'Precision Parts': 32,
    'Reliable Semiconductors': 25,
    'Standard Components': 10,
  },
  by_type: {
    RESISTOR: 45,
    CAPACITOR: 38,
    INDUCTOR: 22,
    DIODE: 18,
    TRANSISTOR: 15,
    IC: 12,
  },
  price_range: {
    min: 0.15,
    max: 0.45,
    average: 0.3,
  },
}

// Component filters
export const mockComponentFilters: ComponentFilterState = {
  search_term: 'resistor',
  component_category_id: 1,
  component_type_id: 1,
  manufacturer: 'Schneider Electric',
  is_preferred: true,
  is_active: true,
  min_price: 0.1,
  max_price: 1.0,
  currency: 'EUR',
}

// Component list state
export const mockComponentListState: ComponentListState = {
  filters: mockComponentFilters,
  sortBy: 'name',
  sortOrder: 'asc',
  page: 1,
  pageSize: 20,
  viewMode: 'grid',
  selectedComponents: [1, 2],
}

// Component search state
export const mockComponentSearchState: ComponentSearchState = {
  query: 'test resistor',
  field: 'name',
  suggestions: ['test resistor 1k', 'test resistor 2k', 'test resistor 10k'],
  recentSearches: [
    { query: 'resistor', timestamp: '2024-01-01T00:00:00Z' },
    { query: 'capacitor', timestamp: '2024-01-01T01:00:00Z' },
    { query: 'diode', timestamp: '2024-01-01T02:00:00Z' },
  ],
  isSearching: false,
  isAdvancedMode: false, // Added missing property
  searchHistory: [
    { query: 'resistor', timestamp: '2024-01-01T00:00:00Z' },
    { query: 'capacitor', timestamp: '2024-01-01T01:00:00Z' },
  ],
  savedSearches: [], // Added missing property
}

// Bulk operation state
export const mockBulkOperationState: BulkOperationState = {
  selectedIds: [1, 2, 3],
  operation: 'update',
  isProcessing: false,
  progress: 0,
  results: [],
  errors: [],
}

// Factory functions for creating test data
export const createMockComponent = (overrides: Partial<ComponentRead> = {}): ComponentRead => ({
  ...mockComponent,
  ...overrides,
})

export const createMockComponentCreate = (
  overrides: Partial<ComponentCreate> = {}
): ComponentCreate => ({
  ...mockComponentCreate,
  ...overrides,
})

export const createMockComponentUpdate = (
  overrides: Partial<ComponentUpdate> = {}
): ComponentUpdate => ({
  ...mockComponentUpdate,
  ...overrides,
})

export const createMockComponentList = (count: number = 3): ComponentRead[] => {
  return Array.from({ length: count }, (_, index) =>
    createMockComponent({
      id: index + 1,
      name: `Test Component ${index + 1}`,
      part_number: `TEST-${String(index + 1).padStart(3, '0')}`,
    })
  )
}

export const createMockPaginatedResponse = (
  items: ComponentRead[] = [mockComponent, mockCapacitor],
  page: number = 1,
  size: number = 10
): ComponentPaginatedResponse => ({
  items,
  total: items.length,
  page,
  size,
  pages: Math.ceil(items.length / size),
})

// API response factories
export const mockApiSuccess = <T>(data: T) => ({
  data,
  error: undefined,
  status: 200,
})

export const mockApiError = (message: string = 'Test error', code: string = 'TEST_ERROR') => ({
  data: undefined,
  error: {
    detail: message,
    error_code: code,
    timestamp: '2024-01-01T00:00:00Z',
  },
  status: 400,
})

// React Query mock responses
export const mockQuerySuccess = <T>(data: T) => ({
  data,
  isLoading: false,
  isError: false,
  error: null,
  isSuccess: true,
  refetch: vi.fn(),
})

export const mockQueryLoading = () => ({
  data: undefined,
  isLoading: true,
  isError: false,
  error: null,
  isSuccess: false,
  refetch: vi.fn(),
})

export const mockQueryError = (error: Error = new Error('Test error')) => ({
  data: undefined,
  isLoading: false,
  isError: true,
  error,
  isSuccess: false,
  refetch: vi.fn(),
})

// Mutation mock responses
export const mockMutationIdle = () => ({
  mutate: vi.fn(),
  mutateAsync: vi.fn(),
  isPending: false,
  isError: false,
  isSuccess: false,
  error: null,
  data: undefined,
  reset: vi.fn(),
})

export const mockMutationPending = () => ({
  mutate: vi.fn(),
  mutateAsync: vi.fn(),
  isPending: true,
  isError: false,
  isSuccess: false,
  error: null,
  data: undefined,
  reset: vi.fn(),
})

export const mockMutationSuccess = <T>(data: T) => ({
  mutate: vi.fn(),
  mutateAsync: vi.fn(),
  isPending: false,
  isError: false,
  isSuccess: true,
  error: null,
  data,
  reset: vi.fn(),
})

export const mockMutationError = (error: Error = new Error('Test mutation error')) => ({
  mutate: vi.fn(),
  mutateAsync: vi.fn(),
  isPending: false,
  isError: true,
  isSuccess: false,
  error,
  data: undefined,
  reset: vi.fn(),
})
