/**
 * Utility functions for the Settings module
 * Ultimate Electrical Designer - Settings & User Preferences
 */

import type {
  UserPreferences,
  SettingsCategory,
  SettingsCategoryConfig,
  ThemeConfig,
  LanguageConfig,
  TimezoneConfig,
} from './types'
import {
  SETTINGS_CATEGORIES,
  THEME_CONFIGS,
  LANGUAGE_CONFIGS,
  TIMEZONE_CONFIGS,
  VALIDATION_RULES,
} from './constants'

/**
 * Get category configuration by ID
 */
export function getCategoryConfig(
  categoryId: SettingsCategory
): SettingsCategoryConfig | undefined {
  return SETTINGS_CATEGORIES.find((cat) => cat.id === categoryId)
}

/**
 * Get all available categories
 */
export function getAllCategories(): SettingsCategoryConfig[] {
  return SETTINGS_CATEGORIES
}

/**
 * Search categories and fields by query
 */
export function searchSettings(query: string): {
  categories: SettingsCategoryConfig[]
  matches: Array<{
    categoryId: SettingsCategory
    sectionId: string
    fieldId: string
    label: string
    description?: string
  }>
} {
  const lowercaseQuery = query.toLowerCase()
  const matches: Array<{
    categoryId: SettingsCategory
    sectionId: string
    fieldId: string
    label: string
    description?: string
  }> = []

  const matchingCategories = SETTINGS_CATEGORIES.filter((category) => {
    let categoryMatches = false

    // Check category name and description
    if (
      category.label.toLowerCase().includes(lowercaseQuery) ||
      category.description.toLowerCase().includes(lowercaseQuery)
    ) {
      categoryMatches = true
    }

    // Check sections and fields
    category.sections.forEach((section) => {
      section.fields.forEach((field) => {
        const fieldMatches =
          field.label.toLowerCase().includes(lowercaseQuery) ||
          (field.description && field.description.toLowerCase().includes(lowercaseQuery))

        if (fieldMatches) {
          categoryMatches = true
          matches.push({
            categoryId: category.id,
            sectionId: section.id,
            fieldId: field.id,
            label: field.label,
            description: field.description,
          })
        }
      })
    })

    return categoryMatches
  })

  return {
    categories: matchingCategories,
    matches,
  }
}

/**
 * Get theme configuration
 */
export function getThemeConfig(theme: string): ThemeConfig | undefined {
  return THEME_CONFIGS[theme]
}

/**
 * Get language configuration
 */
export function getLanguageConfig(languageCode: string): LanguageConfig | undefined {
  return LANGUAGE_CONFIGS.find((lang) => lang.code === languageCode)
}

/**
 * Get timezone configuration
 */
export function getTimezoneConfig(timezone: string): TimezoneConfig | undefined {
  return TIMEZONE_CONFIGS.find((tz) => tz.value === timezone)
}

/**
 * Format preferences for display
 */
export function formatPreferenceValue(key: keyof UserPreferences, value: any): string {
  switch (key) {
    case 'theme':
      return value === 'system' ? 'System Default' : value.charAt(0).toUpperCase() + value.slice(1)

    case 'language':
      const langConfig = getLanguageConfig(value)
      return langConfig ? langConfig.name : value

    case 'timezone':
      const tzConfig = getTimezoneConfig(value)
      return tzConfig ? `${tzConfig.label} (${tzConfig.offset})` : value

    case 'auto_save_interval':
      return `${value} seconds`

    case 'calculation_precision':
      return `${value} decimal places`

    case 'notifications_enabled':
    case 'email_notifications':
    case 'auto_save_enabled':
      return value ? 'Enabled' : 'Disabled'

    default:
      return String(value)
  }
}

/**
 * Validate a single preference field
 */
export function validatePreferenceField(
  key: keyof UserPreferences,
  value: any
): { isValid: boolean; error?: string } {
  switch (key) {
    case 'auto_save_interval':
      if (typeof value !== 'number') {
        return { isValid: false, error: 'Auto save interval must be a number' }
      }
      if (value < VALIDATION_RULES.AUTO_SAVE_INTERVAL.min) {
        return {
          isValid: false,
          error: `Minimum ${VALIDATION_RULES.AUTO_SAVE_INTERVAL.min} seconds`,
        }
      }
      if (value > VALIDATION_RULES.AUTO_SAVE_INTERVAL.max) {
        return {
          isValid: false,
          error: `Maximum ${VALIDATION_RULES.AUTO_SAVE_INTERVAL.max} seconds`,
        }
      }
      break

    case 'calculation_precision':
      if (typeof value !== 'number') {
        return { isValid: false, error: 'Calculation precision must be a number' }
      }
      if (value < VALIDATION_RULES.CALCULATION_PRECISION.min) {
        return {
          isValid: false,
          error: `Minimum ${VALIDATION_RULES.CALCULATION_PRECISION.min} decimal places`,
        }
      }
      if (value > VALIDATION_RULES.CALCULATION_PRECISION.max) {
        return {
          isValid: false,
          error: `Maximum ${VALIDATION_RULES.CALCULATION_PRECISION.max} decimal places`,
        }
      }
      break

    case 'theme':
      if (!['light', 'dark', 'system'].includes(value)) {
        return { isValid: false, error: 'Invalid theme selection' }
      }
      break

    case 'language':
      const validLanguages = LANGUAGE_CONFIGS.map((lang) => lang.code)
      if (!validLanguages.includes(value)) {
        return { isValid: false, error: 'Invalid language selection' }
      }
      break

    case 'units_system':
      if (!['metric', 'imperial'].includes(value)) {
        return { isValid: false, error: 'Invalid units system selection' }
      }
      break

    case 'time_format':
      if (!['12h', '24h'].includes(value)) {
        return { isValid: false, error: 'Invalid time format selection' }
      }
      break

    case 'dashboard_layout':
      if (!['default', 'compact', 'expanded'].includes(value)) {
        return { isValid: false, error: 'Invalid dashboard layout selection' }
      }
      break
  }

  return { isValid: true }
}

/**
 * Get default value for a preference field
 */
export function getDefaultPreferenceValue(key: keyof UserPreferences): any {
  const defaults: UserPreferences = {
    theme: 'system',
    language: 'en',
    timezone: 'UTC',
    date_format: 'YYYY-MM-DD',
    time_format: '24h',
    units_system: 'metric',
    notifications_enabled: true,
    email_notifications: true,
    auto_save_interval: 300,
    dashboard_layout: 'default',
    calculation_precision: 2,
    auto_save_enabled: true,
  }

  return defaults[key]
}

/**
 * Check if preferences are equal
 */
export function arePreferencesEqual(
  prefs1: Partial<UserPreferences>,
  prefs2: Partial<UserPreferences>
): boolean {
  const keys1 = Object.keys(prefs1) as (keyof UserPreferences)[]
  const keys2 = Object.keys(prefs2) as (keyof UserPreferences)[]

  if (keys1.length !== keys2.length) {
    return false
  }

  return keys1.every((key) => prefs1[key] === prefs2[key])
}

/**
 * Merge preferences with defaults
 */
export function mergePreferencesWithDefaults(
  preferences: Partial<UserPreferences>
): UserPreferences {
  const defaults: UserPreferences = {
    theme: 'system',
    language: 'en',
    timezone: 'UTC',
    date_format: 'YYYY-MM-DD',
    time_format: '24h',
    units_system: 'metric',
    notifications_enabled: true,
    email_notifications: true,
    auto_save_interval: 300,
    dashboard_layout: 'default',
    calculation_precision: 2,
    auto_save_enabled: true,
  }

  return { ...defaults, ...preferences }
}

/**
 * Generate settings export filename
 */
export function generateExportFilename(prefix: string = 'ued-settings'): string {
  const date = new Date().toISOString().split('T')[0]
  const time = new Date().toTimeString().split(' ')[0].replace(/:/g, '-')
  return `${prefix}-${date}-${time}.json`
}

/**
 * Parse import file
 */
export async function parseImportFile(file: File): Promise<any> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()

    reader.onload = (event) => {
      try {
        const content = event.target?.result as string
        const data = JSON.parse(content)
        resolve(data)
      } catch (error) {
        reject(new Error('Invalid JSON file'))
      }
    }

    reader.onerror = () => {
      reject(new Error('Failed to read file'))
    }

    reader.readAsText(file)
  })
}

/**
 * Debounce function
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout
  return (...args: Parameters<T>) => {
    clearTimeout(timeout)
    timeout = setTimeout(() => func(...args), wait)
  }
}

/**
 * Throttle function
 */
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args)
      inThrottle = true
      setTimeout(() => (inThrottle = false), limit)
    }
  }
}

/**
 * Deep clone object
 */
export function deepClone<T>(obj: T): T {
  if (obj === null || typeof obj !== 'object') {
    return obj
  }

  if (obj instanceof Date) {
    return new Date(obj.getTime()) as unknown as T
  }

  if (obj instanceof Array) {
    return obj.map((item) => deepClone(item)) as unknown as T
  }

  if (typeof obj === 'object') {
    const cloned = {} as T
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        cloned[key] = deepClone(obj[key])
      }
    }
    return cloned
  }

  return obj
}
