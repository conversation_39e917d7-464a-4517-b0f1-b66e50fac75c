# Project Scaffold Analysis

This document provides a detailed analysis of the project's folder structure, based on the `folder-structure.json` file. It serves as a reference for understanding the high-level architecture and the location of key features.

## High-Level Architecture

The application is structured as a microservices-based architecture:

1.  **Client (`client/`)**: A **Next.js** and **React** single-page application written in **TypeScript**. It serves as the user interface for the entire platform.
2.  **Backend Server (`server/`)**: A **Python** application built with the **FastAPI** framework. It handles core business logic, data management, and user authentication.
3.  **CAD Integrator Service (`cad-integrator-service/`)**: A **C# (.NET)** microservice responsible for integrating with CAD software like AutoCAD.
4.  **Computation Engine Service (`computation-engine-service/`)**: A **C# (.NET)** microservice dedicated to performing complex electrical calculations and simulations.

The services are designed to be containerized using **Docker**, as indicated by the presence of `docker-compose.yml` and `Dockerfile` files in the service directories.

### Detailed Breakdown

#### Client (Frontend)

*   **Framework**: Next.js with the App Router.
*   **Language**: TypeScript.
*   **Styling**: Tailwind CSS.
*   **UI Components**: A large, well-organized component library is located in `client/src/components/ui/`, likely based on a library like Radix UI or shadcn/ui.
*   **State Management**: Uses React Query for server state (`client/src/lib/react-query.tsx`) and Zustand for client state (`client/src/stores/`).
*   **Modularity**: The code is highly organized into domain-driven modules under `client/src/modules/`, such as `components`, `dashboard`, `settings`, and various calculation modules (`cable_sizing`, `heat_tracing`, etc.).
*   **Testing**: Comprehensive testing setup using **Vitest** for unit/integration tests and **Playwright** for end-to-end tests.

#### Server (Backend)

*   **Framework**: FastAPI.
*   **Language**: Python.
*   **Architecture**: Follows a clean, 5-layer architecture as outlined in the design documents:
    *   **API Layer** (`server/src/api/v1/`): Defines the API endpoints.
    *   **Service Layer** (`server/src/core/services/`): Contains the core business logic.
    *   **Repository Layer** (`server/src/core/repositories/`): Abstracts data access.
    *   **Model Layer** (`server/src/core/models/`): Defines data structures using SQLAlchemy.
    *   **Schema Layer** (`server/src/core/schemas/`): Defines data validation and serialization rules using Pydantic.
*   **Database**: Uses **SQLAlchemy** as the ORM, with **Alembic** for database migrations. The presence of `app_dev.db` indicates **SQLite** is used for development.
*   **Security**: Robust security implementation including password handling, input validation, and a unified security validator.

#### Microservices (C#)

*   Both the **CAD Integrator** and **Computation Engine** are self-contained .NET projects.
*   They expose specific functionalities through controllers (`CadController.cs`, `ComputationController.cs`) and contain the core logic within service classes (`AutoCADService.cs`, `PowerFlowSolver.cs`).
*   Each service has its own `Dockerfile`, enabling independent deployment and scaling.