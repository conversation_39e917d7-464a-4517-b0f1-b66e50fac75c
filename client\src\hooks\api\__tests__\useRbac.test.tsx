/**
 * Unit tests for useRbac hooks
 */

import { renderHook, waitFor } from '@testing-library/react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { ReactNode } from 'react'
import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest'

import { 
  useRoles, 
  useCreateRole, 
  useUpdateRole, 
  useDeleteRole,
  useUserRoles,
  useUserRolesSummary,
  useAssignRoleToUser,
  useRemoveRoleFromUser,
  useUserHasRole,
  useUserHasPermission 
} from '../useRbac'
import { rbacApiClient } from '@/lib/api/rbac'

// Mock the rbacApiClient
vi.mock('@/lib/api/rbac', () => ({
  rbacApiClient: {
    getRoles: vi.fn(),
    createRole: vi.fn(),
    updateRole: vi.fn(),
    deleteRole: vi.fn(),
    getUserRoles: vi.fn(),
    getUserRolesSummary: vi.fn(),
    assignRoleToUser: vi.fn(),
    removeRoleFromUser: vi.fn(),
    checkUserHasRole: vi.fn(),
    checkUserHasPermission: vi.fn(),
  }
}))

// Mock data
const mockRoles = {
  items: [
    {
      id: 1,
      name: 'Admin',
      description: 'Administrator role',
      is_system_role: true,
      is_active: true,
      permissions: '["read", "write", "delete"]',
      parent_role_id: null,
      priority: 100,
      notes: 'System admin role',
      created_at: '2023-01-01T00:00:00Z',
      updated_at: '2023-01-01T00:00:00Z',
      is_deleted: false,
      deleted_at: null,
      deleted_by_user_id: null,
    },
    {
      id: 2,
      name: 'Editor',
      description: 'Editor role',
      is_system_role: false,
      is_active: true,
      permissions: '["read", "write"]',
      parent_role_id: null,
      priority: 50,
      notes: 'Content editor role',
      created_at: '2023-01-01T00:00:00Z',
      updated_at: '2023-01-01T00:00:00Z',
      is_deleted: false,
      deleted_at: null,
      deleted_by_user_id: null,
    },
  ],
  total: 2,
  page: 1,
  size: 10,
  pages: 1,
}

const mockUserRolesSummary = {
  user_id: 1,
  active_roles: [
    {
      id: 1,
      name: 'Admin',
      description: 'Administrator role',
      is_system_role: true,
      is_active: true,
      permissions: '["read", "write", "delete"]',
      parent_role_id: null,
      priority: 100,
      notes: 'System admin role',
      created_at: '2023-01-01T00:00:00Z',
      updated_at: '2023-01-01T00:00:00Z',
      is_deleted: false,
      deleted_at: null,
      deleted_by_user_id: null,
    },
  ],
  expired_roles: [],
  inactive_roles: [],
  effective_permissions: ['read', 'write', 'delete'],
}

const mockUserRoles = [
  {
    id: 1,
    name: 'User Role Assignment',
    user_id: 1,
    role_id: 1,
    assigned_by_user_id: null,
    assigned_at: '2023-01-01T00:00:00Z',
    expires_at: null,
    is_active: true,
    assignment_context: 'Initial assignment',
    notes: 'Test assignment',
    created_at: '2023-01-01T00:00:00Z',
    updated_at: '2023-01-01T00:00:00Z',
    is_deleted: false,
    deleted_at: null,
    deleted_by_user_id: null,
    is_expired: false,
  },
]

// Create a wrapper for React Query
const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
      },
    },
  })

  return ({ children }: { children: ReactNode }) => (
    <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
  )
}

describe('useRbac hooks', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  afterEach(() => {
    vi.resetAllMocks()
  })

  describe('useRoles', () => {
    it('should fetch roles successfully', async () => {
      const mockGetRoles = vi.mocked(rbacApiClient.getRoles)
      mockGetRoles.mockResolvedValue(mockRoles)

      const { result } = renderHook(() => useRoles(), {
        wrapper: createWrapper(),
      })

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      expect(result.current.data).toEqual(mockRoles)
      expect(mockGetRoles).toHaveBeenCalledWith(undefined)
    })

    it('should handle error when fetching roles', async () => {
      const mockGetRoles = vi.mocked(rbacApiClient.getRoles)
      mockGetRoles.mockRejectedValue(new Error('API Error'))

      const { result } = renderHook(() => useRoles(), {
        wrapper: createWrapper(),
      })

      await waitFor(() => {
        expect(result.current.isError).toBe(true)
      })

      expect(result.current.error).toBeInstanceOf(Error)
    })
  })

  describe('useCreateRole', () => {
    it('should create role successfully', async () => {
      const mockCreateRole = vi.mocked(rbacApiClient.createRole)
      const newRole = {
        name: 'New Role',
        description: 'New test role',
        is_system_role: false,
        is_active: true,
        permissions: '["read"]',
        parent_role_id: null,
        priority: 10,
        notes: 'Test notes',
      }

      mockCreateRole.mockResolvedValue({
        id: 3,
        ...newRole,
        created_at: '2023-01-01T00:00:00Z',
        updated_at: '2023-01-01T00:00:00Z',
        is_deleted: false,
        deleted_at: null,
        deleted_by_user_id: null,
      })

      const { result } = renderHook(() => useCreateRole(), {
        wrapper: createWrapper(),
      })

      await result.current.mutateAsync(newRole)

      expect(mockCreateRole).toHaveBeenCalledWith(newRole)
      expect(result.current.isSuccess).toBe(true)
    })
  })

  describe('useUpdateRole', () => {
    it('should update role successfully', async () => {
      const mockUpdateRole = vi.mocked(rbacApiClient.updateRole)
      const updateData = {
        name: 'Updated Role',
        description: 'Updated description',
      }

      mockUpdateRole.mockResolvedValue({
        ...mockRoles.items[0],
        ...updateData,
        updated_at: '2023-01-02T00:00:00Z',
      })

      const { result } = renderHook(() => useUpdateRole(), {
        wrapper: createWrapper(),
      })

      await result.current.mutateAsync({ id: 1, data: updateData })

      expect(mockUpdateRole).toHaveBeenCalledWith(1, updateData)
      expect(result.current.isSuccess).toBe(true)
    })
  })

  describe('useDeleteRole', () => {
    it('should delete role successfully', async () => {
      const mockDeleteRole = vi.mocked(rbacApiClient.deleteRole)
      mockDeleteRole.mockResolvedValue(undefined)

      const { result } = renderHook(() => useDeleteRole(), {
        wrapper: createWrapper(),
      })

      await result.current.mutateAsync(1)

      expect(mockDeleteRole).toHaveBeenCalledWith(1)
      expect(result.current.isSuccess).toBe(true)
    })
  })

  describe('useUserRoles', () => {
    it('should fetch user roles successfully', async () => {
      const mockGetUserRoles = vi.mocked(rbacApiClient.getUserRoles)
      mockGetUserRoles.mockResolvedValue(mockUserRoles)

      const { result } = renderHook(() => useUserRoles(1), {
        wrapper: createWrapper(),
      })

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      expect(result.current.data).toEqual(mockUserRoles)
      expect(mockGetUserRoles).toHaveBeenCalledWith(1)
    })

    it('should not fetch when userId is 0', () => {
      const mockGetUserRoles = vi.mocked(rbacApiClient.getUserRoles)

      const { result } = renderHook(() => useUserRoles(0), {
        wrapper: createWrapper(),
      })

      expect(result.current.isLoading).toBe(false)
      expect(mockGetUserRoles).not.toHaveBeenCalled()
    })
  })

  describe('useUserRolesSummary', () => {
    it('should fetch user roles summary successfully', async () => {
      const mockGetUserRolesSummary = vi.mocked(rbacApiClient.getUserRolesSummary)
      mockGetUserRolesSummary.mockResolvedValue(mockUserRolesSummary)

      const { result } = renderHook(() => useUserRolesSummary(1), {
        wrapper: createWrapper(),
      })

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      expect(result.current.data).toEqual(mockUserRolesSummary)
      expect(mockGetUserRolesSummary).toHaveBeenCalledWith(1)
    })
  })

  describe('useAssignRoleToUser', () => {
    it('should assign role to user successfully', async () => {
      const mockAssignRoleToUser = vi.mocked(rbacApiClient.assignRoleToUser)
      const assignmentData = {
        name: 'Test Assignment',
        assignment_context: 'Testing',
      }

      mockAssignRoleToUser.mockResolvedValue({
        ...mockUserRoles[0],
        id: 2,
        name: 'Test Assignment',
        assignment_context: 'Testing',
      })

      const { result } = renderHook(() => useAssignRoleToUser(), {
        wrapper: createWrapper(),
      })

      await result.current.mutateAsync({ 
        userId: 1, 
        roleId: 2, 
        data: assignmentData 
      })

      expect(mockAssignRoleToUser).toHaveBeenCalledWith(1, 2, assignmentData)
      expect(result.current.isSuccess).toBe(true)
    })
  })

  describe('useRemoveRoleFromUser', () => {
    it('should remove role from user successfully', async () => {
      const mockRemoveRoleFromUser = vi.mocked(rbacApiClient.removeRoleFromUser)
      mockRemoveRoleFromUser.mockResolvedValue(undefined)

      const { result } = renderHook(() => useRemoveRoleFromUser(), {
        wrapper: createWrapper(),
      })

      await result.current.mutateAsync({ userId: 1, roleId: 2 })

      expect(mockRemoveRoleFromUser).toHaveBeenCalledWith(1, 2)
      expect(result.current.isSuccess).toBe(true)
    })
  })

  describe('useUserHasRole', () => {
    it('should check if user has role successfully', async () => {
      const mockCheckUserHasRole = vi.mocked(rbacApiClient.checkUserHasRole)
      mockCheckUserHasRole.mockResolvedValue(true)

      const { result } = renderHook(() => useUserHasRole(1, 'Admin'), {
        wrapper: createWrapper(),
      })

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      expect(result.current.data).toBe(true)
      expect(mockCheckUserHasRole).toHaveBeenCalledWith(1, 'Admin')
    })

    it('should not check when userId or roleName is empty', () => {
      const mockCheckUserHasRole = vi.mocked(rbacApiClient.checkUserHasRole)

      const { result } = renderHook(() => useUserHasRole(0, ''), {
        wrapper: createWrapper(),
      })

      expect(result.current.isLoading).toBe(false)
      expect(mockCheckUserHasRole).not.toHaveBeenCalled()
    })
  })

  describe('useUserHasPermission', () => {
    it('should check if user has permission successfully', async () => {
      const mockCheckUserHasPermission = vi.mocked(rbacApiClient.checkUserHasPermission)
      mockCheckUserHasPermission.mockResolvedValue(true)

      const { result } = renderHook(() => useUserHasPermission(1, 'read'), {
        wrapper: createWrapper(),
      })

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      expect(result.current.data).toBe(true)
      expect(mockCheckUserHasPermission).toHaveBeenCalledWith(1, 'read')
    })

    it('should not check when userId or permission is empty', () => {
      const mockCheckUserHasPermission = vi.mocked(rbacApiClient.checkUserHasPermission)

      const { result } = renderHook(() => useUserHasPermission(0, ''), {
        wrapper: createWrapper(),
      })

      expect(result.current.isLoading).toBe(false)
      expect(mockCheckUserHasPermission).not.toHaveBeenCalled()
    })
  })
})