'use client'

import { AuthLayout } from '@/components/auth/layouts/AuthLayout'
import { LoginForm } from '@/components/auth/LoginForm'
import { RouteGuard } from '@/components/auth/RouteGuard'
import Link from 'next/link'
import { useRouter, useSearchParams } from 'next/navigation'
import { useEffect, useState } from 'react'

function LoginContent() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const [showRegistrationSuccess, setShowRegistrationSuccess] = useState(false)

  useEffect(() => {
    const message = searchParams.get('message')
    if (message === 'registration-success') {
      setShowRegistrationSuccess(true)
      // Clear the message after showing it
      setTimeout(() => setShowRegistrationSuccess(false), 5000)
    }
  }, [searchParams])

  const handleLoginSuccess = () => {
    router.push('/dashboard')
  }

  return (
    <AuthLayout
      title="Sign in to your account"
      subtitle="Access your engineering workspace and continue your projects."
      showBackToHome={false}
    >
      {/* Registration Success Message */}
      {showRegistrationSuccess && (
        <div
          className="mb-6 rounded-lg border border-success/20 bg-success/5 p-4"
          role="alert"
          aria-live="polite"
        >
          <div className="flex items-start">
            <div className="flex-shrink-0">
              <svg
                className="h-5 w-5 text-success"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                aria-hidden="true"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-semibold text-success">Registration Successful!</h3>
              <div className="mt-1 text-sm text-success/80">
                Your account has been created successfully. You can now sign in with your
                credentials.
              </div>
            </div>
          </div>
        </div>
      )}

      <LoginForm className="space-y-6" onSuccess={handleLoginSuccess} />

      {/* Footer Links */}
      <div className="mt-6 space-y-4 text-center">
        <p className="text-caption">
          Don&apos;t have an account?{' '}
          <Link
            href="/register"
            className="font-medium text-brand-secondary transition-colors hover:text-brand-secondary/80"
          >
            Create an account
          </Link>
        </p>
        <p className="text-caption">
          <Link
            href="/"
            className="font-medium text-neutral-600 transition-colors hover:text-neutral-900"
          >
            ← Return to homepage
          </Link>
        </p>
      </div>
    </AuthLayout>
  )
}

export default function LoginPage() {
  return (
    <RouteGuard requireAuth={false}>
      <LoginContent />
    </RouteGuard>
  )
}
