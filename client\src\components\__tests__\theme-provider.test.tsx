/**
 * @jest-environment jsdom
 */

import { render, screen, waitFor } from '@testing-library/react'
import { describe, expect, it, beforeEach, afterEach } from 'vitest'
import { UEDThemeProvider, useUEDTheme } from '../theme-provider'

// Mock next-themes
const mockSetTheme = vi.fn()
const mockUseTheme = {
  theme: 'light',
  setTheme: mockSetTheme,
  themes: ['light', 'dark', 'system'],
  systemTheme: 'light',
  resolvedTheme: 'light',
}

vi.mock('next-themes', () => ({
  ThemeProvider: ({ children }: { children: React.ReactNode }) => <div data-testid="theme-provider">{children}</div>,
  useTheme: () => mockUseTheme,
}))

// Test component that uses the theme context
function TestComponent() {
  const { theme, setTheme, themes, resolvedTheme, mounted } = useUEDTheme()
  
  return (
    <div data-testid="test-component">
      <div data-testid="theme">{theme}</div>
      <div data-testid="resolved-theme">{resolvedTheme}</div>
      <div data-testid="mounted">{mounted.toString()}</div>
      <div data-testid="themes">{themes.join(',')}</div>
      <button onClick={() => setTheme('dark')} data-testid="set-theme-button">
        Set Dark Theme
      </button>
    </div>
  )
}

describe('UEDThemeProvider', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  it('renders children correctly', () => {
    render(
      <UEDThemeProvider>
        <div data-testid="child">Test Child</div>
      </UEDThemeProvider>
    )

    expect(screen.getByTestId('child')).toBeInTheDocument()
    expect(screen.getByTestId('child')).toHaveTextContent('Test Child')
  })

  it('provides theme context to children', async () => {
    render(
      <UEDThemeProvider>
        <TestComponent />
      </UEDThemeProvider>
    )

    await waitFor(() => {
      expect(screen.getByTestId('test-component')).toBeInTheDocument()
    })

    expect(screen.getByTestId('theme')).toHaveTextContent('light')
    expect(screen.getByTestId('resolved-theme')).toHaveTextContent('light')
    expect(screen.getByTestId('themes')).toHaveTextContent('light,dark,system')
  })

  it('handles theme changes', async () => {
    render(
      <UEDThemeProvider>
        <TestComponent />
      </UEDThemeProvider>
    )

    const setThemeButton = screen.getByTestId('set-theme-button')
    setThemeButton.click()

    expect(mockSetTheme).toHaveBeenCalledWith('dark')
  })

  it('sets mounted state correctly', async () => {
    render(
      <UEDThemeProvider>
        <TestComponent />
      </UEDThemeProvider>
    )

    await waitFor(() => {
      expect(screen.getByTestId('mounted')).toHaveTextContent('true')
    })
  })

  it('applies custom theme configurations', () => {
    const customThemeConfigs = {
      light: {
        name: 'light',
        label: 'Light',
        colors: {
          primary: '#000000',
          secondary: '#ffffff',
          background: '#ffffff',
          foreground: '#000000',
          muted: '#f5f5f5',
          accent: '#e5e5e5',
          destructive: '#ff0000',
          border: '#e5e5e5',
          input: '#ffffff',
          ring: '#000000',
        },
      },
    }

    render(
      <UEDThemeProvider themeConfigs={customThemeConfigs}>
        <TestComponent />
      </UEDThemeProvider>
    )

    expect(screen.getByTestId('test-component')).toBeInTheDocument()
  })

  it('handles error boundary fallback', () => {
    const ErrorComponent = () => {
      throw new Error('Test error')
    }

    const CustomFallback = ({ error }: { error: Error }) => (
      <div data-testid="error-fallback">Error: {error.message}</div>
    )

    render(
      <UEDThemeProvider fallback={CustomFallback}>
        <ErrorComponent />
      </UEDThemeProvider>
    )

    expect(screen.getByTestId('error-fallback')).toBeInTheDocument()
    expect(screen.getByTestId('error-fallback')).toHaveTextContent('Error: Test error')
  })

  it('provides default configuration when no custom config is provided', () => {
    render(
      <UEDThemeProvider>
        <TestComponent />
      </UEDThemeProvider>
    )

    expect(screen.getByTestId('test-component')).toBeInTheDocument()
  })
})

describe('useUEDTheme hook', () => {
  it('throws error when used outside provider', () => {
    const TestComponentOutsideProvider = () => {
      useUEDTheme()
      return <div>Test</div>
    }

    expect(() => {
      render(<TestComponentOutsideProvider />)
    }).toThrow('useUEDTheme must be used within a UEDThemeProvider')
  })
})

describe('Theme Error Boundary', () => {
  it('renders default fallback when no custom fallback is provided', () => {
    const ErrorComponent = () => {
      throw new Error('Test error')
    }

    render(
      <UEDThemeProvider>
        <ErrorComponent />
      </UEDThemeProvider>
    )

    // Should render the default fallback with light theme
    expect(document.querySelector('[data-theme="light"]')).toBeInTheDocument()
  })
})

describe('Theme configuration', () => {
  it('exports THEME_CONFIGS correctly', () => {
    // This test ensures the theme configurations are properly exported
    expect(() => {
      const { THEME_CONFIGS } = require('../theme-provider')
      expect(THEME_CONFIGS).toBeDefined()
    }).not.toThrow()
  })
})
