# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

The Ultimate Electrical Designer is a comprehensive electrical design platform built with FastAPI (Python) backend and Next.js (React) frontend. It follows a 5-layer architecture pattern with engineering-grade quality standards for professional electrical system design.

## Development Commands

### Backend (Server)
```bash
cd server

# Development server
poetry run python src/main.py run --reload

# Database operations
poetry run python src/main.py migrate
poetry run python src/main.py create-superuser 'Admin' 'Pass123' '<EMAIL>'
poetry run python src/main.py wipe-database --confirm  # DEVELOPMENT ONLY

# Testing
poetry run pytest                    # Run all tests
poetry run pytest --cov=src --cov-report=html  # Run with coverage
poetry run pytest tests/unit/       # Run unit tests only
poetry run pytest tests/integration/  # Run integration tests only
poetry run pytest -m "not performance"  # Skip performance tests

# Code quality
poetry run ruff check               # Linting (performance & documentation focus)
poetry run mypy src                 # Type checking
poetry run bandit -r src            # Security scanning
```

### Frontend (Client)
```bash
cd client

# Development server
npm run dev

# Build and quality checks
npm run build                       # Production build
npm run lint                        # ESLint
npm run type-check                  # TypeScript checking
npm run format                      # Prettier formatting

# Testing
npm run test                        # Unit tests (Vitest)
npm run test:coverage               # Unit tests with coverage
npm run test:e2e                    # E2E tests (Playwright)
npm run test:e2e:ui                 # E2E tests with UI
npx playwright install              # Install browser dependencies
```

## Architecture Overview

### 5-Layer Backend Architecture
```
src/
├── api/v1/                   # API routes and endpoints
├── core/
│   ├── models/              # SQLAlchemy ORM models
│   ├── services/            # Business logic layer
│   ├── repositories/        # Data access layer
│   ├── schemas/             # Pydantic validation schemas
│   ├── errors/              # Unified error handling
│   ├── security/            # Security validation
│   └── utils/               # Utilities and helpers
├── config/                  # Application configuration
├── middleware/              # Custom middleware
└── main.py                  # Application entry point
```

### Frontend Module Structure
```
src/
├── app/                     # Next.js App Router
├── components/
│   ├── ui/                 # shadcn/ui components
│   ├── common/             # Shared components
│   └── domain/             # Domain-specific components
├── modules/                # Feature modules (components, users, etc.)
├── hooks/                  # Custom React hooks
├── stores/                 # Zustand state management
└── lib/                    # Utilities and configurations
```

## Key Development Patterns

### Backend Patterns
- **Unified Error Handling**: All services use `@unified_error_handler` decorator
- **Repository Pattern**: Generic base repository with CRUD operations
- **Dependency Injection**: FastAPI dependencies for service/repository provisioning
- **Type Safety**: Full Pydantic validation and MyPy type checking
- **Soft Delete**: Audit trail with soft deletion patterns

### Frontend Patterns
- **Module-Based Architecture**: Each feature is a self-contained module
- **React Query**: Server state management with caching
- **Zustand**: Client-side state management
- **TypeScript**: Full type safety with strict mode
- **Component Composition**: Reusable UI components with consistent patterns

## Quality Standards

### Backend Quality Gates
- **Tests**: 85%+ coverage required, all tests must pass
- **Type Safety**: MyPy strict mode with no errors
- **Linting**: Ruff focused on performance and documentation
- **Security**: Bandit security scanning with no high-severity issues

### Frontend Quality Gates
- **Tests**: Unit tests with Vitest, E2E tests with Playwright
- **Type Safety**: TypeScript strict mode
- **Linting**: ESLint with Next.js rules
- **Formatting**: Prettier with consistent formatting

## Database Operations

### Development Database Setup
```bash
# Initialize database and run migrations
poetry run python src/main.py migrate

# Create admin user
poetry run python src/main.py create-superuser 'Admin' 'Pass123' '<EMAIL>'

# Reset database (DEVELOPMENT ONLY)
poetry run python src/main.py wipe-database --confirm
```

### Migration Management
- Use Alembic for database migrations
- Models are in `src/core/models/`
- Migrations are auto-generated from model changes
- Always review generated migrations before applying

## Testing Strategy

### Backend Testing
- **Unit Tests**: Individual component testing with pytest
- **Integration Tests**: Multi-component workflow testing
- **API Tests**: Complete endpoint testing with TestClient
- **Performance Tests**: Load testing with specific markers
- **Security Tests**: Security validation testing

### Frontend Testing
- **Unit Tests**: Component testing with Vitest + React Testing Library
- **Integration Tests**: Feature workflow testing
- **E2E Tests**: Full user workflow testing with Playwright
- **MSW Mocking**: Mock Service Worker for API mocking

## Security Considerations

### Authentication
- JWT-based authentication with proper validation
- Role-based access control (RBAC)
- Automatic token refresh handling

### Security Middleware
- Rate limiting per IP
- XSS protection
- CSRF protection
- Input sanitization
- Security headers on all responses

## Common Issues and Solutions

### Backend Issues
- **Database Connection**: Ensure PostgreSQL is running or use SQLite for development
- **Migration Conflicts**: Use `poetry run python src/main.py wipe-database --confirm` in development
- **Type Errors**: Check MyPy configuration in `pyproject.toml`

### Frontend Issues
- **Type Errors**: Run `npm run type-check` to identify TypeScript issues
- **Build Failures**: Check for missing dependencies or ESLint errors
- **Test Failures**: Ensure browser dependencies are installed for Playwright

## Development Workflow

1. **Feature Development**: Use the 5-phase methodology from the README
2. **Code Quality**: Run linting and type checking before commits
3. **Testing**: Ensure all tests pass before submitting changes
4. **Documentation**: Update relevant documentation for new features
5. **Database Changes**: Generate and review migrations for model changes

## Performance Optimization

- **Backend**: Use performance monitoring decorators on services
- **Frontend**: Implement React Query for server state caching
- **Database**: Leverage existing indexing and query optimization
- **Caching**: Utilize the built-in caching middleware

## Electrical Engineering Domain

This is a specialized electrical design application with:
- **Standards Compliance**: IEEE/IEC/EN standards validation
- **Heat Tracing Calculations**: Thermal analysis and cable selection
- **Component Management**: Comprehensive electrical component catalog
- **Professional Reports**: Engineering-grade documentation generation