/**
 * Unit tests for Settings utilities
 * Ultimate Electrical Designer - Settings & User Preferences
 */

import { describe, it, expect, beforeEach } from 'vitest'
import {
  getCategoryConfig,
  getAllCategories,
  searchSettings,
  getThemeConfig,
  getLanguageConfig,
  getTimezoneConfig,
  formatPreferenceValue,
  validatePreferenceField,
  getDefaultPreferenceValue,
  arePreferencesEqual,
  mergePreferencesWithDefaults,
  generateExportFilename,
  parseImportFile,
  debounce,
  throttle,
  deepClone,
} from '../utils'
import {
  SETTINGS_CATEGORIES,
  THEME_CONFIGS,
  LANGUAGE_CONFIGS,
  TIMEZONE_CONFIGS,
} from '../constants'
import type { SettingsCategory, UserPreferences } from '../types'

describe('Settings Utils', () => {
  describe('getCategoryConfig', () => {
    it('should return category configuration for valid category', () => {
      const config = getCategoryConfig('account')
      expect(config).toBeDefined()
      expect(config?.id).toBe('account')
      expect(config?.label).toBe('Account')
    })

    it('should return undefined for invalid category', () => {
      const config = getCategoryConfig('invalid' as SettingsCategory)
      expect(config).toBeUndefined()
    })
  })

  describe('getAllCategories', () => {
    it('should return all categories', () => {
      const categories = getAllCategories()
      expect(categories).toHaveLength(SETTINGS_CATEGORIES.length)
      expect(categories[0]).toHaveProperty('id')
      expect(categories[0]).toHaveProperty('label')
      expect(categories[0]).toHaveProperty('sections')
    })
  })

  describe('searchSettings', () => {
    it('should find matching categories and fields', () => {
      const results = searchSettings('theme')
      expect(results.categories.length).toBeGreaterThan(0)
      expect(results.matches.length).toBeGreaterThan(0)
    })

    it('should return empty results for non-matching query', () => {
      const results = searchSettings('nonexistent')
      expect(results.categories).toHaveLength(0)
      expect(results.matches).toHaveLength(0)
    })

    it('should be case insensitive', () => {
      const lowerResults = searchSettings('theme')
      const upperResults = searchSettings('THEME')
      expect(lowerResults.matches.length).toBe(upperResults.matches.length)
    })
  })

  describe('getThemeConfig', () => {
    it('should return theme configuration for valid theme', () => {
      const config = getThemeConfig('light')
      expect(config).toBeDefined()
      expect(config?.name).toBe('light')
      expect(config?.colors).toBeDefined()
    })

    it('should return undefined for invalid theme', () => {
      const config = getThemeConfig('invalid')
      expect(config).toBeUndefined()
    })
  })

  describe('getLanguageConfig', () => {
    it('should return language configuration for valid language', () => {
      const config = getLanguageConfig('en')
      expect(config).toBeDefined()
      expect(config?.code).toBe('en')
      expect(config?.name).toBe('English')
    })

    it('should return undefined for invalid language', () => {
      const config = getLanguageConfig('invalid')
      expect(config).toBeUndefined()
    })
  })

  describe('getTimezoneConfig', () => {
    it('should return timezone configuration for valid timezone', () => {
      const config = getTimezoneConfig('UTC')
      expect(config).toBeDefined()
      expect(config?.value).toBe('UTC')
      expect(config?.label).toBe('UTC')
    })

    it('should return undefined for invalid timezone', () => {
      const config = getTimezoneConfig('invalid')
      expect(config).toBeUndefined()
    })
  })

  describe('formatPreferenceValue', () => {
    it('should format theme values correctly', () => {
      expect(formatPreferenceValue('theme', 'light')).toBe('Light')
      expect(formatPreferenceValue('theme', 'dark')).toBe('Dark')
      expect(formatPreferenceValue('theme', 'system')).toBe('System Default')
    })

    it('should format language values correctly', () => {
      expect(formatPreferenceValue('language', 'en')).toBe('English')
      expect(formatPreferenceValue('language', 'es')).toBe('Spanish')
    })

    it('should format boolean values correctly', () => {
      expect(formatPreferenceValue('notifications_enabled', true)).toBe('Enabled')
      expect(formatPreferenceValue('notifications_enabled', false)).toBe('Disabled')
    })

    it('should format numeric values correctly', () => {
      expect(formatPreferenceValue('auto_save_interval', 300)).toBe('300 seconds')
      expect(formatPreferenceValue('calculation_precision', 2)).toBe('2 decimal places')
    })
  })

  describe('validatePreferenceField', () => {
    it('should validate auto_save_interval correctly', () => {
      expect(validatePreferenceField('auto_save_interval', 300).isValid).toBe(true)
      expect(validatePreferenceField('auto_save_interval', 10).isValid).toBe(false)
      expect(validatePreferenceField('auto_save_interval', 4000).isValid).toBe(false)
      expect(validatePreferenceField('auto_save_interval', 'invalid').isValid).toBe(false)
    })

    it('should validate calculation_precision correctly', () => {
      expect(validatePreferenceField('calculation_precision', 2).isValid).toBe(true)
      expect(validatePreferenceField('calculation_precision', -1).isValid).toBe(false)
      expect(validatePreferenceField('calculation_precision', 10).isValid).toBe(false)
      expect(validatePreferenceField('calculation_precision', 'invalid').isValid).toBe(false)
    })

    it('should validate theme correctly', () => {
      expect(validatePreferenceField('theme', 'light').isValid).toBe(true)
      expect(validatePreferenceField('theme', 'dark').isValid).toBe(true)
      expect(validatePreferenceField('theme', 'system').isValid).toBe(true)
      expect(validatePreferenceField('theme', 'invalid').isValid).toBe(false)
    })

    it('should validate language correctly', () => {
      expect(validatePreferenceField('language', 'en').isValid).toBe(true)
      expect(validatePreferenceField('language', 'es').isValid).toBe(true)
      expect(validatePreferenceField('language', 'invalid').isValid).toBe(false)
    })
  })

  describe('getDefaultPreferenceValue', () => {
    it('should return correct default values', () => {
      expect(getDefaultPreferenceValue('theme')).toBe('system')
      expect(getDefaultPreferenceValue('language')).toBe('en')
      expect(getDefaultPreferenceValue('auto_save_interval')).toBe(300)
      expect(getDefaultPreferenceValue('notifications_enabled')).toBe(true)
    })
  })

  describe('arePreferencesEqual', () => {
    it('should return true for equal preferences', () => {
      const prefs1 = { theme: 'light', language: 'en' }
      const prefs2 = { theme: 'light', language: 'en' }
      expect(arePreferencesEqual(prefs1, prefs2)).toBe(true)
    })

    it('should return false for different preferences', () => {
      const prefs1 = { theme: 'light', language: 'en' }
      const prefs2 = { theme: 'dark', language: 'en' }
      expect(arePreferencesEqual(prefs1, prefs2)).toBe(false)
    })

    it('should return false for different number of properties', () => {
      const prefs1 = { theme: 'light' }
      const prefs2 = { theme: 'light', language: 'en' }
      expect(arePreferencesEqual(prefs1, prefs2)).toBe(false)
    })
  })

  describe('mergePreferencesWithDefaults', () => {
    it('should merge preferences with defaults', () => {
      const preferences = { theme: 'dark' }
      const merged = mergePreferencesWithDefaults(preferences)

      expect(merged.theme).toBe('dark')
      expect(merged.language).toBe('en') // default
      expect(merged.auto_save_interval).toBe(300) // default
    })

    it('should preserve all provided preferences', () => {
      const preferences = {
        theme: 'dark',
        language: 'es',
        auto_save_interval: 600,
      }
      const merged = mergePreferencesWithDefaults(preferences)

      expect(merged.theme).toBe('dark')
      expect(merged.language).toBe('es')
      expect(merged.auto_save_interval).toBe(600)
    })
  })

  describe('generateExportFilename', () => {
    it('should generate filename with default prefix', () => {
      const filename = generateExportFilename()
      expect(filename).toMatch(/^ued-settings-\d{4}-\d{2}-\d{2}-\d{2}-\d{2}-\d{2}\.json$/)
    })

    it('should generate filename with custom prefix', () => {
      const filename = generateExportFilename('custom')
      expect(filename).toMatch(/^custom-\d{4}-\d{2}-\d{2}-\d{2}-\d{2}-\d{2}\.json$/)
    })
  })

  describe('parseImportFile', () => {
    it('should parse valid JSON file', async () => {
      const jsonData = { preferences: { theme: 'dark' } }
      const blob = new Blob([JSON.stringify(jsonData)], { type: 'application/json' })
      const file = new File([blob], 'test.json', { type: 'application/json' })

      const result = await parseImportFile(file)
      expect(result).toEqual(jsonData)
    })

    it('should reject invalid JSON file', async () => {
      const blob = new Blob(['invalid json'], { type: 'application/json' })
      const file = new File([blob], 'test.json', { type: 'application/json' })

      await expect(parseImportFile(file)).rejects.toThrow('Invalid JSON file')
    })
  })

  describe('debounce', () => {
    it('should debounce function calls', (done) => {
      let callCount = 0
      const debouncedFn = debounce(() => {
        callCount++
      }, 100)

      debouncedFn()
      debouncedFn()
      debouncedFn()

      setTimeout(() => {
        expect(callCount).toBe(1)
        done()
      }, 150)
    })
  })

  describe('throttle', () => {
    it('should throttle function calls', (done) => {
      let callCount = 0
      const throttledFn = throttle(() => {
        callCount++
      }, 100)

      throttledFn()
      throttledFn()
      throttledFn()

      expect(callCount).toBe(1)

      setTimeout(() => {
        throttledFn()
        expect(callCount).toBe(2)
        done()
      }, 150)
    })
  })

  describe('deepClone', () => {
    it('should clone primitive values', () => {
      expect(deepClone(42)).toBe(42)
      expect(deepClone('hello')).toBe('hello')
      expect(deepClone(true)).toBe(true)
      expect(deepClone(null)).toBe(null)
    })

    it('should clone arrays', () => {
      const original = [1, 2, { a: 3 }]
      const cloned = deepClone(original)

      expect(cloned).toEqual(original)
      expect(cloned).not.toBe(original)
      expect(cloned[2]).not.toBe(original[2])
    })

    it('should clone objects', () => {
      const original = { a: 1, b: { c: 2 } }
      const cloned = deepClone(original)

      expect(cloned).toEqual(original)
      expect(cloned).not.toBe(original)
      expect(cloned.b).not.toBe(original.b)
    })

    it('should clone dates', () => {
      const original = new Date('2025-01-01')
      const cloned = deepClone(original)

      expect(cloned).toEqual(original)
      expect(cloned).not.toBe(original)
    })
  })
})
