/**
 * BulkOperationsPanel Organism
 * Advanced bulk operations component with progress tracking and accessibility
 */

import React, { useState } from 'react'
import { Button } from '@/components/ui/button'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Progress } from '@/components/ui/progress'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import { cn } from '@/lib/utils'
import {
  Edit,
  Trash2,
  Copy,
  Download,
  Upload,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Loader2,
  FileText,
  Archive,
  Star,
  StarOff,
} from 'lucide-react'
import { useComponentStoreEnhanced } from '../../hooks/useComponentStoreEnhanced'
import type { ComponentRead, BulkOperationState } from '../../schemas'

export interface BulkOperationsPanelProps {
  components: ComponentRead[]
  className?: string
  onBulkEdit?: (componentIds: number[], updates: any) => Promise<void>
  onBulkDelete?: (componentIds: number[]) => Promise<void>
  onBulkDuplicate?: (componentIds: number[]) => Promise<void>
  onBulkExport?: (componentIds: number[]) => Promise<void>
  onBulkTogglePreferred?: (componentIds: number[], preferred: boolean) => Promise<void>
  onBulkArchive?: (componentIds: number[]) => Promise<void>
  'data-testid'?: string
}

export const BulkOperationsPanel = React.forwardRef<HTMLDivElement, BulkOperationsPanelProps>(
  (
    {
      components,
      className,
      onBulkEdit,
      onBulkDelete,
      onBulkDuplicate,
      onBulkExport,
      onBulkTogglePreferred,
      onBulkArchive,
      'data-testid': testId,
      ...props
    },
    ref
  ) => {
    const { bulkState, setBulkOperation, updateBulkProgress, clearSelection, getSelectedCount } =
      useComponentStoreEnhanced()

    const [isDialogOpen, setIsDialogOpen] = useState(false)
    const [currentOperation, setCurrentOperation] = useState<string | null>(null)
    const [operationData, setOperationData] = useState<any>({})

    const selectedCount = getSelectedCount()
    const selectedComponents = components.filter((c) => bulkState.selected_ids.includes(c.id))

    // Handle operation start
    const handleOperationStart = (operation: string, data?: any) => {
      setCurrentOperation(operation)
      setOperationData(data || {})
      setBulkOperation(operation as BulkOperationState['operation'])

      // Show confirmation dialog for destructive operations
      if (['delete', 'archive'].includes(operation)) {
        setIsDialogOpen(true)
      } else {
        executeOperation(operation, data)
      }
    }

    // Execute the operation
    const executeOperation = async (operation: string, data?: any) => {
      try {
        updateBulkProgress(0)

        switch (operation) {
          case 'edit':
            if (onBulkEdit) {
              await onBulkEdit(bulkState.selected_ids, data)
            }
            break
          case 'delete':
            if (onBulkDelete) {
              await onBulkDelete(bulkState.selected_ids)
            }
            break
          case 'duplicate':
            if (onBulkDuplicate) {
              await onBulkDuplicate(bulkState.selected_ids)
            }
            break
          case 'export':
            if (onBulkExport) {
              await onBulkExport(bulkState.selected_ids)
            }
            break
          case 'toggle_preferred':
            if (onBulkTogglePreferred) {
              await onBulkTogglePreferred(bulkState.selected_ids, data.preferred)
            }
            break
          case 'archive':
            if (onBulkArchive) {
              await onBulkArchive(bulkState.selected_ids)
            }
            break
        }

        updateBulkProgress(100)
        clearSelection()
        setIsDialogOpen(false)
        setCurrentOperation(null)
      } catch (error) {
        console.error('Bulk operation failed:', error)
        // Handle error state
      }
    }

    // Handle dialog confirm
    const handleDialogConfirm = () => {
      if (currentOperation) {
        executeOperation(currentOperation, operationData)
      }
    }

    // Handle dialog cancel
    const handleDialogCancel = () => {
      setIsDialogOpen(false)
      setCurrentOperation(null)
      setOperationData({})
      setBulkOperation(undefined)
    }

    // Get operation details
    const getOperationDetails = (operation: string) => {
      const details = {
        edit: {
          title: 'Bulk Edit Components',
          description: `Edit ${selectedCount} selected component${selectedCount !== 1 ? 's' : ''}`,
          icon: Edit,
          color: 'text-blue-600',
          destructive: false,
        },
        delete: {
          title: 'Delete Components',
          description: `Permanently delete ${selectedCount} selected component${selectedCount !== 1 ? 's' : ''}`,
          icon: Trash2,
          color: 'text-red-600',
          destructive: true,
        },
        duplicate: {
          title: 'Duplicate Components',
          description: `Create copies of ${selectedCount} selected component${selectedCount !== 1 ? 's' : ''}`,
          icon: Copy,
          color: 'text-green-600',
          destructive: false,
        },
        export: {
          title: 'Export Components',
          description: `Export ${selectedCount} selected component${selectedCount !== 1 ? 's' : ''} to file`,
          icon: Download,
          color: 'text-purple-600',
          destructive: false,
        },
        toggle_preferred: {
          title: 'Toggle Preferred Status',
          description: `Update preferred status for ${selectedCount} selected component${selectedCount !== 1 ? 's' : ''}`,
          icon: Star,
          color: 'text-yellow-600',
          destructive: false,
        },
        archive: {
          title: 'Archive Components',
          description: `Archive ${selectedCount} selected component${selectedCount !== 1 ? 's' : ''}`,
          icon: Archive,
          color: 'text-gray-600',
          destructive: true,
        },
      }

      return details[operation as keyof typeof details] || details.edit
    }

    if (selectedCount === 0) {
      return null
    }

    const operationDetails = currentOperation ? getOperationDetails(currentOperation) : null

    return (
      <>
        <div
          ref={ref}
          className={cn('sticky top-0 z-10 border-b border-gray-200 bg-white p-4', className)}
          data-testid={testId || 'bulk-operations-panel'}
          {...props}
        >
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Badge variant="secondary" className="text-sm">
                {selectedCount} selected
              </Badge>

              <span className="text-sm text-gray-600">Bulk operations available</span>
            </div>

            <div className="flex items-center gap-2">
              {/* Edit */}
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleOperationStart('edit')}
                disabled={bulkState.is_processing}
                data-testid={`${testId || 'bulk-operations'}-edit`}
              >
                <Edit className="mr-2 h-4 w-4" />
                Edit
              </Button>

              {/* Duplicate */}
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleOperationStart('duplicate')}
                disabled={bulkState.is_processing}
                data-testid={`${testId || 'bulk-operations'}-duplicate`}
              >
                <Copy className="mr-2 h-4 w-4" />
                Duplicate
              </Button>

              {/* Toggle Preferred */}
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleOperationStart('toggle_preferred', { preferred: true })}
                disabled={bulkState.is_processing}
                data-testid={`${testId || 'bulk-operations'}-preferred`}
              >
                <Star className="mr-2 h-4 w-4" />
                Preferred
              </Button>

              {/* Export */}
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleOperationStart('export')}
                disabled={bulkState.is_processing}
                data-testid={`${testId || 'bulk-operations'}-export`}
              >
                <Download className="mr-2 h-4 w-4" />
                Export
              </Button>

              <Separator orientation="vertical" className="h-6" />

              {/* Archive */}
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleOperationStart('archive')}
                disabled={bulkState.is_processing}
                data-testid={`${testId || 'bulk-operations'}-archive`}
              >
                <Archive className="mr-2 h-4 w-4" />
                Archive
              </Button>

              {/* Delete */}
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleOperationStart('delete')}
                disabled={bulkState.is_processing}
                className="text-red-600 hover:bg-red-50 hover:text-red-700"
                data-testid={`${testId || 'bulk-operations'}-delete`}
              >
                <Trash2 className="mr-2 h-4 w-4" />
                Delete
              </Button>

              {/* Clear Selection */}
              <Button
                variant="ghost"
                size="sm"
                onClick={clearSelection}
                disabled={bulkState.is_processing}
                data-testid={`${testId || 'bulk-operations'}-clear`}
              >
                Clear
              </Button>
            </div>
          </div>

          {/* Progress Bar */}
          {bulkState.is_processing && (
            <div className="mt-4 space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span>Processing {currentOperation}...</span>
                <span>{bulkState.progress}%</span>
              </div>
              <Progress value={bulkState.progress} className="h-2" />
            </div>
          )}

          {/* Results */}
          {bulkState.results.length > 0 && (
            <div className="mt-4 space-y-2">
              <div className="text-sm font-medium">Operation Results:</div>
              <div className="space-y-1">
                {bulkState.results.map((result, index) => (
                  <div
                    key={index}
                    className={cn(
                      'flex items-center gap-2 text-sm',
                      result.success ? 'text-green-600' : 'text-red-600'
                    )}
                  >
                    {result.success ? (
                      <CheckCircle className="h-4 w-4" />
                    ) : (
                      <XCircle className="h-4 w-4" />
                    )}
                    <span>
                      Component {result.id}:{' '}
                      {result.message || (result.success ? 'Success' : 'Failed')}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Confirmation Dialog */}
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle className="flex items-center gap-2">
                {operationDetails && (
                  <operationDetails.icon className={cn('h-5 w-5', operationDetails.color)} />
                )}
                {operationDetails?.title}
              </DialogTitle>
              <DialogDescription>{operationDetails?.description}</DialogDescription>
            </DialogHeader>

            {operationDetails?.destructive && (
              <Alert>
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  This action cannot be undone. Please confirm that you want to proceed.
                </AlertDescription>
              </Alert>
            )}

            {/* Selected Components Preview */}
            <div className="max-h-40 overflow-y-auto">
              <Label className="text-sm font-medium">Selected Components:</Label>
              <div className="mt-2 space-y-1">
                {selectedComponents.slice(0, 10).map((component) => (
                  <div key={component.id} className="text-sm text-gray-600">
                    • {component.name} ({component.manufacturer})
                  </div>
                ))}
                {selectedComponents.length > 10 && (
                  <div className="text-sm text-gray-500">
                    ... and {selectedComponents.length - 10} more
                  </div>
                )}
              </div>
            </div>

            <DialogFooter>
              <Button
                variant="outline"
                onClick={handleDialogCancel}
                disabled={bulkState.is_processing}
              >
                Cancel
              </Button>
              <Button
                variant={operationDetails?.destructive ? 'destructive' : 'default'}
                onClick={handleDialogConfirm}
                disabled={bulkState.is_processing}
              >
                {bulkState.is_processing ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Processing...
                  </>
                ) : (
                  'Confirm'
                )}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </>
    )
  }
)

BulkOperationsPanel.displayName = 'BulkOperationsPanel'
