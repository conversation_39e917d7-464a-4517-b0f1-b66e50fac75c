/**
 * Playwright Global Setup
 *
 * This file is executed once before all E2E tests. It is responsible for
 * initializing and starting the Mock Service Worker (MSW) server, ensuring
 * that all API requests made during the tests are intercepted and handled
 * by the mock handlers instead of hitting a live backend.
 */

import { startMockServer } from '../mocks/server'

async function globalSetup() {
  // Start the MSW server before running any tests
  startMockServer()
}

export default globalSetup
