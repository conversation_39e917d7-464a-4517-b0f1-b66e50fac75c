# src/core/repositories/general/user_preference_repository.py
"""User Preference Repository.

This module provides data access layer for UserPreference entities,
extending the base repository with user preference-specific query methods
and operations.
"""

from datetime import UTC
from typing import Any, Optional, Dict

from sqlalchemy import and_, select, update
from sqlalchemy.orm import Session

from src.config.logging_config import logger

# Unified systems imports
from src.core.errors.unified_error_handler import handle_repository_errors
from src.core.models.general.user import UserPreference
from src.core.monitoring.unified_performance_monitor import (
    monitor_repository_performance,
)
from src.core.repositories.base_repository import BaseRepository


class UserPreferenceRepository(BaseRepository[UserPreference]):
    """Repository for UserPreference entity data access operations.

    Extends BaseRepository with user preference-specific query methods and
    enhanced error handling for user preference operations.
    """

    def __init__(self, db_session: Session):
        """Initialize the UserPreference repository.

        Args:
            db_session: SQLAlchemy database session

        """
        super().__init__(db_session, UserPreference)
        logger.debug("UserPreferenceRepository initialized")

    @handle_repository_errors("user_preference")
    @monitor_repository_performance("user_preference")
    def get_by_user_id(self, user_id: int) -> Optional[UserPreference]:
        """Get user preferences by user ID.

        Args:
            user_id: User ID

        Returns:
            Optional[UserPreference]: User preferences or None if not found

        Raises:
            DatabaseError: If database operation fails

        """
        logger.debug(f"Retrieving preferences for user {user_id}")

        stmt = select(self.model).where(
            and_(
                self.model.user_id == user_id,
                self.model.is_deleted == False,
            )
        )
        result = self.db_session.scalar(stmt)

        if result:
            logger.debug(f"Preferences found for user {user_id}")
        else:
            logger.debug(f"No preferences found for user {user_id}")

        return result

    @handle_repository_errors("user_preference")
    @monitor_repository_performance("user_preference")
    def soft_delete_preferences(
        self, user_id: int, deleted_by_user_id: Optional[int] = None
    ) -> bool:
        """Soft delete user preferences.

        Args:
            user_id: User ID
            deleted_by_user_id: ID of user performing the deletion

        Returns:
            bool: True if deletion was successful, False if preferences not found

        Raises:
            DatabaseError: If database operation fails

        """
        logger.debug(f"Soft deleting preferences for user {user_id}")

        from datetime import datetime

        update_data = {
            "is_deleted": True,
            "deleted_at": datetime.now(UTC),
            "deleted_by_user_id": deleted_by_user_id,
        }

        stmt = (
            update(self.model)
            .where(and_(self.model.user_id == user_id, self.model.is_deleted == False))
            .values(**update_data)
        )

        result = self.db_session.execute(stmt)

        if result.rowcount > 0:
            logger.debug(f"Preferences soft deleted for user {user_id}")
            return True
        logger.debug(f"No preferences found for user {user_id} or already deleted")
        return False

    @handle_repository_errors("user_preference")
    @monitor_repository_performance("user_preference")
    def create_or_update_preferences(
        self, user_id: int, preferences_data: Dict[str, Any]
    ) -> UserPreference:
        """Create or update user preferences.

        Args:
            user_id: User ID
            preferences_data: Dictionary containing preference data

        Returns:
            UserPreference: Created or updated user preferences

        Raises:
            DatabaseError: If database operation fails

        """
        logger.debug(f"Creating or updating preferences for user {user_id}")

        # Check if preferences already exist
        existing_preferences = self.get_by_user_id(user_id)

        if existing_preferences:
            # Update existing preferences
            logger.debug(f"Updating existing preferences for user {user_id}")

            # Update the preferences data
            for key, value in preferences_data.items():
                if hasattr(existing_preferences, key):
                    setattr(existing_preferences, key, value)

            # Update timestamp
            from datetime import datetime
            from datetime import timezone as tz

            existing_preferences.updated_at = datetime.now(tz.utc)

            self.db_session.flush()
            logger.debug(f"Updated preferences for user {user_id}")
            return existing_preferences
        else:
            # Create new preferences
            logger.debug(f"Creating new preferences for user {user_id}")

            # Add user_id to the data
            preferences_data["user_id"] = user_id

            # Create new preferences using the base repository create method
            new_preferences = self.create(preferences_data)

            logger.debug(f"Created new preferences for user {user_id}")
            return new_preferences
