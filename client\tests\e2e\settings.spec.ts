/**
 * E2E tests for Settings module
 * Ultimate Electrical Designer - Settings & User Preferences
 */

import { test, expect } from '@playwright/test'

test.describe('Settings Module E2E Tests', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to settings page
    await page.goto('/settings')

    // Wait for the page to load
    await page.waitForLoadState('networkidle')
  })

  test.describe('Settings Panel Navigation', () => {
    test('should display settings panel with all categories', async ({ page }) => {
      // Check main heading
      await expect(page.getByRole('heading', { name: 'Settings' })).toBeVisible()

      // Check description
      await expect(page.getByText('Manage your account settings and preferences')).toBeVisible()

      // Check all category tabs are present
      await expect(page.getByRole('tab', { name: 'Account' })).toBeVisible()
      await expect(page.getByRole('tab', { name: 'Appearance' })).toBeVisible()
      await expect(page.getByRole('tab', { name: 'Notifications' })).toBeVisible()
      await expect(page.getByRole('tab', { name: 'Privacy' })).toBeVisible()
      await expect(page.getByRole('tab', { name: 'Advanced' })).toBeVisible()
      await expect(page.getByRole('tab', { name: 'Engineering Calculations' })).toBeVisible()
    })

    test('should navigate between categories', async ({ page }) => {
      // Start on Account tab (default)
      await expect(page.getByRole('tab', { name: 'Account' })).toHaveAttribute(
        'aria-selected',
        'true'
      )

      // Click on Appearance tab
      await page.getByRole('tab', { name: 'Appearance' }).click()
      await expect(page.getByRole('tab', { name: 'Appearance' })).toHaveAttribute(
        'aria-selected',
        'true'
      )

      // Check that appearance content is visible
      await expect(page.getByText('Theme')).toBeVisible()
      await expect(page.getByText('Color scheme and visual appearance')).toBeVisible()

      // Click on Engineering tab
      await page.getByRole('tab', { name: 'Engineering Calculations' }).click()
      await expect(page.getByRole('tab', { name: 'Engineering Calculations' })).toHaveAttribute(
        'aria-selected',
        'true'
      )

      // Check that engineering content is visible
      await expect(page.getByText('Units & Measurements')).toBeVisible()
    })

    test('should support keyboard navigation', async ({ page }) => {
      // Focus on first tab
      await page.getByRole('tab', { name: 'Account' }).focus()

      // Navigate with arrow keys
      await page.keyboard.press('ArrowRight')
      await expect(page.getByRole('tab', { name: 'Appearance' })).toBeFocused()

      await page.keyboard.press('ArrowRight')
      await expect(page.getByRole('tab', { name: 'Notifications' })).toBeFocused()

      // Navigate back
      await page.keyboard.press('ArrowLeft')
      await expect(page.getByRole('tab', { name: 'Appearance' })).toBeFocused()
    })
  })

  test.describe('Settings Search', () => {
    test('should search for settings', async ({ page }) => {
      // Find search input
      const searchInput = page.getByPlaceholder('Search settings...')
      await expect(searchInput).toBeVisible()

      // Type search query
      await searchInput.fill('theme')

      // Wait for search results
      await page.waitForTimeout(500) // Debounce delay

      // Check that search results appear
      await expect(page.getByText('Settings')).toBeVisible() // Results section

      // Clear search
      await page.getByRole('button', { name: 'Clear search' }).click()
      await expect(searchInput).toHaveValue('')
    })

    test('should navigate to setting from search results', async ({ page }) => {
      // Search for a specific setting
      await page.getByPlaceholder('Search settings..').fill('notifications')
      await page.waitForTimeout(500)

      // Click on a search result
      await page.getByText('Email Notifications').click()

      // Should navigate to notifications category
      await expect(page.getByRole('tab', { name: 'Notifications' })).toHaveAttribute(
        'aria-selected',
        'true'
      )
    })
  })

  test.describe('Settings Forms', () => {
    test('should update appearance settings', async ({ page }) => {
      // Navigate to appearance tab
      await page.getByRole('tab', { name: 'Appearance' }).click()

      // Change theme
      await page.getByLabel('Color Theme').selectOption('dark')

      // Change language
      await page.getByLabel('Language').selectOption('es')

      // Change time format
      await page.getByLabel('Time Format').selectOption('12h')

      // Check that unsaved changes indicator appears
      await expect(page.getByText(/unsaved change/)).toBeVisible()

      // Save changes
      await page.getByRole('button', { name: 'Save Changes' }).click()

      // Wait for save confirmation
      await expect(page.getByText('Preferences updated successfully')).toBeVisible()

      // Check that unsaved changes indicator disappears
      await expect(page.getByText('All saved')).toBeVisible()
    })

    test('should toggle boolean settings', async ({ page }) => {
      // Navigate to notifications tab
      await page.getByRole('tab', { name: 'Notifications' }).click()

      // Toggle notifications
      const notificationsToggle = page.getByLabel('Enable Notifications')
      await notificationsToggle.click()

      // Toggle email notifications
      const emailToggle = page.getByLabel('Email Notifications')
      await emailToggle.click()

      // Save changes
      await page.getByRole('button', { name: 'Save Changes' }).click()

      // Verify save
      await expect(page.getByText('Preferences updated successfully')).toBeVisible()
    })

    test('should validate numeric inputs', async ({ page }) => {
      // Navigate to advanced tab
      await page.getByRole('tab', { name: 'Advanced' }).click()

      // Enter invalid auto save interval
      await page.getByLabel('Auto Save Interval (seconds)').fill('10')

      // Check validation error
      await expect(page.getByText('Auto save interval must be at least 30 seconds')).toBeVisible()

      // Enter valid value
      await page.getByLabel('Auto Save Interval (seconds)').fill('300')

      // Error should disappear
      await expect(
        page.getByText('Auto save interval must be at least 30 seconds')
      ).not.toBeVisible()
    })
  })

  test.describe('Auto-save Functionality', () => {
    test('should auto-save changes when enabled', async ({ page }) => {
      // Enable auto-save mode (this would be a setting or URL parameter)
      await page.goto('/settings?autoSave=true')

      // Navigate to appearance tab
      await page.getByRole('tab', { name: 'Appearance' }).click()

      // Change a setting
      await page.getByLabel('Color Theme').selectOption('dark')

      // Wait for auto-save
      await expect(page.getByText('Auto-saving...')).toBeVisible()
      await expect(page.getByText('Auto-saving...')).not.toBeVisible()

      // No manual save button should be visible
      await expect(page.getByRole('button', { name: 'Save Changes' })).not.toBeVisible()
    })
  })

  test.describe('Import/Export Functionality', () => {
    test('should export settings', async ({ page }) => {
      // Click export button
      await page.getByRole('button', { name: 'Export' }).click()

      // Export dialog should open
      await expect(page.getByRole('dialog', { name: 'Export Settings' })).toBeVisible()

      // Configure export options
      await page.getByLabel('Include metadata').check()

      // Start download
      const downloadPromise = page.waitForEvent('download')
      await page.getByRole('button', { name: 'Export Settings' }).click()

      // Verify download
      const download = await downloadPromise
      expect(download.suggestedFilename()).toMatch(/ued-settings-.*\.json/)

      // Dialog should close
      await expect(page.getByRole('dialog', { name: 'Export Settings' })).not.toBeVisible()
    })

    test('should import settings', async ({ page }) => {
      // Click import button
      await page.getByRole('button', { name: 'Import' }).click()

      // Import dialog should open
      await expect(page.getByRole('dialog', { name: 'Import Settings' })).toBeVisible()

      // Upload file (mock file upload)
      const fileInput = page.getByLabel('Select Settings File')
      await fileInput.setInputFiles({
        name: 'test-settings.json',
        mimeType: 'application/json',
        buffer: Buffer.from(
          JSON.stringify({
            preferences: {
              theme: 'dark',
              language: 'es',
            },
            version: '1.0',
          })
        ),
      })

      // Preview should appear
      await expect(page.getByText('Import Preview')).toBeVisible()

      // Import settings
      await page.getByRole('button', { name: 'Import Settings' }).click()

      // Verify import success
      await expect(page.getByText('Preferences imported successfully')).toBeVisible()

      // Dialog should close
      await expect(page.getByRole('dialog', { name: 'Import Settings' })).not.toBeVisible()
    })
  })

  test.describe('Reset Functionality', () => {
    test('should reset settings to defaults', async ({ page }) => {
      // Make some changes first
      await page.getByRole('tab', { name: 'Appearance' }).click()
      await page.getByLabel('Color Theme').selectOption('dark')
      await page.getByRole('button', { name: 'Save Changes' }).click()
      await expect(page.getByText('Preferences updated successfully')).toBeVisible()

      // Click reset button
      await page.getByRole('button', { name: 'Reset' }).click()

      // Reset dialog should open
      await expect(page.getByRole('dialog', { name: 'Reset Settings' })).toBeVisible()

      // Select reset all option
      await page.getByLabel('Reset all settings to defaults').check()

      // Confirm reset
      await page.getByRole('button', { name: 'Reset Settings' }).click()

      // Confirmation dialog
      await expect(page.getByRole('dialog', { name: 'Confirm Reset' })).toBeVisible()
      await page.getByRole('button', { name: 'Reset Settings' }).click()

      // Verify reset success
      await expect(page.getByText('Preferences reset to defaults successfully')).toBeVisible()

      // Check that theme is back to default
      await page.getByRole('tab', { name: 'Appearance' }).click()
      await expect(page.getByLabel('Color Theme')).toHaveValue('system')
    })
  })

  test.describe('Cross-tab Synchronization', () => {
    test('should sync changes across tabs', async ({ browser }) => {
      // Open two tabs
      const context = await browser.newContext()
      const page1 = await context.newPage()
      const page2 = await context.newPage()

      // Navigate both to settings
      await page1.goto('/settings')
      await page2.goto('/settings')

      // Make change in first tab
      await page1.getByRole('tab', { name: 'Appearance' }).click()
      await page1.getByLabel('Color Theme').selectOption('dark')

      // Check that change appears in second tab
      await page2.getByRole('tab', { name: 'Appearance' }).click()
      await expect(page2.getByLabel('Color Theme')).toHaveValue('dark')

      await context.close()
    })
  })

  test.describe('Accessibility', () => {
    test('should be keyboard navigable', async ({ page }) => {
      // Tab through the interface
      await page.keyboard.press('Tab')
      await expect(page.getByPlaceholder('Search settings...')).toBeFocused()

      await page.keyboard.press('Tab')
      await expect(page.getByRole('button', { name: 'Import' })).toBeFocused()

      await page.keyboard.press('Tab')
      await expect(page.getByRole('button', { name: 'Export' })).toBeFocused()
    })

    test('should have proper ARIA labels', async ({ page }) => {
      // Check that tabs have proper ARIA attributes
      const accountTab = page.getByRole('tab', { name: 'Account' })
      await expect(accountTab).toHaveAttribute('aria-selected', 'true')

      // Check that form controls have labels
      await page.getByRole('tab', { name: 'Appearance' }).click()
      await expect(page.getByLabel('Color Theme')).toBeVisible()
      await expect(page.getByLabel('Language')).toBeVisible()
    })

    test('should support screen readers', async ({ page }) => {
      // Check for proper heading structure
      await expect(page.getByRole('heading', { level: 1, name: 'Settings' })).toBeVisible()

      // Check for section headings
      await page.getByRole('tab', { name: 'Appearance' }).click()
      await expect(page.getByRole('heading', { level: 3, name: 'Theme' })).toBeVisible()
    })
  })

  test.describe('Error Handling', () => {
    test('should handle network errors gracefully', async ({ page }) => {
      // Simulate network failure
      await page.route('**/api/v1/users/me/preferences', (route) => {
        route.abort('failed')
      })

      await page.goto('/settings')

      // Should show error state but not crash
      await expect(page.getByText('Settings')).toBeVisible()
    })

    test('should show validation errors', async ({ page }) => {
      await page.getByRole('tab', { name: 'Engineering Calculations' }).click()

      // Enter invalid precision
      await page.getByLabel('Decimal Precision').fill('10')

      // Should show validation error
      await expect(page.getByText('Maximum 6 decimal places')).toBeVisible()
    })
  })
})
