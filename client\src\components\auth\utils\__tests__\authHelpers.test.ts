/**
 * Authentication Helpers Tests
 * Tests for authentication utility functions and error handling
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import {
  parseAuthError,
  sanitizeLoginData,
  sanitizeRegisterData,
  generateA11yAnnouncement,
  meetsMinimumPasswordRequirements,
  rateLimitUtils,
  storageUtils,
} from '../authHelpers'

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
}

Object.defineProperty(window, 'localStorage', {
  value: localStorageMock,
})

describe('Authentication Helpers', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('parseAuthError', () => {
    it('should handle network errors', () => {
      const error = { message: 'Network Error' }
      const result = parseAuthError(error)

      expect(result.type).toBe('network')
      expect(result.message).toContain('Unable to connect to the server')
    })

    it('should handle 400 validation errors', () => {
      const error = {
        response: {
          status: 400,
          data: { detail: 'Invalid input data' },
        },
      }
      const result = parseAuthError(error)

      expect(result.type).toBe('validation')
      expect(result.message).toBe('Invalid input data')
    })

    it('should handle 401 authentication errors', () => {
      const error = {
        response: {
          status: 401,
          data: { detail: 'Invalid credentials' },
        },
      }
      const result = parseAuthError(error)

      expect(result.type).toBe('server')
      expect(result.message).toContain('Invalid email or password')
    })

    it('should handle 409 conflict errors', () => {
      const error = {
        response: {
          status: 409,
          data: { detail: 'User already exists' },
        },
      }
      const result = parseAuthError(error)

      expect(result.type).toBe('server')
      expect(result.message).toContain('already exists')
    })

    it('should handle 429 rate limiting errors', () => {
      const error = {
        response: {
          status: 429,
          data: { detail: 'Too many requests' },
        },
      }
      const result = parseAuthError(error)

      expect(result.type).toBe('server')
      expect(result.message).toContain('Too many attempts')
    })

    it('should handle 500 server errors', () => {
      const error = {
        response: {
          status: 500,
          data: { detail: 'Internal server error' },
        },
      }
      const result = parseAuthError(error)

      expect(result.type).toBe('server')
      expect(result.message).toContain('Server error')
    })

    it('should handle unknown errors', () => {
      const error = {
        response: {
          status: 418,
          data: { detail: "I'm a teapot" },
        },
      }
      const result = parseAuthError(error)

      expect(result.type).toBe('unknown')
      expect(result.message).toContain('unexpected error')
    })
  })

  describe('sanitizeLoginData', () => {
    it('should sanitize and format login data', () => {
      const input = {
        username: '  <EMAIL>  ',
        password: 'MyPassword123!',
      }

      const result = sanitizeLoginData(input)

      expect(result.username).toBe('<EMAIL>')
      expect(result.password).toBe('MyPassword123!') // Password should not be modified
    })

    it('should handle special characters in username', () => {
      const input = {
        username: '<script><EMAIL></script>',
        password: 'password',
      }

      const result = sanitizeLoginData(input)

      expect(result.username).toBe('<EMAIL>/script')
    })
  })

  describe('sanitizeRegisterData', () => {
    it('should sanitize and format registration data', () => {
      const input = {
        name: '  TestUser  ',
        email: '  <EMAIL>  ',
        password: 'MyPassword123!',
        confirmPassword: 'MyPassword123!',
      }

      const result = sanitizeRegisterData(input)

      expect(result.name).toBe('TestUser')
      expect(result.email).toBe('<EMAIL>')
      expect(result.password).toBe('MyPassword123!')
      expect(result.confirmPassword).toBe('MyPassword123!')
    })
  })

  describe('generateA11yAnnouncement', () => {
    it('should generate login announcements', () => {
      expect(generateA11yAnnouncement('submitting', 'login')).toBe('Signing in, please wait...')
      expect(generateA11yAnnouncement('success', 'login')).toBe('Sign in successful')
      expect(generateA11yAnnouncement('error', 'login', 'Invalid credentials')).toBe(
        'Sign in failed. Invalid credentials'
      )
    })

    it('should generate register announcements', () => {
      expect(generateA11yAnnouncement('submitting', 'register')).toBe(
        'Creating account, please wait...'
      )
      expect(generateA11yAnnouncement('success', 'register')).toBe('Account created successfully')
      expect(generateA11yAnnouncement('error', 'register')).toBe(
        'Account creation failed. Please try again.'
      )
    })

    it('should return empty string for idle state', () => {
      expect(generateA11yAnnouncement('idle', 'login')).toBe('')
    })
  })

  describe('meetsMinimumPasswordRequirements', () => {
    it('should return true for strong passwords', () => {
      expect(meetsMinimumPasswordRequirements('TestPassword123!')).toBe(true)
    })

    it('should return false for weak passwords', () => {
      expect(meetsMinimumPasswordRequirements('password')).toBe(false) // No uppercase, numbers, special
      expect(meetsMinimumPasswordRequirements('PASSWORD')).toBe(false) // No lowercase, numbers, special
      expect(meetsMinimumPasswordRequirements('Password')).toBe(false) // No numbers, special
      expect(meetsMinimumPasswordRequirements('Pass1')).toBe(false) // Too short, no special
    })
  })

  describe('storageUtils', () => {
    beforeEach(() => {
      localStorageMock.getItem.mockReturnValue(null)
      localStorageMock.setItem.mockImplementation(() => {})
      localStorageMock.removeItem.mockImplementation(() => {})
    })

    describe('saveFormData', () => {
      it('should save form data excluding sensitive fields', () => {
        const data = {
          username: 'testuser',
          email: '<EMAIL>',
          password: 'secret',
          confirmPassword: 'secret',
        }

        storageUtils.saveFormData('test-form', data)

        expect(localStorageMock.setItem).toHaveBeenCalledWith(
          'test-form',
          JSON.stringify({
            username: 'testuser',
            email: '<EMAIL>',
          })
        )
      })

      it('should handle localStorage errors gracefully', () => {
        localStorageMock.setItem.mockImplementation(() => {
          throw new Error('Storage full')
        })

        // Should not throw
        expect(() => storageUtils.saveFormData('test', {})).not.toThrow()
      })
    })

    describe('loadFormData', () => {
      it('should load saved form data', () => {
        const savedData = { username: 'testuser', email: '<EMAIL>' }
        localStorageMock.getItem.mockReturnValue(JSON.stringify(savedData))

        const result = storageUtils.loadFormData('test-form')

        expect(result).toEqual(savedData)
        expect(localStorageMock.getItem).toHaveBeenCalledWith('test-form')
      })

      it('should return null for non-existent data', () => {
        localStorageMock.getItem.mockReturnValue(null)

        const result = storageUtils.loadFormData('non-existent')

        expect(result).toBeNull()
      })

      it('should handle JSON parse errors gracefully', () => {
        localStorageMock.getItem.mockReturnValue('invalid json')

        const result = storageUtils.loadFormData('test')

        expect(result).toBeNull()
      })
    })

    describe('clearFormData', () => {
      it('should clear form data', () => {
        storageUtils.clearFormData('test-form')

        expect(localStorageMock.removeItem).toHaveBeenCalledWith('test-form')
      })
    })
  })

  describe('rateLimitUtils', () => {
    beforeEach(() => {
      localStorageMock.getItem.mockReturnValue(null)
      vi.useFakeTimers()
    })

    afterEach(() => {
      vi.useRealTimers()
    })

    describe('isRateLimited', () => {
      it('should return false when no attempts recorded', () => {
        localStorageMock.getItem.mockReturnValue(null)

        const result = rateLimitUtils.isRateLimited('test-key')

        expect(result).toBe(false)
      })

      it('should return true when rate limit exceeded', () => {
        const now = Date.now()
        const attempts = Array(5).fill(now - 1000) // 5 attempts 1 second ago
        localStorageMock.getItem.mockReturnValue(JSON.stringify(attempts))

        const result = rateLimitUtils.isRateLimited('test-key', 5, 15 * 60 * 1000)

        expect(result).toBe(true)
      })

      it('should return false when old attempts are outside window', () => {
        const now = Date.now()
        const attempts = Array(5).fill(now - 20 * 60 * 1000) // 5 attempts 20 minutes ago
        localStorageMock.getItem.mockReturnValue(JSON.stringify(attempts))

        const result = rateLimitUtils.isRateLimited('test-key', 5, 15 * 60 * 1000)

        expect(result).toBe(false)
      })
    })

    describe('recordAttempt', () => {
      it('should record new attempt', () => {
        localStorageMock.getItem.mockReturnValue('[]')

        rateLimitUtils.recordAttempt('test-key')

        expect(localStorageMock.setItem).toHaveBeenCalled()
      })
    })

    describe('clearRateLimit', () => {
      it('should clear rate limit data', () => {
        rateLimitUtils.clearRateLimit('test-key')

        expect(localStorageMock.removeItem).toHaveBeenCalledWith('rate_limit_test-key')
      })
    })
  })
})
