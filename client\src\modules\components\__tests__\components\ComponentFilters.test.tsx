/**
 * ComponentFilters Unit Tests
 * Tests the ComponentFilters component with filter controls and state management
 */

import { screen } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { mockComponentFilters, renderWithProviders } from '@/test/utils'
import { beforeEach, describe, expect, it, vi } from 'vitest'
import { ComponentFilters } from '../../components/ComponentFilters'

// Mock the API hooks
vi.mock('../../api/componentQueries', () => ({
  useComponentCategories: vi.fn(),
  useComponentTypes: vi.fn(),
}))

import { useComponentCategories, useComponentTypes } from '../../api/componentQueries'

describe('ComponentFilters', () => {
  const mockHandlers = {
    onFiltersChange: vi.fn(),
    onClear: vi.fn(),
  }

  const mockCategories = [
    { name: 'Resistor', value: 'RESISTOR' },
    { name: 'Capacitor', value: 'CAPACITOR' },
    { name: 'Inductor', value: 'INDUCTOR' },
    { name: 'Diode', value: 'DIODE' },
    { name: 'Transistor', value: 'TRANSISTOR' },
  ]

  const mockTypes = [
    { name: 'Fixed Resistor', value: 'FIXED_RESISTOR' },
    { name: 'Variable Resistor', value: 'VARIABLE_RESISTOR' },
  ]

  beforeEach(() => {
    vi.clearAllMocks()

    // Setup mock return values
    vi.mocked(useComponentCategories).mockReturnValue({
      data: mockCategories,
      isLoading: false,
      error: null,
    } as any)

    vi.mocked(useComponentTypes).mockReturnValue({
      data: mockTypes,
      isLoading: false,
      error: null,
    } as any)
  })

  describe('Rendering', () => {
    it('renders all filter controls', async () => {
      const user = userEvent.setup()
      renderWithProviders(<ComponentFilters filters={mockComponentFilters} {...mockHandlers} />)

      expect(screen.getByTestId('category-filter')).toBeInTheDocument()
      expect(screen.getByText('Category')).toBeInTheDocument()
      expect(screen.getByText('Component Type')).toBeInTheDocument()
      expect(screen.getByText('Manufacturer')).toBeInTheDocument()
      expect(screen.getByText('Price Range')).toBeInTheDocument()
      expect(screen.getByText('Status')).toBeInTheDocument()

      // Expand manufacturer section to access the filter
      await user.click(screen.getByRole('button', { name: /manufacturer/i }))
      expect(screen.getByTestId('manufacturer-filter')).toBeInTheDocument()
    })

    it('renders with initial filter values', async () => {
      const user = userEvent.setup()
      renderWithProviders(<ComponentFilters filters={mockComponentFilters} {...mockHandlers} />)

      const categorySelect = screen.getByTestId('category-filter')
      expect(categorySelect).toHaveValue('1')

      // Expand manufacturer section to access the filter
      await user.click(screen.getByRole('button', { name: /manufacturer/i }))
      const manufacturerSelect = screen.getByTestId('manufacturer-filter')
      expect(manufacturerSelect).toHaveValue('Schneider Electric')
    })

    it('renders clear filters button', () => {
      renderWithProviders(<ComponentFilters filters={mockComponentFilters} {...mockHandlers} />)

      expect(screen.getByTestId('clear-filters-btn')).toBeInTheDocument()
    })

    it('shows active filter count', () => {
      renderWithProviders(<ComponentFilters filters={mockComponentFilters} {...mockHandlers} />)

      // Count should be based on non-null/empty values in mockComponentFilters
      expect(screen.getByText('9')).toBeInTheDocument()
    })
  })

  describe('Category Filter', () => {
    it('calls onFiltersChange when category is changed', async () => {
      const user = userEvent.setup()

      renderWithProviders(<ComponentFilters filters={{}} {...mockHandlers} />)

      const categorySelect = screen.getByTestId('category-filter')
      await user.selectOptions(categorySelect, 'CAPACITOR')

      expect(mockHandlers.onFiltersChange).toHaveBeenCalledWith({
        category: 'CAPACITOR',
        component_type: null,
      })
    })

    it('renders all category options', () => {
      renderWithProviders(<ComponentFilters filters={{}} {...mockHandlers} />)

      const categorySelect = screen.getByTestId('category-filter')
      expect(categorySelect).toBeInTheDocument()

      // Check for some key options
      expect(screen.getByRole('option', { name: /resistor/i })).toBeInTheDocument()
      expect(screen.getByRole('option', { name: /capacitor/i })).toBeInTheDocument()
      expect(screen.getByRole('option', { name: /inductor/i })).toBeInTheDocument()
    })
  })

  describe('Component Type Filter', () => {
    it('calls onFiltersChange when component type is changed', async () => {
      const user = userEvent.setup()

      renderWithProviders(<ComponentFilters filters={{}} {...mockHandlers} />)

      // Find the radio button for component type
      const typeRadio = screen.getByRole('radio', { name: /fixed resistor/i })
      await user.click(typeRadio)

      expect(mockHandlers.onFiltersChange).toHaveBeenCalledWith({
        component_type: 'FIXED_RESISTOR',
      })
    })

    it('updates component type options based on category', async () => {
      renderWithProviders(
        <ComponentFilters
          filters={{ category: 'RESISTOR' as ComponentCategoryType }}
          {...mockHandlers}
        />
      )

      // Should show resistor-specific types
      expect(screen.getByRole('radio', { name: /fixed resistor/i })).toBeInTheDocument()
    })
  })

  describe('Manufacturer Filter', () => {
    it('calls onFiltersChange when manufacturer is changed', async () => {
      const user = userEvent.setup()

      renderWithProviders(<ComponentFilters filters={{}} {...mockHandlers} />)

      // Expand manufacturer section first
      await user.click(screen.getByRole('button', { name: /manufacturer/i }))

      const manufacturerSelect = screen.getByTestId('manufacturer-filter')
      await user.selectOptions(manufacturerSelect, 'Schneider Electric')

      expect(mockHandlers.onFiltersChange).toHaveBeenCalledWith({
        manufacturer: 'Schneider Electric',
      })
    })

    it('provides manufacturer options', async () => {
      const user = userEvent.setup()
      renderWithProviders(<ComponentFilters filters={{}} {...mockHandlers} />)

      // Expand manufacturer section first
      await user.click(screen.getByRole('button', { name: /manufacturer/i }))

      expect(screen.getByRole('option', { name: /schneider electric/i })).toBeInTheDocument()
      expect(screen.getByRole('option', { name: /abb/i })).toBeInTheDocument()
    })
  })

  describe('Price Range Filter', () => {
    it('calls onFiltersChange when min price is changed', async () => {
      const user = userEvent.setup()

      renderWithProviders(<ComponentFilters filters={{}} {...mockHandlers} />)

      // Expand price section first
      await user.click(screen.getByRole('button', { name: /price range/i }))

      const minPriceInput = screen.getByPlaceholderText('0')
      await user.type(minPriceInput, '0.50')

      expect(mockHandlers.onFiltersChange).toHaveBeenCalledWith({
        min_price: 0.5,
      })
    })

    it('calls onFiltersChange when max price is changed', async () => {
      const user = userEvent.setup()

      renderWithProviders(<ComponentFilters filters={{}} {...mockHandlers} />)

      // Expand price section first
      await user.click(screen.getByRole('button', { name: /price range/i }))

      const maxPriceInput = screen.getByPlaceholderText('∞')
      await user.type(maxPriceInput, '2.00')

      expect(mockHandlers.onFiltersChange).toHaveBeenCalledWith({
        max_price: 2.0,
      })
    })

    it('renders currency selector', async () => {
      const user = userEvent.setup()
      renderWithProviders(<ComponentFilters filters={{}} {...mockHandlers} />)

      // Expand price section first
      await user.click(screen.getByRole('button', { name: /price range/i }))

      expect(screen.getByDisplayValue('EUR')).toBeInTheDocument()
    })
  })

  describe('Status Filters', () => {
    it('calls onFiltersChange when preferred filter is toggled', async () => {
      const user = userEvent.setup()

      renderWithProviders(<ComponentFilters filters={{}} {...mockHandlers} />)

      const preferredRadio = screen.getByRole('radio', { name: /preferred only/i })
      await user.click(preferredRadio)

      expect(mockHandlers.onFiltersChange).toHaveBeenCalledWith({
        is_preferred: true,
      })
    })

    it('calls onFiltersChange when active filter is toggled', async () => {
      const user = userEvent.setup()

      renderWithProviders(<ComponentFilters filters={{}} {...mockHandlers} />)

      const activeRadio = screen.getByRole('radio', { name: /active only/i })
      await user.click(activeRadio)

      expect(mockHandlers.onFiltersChange).toHaveBeenCalledWith({
        is_active: true,
      })
    })
  })

  describe('Filter Actions', () => {
    it('calls onClear when clear button is clicked', async () => {
      const user = userEvent.setup()

      renderWithProviders(<ComponentFilters filters={mockComponentFilters} {...mockHandlers} />)

      const clearButton = screen.getByTestId('clear-filters-btn')
      await user.click(clearButton)

      expect(mockHandlers.onClear).toHaveBeenCalled()
    })

    it('hides clear button when no filters are active', () => {
      renderWithProviders(<ComponentFilters filters={{}} {...mockHandlers} />)

      expect(screen.queryByTestId('clear-filters-btn')).not.toBeInTheDocument()
    })
  })

  describe('Collapsible Sections', () => {
    it('toggles category filter section', async () => {
      const user = userEvent.setup()

      renderWithProviders(<ComponentFilters filters={{}} {...mockHandlers} />)

      const categoryToggle = screen.getByRole('button', { name: /category/i })
      await user.click(categoryToggle)

      expect(screen.queryByTestId('category-filter')).not.toBeInTheDocument()
    })

    it('toggles entire filter panel', async () => {
      const user = userEvent.setup()

      renderWithProviders(<ComponentFilters filters={{}} {...mockHandlers} />)

      const toggleButtons = screen.getAllByRole('button')
      const panelToggle = toggleButtons.find((button) => button.querySelector('svg'))
      await user.click(panelToggle!)

      expect(screen.queryByTestId('category-filter')).not.toBeInTheDocument()
    })
  })

  describe('Accessibility', () => {
    it('has proper section structure', () => {
      renderWithProviders(<ComponentFilters filters={mockComponentFilters} {...mockHandlers} />)

      expect(screen.getByText('Filters')).toBeInTheDocument()
      expect(screen.getByText('Category')).toBeInTheDocument()
      expect(screen.getByText('Component Type')).toBeInTheDocument()
    })

    it('provides proper focus management', async () => {
      const user = userEvent.setup()

      renderWithProviders(<ComponentFilters filters={{}} {...mockHandlers} />)

      const categorySelect = screen.getByTestId('category-filter')
      await user.click(categorySelect)

      expect(categorySelect).toHaveFocus()
    })
  })
})
