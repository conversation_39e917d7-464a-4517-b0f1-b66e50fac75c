# State Management Architecture

This document outlines the comprehensive state management architecture for the Components module, including Zustand store design, React Query integration patterns, and optimistic update strategies.

## State Separation Strategy

### Server State (React Query)

- Component data from API
- Search results and suggestions
- Component statistics
- Categories and types
- Bulk operation results

### Client State (Zustand)

- UI state (view modes, filters, selections)
- Form state (form data, validation, dirty tracking)
- Search state (queries, history, suggestions)
- User preferences (display options, theme)
- Modal and sidebar states

## Zustand Store Architecture

### Store Structure

```typescript
interface ComponentStore {
  // List Management
  listState: ComponentListState
  setListState: (state: Partial<ComponentListState>) => void
  updateFilters: (filters: Partial<ComponentFilter>) => void
  clearFilters: () => void

  // Search Management
  searchState: SearchState
  setSearchState: (state: Partial<SearchState>) => void
  addToSearchHistory: (query: string) => void
  clearSearchHistory: () => void

  // Bulk Operations
  bulkState: BulkOperationState
  setBulkState: (state: Partial<BulkOperationState>) => void
  selectComponent: (id: number) => void
  deselectComponent: (id: number) => void
  selectAll: (ids: number[]) => void
  clearSelection: () => void

  // Form Management
  formState: ComponentFormState
  setFormState: (state: Partial<ComponentFormState>) => void
  updateFormField: (field: string, value: any) => void
  setFormErrors: (errors: Record<string, string>) => void
  resetForm: () => void

  // UI State
  sidebarState: SidebarState
  setSidebarState: (state: Partial<SidebarState>) => void
  toggleSidebar: () => void

  modalState: ModalState
  setModalState: (state: Partial<ModalState>) => void
  openModal: (type: ModalType, data?: any) => void
  closeModal: () => void

  // User Preferences
  userPreferences: UserPreferences
  setUserPreferences: (preferences: Partial<UserPreferences>) => void
  updateDisplayOptions: (options: Partial<ComponentDisplayOptions>) => void

  // Notifications
  notifications: Notification[]
  addNotification: (notification: Omit<Notification, 'id'>) => void
  removeNotification: (id: string) => void
  clearNotifications: () => void

  // Actions
  reset: () => void
}
```

### Store Implementation Pattern

```typescript
export const useComponentStore = create<ComponentStore>()(
  devtools(
    persist(
      (set, get) => ({
        // State
        listState: createDefaultListState(),
        searchState: createDefaultSearchState(),
        bulkState: createDefaultBulkState(),
        formState: createDefaultFormState(),
        sidebarState: createDefaultSidebarState(),
        modalState: createDefaultModalState(),
        userPreferences: createDefaultUserPreferences(),
        notifications: [],

        // Actions
        setListState: (state) =>
          set((prev) => ({
            listState: { ...prev.listState, ...state },
          })),

        updateFilters: (filters) =>
          set((prev) => ({
            listState: {
              ...prev.listState,
              filters: { ...prev.listState.filters, ...filters },
              pagination: { ...prev.listState.pagination, page: 1 }, // Reset to first page
            },
          })),

        // ... other actions
      }),
      {
        name: 'component-store',
        partialize: (state) => ({
          // Only persist certain parts of the state
          userPreferences: state.userPreferences,
          searchState: {
            searchHistory: state.searchState.searchHistory,
            savedSearches: state.searchState.savedSearches,
          },
          listState: {
            viewMode: state.listState.viewMode,
            displayOptions: state.listState.displayOptions,
            pagination: {
              size: state.listState.pagination.size,
            },
          },
        }),
      }
    ),
    { name: 'component-store' }
  )
)
```

## React Query Integration

### Query Configuration

```typescript
// Query keys factory
export const componentQueryKeys = {
  all: ['components'] as const,
  lists: () => [...componentQueryKeys.all, 'list'] as const,
  list: (filters: ComponentFilter) => [...componentQueryKeys.lists(), filters] as const,
  details: () => [...componentQueryKeys.all, 'detail'] as const,
  detail: (id: number) => [...componentQueryKeys.details(), id] as const,
  search: (params: ComponentAdvancedSearch) =>
    [...componentQueryKeys.all, 'search', params] as const,
  stats: () => [...componentQueryKeys.all, 'stats'] as const,
  suggestions: (query: string) => [...componentQueryKeys.all, 'suggestions', query] as const,
}

// Query hooks
export const useComponents = (filters: ComponentFilter = {}) => {
  return useQuery({
    queryKey: componentQueryKeys.list(filters),
    queryFn: () => componentApi.list(filters),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    refetchOnWindowFocus: false,
  })
}

export const useComponent = (id: number) => {
  return useQuery({
    queryKey: componentQueryKeys.detail(id),
    queryFn: () => componentApi.getById(id),
    enabled: !!id,
    staleTime: 10 * 60 * 1000, // 10 minutes
  })
}
```

### Mutation Configuration

```typescript
// Mutation hooks with optimistic updates
export const useCreateComponent = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: componentApi.create,
    onMutate: async (newComponent) => {
      // Cancel outgoing refetches
      await queryClient.cancelQueries({ queryKey: componentQueryKeys.lists() })

      // Snapshot previous value
      const previousComponents = queryClient.getQueriesData({
        queryKey: componentQueryKeys.lists(),
      })

      // Optimistically update
      queryClient.setQueriesData(
        { queryKey: componentQueryKeys.lists() },
        (old: ComponentPaginatedResponse | undefined) => {
          if (!old) return old

          const optimisticComponent = {
            ...newComponent,
            id: Date.now(), // Temporary ID
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
          }

          return {
            ...old,
            items: [optimisticComponent, ...old.items],
            total: old.total + 1,
          }
        }
      )

      return { previousComponents }
    },
    onError: (err, newComponent, context) => {
      // Rollback on error
      if (context?.previousComponents) {
        context.previousComponents.forEach(([queryKey, data]) => {
          queryClient.setQueryData(queryKey, data)
        })
      }
    },
    onSettled: () => {
      // Always refetch after error or success
      queryClient.invalidateQueries({ queryKey: componentQueryKeys.lists() })
    },
  })
}
```

## State Synchronization Patterns

### Derived State Pattern

```typescript
// Custom hook for derived state
export const useComponentListData = () => {
  const { listState, searchState } = useComponentStore()
  const { data, isLoading, error } = useComponents(listState.filters)

  // Derive computed state
  const selectedComponents = useMemo(() => {
    if (!data?.items) return []
    return data.items.filter((component) => listState.selectedIds.includes(component.id))
  }, [data?.items, listState.selectedIds])

  const filteredCount = data?.total ?? 0
  const selectedCount = listState.selectedIds.length

  return {
    components: data?.items ?? [],
    selectedComponents,
    filteredCount,
    selectedCount,
    isLoading,
    error,
    hasSelection: selectedCount > 0,
    isAllSelected: selectedCount === filteredCount && filteredCount > 0,
  }
}
```

### State Synchronization Hook

```typescript
// Hook to sync URL with store state
export const useComponentStateSync = () => {
  const router = useRouter()
  const searchParams = useSearchParams()
  const { listState, setListState, searchState, setSearchState } = useComponentStore()

  // Sync URL to store
  useEffect(() => {
    const filters = Object.fromEntries(searchParams.entries())
    const urlState = parseUrlFilters(filters)

    if (!isEqual(urlState, listState.filters)) {
      setListState({ filters: urlState })
    }
  }, [searchParams, listState.filters, setListState])

  // Sync store to URL
  const updateUrl = useCallback(
    debounce((filters: ComponentFilter) => {
      const urlParams = serializeFilters(filters)
      const newUrl = `${window.location.pathname}?${urlParams.toString()}`
      router.replace(newUrl, { scroll: false })
    }, 300),
    [router]
  )

  useEffect(() => {
    updateUrl(listState.filters)
  }, [listState.filters, updateUrl])
}
```

## Optimistic Updates Strategy

### Component Creation

1. **Immediate UI Update**: Add component to list with temporary ID
2. **Validation**: Show validation errors if any
3. **Server Sync**: Send request to server
4. **Success**: Replace temporary component with server response
5. **Error**: Remove optimistic component and show error

### Component Updates

1. **Immediate UI Update**: Update component in place
2. **Server Sync**: Send update request
3. **Success**: Confirm update with server data
4. **Error**: Rollback to previous state and show error

### Bulk Operations

1. **Progress Tracking**: Show progress bar for bulk operations
2. **Partial Success**: Handle partial success scenarios
3. **Error Recovery**: Provide retry mechanisms for failed items

## Performance Optimization

### Memoization Strategy

```typescript
// Memoized selectors
export const useComponentSelectors = () => {
  const store = useComponentStore()

  return useMemo(
    () => ({
      selectedIds: store.listState.selectedIds,
      filters: store.listState.filters,
      viewMode: store.listState.viewMode,
      searchQuery: store.searchState.query,
    }),
    [
      store.listState.selectedIds,
      store.listState.filters,
      store.listState.viewMode,
      store.searchState.query,
    ]
  )
}
```

### Subscription Optimization

```typescript
// Selective subscriptions to prevent unnecessary re-renders
export const useComponentListState = () => {
  return useComponentStore(useCallback((state) => state.listState, []))
}

export const useComponentSearchState = () => {
  return useComponentStore(useCallback((state) => state.searchState, []))
}
```

## Error Handling Strategy

### Error Boundaries

- Component-level error boundaries for graceful degradation
- Global error boundary for unhandled errors
- Error reporting integration

### Error State Management

```typescript
interface ErrorState {
  hasError: boolean;
  error: Error | null;
  errorBoundary: string | null;
  retryCount: number;
}

// Error actions
const errorActions = {
  setError: (error: Error, boundary?: string) => void;
  clearError: () => void;
  retry: () => void;
  reportError: (error: Error, context: any) => void;
};
```

## Testing Strategy

### Store Testing

- Unit tests for store actions and state updates
- Integration tests for store-component interactions
- Mock implementations for testing

### Query Testing

- Mock API responses for consistent testing
- Test optimistic updates and error scenarios
- Test cache invalidation and synchronization

## Next Steps

1. Implement enhanced Zustand store with proper TypeScript types
2. Create React Query hooks with optimistic updates
3. Implement state synchronization patterns
4. Add comprehensive error handling
5. Create performance monitoring and optimization
