import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import { ReactQueryProvider } from '../lib/react-query'
import './globals.css'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: {
    default: 'Ultimate Electrical Designer - Professional Electrical Engineering Software',
    template: '%s | Ultimate Electrical Designer',
  },
  description:
    'Professional electrical engineering software for industrial applications. Design heat tracing systems, perform load calculations, and manage projects with industry-leading precision and compliance.',
  keywords: [
    'electrical engineering',
    'heat tracing',
    'load calculations',
    'electrical design',
    'industrial engineering',
    'NEC compliance',
    'cable sizing',
    'project management',
    'electrical calculations',
    'engineering software',
  ],
  authors: [{ name: 'Ultimate Electrical Designer Team' }],
  creator: 'Ultimate Electrical Designer',
  publisher: 'Ultimate Electrical Designer',
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  openGraph: {
    type: 'website',
    locale: 'en_US',
    url: 'https://ultimate-electrical-designer.com',
    title: 'Ultimate Electrical Designer - Professional Electrical Engineering Software',
    description:
      'Professional electrical engineering software for industrial applications. Design heat tracing systems, perform load calculations, and manage projects with industry-leading precision and compliance.',
    siteName: 'Ultimate Electrical Designer',
    images: [
      {
        url: '/og-image.png',
        width: 1200,
        height: 630,
        alt: 'Ultimate Electrical Designer - Professional Electrical Engineering Software',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Ultimate Electrical Designer - Professional Electrical Engineering Software',
    description:
      'Professional electrical engineering software for industrial applications. Design heat tracing systems, perform load calculations, and manage projects with industry-leading precision and compliance.',
    images: ['/og-image.png'],
    creator: '@UltimateElectricalDesigner',
  },
  verification: {
    google: 'your-google-verification-code',
    yandex: 'your-yandex-verification-code',
  },
  category: 'Engineering Software',
}

interface RootLayoutProps {
  children: React.ReactNode
}

export default function RootLayout({ children }: RootLayoutProps) {
  // Import theme provider dynamically to avoid SSR issues
  const { UEDThemeProvider } = require('../components/theme-provider')

  return (
    <html lang="en" className="h-full scroll-smooth" suppressHydrationWarning>
      <head>
        <link rel="icon" href="/favicon.ico" sizes="any" />
        <link rel="icon" href="/favicon.svg" type="image/svg+xml" />
        <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
        <link rel="manifest" href="/manifest.json" />
        <meta name="theme-color" content="#E67E22" />
        <meta name="viewport" content="width=device-width, initial-scale=1, viewport-fit=cover" />

        {/* Performance optimizations */}
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
        <link rel="dns-prefetch" href="//fonts.googleapis.com" />
        <link rel="dns-prefetch" href="//fonts.gstatic.com" />
      </head>
      <body className={`${inter.className} h-full antialiased`}>
        <UEDThemeProvider
          themes={['light', 'dark', 'system']}
          defaultTheme="system"
          enableSystem={true}
          storageKey="ued-theme"
          disableTransitionOnChange={false}
        >
          <ReactQueryProvider>
            <div className="h-full">{children}</div>
          </ReactQueryProvider>
        </UEDThemeProvider>
      </body>
    </html>
  )
}
