/**
 * ComponentList Organism
 * Complex component for displaying and managing lists of components
 */

import React from 'react'
import { Button } from '@/components/ui/button'
import { Checkbox } from '@/components/ui/checkbox'
import { Input } from '@/components/ui/input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Separator } from '@/components/ui/separator'
import { cn } from '@/lib/utils'
import {
  Grid,
  List,
  Table,
  Search,
  Filter,
  SortAsc,
  SortDesc,
  MoreHorizontal,
  Plus,
  Download,
  Upload,
  Trash2,
  Edit,
  Copy,
} from 'lucide-react'
import type { ComponentRead, ViewMode, SortConfig, PaginationConfig } from '../../schemas'
import { ComponentCard } from '../molecules/ComponentCard'
import { useComponentStoreEnhanced } from '../../hooks/useComponentStoreEnhanced'

export interface ComponentListProps {
  components: ComponentRead[]
  loading?: boolean
  error?: string | null
  viewMode?: ViewMode
  sortConfig?: SortConfig
  pagination?: PaginationConfig
  showHeader?: boolean
  showFilters?: boolean
  showBulkActions?: boolean
  showPagination?: boolean
  className?: string
  onComponentSelect?: (component: ComponentRead) => void
  onComponentEdit?: (component: ComponentRead) => void
  onComponentDelete?: (component: ComponentRead) => void
  onComponentView?: (component: ComponentRead) => void
  onComponentTogglePreferred?: (component: ComponentRead) => void
  onBulkAction?: (action: string, componentIds: number[]) => void
  onViewModeChange?: (mode: ViewMode) => void
  onSortChange?: (config: SortConfig) => void
  onPageChange?: (page: number) => void
  onPageSizeChange?: (size: number) => void
  'data-testid'?: string
}

export const ComponentList = React.forwardRef<HTMLDivElement, ComponentListProps>(
  (
    {
      components,
      loading = false,
      error = null,
      viewMode = 'grid',
      sortConfig = { field: 'name', order: 'asc' },
      pagination,
      showHeader = true,
      showFilters = true,
      showBulkActions = true,
      showPagination = true,
      className,
      onComponentSelect,
      onComponentEdit,
      onComponentDelete,
      onComponentView,
      onComponentTogglePreferred,
      onBulkAction,
      onViewModeChange,
      onSortChange,
      onPageChange,
      onPageSizeChange,
      'data-testid': testId,
      ...props
    },
    ref
  ) => {
    const {
      bulkState,
      selectComponent,
      deselectComponent,
      selectAll,
      clearSelection,
      getSelectedCount,
      isComponentSelected,
    } = useComponentStoreEnhanced()

    const selectedCount = getSelectedCount()
    const isAllSelected = selectedCount === components.length && components.length > 0

    // Handle component selection
    const handleComponentSelect = (component: ComponentRead) => {
      if (isComponentSelected(component.id)) {
        deselectComponent(component.id)
      } else {
        selectComponent(component.id)
      }

      if (onComponentSelect) {
        onComponentSelect(component)
      }
    }

    // Handle select all
    const handleSelectAll = (checked: boolean) => {
      if (checked) {
        selectAll(components.map((c) => c.id))
      } else {
        clearSelection()
      }
    }

    // Handle bulk actions
    const handleBulkAction = (action: string) => {
      if (onBulkAction && selectedCount > 0) {
        onBulkAction(action, bulkState.selected_ids)
      }
    }

    // Handle view mode change
    const handleViewModeChange = (mode: ViewMode) => {
      if (onViewModeChange) {
        onViewModeChange(mode)
      }
    }

    // Handle sort change
    const handleSortChange = (field: string) => {
      const newOrder = sortConfig.field === field && sortConfig.order === 'asc' ? 'desc' : 'asc'
      const newConfig = { field, order: newOrder }

      if (onSortChange) {
        onSortChange(newConfig)
      }
    }

    // Render loading state
    if (loading) {
      return (
        <div className="space-y-4" data-testid={`${testId || 'component-list'}-loading`}>
          {Array.from({ length: 6 }).map((_, i) => (
            <div key={i} className="animate-pulse">
              <div className="h-32 rounded-lg bg-gray-200"></div>
            </div>
          ))}
        </div>
      )
    }

    // Render error state
    if (error) {
      return (
        <div
          className="py-8 text-center text-red-600"
          data-testid={`${testId || 'component-list'}-error`}
        >
          <p>Error loading components: {error}</p>
        </div>
      )
    }

    // Render empty state
    if (!components.length) {
      return (
        <div
          className="py-12 text-center text-gray-500"
          data-testid={`${testId || 'component-list'}-empty`}
        >
          <p className="text-lg font-medium">No components found</p>
          <p className="mt-1 text-sm">Try adjusting your search or filters</p>
        </div>
      )
    }

    return (
      <div
        ref={ref}
        className={cn('space-y-4', className)}
        data-testid={testId || 'component-list'}
        {...props}
      >
        {/* Header */}
        {showHeader && (
          <div className="flex items-center justify-between gap-4">
            <div className="flex items-center gap-4">
              <h2 className="text-lg font-semibold">Components ({components.length})</h2>

              {selectedCount > 0 && (
                <span className="text-sm text-gray-600">{selectedCount} selected</span>
              )}
            </div>

            <div className="flex items-center gap-2">
              {/* View mode toggle */}
              <div className="flex items-center rounded-lg border p-1">
                <Button
                  variant={viewMode === 'grid' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => handleViewModeChange('grid')}
                  aria-label="Grid view"
                  data-testid={`${testId || 'component-list'}-view-grid`}
                >
                  <Grid className="h-4 w-4" />
                </Button>
                <Button
                  variant={viewMode === 'list' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => handleViewModeChange('list')}
                  aria-label="List view"
                  data-testid={`${testId || 'component-list'}-view-list`}
                >
                  <List className="h-4 w-4" />
                </Button>
                <Button
                  variant={viewMode === 'table' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => handleViewModeChange('table')}
                  aria-label="Table view"
                  data-testid={`${testId || 'component-list'}-view-table`}
                >
                  <Table className="h-4 w-4" />
                </Button>
              </div>

              {/* Sort controls */}
              <Select value={sortConfig.field} onValueChange={(field) => handleSortChange(field)}>
                <SelectTrigger className="w-40">
                  <SelectValue placeholder="Sort by" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="name">Name</SelectItem>
                  <SelectItem value="manufacturer">Manufacturer</SelectItem>
                  <SelectItem value="category">Category</SelectItem>
                  <SelectItem value="unit_price">Price</SelectItem>
                  <SelectItem value="created_at">Date Added</SelectItem>
                </SelectContent>
              </Select>

              <Button
                variant="outline"
                size="sm"
                onClick={() => handleSortChange(sortConfig.field)}
                aria-label={`Sort ${sortConfig.order === 'asc' ? 'descending' : 'ascending'}`}
                data-testid={`${testId || 'component-list'}-sort-order`}
              >
                {sortConfig.order === 'asc' ? (
                  <SortAsc className="h-4 w-4" />
                ) : (
                  <SortDesc className="h-4 w-4" />
                )}
              </Button>
            </div>
          </div>
        )}

        {/* Bulk actions bar */}
        {showBulkActions && selectedCount > 0 && (
          <div className="flex items-center justify-between rounded-lg border border-blue-200 bg-blue-50 p-3">
            <div className="flex items-center gap-3">
              <Checkbox
                checked={isAllSelected}
                onCheckedChange={handleSelectAll}
                aria-label="Select all components"
                data-testid={`${testId || 'component-list'}-select-all`}
              />
              <span className="text-sm font-medium">
                {selectedCount} component{selectedCount !== 1 ? 's' : ''} selected
              </span>
            </div>

            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleBulkAction('edit')}
                data-testid={`${testId || 'component-list'}-bulk-edit`}
              >
                <Edit className="mr-2 h-4 w-4" />
                Edit
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleBulkAction('duplicate')}
                data-testid={`${testId || 'component-list'}-bulk-duplicate`}
              >
                <Copy className="mr-2 h-4 w-4" />
                Duplicate
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleBulkAction('export')}
                data-testid={`${testId || 'component-list'}-bulk-export`}
              >
                <Download className="mr-2 h-4 w-4" />
                Export
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleBulkAction('delete')}
                className="text-red-600 hover:text-red-700"
                data-testid={`${testId || 'component-list'}-bulk-delete`}
              >
                <Trash2 className="mr-2 h-4 w-4" />
                Delete
              </Button>
            </div>
          </div>
        )}

        {/* Component grid/list */}
        <div
          className={cn(
            'gap-4',
            viewMode === 'grid' && 'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4',
            viewMode === 'list' && 'space-y-2',
            viewMode === 'table' && 'space-y-1'
          )}
          data-testid={`${testId || 'component-list'}-items`}
        >
          {components.map((component) => (
            <ComponentCard
              key={component.id}
              component={component}
              isSelected={isComponentSelected(component.id)}
              showSelection={showBulkActions}
              compact={viewMode === 'list'}
              variant={viewMode === 'table' ? 'minimal' : 'default'}
              onSelect={handleComponentSelect}
              onEdit={onComponentEdit}
              onDelete={onComponentDelete}
              onView={onComponentView}
              onTogglePreferred={onComponentTogglePreferred}
              data-testid={`${testId || 'component-list'}-item-${component.id}`}
            />
          ))}
        </div>

        {/* Pagination */}
        {showPagination && pagination && (
          <div className="flex items-center justify-between pt-4">
            <div className="text-sm text-gray-600">
              Showing {(pagination.page - 1) * pagination.size + 1} to{' '}
              {Math.min(pagination.page * pagination.size, pagination.total)} of {pagination.total}{' '}
              results
            </div>

            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                disabled={pagination.page <= 1}
                onClick={() => onPageChange?.(pagination.page - 1)}
                data-testid={`${testId || 'component-list'}-prev-page`}
              >
                Previous
              </Button>

              <span className="text-sm">
                Page {pagination.page} of {Math.ceil(pagination.total / pagination.size)}
              </span>

              <Button
                variant="outline"
                size="sm"
                disabled={pagination.page >= Math.ceil(pagination.total / pagination.size)}
                onClick={() => onPageChange?.(pagination.page + 1)}
                data-testid={`${testId || 'component-list'}-next-page`}
              >
                Next
              </Button>
            </div>
          </div>
        )}
      </div>
    )
  }
)

ComponentList.displayName = 'ComponentList'
