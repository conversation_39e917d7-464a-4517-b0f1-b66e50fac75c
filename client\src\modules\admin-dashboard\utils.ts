/**
 * Utility functions for the admin dashboard domain
 */

import type {
  AdminDashboardMetrics,
  AuditLogEntry,
  ProjectOversightSummary,
  SecurityAlert,
  SystemConfiguration,
  UserManagementSummary,
} from './types'

/**
 * Admin permission validation utilities
 */

/**
 * Check if user has permission to perform admin action
 */
export function hasAdminPermission(action: string, userRole?: string, isAdmin?: boolean): boolean {
  // Basic admin check
  if (!isAdmin) {
    return false
  }

  // Define permission matrix for admin actions
  const adminPermissions = {
    'user:read': true,
    'user:write': true,
    'user:delete': true,
    'system:read': true,
    'system:write': true,
    'security:read': true,
    'security:write': true,
    'audit:read': true,
    'audit:export': true,
    'config:read': true,
    'config:write': true,
    'project:read': true,
    'project:write': true,
    'component:read': true,
    'component:write': true,
  }

  return adminPermissions[action as keyof typeof adminPermissions] || false
}

/**
 * Check if admin action requires confirmation
 */
export function requiresConfirmation(action: string): boolean {
  const destructiveActions = [
    'user:delete',
    'user:bulk_delete',
    'system:restart',
    'system:reset',
    'security:clear_alerts',
    'audit:clear_logs',
    'config:reset',
    'project:delete',
    'component:delete',
  ]

  return destructiveActions.includes(action)
}

/**
 * Get permission error message
 */
export function getPermissionErrorMessage(action: string): string {
  const messages = {
    'user:read': 'You do not have permission to view user information.',
    'user:write': 'You do not have permission to modify user accounts.',
    'user:delete': 'You do not have permission to delete user accounts.',
    'system:read': 'You do not have permission to view system information.',
    'system:write': 'You do not have permission to modify system settings.',
    'security:read': 'You do not have permission to view security information.',
    'security:write': 'You do not have permission to modify security settings.',
    'audit:read': 'You do not have permission to view audit logs.',
    'audit:export': 'You do not have permission to export audit data.',
    'config:read': 'You do not have permission to view system configuration.',
    'config:write': 'You do not have permission to modify system configuration.',
    'project:read': 'You do not have permission to view project information.',
    'project:write': 'You do not have permission to modify projects.',
    'component:read': 'You do not have permission to view component information.',
    'component:write': 'You do not have permission to modify components.',
  }

  return (
    messages[action as keyof typeof messages] ||
    'You do not have permission to perform this action.'
  )
}

/**
 * Format bytes to human readable format
 */
export function formatBytes(bytes: number, decimals = 2): string {
  if (bytes === 0) return '0 Bytes'

  const k = 1024
  const dm = decimals < 0 ? 0 : decimals
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB']

  const i = Math.floor(Math.log(bytes) / Math.log(k))

  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i]
}

/**
 * Format uptime duration to human readable format
 */
export function formatUptime(uptimeString: string): string {
  try {
    // Parse uptime string (e.g., "5 days, 3 hours, 45 minutes")
    const parts = uptimeString.split(', ')
    const formatted = parts.map((part) => {
      const [value, unit] = part.split(' ')
      const num = parseInt(value)
      if (num === 1) {
        return `${num} ${unit.slice(0, -1)}` // Remove 's' for singular
      }
      return part
    })

    return formatted.join(', ')
  } catch {
    return uptimeString
  }
}

/**
 * Calculate percentage with safe division
 */
export function calculatePercentage(value: number, total: number): number {
  if (total === 0) return 0
  return Math.round((value / total) * 100)
}

/**
 * Get status color based on metric value and thresholds
 */
export function getStatusColor(
  value: number,
  thresholds: { warning: number; critical: number },
  isInverted = false
): 'success' | 'warning' | 'danger' {
  if (isInverted) {
    // For metrics where lower is better (e.g., error rate)
    if (value >= thresholds.critical) return 'danger'
    if (value >= thresholds.warning) return 'warning'
    return 'success'
  } else {
    // For metrics where higher is better (e.g., uptime)
    if (value <= thresholds.critical) return 'danger'
    if (value <= thresholds.warning) return 'warning'
    return 'success'
  }
}

/**
 * Format user role for display
 */
export function formatUserRole(role: UserManagementSummary['role']): string {
  const roleMap = {
    ADMIN: 'Administrator',
    EDITOR: 'Editor',
    VIEWER: 'Viewer',
  }
  return roleMap[role] || role
}

/**
 * Get role color for UI display
 */
export function getRoleColor(role: UserManagementSummary['role']): string {
  const colorMap = {
    ADMIN: 'text-red-600 bg-red-100',
    EDITOR: 'text-yellow-600 bg-yellow-100',
    VIEWER: 'text-green-600 bg-green-100',
  }
  return colorMap[role] || 'text-gray-600 bg-gray-100'
}

/**
 * Format project status for display
 */
export function formatProjectStatus(status: ProjectOversightSummary['status']): string {
  const statusMap = {
    active: 'Active',
    completed: 'Completed',
    on_hold: 'On Hold',
    draft: 'Draft',
  }
  return statusMap[status] || status
}

/**
 * Get project status color for UI display
 */
export function getProjectStatusColor(status: ProjectOversightSummary['status']): string {
  const colorMap = {
    active: 'text-green-600 bg-green-100',
    completed: 'text-blue-600 bg-blue-100',
    on_hold: 'text-yellow-600 bg-yellow-100',
    draft: 'text-gray-600 bg-gray-100',
  }
  return colorMap[status] || 'text-gray-600 bg-gray-100'
}

/**
 * Get project priority color for UI display
 */
export function getProjectPriorityColor(priority: ProjectOversightSummary['priority']): string {
  const colorMap = {
    critical: 'text-red-600 bg-red-100',
    high: 'text-orange-600 bg-orange-100',
    medium: 'text-yellow-600 bg-yellow-100',
    low: 'text-green-600 bg-green-100',
  }
  return colorMap[priority] || 'text-gray-600 bg-gray-100'
}

/**
 * Format audit action for display
 */
export function formatAuditAction(action: AuditLogEntry['action']): string {
  const actionMap = {
    login: 'Login',
    logout: 'Logout',
    create: 'Create',
    update: 'Update',
    delete: 'Delete',
    export: 'Export',
    import: 'Import',
    admin_action: 'Admin Action',
  }
  return actionMap[action] || action
}

/**
 * Get audit action color for UI display
 */
export function getAuditActionColor(action: AuditLogEntry['action']): string {
  const colorMap = {
    login: 'text-green-600 bg-green-100',
    logout: 'text-gray-600 bg-gray-100',
    create: 'text-blue-600 bg-blue-100',
    update: 'text-yellow-600 bg-yellow-100',
    delete: 'text-red-600 bg-red-100',
    export: 'text-purple-600 bg-purple-100',
    import: 'text-indigo-600 bg-indigo-100',
    admin_action: 'text-orange-600 bg-orange-100',
  }
  return colorMap[action] || 'text-gray-600 bg-gray-100'
}

/**
 * Get security alert severity color for UI display
 */
export function getSecurityAlertColor(severity: SecurityAlert['severity']): string {
  const colorMap = {
    critical: 'text-red-600 bg-red-100 border-red-200',
    high: 'text-orange-600 bg-orange-100 border-orange-200',
    medium: 'text-yellow-600 bg-yellow-100 border-yellow-200',
    low: 'text-blue-600 bg-blue-100 border-blue-200',
  }
  return colorMap[severity] || 'text-gray-600 bg-gray-100 border-gray-200'
}

/**
 * Format security alert type for display
 */
export function formatSecurityAlertType(type: SecurityAlert['type']): string {
  const typeMap = {
    failed_login: 'Failed Login',
    suspicious_activity: 'Suspicious Activity',
    unauthorized_access: 'Unauthorized Access',
    data_breach: 'Data Breach',
    system_error: 'System Error',
  }
  return typeMap[type] || type
}

/**
 * Get configuration category color for UI display
 */
export function getConfigCategoryColor(category: SystemConfiguration['category']): string {
  const colorMap = {
    authentication: 'text-blue-600 bg-blue-100',
    security: 'text-red-600 bg-red-100',
    performance: 'text-green-600 bg-green-100',
    features: 'text-purple-600 bg-purple-100',
    integrations: 'text-yellow-600 bg-yellow-100',
  }
  return colorMap[category] || 'text-gray-600 bg-gray-100'
}

/**
 * Validate configuration value based on data type
 */
export function validateConfigValue(
  value: string,
  dataType: SystemConfiguration['dataType']
): { isValid: boolean; error?: string } {
  switch (dataType) {
    case 'number':
      if (isNaN(Number(value))) {
        return { isValid: false, error: 'Value must be a valid number' }
      }
      break
    case 'boolean':
      if (!['true', 'false'].includes(value.toLowerCase())) {
        return { isValid: false, error: 'Value must be true or false' }
      }
      break
    case 'json':
      try {
        JSON.parse(value)
      } catch {
        return { isValid: false, error: 'Value must be valid JSON' }
      }
      break
    case 'string':
      // String values are always valid
      break
    default:
      return { isValid: false, error: 'Unknown data type' }
  }

  return { isValid: true }
}

/**
 * Format relative time (e.g., "2 hours ago")
 */
export function formatRelativeTime(dateString: string): string {
  try {
    const date = new Date(dateString)
    const now = new Date()
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000)

    if (diffInSeconds < 60) {
      return 'Just now'
    } else if (diffInSeconds < 3600) {
      const minutes = Math.floor(diffInSeconds / 60)
      return `${minutes} minute${minutes === 1 ? '' : 's'} ago`
    } else if (diffInSeconds < 86400) {
      const hours = Math.floor(diffInSeconds / 3600)
      return `${hours} hour${hours === 1 ? '' : 's'} ago`
    } else if (diffInSeconds < 2592000) {
      const days = Math.floor(diffInSeconds / 86400)
      return `${days} day${days === 1 ? '' : 's'} ago`
    } else {
      return date.toLocaleDateString()
    }
  } catch {
    return 'Unknown'
  }
}

/**
 * Generate system health summary from metrics
 */
export function generateHealthSummary(metrics: AdminDashboardMetrics): {
  status: 'healthy' | 'warning' | 'critical'
  issues: string[]
  score: number
} {
  const issues: string[] = []
  let score = 100

  // Check error rate
  if (metrics.errorRate > 5) {
    issues.push('High error rate detected')
    score -= 20
  } else if (metrics.errorRate > 2) {
    issues.push('Elevated error rate')
    score -= 10
  }

  // Check memory usage
  if (metrics.memoryUsage > 90) {
    issues.push('Critical memory usage')
    score -= 25
  } else if (metrics.memoryUsage > 80) {
    issues.push('High memory usage')
    score -= 15
  }

  // Check disk usage
  if (metrics.diskUsage > 95) {
    issues.push('Critical disk usage')
    score -= 25
  } else if (metrics.diskUsage > 85) {
    issues.push('High disk usage')
    score -= 15
  }

  // Check server load
  if (metrics.serverLoad > 0.9) {
    issues.push('High server load')
    score -= 20
  } else if (metrics.serverLoad > 0.7) {
    issues.push('Elevated server load')
    score -= 10
  }

  // Determine overall status
  let status: 'healthy' | 'warning' | 'critical'
  if (score >= 80) {
    status = 'healthy'
  } else if (score >= 60) {
    status = 'warning'
  } else {
    status = 'critical'
  }

  return { status, issues, score }
}

/**
 * Filter and sort admin dashboard data
 */
export function filterAndSortData<T extends Record<string, any>>(
  data: T[],
  searchQuery: string,
  sortBy: keyof T,
  sortOrder: 'asc' | 'desc',
  searchFields: (keyof T)[]
): T[] {
  let filtered = data

  // Apply search filter
  if (searchQuery.trim()) {
    const query = searchQuery.toLowerCase()
    filtered = data.filter((item) =>
      searchFields.some((field) => {
        const value = item[field]
        return value && value.toString().toLowerCase().includes(query)
      })
    )
  }

  // Apply sorting
  filtered.sort((a, b) => {
    const aValue = a[sortBy]
    const bValue = b[sortBy]

    if (aValue === bValue) return 0

    let comparison = 0
    if (aValue > bValue) {
      comparison = 1
    } else if (aValue < bValue) {
      comparison = -1
    }

    return sortOrder === 'desc' ? -comparison : comparison
  })

  return filtered
}

/**
 * Performance monitoring utilities
 */

/**
 * Measure component render performance
 */
export function measurePerformance<T extends any[], R>(
  fn: (...args: T) => R,
  name: string
): (...args: T) => R {
  return (...args: T): R => {
    const start = performance.now()
    const result = fn(...args)
    const end = performance.now()

    if (process.env.NODE_ENV === 'development') {
      console.log(`[Performance] ${name}: ${(end - start).toFixed(2)}ms`)
    }

    return result
  }
}

/**
 * Debounce function for performance optimization
 */
export function debounce<T extends any[]>(
  func: (...args: T) => void,
  wait: number
): (...args: T) => void {
  let timeout: NodeJS.Timeout | null = null

  return (...args: T) => {
    if (timeout) {
      clearTimeout(timeout)
    }

    timeout = setTimeout(() => {
      func(...args)
    }, wait)
  }
}

/**
 * Throttle function for performance optimization
 */
export function throttle<T extends any[]>(
  func: (...args: T) => void,
  limit: number
): (...args: T) => void {
  let inThrottle = false

  return (...args: T) => {
    if (!inThrottle) {
      func(...args)
      inThrottle = true
      setTimeout(() => {
        inThrottle = false
      }, limit)
    }
  }
}

/**
 * Memoization utility for expensive calculations
 */
export function memoize<T extends any[], R>(
  fn: (...args: T) => R,
  keyGenerator?: (...args: T) => string
): (...args: T) => R {
  const cache = new Map<string, R>()

  return (...args: T): R => {
    const key = keyGenerator ? keyGenerator(...args) : JSON.stringify(args)

    if (cache.has(key)) {
      return cache.get(key)!
    }

    const result = fn(...args)
    cache.set(key, result)

    return result
  }
}

/**
 * Check if data is stale and needs refresh
 */
export function isDataStale(
  lastUpdated: string | null,
  maxAge: number = 5 * 60 * 1000 // 5 minutes default
): boolean {
  if (!lastUpdated) return true

  const now = Date.now()
  const updated = new Date(lastUpdated).getTime()

  return now - updated > maxAge
}

/**
 * Calculate data freshness percentage
 */
export function getDataFreshness(
  lastUpdated: string | null,
  maxAge: number = 5 * 60 * 1000
): number {
  if (!lastUpdated) return 0

  const now = Date.now()
  const updated = new Date(lastUpdated).getTime()
  const age = now - updated

  if (age >= maxAge) return 0

  return Math.round(((maxAge - age) / maxAge) * 100)
}

/**
 * Batch multiple operations for better performance
 */
export function batchOperations<T>(
  operations: Array<() => Promise<T>>,
  batchSize: number = 5
): Promise<T[]> {
  const batches: Array<Array<() => Promise<T>>> = []

  for (let i = 0; i < operations.length; i += batchSize) {
    batches.push(operations.slice(i, i + batchSize))
  }

  return batches.reduce(
    async (acc, batch) => {
      const results = await acc
      const batchResults = await Promise.all(batch.map((op) => op()))
      return [...results, ...batchResults]
    },
    Promise.resolve([] as T[])
  )
}
