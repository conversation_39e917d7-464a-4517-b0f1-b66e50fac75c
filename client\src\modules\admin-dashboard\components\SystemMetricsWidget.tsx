/**
 * System metrics widget for admin dashboard
 */

'use client'

import { SystemMetricsWidgetProps } from '../types'
import { formatUptime, generateHealthSummary } from '../utils'

export function SystemMetricsWidget({
  metrics,
  isLoading,
  onRefresh,
  className = '',
}: SystemMetricsWidgetProps) {
  const healthSummary = metrics ? generateHealthSummary(metrics) : null

  if (isLoading) {
    return (
      <div className={`overflow-hidden rounded-lg bg-white shadow ${className}`}>
        <div className="p-6">
          <div className="animate-pulse">
            <div className="mb-4 h-6 w-1/2 rounded bg-gray-200"></div>
            <div className="space-y-4">
              {Array.from({ length: 4 }).map((_, i) => (
                <div key={i} className="flex items-center justify-between">
                  <div className="h-4 w-1/3 rounded bg-gray-200"></div>
                  <div className="h-4 w-1/4 rounded bg-gray-200"></div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    )
  }

  if (!metrics) {
    return (
      <div className={`overflow-hidden rounded-lg bg-white shadow ${className}`}>
        <div className="p-6">
          <div className="text-center">
            <svg
              className="mx-auto h-12 w-12 text-gray-400"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
              />
            </svg>
            <h3 className="mt-2 text-sm font-medium text-gray-900">No metrics available</h3>
            <p className="mt-1 text-sm text-gray-500">System metrics could not be loaded.</p>
          </div>
        </div>
      </div>
    )
  }

  const systemHealthColor =
    healthSummary?.status === 'healthy'
      ? 'text-green-600 bg-green-100'
      : healthSummary?.status === 'warning'
        ? 'text-yellow-600 bg-yellow-100'
        : 'text-red-600 bg-red-100'

  return (
    <article
      className={`overflow-hidden rounded-lg bg-white shadow ${className}`}
      aria-labelledby="system-metrics-title"
    >
      <div className="p-6">
        {/* Header */}
        <header className="flex items-center justify-between">
          <h3 id="system-metrics-title" className="text-lg font-medium text-gray-900">
            System Metrics
          </h3>
          <div className="flex items-center space-x-2">
            <span
              className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${systemHealthColor}`}
            >
              {healthSummary?.status === 'healthy' && (
                <svg className="mr-1 h-3 w-3" fill="currentColor" viewBox="0 0 20 20">
                  <path
                    fillRule="evenodd"
                    d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                    clipRule="evenodd"
                  />
                </svg>
              )}
              {healthSummary?.status === 'warning' && (
                <svg className="mr-1 h-3 w-3" fill="currentColor" viewBox="0 0 20 20">
                  <path
                    fillRule="evenodd"
                    d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                    clipRule="evenodd"
                  />
                </svg>
              )}
              {healthSummary?.status === 'critical' && (
                <svg className="mr-1 h-3 w-3" fill="currentColor" viewBox="0 0 20 20">
                  <path
                    fillRule="evenodd"
                    d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                    clipRule="evenodd"
                  />
                </svg>
              )}
              {healthSummary?.status || 'Unknown'}
            </span>
            {onRefresh && (
              <button
                onClick={onRefresh}
                className="rounded-md p-1 text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500"
                aria-label="Refresh metrics"
              >
                <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                  />
                </svg>
              </button>
            )}
          </div>
        </header>

        {/* Metrics Grid */}
        <section className="mt-6 grid grid-cols-2 gap-4" aria-label="System Performance Metrics">
          {/* System Uptime */}
          <div className="rounded-lg bg-gray-50 p-4">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <svg
                  className="h-6 w-6 text-green-400"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                </svg>
              </div>
              <div className="ml-3 w-0 flex-1">
                <dl>
                  <dt className="truncate text-sm font-medium text-gray-500">System Uptime</dt>
                  <dd className="text-lg font-medium text-gray-900">
                    {formatUptime(metrics.systemUptime)}
                  </dd>
                </dl>
              </div>
            </div>
          </div>

          {/* Server Load */}
          <div className="rounded-lg bg-gray-50 p-4">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <svg
                  className="h-6 w-6 text-blue-400"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
                  />
                </svg>
              </div>
              <div className="ml-3 w-0 flex-1">
                <dl>
                  <dt className="truncate text-sm font-medium text-gray-500">Server Load</dt>
                  <dd className="text-lg font-medium text-gray-900">
                    {(metrics.serverLoad * 100).toFixed(1)}%
                  </dd>
                </dl>
              </div>
            </div>
          </div>

          {/* Memory Usage */}
          <div className="rounded-lg bg-gray-50 p-4">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <svg
                  className="h-6 w-6 text-yellow-400"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"
                  />
                </svg>
              </div>
              <div className="ml-3 w-0 flex-1">
                <dl>
                  <dt className="truncate text-sm font-medium text-gray-500">Memory Usage</dt>
                  <dd className="text-lg font-medium text-gray-900">{metrics.memoryUsage}%</dd>
                </dl>
              </div>
            </div>
          </div>

          {/* Disk Usage */}
          <div className="rounded-lg bg-gray-50 p-4">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <svg
                  className="h-6 w-6 text-purple-400"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4"
                  />
                </svg>
              </div>
              <div className="ml-3 w-0 flex-1">
                <dl>
                  <dt className="truncate text-sm font-medium text-gray-500">Disk Usage</dt>
                  <dd className="text-lg font-medium text-gray-900">{metrics.diskUsage}%</dd>
                </dl>
              </div>
            </div>
          </div>
        </section>

        {/* Additional Metrics */}
        <section className="mt-6 space-y-3" aria-label="Additional System Information">
          <div className="flex items-center justify-between text-sm">
            <span className="text-gray-500">Database Connections</span>
            <span className="font-medium text-gray-900">{metrics.databaseConnections}</span>
          </div>
          <div className="flex items-center justify-between text-sm">
            <span className="text-gray-500">API Requests Today</span>
            <span className="font-medium text-gray-900">
              {metrics.apiRequestsToday.toLocaleString()}
            </span>
          </div>
          <div className="flex items-center justify-between text-sm">
            <span className="text-gray-500">Error Rate</span>
            <span
              className={`font-medium ${
                metrics.errorRate > 5
                  ? 'text-red-600'
                  : metrics.errorRate > 2
                    ? 'text-yellow-600'
                    : 'text-green-600'
              }`}
            >
              {metrics.errorRate.toFixed(2)}%
            </span>
          </div>
          <div className="flex items-center justify-between text-sm">
            <span className="text-gray-500">Last Backup</span>
            <span className="font-medium text-gray-900">
              {new Date(metrics.lastBackup).toLocaleDateString()}
            </span>
          </div>
        </section>

        {/* Health Issues */}
        {healthSummary?.issues && healthSummary.issues.length > 0 && (
          <div className="mt-6 rounded-md bg-yellow-50 p-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                  <path
                    fillRule="evenodd"
                    d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                    clipRule="evenodd"
                  />
                </svg>
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-yellow-800">System Issues Detected</h3>
                <div className="mt-2 text-sm text-yellow-700">
                  <ul className="list-disc space-y-1 pl-5">
                    {healthSummary.issues.map((issue, index) => (
                      <li key={index}>{issue}</li>
                    ))}
                  </ul>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </article>
  )
}
