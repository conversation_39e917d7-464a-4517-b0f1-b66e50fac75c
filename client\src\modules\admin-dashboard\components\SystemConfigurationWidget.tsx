/**
 * System configuration widget for admin dashboard
 */

'use client'

import React, { useState } from 'react'
import { SystemConfigurationWidgetProps } from '../types'
import { getConfigCategoryColor, validateConfigValue } from '../utils'

export function SystemConfigurationWidget({
  configurations,
  isLoading,
  onConfigChange,
  onViewAll,
  className = '',
}: SystemConfigurationWidgetProps) {
  const [filter, setFilter] = useState<
    'all' | 'authentication' | 'security' | 'performance' | 'features' | 'integrations'
  >('all')
  const [editingConfig, setEditingConfig] = useState<string | null>(null)
  const [editValue, setEditValue] = useState('')

  // Filter configurations based on selected filter
  const filteredConfigs =
    filter === 'all'
      ? configurations
      : configurations.filter((config) => config.category === filter)

  // Group configurations by category
  const configsByCategory = filteredConfigs.reduce(
    (acc, config) => {
      if (!acc[config.category]) {
        acc[config.category] = []
      }
      acc[config.category].push(config)
      return acc
    },
    {} as Record<string, typeof configurations>
  )

  const handleEditStart = (config: (typeof configurations)[0]) => {
    setEditingConfig(config.id)
    setEditValue(config.value)
  }

  const handleEditCancel = () => {
    setEditingConfig(null)
    setEditValue('')
  }

  const handleEditSave = (config: (typeof configurations)[0]) => {
    const validation = validateConfigValue(editValue, config.dataType)
    if (!validation.isValid) {
      alert(validation.error)
      return
    }

    if (onConfigChange) {
      onConfigChange(config, editValue)
    }
    setEditingConfig(null)
    setEditValue('')
  }

  if (isLoading) {
    return (
      <div className={`overflow-hidden rounded-lg bg-white shadow ${className}`}>
        <div className="p-6">
          <div className="animate-pulse">
            <div className="mb-4 h-6 w-1/2 rounded bg-gray-200"></div>
            <div className="space-y-3">
              {Array.from({ length: 6 }).map((_, i) => (
                <div key={i} className="flex items-center justify-between">
                  <div className="h-4 w-1/3 rounded bg-gray-200"></div>
                  <div className="h-4 w-1/4 rounded bg-gray-200"></div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className={`overflow-hidden rounded-lg bg-white shadow ${className}`}>
      <div className="p-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-medium text-gray-900">System Configuration</h3>
          {onViewAll && (
            <button onClick={onViewAll} className="text-sm text-blue-600 hover:text-blue-500">
              View All Settings
            </button>
          )}
        </div>

        {/* Filter Tabs */}
        <div className="mt-6">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8">
              {[
                { key: 'all', label: 'All Settings' },
                { key: 'authentication', label: 'Authentication' },
                { key: 'security', label: 'Security' },
                { key: 'performance', label: 'Performance' },
                { key: 'features', label: 'Features' },
                { key: 'integrations', label: 'Integrations' },
              ].map((tab) => (
                <button
                  key={tab.key}
                  onClick={() => setFilter(tab.key as any)}
                  className={`whitespace-nowrap border-b-2 px-1 py-2 text-sm font-medium ${
                    filter === tab.key
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700'
                  }`}
                >
                  {tab.label}
                </button>
              ))}
            </nav>
          </div>
        </div>

        {/* Configuration Groups */}
        <div className="mt-6 space-y-6">
          {Object.entries(configsByCategory).map(([category, configs]) => (
            <div key={category}>
              <h4 className="mb-3 flex items-center text-sm font-medium text-gray-900">
                <span
                  className={`mr-2 inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${getConfigCategoryColor(category as any)}`}
                >
                  {category.charAt(0).toUpperCase() + category.slice(1)}
                </span>
                ({configs.length} settings)
              </h4>

              <div className="space-y-3">
                {configs.map((config) => (
                  <div
                    key={config.id}
                    className="flex items-center justify-between rounded-lg border border-gray-200 p-4"
                  >
                    <div className="min-w-0 flex-1">
                      <div className="flex items-center space-x-2">
                        <h5 className="text-sm font-medium text-gray-900">{config.key}</h5>
                        <span className="inline-flex items-center rounded-full bg-gray-100 px-2 py-1 text-xs font-medium text-gray-800">
                          {config.dataType}
                        </span>
                        {config.requiresRestart && (
                          <span className="inline-flex items-center rounded-full bg-yellow-100 px-2 py-1 text-xs font-medium text-yellow-800">
                            Requires Restart
                          </span>
                        )}
                      </div>

                      <p className="mt-1 text-sm text-gray-500">{config.description}</p>

                      <div className="mt-2 flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          {editingConfig === config.id ? (
                            <div className="flex items-center space-x-2">
                              <input
                                type="text"
                                value={editValue}
                                onChange={(e) => setEditValue(e.target.value)}
                                className="block w-48 rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                                placeholder={config.value}
                              />
                              <button
                                onClick={() => handleEditSave(config)}
                                className="inline-flex items-center rounded-md bg-green-600 px-2 py-1 text-xs font-medium text-white hover:bg-green-500"
                              >
                                Save
                              </button>
                              <button
                                onClick={handleEditCancel}
                                className="inline-flex items-center rounded-md bg-gray-600 px-2 py-1 text-xs font-medium text-white hover:bg-gray-500"
                              >
                                Cancel
                              </button>
                            </div>
                          ) : (
                            <div className="flex items-center space-x-2">
                              <span className="rounded bg-gray-100 px-2 py-1 font-mono text-sm text-gray-900">
                                {config.dataType === 'json'
                                  ? JSON.stringify(JSON.parse(config.value), null, 2).slice(0, 50) +
                                    '...'
                                  : config.value}
                              </span>
                              {config.isEditable && (
                                <button
                                  onClick={() => handleEditStart(config)}
                                  className="text-sm text-blue-600 hover:text-blue-500"
                                >
                                  Edit
                                </button>
                              )}
                            </div>
                          )}
                        </div>

                        <div className="text-xs text-gray-500">
                          Modified by {config.modifiedBy} on{' '}
                          {new Date(config.lastModified).toLocaleDateString()}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>

        {filteredConfigs.length === 0 && (
          <div className="py-6 text-center">
            <svg
              className="mx-auto h-12 w-12 text-gray-400"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"
              />
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
              />
            </svg>
            <h3 className="mt-2 text-sm font-medium text-gray-900">No configuration settings</h3>
            <p className="mt-1 text-sm text-gray-500">
              {filter === 'all'
                ? 'No configuration settings found.'
                : `No ${filter} configuration settings found.`}
            </p>
          </div>
        )}

        {/* Configuration Summary */}
        <div className="mt-6 rounded-lg bg-gray-50 p-4">
          <h4 className="mb-2 text-sm font-medium text-gray-900">Configuration Summary</h4>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-gray-500">Total Settings:</span>
              <span className="ml-2 font-medium text-gray-900">{configurations.length}</span>
            </div>
            <div>
              <span className="text-gray-500">Editable:</span>
              <span className="ml-2 font-medium text-gray-900">
                {configurations.filter((c) => c.isEditable).length}
              </span>
            </div>
            <div>
              <span className="text-gray-500">Require Restart:</span>
              <span className="ml-2 font-medium text-gray-900">
                {configurations.filter((c) => c.requiresRestart).length}
              </span>
            </div>
            <div>
              <span className="text-gray-500">Categories:</span>
              <span className="ml-2 font-medium text-gray-900">
                {Object.keys(configsByCategory).length}
              </span>
            </div>
          </div>
        </div>

        {/* Warning for restart required */}
        {configurations.some((c) => c.requiresRestart) && (
          <div className="mt-4 rounded-md bg-yellow-50 p-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                  <path
                    fillRule="evenodd"
                    d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                    clipRule="evenodd"
                  />
                </svg>
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-yellow-800">Restart Required</h3>
                <div className="mt-2 text-sm text-yellow-700">
                  Some configuration changes require a system restart to take effect.
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
