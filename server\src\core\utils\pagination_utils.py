# src/core/utils/pagination_utils.py
"""Pagination Utilities.

This module provides standardized pagination, sorting, and filtering utilities
for database queries, particularly with SQLAlchemy.

Key Features:
- Standardized pagination logic
- Query parameter parsing and validation
- Sorting and filtering helpers
- Pagination metadata calculation
"""

import math
from dataclasses import dataclass
from typing import Any, Dict, List, Optional, Type, TypeVar, Union

from sqlalchemy import asc, desc, func, select
from sqlalchemy.orm import Query, Session
from sqlalchemy.sql import Select

from src.config.logging_config import logger

# Type variable for SQLAlchemy models
ModelType = TypeVar("ModelType")


@dataclass
class PaginationParams:
    """Parameters for pagination."""

    page: int = 1
    per_page: int = 10
    max_per_page: int = 100

    def __post_init__(self) -> None:
        """Validate pagination parameters."""
        if self.page < 1:
            self.page = 1
        if self.per_page < 1:
            self.per_page = 10
        if self.per_page > self.max_per_page:
            self.per_page = self.max_per_page

    @property
    def limit(self) -> int:
        """Alias for per_page for backward compatibility."""
        return self.per_page

    @property
    def offset(self) -> int:
        """Calculate offset based on page and per_page."""
        return (self.page - 1) * self.per_page


@dataclass
class SortParams:
    """Parameters for sorting."""

    sort_by: Optional[str] = None
    sort_order: str = "asc"  # 'asc' or 'desc'

    def __post_init__(self) -> None:
        """Validate sort parameters."""
        if self.sort_order not in ("asc", "desc"):
            self.sort_order = "asc"


@dataclass
class PaginationResult:
    """Result of pagination operation."""

    items: List[Any]
    total: int
    page: int
    per_page: int
    total_pages: int
    has_next: bool
    has_prev: bool
    next_page: Optional[int]
    prev_page: Optional[int]


def calculate_pagination_metadata(
    total: int, page: int, per_page: int
) -> Dict[str, Any]:
    """Calculate pagination metadata.

    Args:
        total: Total number of items
        page: Current page number (1-based)
        per_page: Items per page

    Returns:
        Dict containing pagination metadata

    """
    total = total if total is not None else 0  # Handle case where total is None
    total_pages = math.ceil(total / per_page) if total > 0 else 1
    has_next = page < total_pages
    has_prev = page > 1
    next_page = page + 1 if has_next else None
    prev_page = page - 1 if has_prev else None

    return {
        "total": total,
        "page": page,
        "per_page": per_page,
        "total_pages": total_pages,
        "has_next": has_next,
        "has_prev": has_prev,
        "next_page": next_page,
        "prev_page": prev_page,
    }


def apply_pagination(
    query: Union[Query[Any], Select[Any]], pagination_params: PaginationParams
) -> Union[Query[Any], Select[Any]]:
    """Apply pagination to SQLAlchemy query.

    Args:
        query: SQLAlchemy query or select statement
        pagination_params: Pagination parameters

    Returns:
        Query with pagination applied

    """
    offset = (pagination_params.page - 1) * pagination_params.per_page
    return query.offset(offset).limit(pagination_params.per_page)


def apply_sorting(
    query: Union[Query[Any], Select[Any]],
    model: Type[ModelType],
    sort_params: SortParams,
    allowed_fields: Optional[List[str]] = None,
) -> Union[Query[Any], Select[Any]]:
    """Apply sorting to SQLAlchemy query.

    Args:
        query: SQLAlchemy query or select statement
        model: SQLAlchemy model class
        sort_params: Sort parameters
        allowed_fields: List of allowed sort fields (None for all)

    Returns:
        Query with sorting applied

    Raises:
        ValueError: If sort field is not allowed

    """
    if not sort_params.sort_by:
        return query

    # Validate sort field
    if allowed_fields and sort_params.sort_by not in allowed_fields:
        raise ValueError(
            f"Sort field '{sort_params.sort_by}' not allowed. Allowed fields: {allowed_fields}"
        )

    # Check if field exists on model
    if not hasattr(model, sort_params.sort_by):
        raise ValueError(
            f"Sort field '{sort_params.sort_by}' does not exist on model {model.__name__}"
        )

    # Get the column
    column = getattr(model, sort_params.sort_by)

    # Apply sorting
    if sort_params.sort_order == "desc":
        return query.order_by(desc(column))
    return query.order_by(asc(column))


def paginate_query(
    session: Session,
    query: Union[Query[Any], Select[Any]],
    model: Type[ModelType],
    pagination_params: PaginationParams,
    sort_params: Optional[SortParams] = None,
    allowed_sort_fields: Optional[List[str]] = None,
) -> PaginationResult:
    """Apply pagination and sorting to query and return results.

    Args:
        session: SQLAlchemy session
        query: Base query to paginate
        model: SQLAlchemy model class
        pagination_params: Pagination parameters
        sort_params: Sort parameters (optional)
        allowed_sort_fields: Allowed sort fields (optional)

    Returns:
        PaginationResult with items and metadata

    """
    # Apply sorting if provided
    if sort_params:
        query = apply_sorting(query, model, sort_params, allowed_sort_fields)

    # Get total count (before pagination)
    if isinstance(query, Select):
        count_query = session.scalar(select(func.count()).select_from(query.subquery()))
        total = count_query or 0
    else:
        total = query.count()

    # Apply pagination
    paginated_query = apply_pagination(query, pagination_params)

    # Execute query
    if isinstance(paginated_query, Select):
        items = list(session.scalars(paginated_query).all())
    else:
        items = paginated_query.all()

    # Calculate metadata
    metadata = calculate_pagination_metadata(
        total=total, page=pagination_params.page, per_page=pagination_params.per_page
    )

    return PaginationResult(items=items, **metadata)


def parse_pagination_params(
    page: Optional[int] = None, per_page: Optional[int] = None, max_per_page: int = 100
) -> PaginationParams:
    """Parse and validate pagination parameters from request.

    Args:
        page: Page number from request
        per_page: Items per page from request
        max_per_page: Maximum allowed items per page

    Returns:
        PaginationParams with validated values

    """
    return PaginationParams(
        page=page or 1, per_page=per_page or 10, max_per_page=max_per_page
    )


def parse_sort_params(
    sort_by: Optional[str] = None, sort_order: Optional[str] = None
) -> SortParams:
    """Parse and validate sort parameters from request.

    Args:
        sort_by: Field to sort by
        sort_order: Sort order ('asc' or 'desc')

    Returns:
        SortParams with validated values

    """
    return SortParams(sort_by=sort_by, sort_order=sort_order or "asc")


def create_pagination_response(
    items: List[Any],
    pagination_result: PaginationResult,
    item_schema: Optional[Type[Any]] = None,
) -> Dict[str, Any]:
    """Create standardized pagination response.

    Args:
        items: List of items (already converted to schemas if needed)
        pagination_result: Pagination result with metadata
        item_schema: Optional schema class for item conversion

    Returns:
        Dict with standardized pagination response format

    """
    # Convert items to schema if provided
    if item_schema:
        if hasattr(item_schema, "model_validate"):
            # Pydantic v2
            items = [item_schema.model_validate(item) for item in items]
        elif hasattr(item_schema, "from_orm"):
            # Pydantic v1
            items = [item_schema.from_orm(item) for item in items]

    return {
        "items": items,
        "pagination": {
            "total": pagination_result.total,
            "page": pagination_result.page,
            "per_page": pagination_result.per_page,
            "total_pages": pagination_result.total_pages,
            "has_next": pagination_result.has_next,
            "has_prev": pagination_result.has_prev,
            "next_page": pagination_result.next_page,
            "prev_page": pagination_result.prev_page,
        },
    }


# Filtering utilities


def apply_text_filter(
    query: Union[Query[Any], Select[Any]],
    model: Type[ModelType],
    field_name: str,
    search_term: str,
    case_sensitive: bool = False,
) -> Union[Query[Any], Select[Any]]:
    """Apply text search filter to query.

    Args:
        query: SQLAlchemy query
        model: Model class
        field_name: Field to search in
        search_term: Text to search for
        case_sensitive: Whether search is case sensitive

    Returns:
        Query with text filter applied

    """
    if not search_term or not hasattr(model, field_name):
        return query

    field = getattr(model, field_name)

    if case_sensitive:
        return query.where(field.contains(search_term))
    return query.where(field.ilike(f"%{search_term}%"))


def apply_date_range_filter(
    query: Union[Query[Any], Select[Any]],
    model: Type[ModelType],
    field_name: str,
    start_date: Optional[Any] = None,
    end_date: Optional[Any] = None,
) -> Union[Query[Any], Select[Any]]:
    """Apply date range filter to query.

    Args:
        query: SQLAlchemy query
        model: Model class
        field_name: Date field name
        start_date: Start of date range
        end_date: End of date range

    Returns:
        Query with date range filter applied

    """
    if not hasattr(model, field_name):
        return query

    field = getattr(model, field_name)

    if start_date:
        query = query.where(field >= start_date)
    if end_date:
        query = query.where(field <= end_date)

    return query
