/**
 * Reusable Form Field Component
 * Provides consistent styling and accessibility for form inputs
 */

'use client'

import { forwardRef } from 'react'
import { cn } from '@/lib/utils'

interface FormFieldProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string
  error?: string
  helperText?: string
  icon?: React.ReactNode
  required?: boolean
  variant?: 'default' | 'email' | 'text'
}

export const FormField = forwardRef<HTMLInputElement, FormFieldProps>(
  (
    {
      label,
      error,
      helperText,
      icon,
      required = false,
      variant = 'default',
      className,
      type = 'text',
      ...props
    },
    ref
  ) => {
    const inputId = props.id || `field-${Math.random().toString(36).substr(2, 9)}`

    return (
      <div className="space-y-2">
        {/* Label */}
        {label && (
          <label htmlFor={inputId} className="form-label">
            {label}
            {required && (
              <span className="ml-1 text-error" aria-label="required">
                *
              </span>
            )}
          </label>
        )}

        {/* Input Container */}
        <div className="relative">
          {/* Icon */}
          {icon && (
            <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
              <div className="h-5 w-5 text-neutral-400" aria-hidden="true">
                {icon}
              </div>
            </div>
          )}

          {/* Input */}
          <input
            ref={ref}
            id={inputId}
            type={type}
            className={cn(
              'form-input',
              {
                'pl-10': icon,
                'pl-4': !icon,
                'border-error focus:border-error focus:ring-error/20': error,
                'border-neutral-300 focus:border-brand-secondary focus:ring-brand-secondary/20':
                  !error,
              },
              className
            )}
            aria-invalid={error ? 'true' : 'false'}
            aria-describedby={
              error ? `${inputId}-error` : helperText ? `${inputId}-helper` : undefined
            }
            {...props}
          />
        </div>

        {/* Error Message */}
        {error && (
          <p id={`${inputId}-error`} className="form-error" role="alert" aria-live="polite">
            {error}
          </p>
        )}

        {/* Helper Text */}
        {helperText && !error && (
          <p id={`${inputId}-helper`} className="text-sm text-neutral-500">
            {helperText}
          </p>
        )}
      </div>
    )
  }
)

FormField.displayName = 'FormField'

/**
 * Email Input Component
 * Specialized input for email addresses with validation
 */
interface EmailInputProps extends Omit<FormFieldProps, 'type' | 'variant' | 'icon'> {
  showValidationIcon?: boolean
}

export const EmailInput = forwardRef<HTMLInputElement, EmailInputProps>(
  ({ showValidationIcon = true, value = '', ...props }, ref) => {
    const emailValue = String(value)
    const isValidEmail = emailValue && /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(emailValue)

    const emailIcon = (
      <svg fill="none" stroke="currentColor" viewBox="0 0 24 24" className="h-full w-full">
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207"
        />
      </svg>
    )

    return (
      <div className="space-y-2">
        <FormField
          ref={ref}
          type="email"
          icon={emailIcon}
          autoComplete="email"
          value={value}
          {...props}
        />

        {/* Email Validation Indicator */}
        {showValidationIcon && emailValue && (
          <div className="flex items-center space-x-2">
            <div
              className={cn(
                'flex h-4 w-4 items-center justify-center rounded-full transition-colors duration-200',
                {
                  'bg-green-100 text-green-600': isValidEmail,
                  'bg-red-100 text-red-600': !isValidEmail,
                }
              )}
            >
              {isValidEmail ? (
                <svg className="h-3 w-3" fill="currentColor" viewBox="0 0 20 20">
                  <path
                    fillRule="evenodd"
                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                    clipRule="evenodd"
                  />
                </svg>
              ) : (
                <svg className="h-3 w-3" fill="currentColor" viewBox="0 0 20 20">
                  <path
                    fillRule="evenodd"
                    d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                    clipRule="evenodd"
                  />
                </svg>
              )}
            </div>
            <span
              className={cn('text-sm transition-colors duration-200', {
                'text-green-700': isValidEmail,
                'text-red-600': !isValidEmail,
              })}
            >
              {isValidEmail ? 'Valid email format' : 'Invalid email format'}
            </span>
          </div>
        )}
      </div>
    )
  }
)

EmailInput.displayName = 'EmailInput'

/**
 * Username Input Component
 * Specialized input for usernames with validation
 */
interface UsernameInputProps extends Omit<FormFieldProps, 'type' | 'variant' | 'icon'> {
  showValidationIcon?: boolean
}

export const UsernameInput = forwardRef<HTMLInputElement, UsernameInputProps>(
  ({ showValidationIcon = true, value = '', ...props }, ref) => {
    const usernameValue = String(value)
    const isValidUsername =
      usernameValue && /^[a-zA-Z0-9_-]+$/.test(usernameValue) && usernameValue.length >= 3

    const userIcon = (
      <svg fill="none" stroke="currentColor" viewBox="0 0 24 24" className="h-full w-full">
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
        />
      </svg>
    )

    return (
      <div className="space-y-2">
        <FormField
          ref={ref}
          type="text"
          icon={userIcon}
          autoComplete="username"
          value={value}
          {...props}
        />

        {/* Username Validation Indicator */}
        {showValidationIcon && usernameValue && (
          <div className="flex items-center space-x-2">
            <div
              className={cn(
                'flex h-4 w-4 items-center justify-center rounded-full transition-colors duration-200',
                {
                  'bg-green-100 text-green-600': isValidUsername,
                  'bg-red-100 text-red-600': !isValidUsername,
                }
              )}
            >
              {isValidUsername ? (
                <svg className="h-3 w-3" fill="currentColor" viewBox="0 0 20 20">
                  <path
                    fillRule="evenodd"
                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                    clipRule="evenodd"
                  />
                </svg>
              ) : (
                <svg className="h-3 w-3" fill="currentColor" viewBox="0 0 20 20">
                  <path
                    fillRule="evenodd"
                    d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                    clipRule="evenodd"
                  />
                </svg>
              )}
            </div>
            <span
              className={cn('text-sm transition-colors duration-200', {
                'text-green-700': isValidUsername,
                'text-red-600': !isValidUsername,
              })}
            >
              {isValidUsername
                ? 'Valid username format'
                : 'Username must be 3+ characters, letters/numbers only'}
            </span>
          </div>
        )}
      </div>
    )
  }
)

UsernameInput.displayName = 'UsernameInput'
