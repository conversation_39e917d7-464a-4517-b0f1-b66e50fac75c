#!/usr/bin/env python3
"""Component Type Service for Ultimate Electrical Designer.

This module provides comprehensive business logic for component type management
operations, including CRUD operations, validation, category relationships, and
type classification for electrical components.

Key Features:
- Complete type lifecycle management (create, read, update, delete)
- Category relationship validation and management
- Type validation and business rule enforcement
- Bulk operations support with transaction management
- Specifications template management
- Audit logging and change tracking
- Professional electrical design standards compliance
"""

import json
from typing import Any, Dict, List, Optional, Tuple

from src.config.logging_config import logger
from src.core.errors.exceptions import (
    BusinessLogicError,
    NotFoundError,
    ValidationError,
)
from src.core.errors.unified_error_handler import handle_service_errors
from src.core.models.general.component_type import ComponentType
from src.core.monitoring.unified_performance_monitor import monitor_service_performance
from src.core.repositories.general.component_type_repository import (
    ComponentTypeRepository,
)
from src.core.schemas.general.component_type_schemas import (
    ComponentTypeCreateSchema,
    ComponentTypeListResponseSchema,
    ComponentTypeReadSchema,
    ComponentTypeSearchSchema,
    ComponentTypeSummarySchema,
    ComponentTypeUpdateSchema,
    ComponentTypeValidationResultSchema,
)
from src.core.utils.pagination_utils import PaginationParams


class ComponentTypeService:
    """Service for component type business logic operations.
    
    This service provides comprehensive business logic for component type
    management, including validation, category relationships, and
    professional electrical design standards compliance.
    """

    def __init__(self, type_repo: ComponentTypeRepository):
        """Initialize service with repository dependency.
        
        Args:
            type_repo: ComponentTypeRepository instance

        """
        self.type_repo = type_repo

    @handle_service_errors("create_component_type")
    @monitor_service_performance("create_component_type")
    def create_type(self, type_data: ComponentTypeCreateSchema) -> ComponentTypeReadSchema:
        """Create a new component type.
        
        Args:
            type_data: Component type creation data
            
        Returns:
            ComponentTypeReadSchema: Created component type data
            
        Raises:
            ValidationError: If type data is invalid
            BusinessLogicError: If business rules are violated

        """
        logger.info(f"Creating component type: {type_data.name}")

        # Validate type data
        validation_result = self._validate_type_data(type_data)
        if not validation_result.is_valid:
            raise ValidationError(
                f"Component type validation failed: {', '.join(validation_result.errors)}"
            )

        # Check for duplicate types in the same category
        existing = self.type_repo.get_by_name(type_data.name, type_data.category_id)
        if existing:
            raise BusinessLogicError(
                detail=f"Component type '{type_data.name}' already exists in this category"
            )

        # Validate category exists
        if not self.type_repo.validate_category_exists(type_data.category_id):
            raise ValidationError("Category does not exist or is inactive")

        # Create component type
        type_dict = type_data.model_dump(exclude_unset=True)
        component_type = self.type_repo.create(type_dict)

        logger.info(f"Created component type: {component_type.id}")
        return self._convert_to_read_schema(component_type)

    @handle_service_errors("get_component_type")
    @monitor_service_performance("get_component_type")
    def get_type(self, type_id: int) -> ComponentTypeReadSchema:
        """Get component type by ID.
        
        Args:
            type_id: Component type ID
            
        Returns:
            ComponentTypeReadSchema: Component type data
            
        Raises:
            NotFoundError: If component type not found

        """
        logger.debug(f"Retrieving component type: {type_id}")

        component_type = self.type_repo.get_by_id(type_id)
        if not component_type or component_type.is_deleted:
            raise NotFoundError(
                code="COMPONENT_TYPE_NOT_FOUND",
                detail=f"Component type {type_id} not found"
            )

        return self._convert_to_read_schema(component_type)

    @handle_service_errors("update_component_type")
    @monitor_service_performance("update_component_type")
    def update_type(
        self, type_id: int, type_data: ComponentTypeUpdateSchema
    ) -> ComponentTypeReadSchema:
        """Update component type.
        
        Args:
            type_id: Component type ID
            type_data: Component type update data
            
        Returns:
            ComponentTypeReadSchema: Updated component type data
            
        Raises:
            NotFoundError: If component type not found
            ValidationError: If update data is invalid
            BusinessLogicError: If business rules are violated

        """
        logger.info(f"Updating component type: {type_id}")

        # Validate type exists
        component_type = self.type_repo.get_by_id(type_id)
        if not component_type or component_type.is_deleted:
            raise NotFoundError(
                code="COMPONENT_TYPE_NOT_FOUND",
                detail=f"Component type {type_id} not found"
            )

        # Validate category if being changed
        if type_data.category_id is not None and type_data.category_id != component_type.category_id:
            if not self.type_repo.validate_category_exists(type_data.category_id):
                raise ValidationError("New category does not exist or is inactive")

        # Check for name conflicts if name is being changed
        if type_data.name and type_data.name != component_type.name:
            category_id = type_data.category_id or component_type.category_id
            existing = self.type_repo.get_by_name(type_data.name, category_id)
            if existing and existing.id != type_id:
                raise BusinessLogicError(
                    f"Component type '{type_data.name}' already exists in this category"
                )

        # Update component type
        update_dict = type_data.model_dump(exclude_unset=True)
        updated_type = self.type_repo.update(type_id, update_dict)

        logger.info(f"Updated component type: {type_id}")
        return self._convert_to_read_schema(updated_type)

    @handle_service_errors("delete_component_type")
    @monitor_service_performance("delete_component_type")
    def delete_type(self, type_id: int, deleted_by_user_id: Optional[int] = None) -> bool:
        """Soft delete a component type with dependency checking.
        
        Args:
            type_id: Component type ID
            deleted_by_user_id: ID of user performing deletion
            
        Returns:
            bool: True if deleted successfully
            
        Raises:
            NotFoundError: If component type not found
            BusinessLogicError: If component type has dependencies

        """
        logger.info(f"Deleting component type: {type_id}")

        # Validate type exists
        component_type = self.type_repo.get_by_id(type_id)
        if not component_type or component_type.is_deleted:
            raise NotFoundError(
                code="COMPONENT_TYPE_NOT_FOUND",
                detail=f"Component type {type_id} not found"
            )

        # Check if type can be deleted
        can_delete, reason = component_type.can_delete()
        if not can_delete:
            raise BusinessLogicError(f"Cannot delete component type: {reason}")

        # Perform soft delete
        component_type.soft_delete(deleted_by_user_id)
        self.type_repo.db_session.commit()

        logger.info(f"Deleted component type: {type_id}")
        return True

    @handle_service_errors("list_component_types")
    @monitor_service_performance("list_component_types")
    def list_types(
        self, 
        search_schema: Optional[ComponentTypeSearchSchema] = None,
        pagination: Optional[PaginationParams] = None
    ) -> ComponentTypeListResponseSchema:
        """List component types with filtering and pagination.
        
        Args:
            search_schema: Optional search parameters
            pagination: Optional pagination parameters
            
        Returns:
            ComponentTypeListResponseSchema: Paginated component type list

        """
        logger.debug("Listing component types")

        # Set defaults
        if search_schema is None:
            search_schema = ComponentTypeSearchSchema()
        if pagination is None:
            pagination = PaginationParams()

        # Get component types
        types, total_count = self.type_repo.search_types(search_schema, pagination)

        # Convert to summary schemas
        type_summaries = [
            self._convert_to_summary_schema(component_type) for component_type in types
        ]

        # Build response
        response = ComponentTypeListResponseSchema(
            component_types=type_summaries,
            total_count=total_count,
            page=pagination.page,
            page_size=pagination.limit,
            total_pages=(total_count + pagination.limit - 1) // pagination.limit,
            has_next=pagination.offset + len(types) < total_count,
            has_previous=pagination.page > 1,
        )

        logger.debug(f"Listed {len(types)} component types (total: {total_count})")
        return response

    @handle_service_errors("get_types_by_category")
    @monitor_service_performance("get_types_by_category")
    def get_types_by_category(
        self, category_id: int, include_inactive: bool = False, skip: int = 0, limit: int = 100
    ) -> List[ComponentTypeSummarySchema]:
        """Get component types by category.
        
        Args:
            category_id: Category ID
            include_inactive: Whether to include inactive types
            skip: Number of records to skip
            limit: Maximum number of records to return
            
        Returns:
            List[ComponentTypeSummarySchema]: Component types in category

        """
        logger.debug(f"Getting component types for category {category_id}")

        types = self.type_repo.get_by_category(category_id, include_inactive, skip, limit)
        
        return [self._convert_to_summary_schema(component_type) for component_type in types]

    @handle_service_errors("update_specifications_template")
    @monitor_service_performance("update_specifications_template")
    def update_specifications_template(
        self, type_id: int, template: Dict[str, Any]
    ) -> ComponentTypeReadSchema:
        """Update specifications template for a component type.
        
        Args:
            type_id: Component type ID
            template: New specifications template
            
        Returns:
            ComponentTypeReadSchema: Updated component type
            
        Raises:
            NotFoundError: If component type not found
            ValidationError: If template is invalid

        """
        logger.info(f"Updating specifications template for type {type_id}")

        # Validate type exists
        component_type = self.type_repo.get_by_id(type_id)
        if not component_type or component_type.is_deleted:
            raise NotFoundError(
                code="COMPONENT_TYPE_NOT_FOUND",
                detail=f"Component type {type_id} not found"
            )

        # Update template
        updated_type = self.type_repo.update_specifications_template(type_id, template)
        if not updated_type:
            raise ValidationError("Failed to update specifications template")

        logger.info(f"Updated specifications template for type {type_id}")
        return self._convert_to_read_schema(updated_type)

    def _validate_type_data(self, type_data: ComponentTypeCreateSchema) -> ComponentTypeValidationResultSchema:
        """Validate component type creation data.
        
        Args:
            type_data: Component type data to validate
            
        Returns:
            ComponentTypeValidationResultSchema: Validation result

        """
        errors = []
        warnings = []

        # Basic validation (Pydantic handles most of this)
        if not type_data.name or len(type_data.name.strip()) == 0:
            errors.append("Component type name is required")

        # Business rule validation
        if not self.type_repo.validate_category_exists(type_data.category_id):
            errors.append("Category does not exist or is inactive")

        # Validate specifications template if provided
        if type_data.specifications_template:
            # Basic template validation
            if not isinstance(type_data.specifications_template, dict):
                errors.append("Specifications template must be a dictionary")

        return ComponentTypeValidationResultSchema(
            is_valid=len(errors) == 0,
            errors=errors,
            warnings=warnings,
        )

    def _convert_to_read_schema(self, component_type: ComponentType) -> ComponentTypeReadSchema:
        """Convert ComponentType model to read schema.
        
        Args:
            component_type: ComponentType model instance
            
        Returns:
            ComponentTypeReadSchema: Read schema

        """
        # Parse JSON fields to dictionaries
        metadata_dict = None
        if component_type.metadata_json:
            try:
                metadata_dict = json.loads(component_type.metadata_json) if isinstance(component_type.metadata_json, str) else component_type.metadata_json
            except (json.JSONDecodeError, TypeError):
                metadata_dict = None

        specifications_dict = None
        if component_type.specifications_template:
            try:
                specifications_dict = json.loads(component_type.specifications_template) if isinstance(component_type.specifications_template, str) else component_type.specifications_template
            except (json.JSONDecodeError, TypeError):
                specifications_dict = None

        return ComponentTypeReadSchema(
            id=component_type.id,
            name=component_type.name,
            description=component_type.description,
            category_id=component_type.category_id,
            is_active=component_type.is_active,
            specifications_template=specifications_dict,
            metadata=metadata_dict,
            full_name=component_type.full_name,
            category_path=component_type.category_path,
            component_count=component_type.component_count,
            has_specifications_template=component_type.has_specifications_template,
            category_name=component_type.category.name if component_type.category else None,
            created_at=component_type.created_at,
            updated_at=component_type.updated_at,
        )

    def _convert_to_summary_schema(self, component_type: ComponentType) -> ComponentTypeSummarySchema:
        """Convert ComponentType model to summary schema.
        
        Args:
            component_type: ComponentType model instance
            
        Returns:
            ComponentTypeSummarySchema: Summary schema

        """
        return ComponentTypeSummarySchema(
            id=component_type.id,
            name=component_type.name,
            description=component_type.description,
            category_id=component_type.category_id,
            category_name=component_type.category.name if component_type.category else None,
            is_active=component_type.is_active,
            component_count=component_type.component_count,
        )
