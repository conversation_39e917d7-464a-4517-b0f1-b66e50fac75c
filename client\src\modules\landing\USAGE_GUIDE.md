# Landing Page Module - Usage Guide

## Quick Start

### Basic Implementation

Replace your existing landing page with the new module:

```tsx
// pages/index.tsx or app/page.tsx
import { LandingPage } from '@/modules/landing'

export default function HomePage() {
  return <LandingPage />
}
```

### Custom Data

Provide custom content data:

```tsx
import { LandingPage } from '@/modules/landing'
import type { LandingPageData } from '@/modules/landing'

const customData: LandingPageData = {
  hero: {
    badge: { icon: 'star', text: 'New Release' },
    title: 'Custom Title',
    subtitle: 'Custom Subtitle',
    description: 'Custom description...',
    backgroundPattern: true,
    floatingElements: true,
  },
  features: [
    {
      id: 'feature-1',
      title: 'Custom Feature',
      description: 'Feature description...',
      icon: 'zap',
      color: 'primary',
    },
  ],
  trustIndicators: [
    {
      id: 'trust-1',
      icon: 'shield',
      label: 'Security',
      value: '100% Secure',
    },
  ],
  cta: {
    title: 'Ready to Start?',
    subtitle: 'Get Started',
    description: 'Join us today...',
    primaryAction: { label: 'Sign Up', href: '/signup' },
    secondaryAction: { label: 'Learn More', href: '/about' },
  },
}

export default function HomePage() {
  return <LandingPage data={customData} />
}
```

## Component API Reference

### LandingPage

Main component that renders the complete landing page.

```tsx
interface LandingPageProps {
  data?: Partial<LandingPageData>
  className?: string
}
```

**Props:**

- `data` (optional): Custom landing page data
- `className` (optional): Additional CSS classes

### HeroSection

Hero section with authentication-aware CTAs.

```tsx
interface HeroSectionProps {
  hero: HeroSectionData
  className?: string
}
```

**Features:**

- Authentication-aware button text
- Responsive design
- Smooth animations
- Background patterns and floating elements

### FeaturesSection

Interactive features showcase with hover effects.

```tsx
interface FeaturesSectionProps {
  features: FeatureItem[]
  className?: string
}
```

**Features:**

- Interactive feature cards
- Hover animations
- Responsive grid layout
- Accessibility optimized

### TrustIndicators

Professional trust indicators with animated statistics.

```tsx
interface TrustIndicatorProps {
  indicators: TrustIndicator[]
  className?: string
}
```

**Features:**

- Animated number counters
- Professional badges
- Compliance indicators
- Responsive layout

### CTASection

Call-to-action section with multiple conversion paths.

```tsx
interface CTASectionProps {
  cta: CTASectionData
  className?: string
}
```

**Features:**

- Authentication-aware messaging
- Multiple action buttons
- Professional design
- Trust building elements

## State Management

### Server State (React Query)

```tsx
import { useLandingPageData, useLandingPageSection } from '@/modules/landing'

// Get all landing page data
const { data, isLoading, error } = useLandingPageData()

// Get specific section data
const { data: heroData } = useLandingPageSection('hero')
const { data: featuresData } = useLandingPageSection('features')
```

### Client State (Zustand)

```tsx
import { useLandingPageStore } from '@/modules/landing'

const {
  isHeroVisible,
  activeFeature,
  hoveredFeature,
  scrollProgress,
  setActiveFeature,
  setHoveredFeature,
} = useLandingPageStore()
```

## Customization Examples

### Custom Styling

```tsx
// Add custom classes
<LandingPage className="custom-landing-page" />

// Override specific sections
<HeroSection
  hero={heroData}
  className="bg-gradient-to-r from-purple-900 to-blue-900"
/>
```

### Custom Features

```tsx
const customFeatures: FeatureItem[] = [
  {
    id: 'ai-powered',
    title: 'AI-Powered Analysis',
    description: 'Advanced AI algorithms for optimal design recommendations.',
    icon: 'cpu',
    color: 'primary',
    href: '/features/ai'
  },
  {
    id: 'cloud-sync',
    title: 'Cloud Synchronization',
    description: 'Real-time sync across all your devices.',
    icon: 'cloud',
    color: 'secondary'
  }
]

<FeaturesSection features={customFeatures} />
```

### Custom Trust Indicators

```tsx
const customTrustIndicators: TrustIndicator[] = [
  {
    id: 'customers',
    icon: 'users',
    label: 'Happy Customers',
    value: '10,000+'
  },
  {
    id: 'uptime',
    icon: 'check-circle',
    label: 'Uptime',
    value: '99.99%'
  }
]

<TrustIndicators indicators={customTrustIndicators} />
```

## Advanced Usage

### Dynamic Content Loading

```tsx
import { useState, useEffect } from 'react'
import { LandingPage } from '@/modules/landing'

export default function DynamicLandingPage() {
  const [landingData, setLandingData] = useState(null)

  useEffect(() => {
    // Load content from CMS or API
    fetchLandingPageContent().then(setLandingData)
  }, [])

  if (!landingData) {
    return <div>Loading...</div>
  }

  return <LandingPage data={landingData} />
}
```

### A/B Testing Integration

```tsx
import { LandingPage } from '@/modules/landing'
import { useABTest } from '@/hooks/useABTest'

export default function ABTestLandingPage() {
  const variant = useABTest('landing-page-hero')

  const heroData = variant === 'B' ? alternativeHeroData : defaultHeroData

  return <LandingPage data={{ hero: heroData }} />
}
```

### Analytics Integration

```tsx
import { useEffect } from 'react'
import { LandingPage } from '@/modules/landing'
import { trackPageView } from '@/utils/analytics'

export default function AnalyticsLandingPage() {
  useEffect(() => {
    trackPageView('landing-page')
  }, [])

  return <LandingPage />
}
```

## Performance Optimization

### Preloading Critical Resources

```tsx
// In your layout or _app.tsx
<Head>
  <link rel="preconnect" href="https://fonts.googleapis.com" />
  <link rel="preload" href="/hero-background.jpg" as="image" />
</Head>
```

### Image Optimization

```tsx
import Image from 'next/image'

// Use Next.js Image component for optimized loading
;<Image
  src="/feature-image.jpg"
  alt="Feature description"
  width={400}
  height={300}
  priority={false} // Only true for above-the-fold images
/>
```

## Testing

### Unit Testing Components

```tsx
import { render, screen } from '@testing-library/react'
import { HeroSection } from '@/modules/landing'

test('renders hero section with correct content', () => {
  const heroData = {
    badge: { icon: 'star', text: 'Test Badge' },
    title: 'Test Title',
    subtitle: 'Test Subtitle',
    description: 'Test description',
  }

  render(<HeroSection hero={heroData} />)

  expect(screen.getByText('Test Title')).toBeInTheDocument()
  expect(screen.getByText('Test Badge')).toBeInTheDocument()
})
```

### E2E Testing

```typescript
// tests/e2e/landing-page.spec.ts
import { test, expect } from '@playwright/test'

test('landing page loads and displays correctly', async ({ page }) => {
  await page.goto('/')

  await expect(page.getByText('Ultimate Electrical Designer')).toBeVisible()
  await expect(page.getByText('Start Designing')).toBeVisible()

  // Test responsive design
  await page.setViewportSize({ width: 375, height: 667 })
  await expect(page.getByText('Ultimate Electrical Designer')).toBeVisible()
})
```

## Troubleshooting

### Common Issues

1. **Hydration Errors**: Ensure server and client render the same content
2. **Animation Issues**: Check if CSS animations are properly loaded
3. **Type Errors**: Verify all props match TypeScript interfaces
4. **Performance Issues**: Use React DevTools Profiler to identify bottlenecks

### Debug Mode

Enable debug logging:

```tsx
// Set environment variable
process.env.DEBUG_LANDING_PAGE = 'true'

// Or use debug prop
<LandingPage debug={true} />
```

### Performance Monitoring

```tsx
import { getCLS, getFID, getFCP, getLCP, getTTFB } from 'web-vitals'

// Monitor Core Web Vitals
getCLS(console.log)
getFID(console.log)
getFCP(console.log)
getLCP(console.log)
getTTFB(console.log)
```

## Best Practices

1. **Always provide alt text** for images and icons
2. **Use semantic HTML** for better accessibility
3. **Test on real devices** for responsive design
4. **Monitor performance** regularly
5. **Keep content updated** and relevant
6. **Follow brand guidelines** consistently
7. **Test with screen readers** for accessibility
8. **Optimize images** for web delivery

## Support

For questions or issues:

1. Check the README.md for basic information
2. Review ARCHITECTURE.md for design decisions
3. Look at existing tests for usage examples
4. Create an issue in the project repository
