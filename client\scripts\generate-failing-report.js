#!/usr/bin/env node

const { exec } = require('child_process')
const fs = require('fs')
const path = require('path')

const outputFileName = 'failing-vitest-tests.json'

// --- NEW CODE START: Help message and handling ---
const helpMessage = `
Usage: npm run test:failing [vitest-arguments]

Generates a JSON report of only failing Vitest tests.

Options:
  [vitest-arguments]  Optional arguments to pass directly to Vitest.
                      Examples:
                      - To run tests in a specific directory:
                        npm run test:failing src/components/
                      - To run tests matching a pattern (remember quotes for globs):
                        npm run test:failing "src/utils/**/*.test.ts"
                      - To pass Vitest CLI options (use -- to separate from npm args):
                        npm run test:failing -- --testNamePattern "My specific test"
  -h, --help          Show this help message.

Report will be saved to: ${outputFileName}
`

// Capture additional arguments passed to this script
const rawArgs = process.argv.slice(2)

// Check for help flags
if (rawArgs.includes('-h') || rawArgs.includes('--help')) {
  console.log(helpMessage)
  process.exit(0) // Exit successfully after showing help
}

console.log('📊 Generating failing Vitest tests report...')
console.log('--- Debug Log ---')

// Define a much larger buffer size, e.g., 200MB (200 * 1024 * 1024 bytes)
const MAX_BUFFER_SIZE = 200 * 1024 * 1024 // Increased from 50MB

console.log(`Debug: Using maxBuffer size: ${MAX_BUFFER_SIZE / (1024 * 1024)} MB`)

// Join remaining arguments for Vitest
const vitestArgs = rawArgs.join(' ')

let vitestCommand = 'npx vitest run --reporter=json'
if (vitestArgs) {
  vitestCommand += ` ${vitestArgs}`
  console.log(`Debug: Appending Vitest-specific arguments: "${vitestArgs}"`)
}
console.log(`Debug: Executing command: ${vitestCommand}`)
// --- NEW CODE END ---

// 1. Run Vitest and capture JSON output
exec(vitestCommand, { maxBuffer: MAX_BUFFER_SIZE }, (error, stdout, stderr) => {
  console.log('Debug: Vitest command execution finished.')

  if (error) {
    console.error(`❌ Vitest command failed with error code ${error.code}.`)
    console.error('Debug: Error object:', error)
    if (error.signal) {
      console.error(`Debug: Signal received: ${error.signal}`)
    }
    if (error.killed) {
      console.error(`Debug: Process was killed: ${error.killed}`)
    }
    console.error('stderr (from Vitest process):', stderr)
    console.error('stdout (from Vitest process - might be partial):', stdout)
    console.error('Please ensure your tests can run successfully before generating a report.')
    process.exit(1)
  }

  if (stderr && !stderr.includes('Generated JSON report')) {
    console.warn('⚠️ Vitest reported some warnings/errors to stderr (might not be critical):')
    console.warn('stderr (from Vitest process):', stderr)
  } else if (stderr) {
    console.log('Debug: stderr contains "Generated JSON report" message, ignoring.')
  } else {
    console.log('Debug: stderr is empty or null.')
  }

  if (!stdout || stdout.trim().length === 0) {
    console.error('❌ Vitest command produced no stdout. This is unexpected for a JSON reporter.')
    console.error(
      'Debug: Check if Vitest is installed or if there are severe test configuration issues.'
    )
    process.exit(1)
  }

  let vitestResults
  try {
    console.log('Debug: Attempting to parse stdout as JSON. Length:', stdout.length, 'bytes.')
    vitestResults = JSON.parse(stdout)
    console.log('Debug: Successfully parsed JSON output.')
  } catch (parseError) {
    console.error('❌ Failed to parse Vitest JSON output.')
    console.error(
      'Debug: This might happen if Vitest produced non-JSON output or if there was an internal error.'
    )
    console.error('Debug: Error details:', parseError.message)
    console.error(
      'Debug: Partial stdout that caused parse error (first 200 chars):',
      stdout.substring(0, 200)
    )
    process.exit(1)
  }

  // 2. Filter for failing tests
  const failingTests = []
  if (vitestResults && Array.isArray(vitestResults.testResults)) {
    console.log(`Debug: Found ${vitestResults.testResults.length} test result files.`)
    vitestResults.testResults.forEach((fileResult) => {
      if (Array.isArray(fileResult.assertionResults)) {
        fileResult.assertionResults.forEach((test) => {
          if (test.status === 'failed') {
            const summarizedFailureMessages = test.failureMessages.map((message) => {
              const lines = message.split('\n')
              const summaryLines = []
              for (let i = 0; i < Math.min(lines.length, 5); i++) {
                if (lines[i].trim() === '') {
                  break
                }
                summaryLines.push(lines[i])
              }
              if (lines.length > summaryLines.length && summaryLines.length > 0) {
                if (lines.join('\n').trim() !== summaryLines.join('\n').trim()) {
                  summaryLines.push('    ... (full stack trace omitted)')
                }
              }
              return summaryLines.join('\n')
            })

            failingTests.push({
              testFilePath: fileResult.testFilePath,
              fullName: test.fullName,
              title: test.title,
              status: test.status,
              ancestorTitles: test.ancestorTitles,
              failureMessages: summarizedFailureMessages,
              duration: test.duration,
            })
          }
        })
      }
    })
  } else {
    console.warn(
      '⚠️ No "testResults" or "assertionResults" found in Vitest output. Report might be empty.'
    )
    console.warn(
      'Debug: Full Vitest results object for inspection:',
      JSON.stringify(vitestResults, null, 2)
    )
  }

  // 3. Save the filtered results to a file
  const outputPath = path.resolve(process.cwd(), outputFileName)
  try {
    fs.writeFileSync(outputPath, JSON.stringify(failingTests, null, 2), 'utf8')
    console.log(`✅ Successfully generated report for ${failingTests.length} failing tests.`)
    console.log(`Report saved to: ${outputPath}`)
    if (failingTests.length === 0) {
      console.log('🎉 All tests passed! The report file is empty because no failures were found.')
    }
  } catch (writeError) {
    console.error(`❌ Failed to write the report file to ${outputPath}.`)
    console.error('Debug: Write error details:', writeError.message)
    process.exit(1)
  }

  console.log('--- Debug Log End ---')
  process.exit(0)
})
