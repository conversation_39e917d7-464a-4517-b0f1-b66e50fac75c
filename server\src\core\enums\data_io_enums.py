# src/core/enums/data_io_enums.py
"""This module defines enumeration types pertaining to data input/output
operations, reporting, and data mapping within the Ultimate Electrical Designer
application. It encompasses classifications for file formats, data types,
import/parse statuses, mapping methodologies, and various report documents
and templates. These enums are crucial for facilitating seamless data exchange
and customizable documentation generation.
"""

from enum import Enum


class FileFormat(Enum):
    """Common file formats for data import/export."""

    CSV = "CSV"
    EXCEL = "Excel (XLSX)"
    JSON = "JSON"
    XML = "XML"
    PDF = "PDF"
    DWG = "AutoCAD Drawing (DWG)"
    DXF = "Drawing Exchange Format (DXF)"
    IFC = "Industry Foundation Classes (IFC)"  # For BIM
    STEP = "Standard for the Exchange of Product model data (STEP)"  # For 3D models
    EPLAN_P8 = "EPLAN P8 Export"  # Specific electrical CAD format
    REVIT = "Revit Model"  # BIM
    SQL = "SQL Script"
    PLANT_3D = "AutoCAD Plant 3D"  # For piping data
    SP_P_ID = "SmartPlant P&ID"  # For P&ID data
    TEXT = "Text (TXT)"
    MARKDOWN = "Markdown (MD)"
    LATEX = "LaTeX"
    DXF_12 = "DXF (R12)"  # Specific DXF version sometimes needed


class MappingDataType(Enum):
    """Fundamental data types used in data mapping."""

    STRING = "String"
    INTEGER = "Integer"
    FLOAT = "Float"
    BOOLEAN = "Boolean"
    DATE = "Date"
    DATETIME = "DateTime"
    LIST = "List"
    DICTIONARY = "Dictionary"
    ENUM = "Enum"  # For mapping to internal enums


class DomainDataType(Enum):
    """Specific domain entities or types for which data might be imported/exported or mapped.
    This provides high-level context for data operations.
    """

    PROJECT = "Project"
    COMPONENT = "Component"
    CIRCUIT = "Circuit"
    BUS = "Bus"
    CABLE = "Cable"
    LOAD = "Load"
    REPORT = "Report"
    SPECIFICATION = "Specification"
    CALCULATION_RESULT = "Calculation Result"
    BILL_OF_MATERIALS = "Bill of Materials (BOM)"
    DRAWING = "Drawing"
    DOCUMENT = "Document"
    USER = "User"
    EQUIPMENT = "Equipment"  # Keep for historical or high-level reference if distinct from component
    PIPE = "Pipe"
    VALVE = "Valve"
    TANK = "Tank"
    SENSOR = "Sensor"
    MOTOR = "Motor"
    HEAT_TRACING_SYSTEM = "Heat Tracing System"
    WIRING = "Wiring"
    SITE = "Site"
    LOCATION = "Location"
    SYSTEM = "System"  # Generic system grouping


class ImportStatus(Enum):
    """Status of a data import operation."""

    PENDING = "Pending"
    IN_PROGRESS = "In Progress"
    COMPLETED = "Completed"
    FAILED = "Failed"
    PARTIALLY_COMPLETED = "Partially Completed"
    CANCELLED = "Cancelled"
    VALIDATING = "Validating"  # New: during data validation phase


class ParseStatus(Enum):
    """Status of parsing a data file."""

    SUCCESS = "Success"
    PARTIAL_SUCCESS = "Partial Success"
    FAILED = "Failed"
    INVALID_FORMAT = "Invalid Format"
    EMPTY_DATA = "Empty Data"
    DATA_STRUCTURE_MISMATCH = "Data Structure Mismatch"  # New


class MappingType(Enum):
    """Types of data mapping operations."""

    DIRECT = "Direct Mapping"
    CALCULATED = "Calculated Mapping"
    CONDITIONAL = "Conditional Mapping"
    LOOKUP = "Lookup Mapping"
    TRANSFORMATION = "Transformation Mapping"
    AGGREGATION = "Aggregation Mapping"
    DEFAULT_VALUE = (
        "Default Value Mapping"  # New: setting a default if source is missing
    )


class ReportDocumentType(Enum):
    """Specific types of reports or documents generated by the system."""

    SINGLE_LINE_DIAGRAM = "Single Line Diagram"
    SCHEMATIC_DIAGRAM = "Schematic Diagram"
    CABLE_SCHEDULE = "Cable Schedule"
    LOAD_SCHEDULE = "Load Schedule"
    BILL_OF_MATERIALS = "Bill of Materials (BOM)"
    SPECIFICATION_SHEET = "Specification Sheet"
    DATASHEET = "Datasheet"
    CALCULATION_REPORT = "Calculation Report"
    EXECUTIVE_SUMMARY = "Executive Summary"
    TECHNICAL_REPORT = "Technical Report"
    SAFETY_REPORT = "Safety Report"
    PROJECT_PLAN = "Project Plan"
    MAINTENANCE_SCHEDULE = "Maintenance Schedule"
    COMMISSIONING_REPORT = "Commissioning Report"
    HAZARDOUS_AREA_CLASSIFICATION_REPORT = "Hazardous Area Classification Report"
    GROUNDING_DESIGN_REPORT = "Grounding Design Report"
    ARC_FLASH_REPORT = "Arc Flash Report"
    P_ID = "Piping & Instrumentation Diagram (P&ID)"  # If the tool supports mechanical elements
    ISOMETRIC_DRAWING = "Isometric Drawing"
    GENERAL_ARRANGEMENT_DRAWING = "General Arrangement Drawing (GA)"
    THREE_DIMENSIONAL_MODEL = "3D Model"


class TemplateCategory(Enum):
    """Categories for report or document templates."""

    STANDARD = "Standard Template"
    DETAILED = "Detailed Template"
    SUMMARY = "Summary Template"
    CUSTOM = "Custom Template"
    CLIENT_SPECIFIC = "Client Specific Template"
    REGULATORY = "Regulatory Template"  # For compliance docs


class RenderingEngine(Enum):
    """Engines used for rendering documents or reports."""

    JINJA2 = "Jinja2"  # Python templating
    MUSTACHE = "Mustache"  # Logic-less templating
    SIMPLE_TEXT = "Simple Text"  # Basic string formatting
    MARKDOWN = "Markdown"
    LATEX = "LaTeX"
    HTML_CSS = "HTML/CSS"  # For web-based reports
    PDF_ENGINE = "PDF Engine"  # Generic PDF generation engine


class ReportStatus(Enum):
    """Status of a report generation process."""

    PENDING = "Pending"
    GENERATING = "Generating"
    COMPLETED = "Completed"
    FAILED = "Failed"
    CANCELLED = "Cancelled"
    REGENERATING = "Regenerating"  # New


class ReportPriority(Enum):
    """Priority level for report generation."""

    LOW = "Low"
    NORMAL = "Normal"
    HIGH = "High"
    URGENT = "Urgent"


class TrendPeriod(Enum):
    """Time periods for trending data."""

    HOURLY = "Hourly"
    DAILY = "Daily"
    WEEKLY = "Weekly"
    MONTHLY = "Monthly"
    QUARTERLY = "Quarterly"
    YEARLY = "Yearly"
    CUSTOM = "Custom Range"  # For user-defined periods
