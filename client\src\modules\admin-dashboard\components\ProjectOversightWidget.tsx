/**
 * Project oversight widget for admin dashboard
 */

'use client'

import React, { useState } from 'react'
import { ProjectOversightWidgetProps } from '../types'
import {
  formatProjectStatus,
  getProjectStatusColor,
  getProjectPriorityColor,
  formatRelativeTime,
} from '../utils'

export function ProjectOversightWidget({
  projects,
  isLoading,
  onProjectClick,
  onViewAll,
  className = '',
}: ProjectOversightWidgetProps) {
  const [filter, setFilter] = useState<'all' | 'active' | 'completed' | 'on_hold' | 'draft'>('all')

  // Calculate project statistics
  const stats = {
    total: projects.length,
    active: projects.filter((p) => p.status === 'active').length,
    completed: projects.filter((p) => p.status === 'completed').length,
    onHold: projects.filter((p) => p.status === 'on_hold').length,
    draft: projects.filter((p) => p.status === 'draft').length,
    critical: projects.filter((p) => p.priority === 'critical').length,
    high: projects.filter((p) => p.priority === 'high').length,
  }

  // Filter projects based on selected filter
  const filteredProjects = filter === 'all' ? projects : projects.filter((p) => p.status === filter)

  // Sort projects by priority and last modified
  const sortedProjects = [...filteredProjects].sort((a, b) => {
    const priorityOrder = { critical: 4, high: 3, medium: 2, low: 1 }
    const aPriority = priorityOrder[a.priority]
    const bPriority = priorityOrder[b.priority]

    if (aPriority !== bPriority) {
      return bPriority - aPriority
    }

    return new Date(b.lastModified).getTime() - new Date(a.lastModified).getTime()
  })

  if (isLoading) {
    return (
      <div className={`overflow-hidden rounded-lg bg-white shadow ${className}`}>
        <div className="p-6">
          <div className="animate-pulse">
            <div className="mb-4 h-6 w-1/2 rounded bg-gray-200"></div>
            <div className="space-y-3">
              {Array.from({ length: 5 }).map((_, i) => (
                <div key={i} className="flex items-center space-x-3">
                  <div className="h-4 w-1/3 rounded bg-gray-200"></div>
                  <div className="h-4 w-1/4 rounded bg-gray-200"></div>
                  <div className="h-4 w-1/5 rounded bg-gray-200"></div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className={`overflow-hidden rounded-lg bg-white shadow ${className}`}>
      <div className="p-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-medium text-gray-900">Project Oversight</h3>
          {onViewAll && (
            <button onClick={onViewAll} className="text-sm text-blue-600 hover:text-blue-500">
              View All Projects
            </button>
          )}
        </div>

        {/* Statistics */}
        <div className="mt-6 grid grid-cols-2 gap-4 lg:grid-cols-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-gray-900">{stats.total}</div>
            <div className="text-sm text-gray-500">Total</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">{stats.active}</div>
            <div className="text-sm text-gray-500">Active</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">{stats.completed}</div>
            <div className="text-sm text-gray-500">Completed</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-red-600">{stats.critical + stats.high}</div>
            <div className="text-sm text-gray-500">High Priority</div>
          </div>
        </div>

        {/* Filter Tabs */}
        <div className="mt-6">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8">
              {[
                { key: 'all', label: 'All', count: stats.total },
                { key: 'active', label: 'Active', count: stats.active },
                { key: 'completed', label: 'Completed', count: stats.completed },
                { key: 'on_hold', label: 'On Hold', count: stats.onHold },
                { key: 'draft', label: 'Draft', count: stats.draft },
              ].map((tab) => (
                <button
                  key={tab.key}
                  onClick={() => setFilter(tab.key as any)}
                  className={`whitespace-nowrap border-b-2 px-1 py-2 text-sm font-medium ${
                    filter === tab.key
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700'
                  }`}
                >
                  {tab.label}
                  {tab.count > 0 && (
                    <span
                      className={`ml-2 rounded-full px-2.5 py-0.5 text-xs ${
                        filter === tab.key
                          ? 'bg-blue-100 text-blue-600'
                          : 'bg-gray-100 text-gray-900'
                      }`}
                    >
                      {tab.count}
                    </span>
                  )}
                </button>
              ))}
            </nav>
          </div>
        </div>

        {/* Projects List */}
        <div className="mt-6">
          <div className="max-h-80 space-y-3 overflow-y-auto">
            {sortedProjects.slice(0, 10).map((project) => (
              <div
                key={project.id}
                className="flex items-center justify-between rounded-lg border border-gray-200 p-4 transition-colors hover:bg-gray-50"
              >
                <div className="min-w-0 flex-1">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <h4 className="truncate text-sm font-medium text-gray-900">{project.name}</h4>
                      <span
                        className={`inline-flex items-center rounded-full px-2 py-1 text-xs font-medium ${getProjectStatusColor(project.status)}`}
                      >
                        {formatProjectStatus(project.status)}
                      </span>
                      <span
                        className={`inline-flex items-center rounded-full px-2 py-1 text-xs font-medium ${getProjectPriorityColor(project.priority)}`}
                      >
                        {project.priority}
                      </span>
                    </div>
                  </div>

                  <p className="mt-1 truncate text-sm text-gray-500">{project.description}</p>

                  <div className="mt-2 flex items-center justify-between">
                    <div className="flex items-center space-x-4 text-xs text-gray-500">
                      <span>Owner: {project.owner}</span>
                      <span>{project.calculationCount} calculations</span>
                      <span>{project.teamSize} team members</span>
                    </div>
                    <span className="text-xs text-gray-400">
                      Modified {formatRelativeTime(project.lastModified)}
                    </span>
                  </div>

                  {/* Progress Bar */}
                  <div className="mt-2">
                    <div className="flex items-center justify-between text-xs">
                      <span className="text-gray-500">Progress</span>
                      <span className="font-medium text-gray-900">{project.progress}%</span>
                    </div>
                    <div className="mt-1 h-2 w-full rounded-full bg-gray-200">
                      <div
                        className={`h-2 rounded-full ${
                          project.progress >= 80
                            ? 'bg-green-600'
                            : project.progress >= 50
                              ? 'bg-yellow-600'
                              : 'bg-red-600'
                        }`}
                        style={{ width: `${project.progress}%` }}
                      ></div>
                    </div>
                  </div>
                </div>

                {onProjectClick && (
                  <button
                    onClick={() => onProjectClick(project)}
                    className="ml-4 rounded-md p-1 text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    aria-label={`View ${project.name} details`}
                  >
                    <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M9 5l7 7-7 7"
                      />
                    </svg>
                  </button>
                )}
              </div>
            ))}
          </div>

          {sortedProjects.length > 10 && (
            <div className="mt-3 text-center">
              <button onClick={onViewAll} className="text-sm text-blue-600 hover:text-blue-500">
                View all {sortedProjects.length} projects
              </button>
            </div>
          )}

          {sortedProjects.length === 0 && (
            <div className="py-6 text-center">
              <svg
                className="mx-auto h-12 w-12 text-gray-400"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"
                />
              </svg>
              <h3 className="mt-2 text-sm font-medium text-gray-900">No projects found</h3>
              <p className="mt-1 text-sm text-gray-500">
                {filter === 'all'
                  ? 'No projects have been created yet.'
                  : `No projects with status "${filter}" found.`}
              </p>
            </div>
          )}
        </div>

        {/* Quick Actions */}
        <div className="mt-6 flex flex-wrap gap-2">
          <button className="inline-flex items-center rounded-md bg-blue-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-blue-500">
            <svg className="mr-1 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 6v6m0 0v6m0-6h6m-6 0H6"
              />
            </svg>
            New Project
          </button>
          <button className="inline-flex items-center rounded-md bg-gray-100 px-3 py-2 text-sm font-medium text-gray-700 hover:bg-gray-200">
            <svg className="mr-1 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
              />
            </svg>
            Export Report
          </button>
        </div>
      </div>
    </div>
  )
}
