"""Pydantic schemas for Activity Log and Audit Trail models.

This module defines request/response schemas for the audit trail system,
including activity logs, audit trails, and related operations.
"""

import datetime
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, Field, validator

from src.core.schemas.base import BaseSchema
from src.core.enums.system_enums import ErrorSeverity


class ActivityLogBase(BaseModel):
    """Base schema for ActivityLog with common fields."""
    
    action_type: str = Field(..., min_length=1, max_length=100, description="Type of action")
    action_description: str = Field(..., min_length=1, max_length=1000, description="Description of the action")
    user_id: Optional[int] = Field(None, description="ID of the user who performed the action")
    session_id: Optional[str] = Field(None, max_length=255, description="Session identifier")
    target_type: Optional[str] = Field(None, max_length=100, description="Type of target entity")
    target_id: Optional[int] = Field(None, description="ID of target entity")
    target_name: Optional[str] = Field(None, max_length=255, description="Name of target entity")
    request_method: Optional[str] = Field(None, max_length=10, description="HTTP request method")
    request_path: Optional[str] = Field(None, max_length=500, description="Request path")
    request_ip: Optional[str] = Field(None, max_length=45, description="IP address")
    user_agent: Optional[str] = Field(None, max_length=500, description="User agent")
    status: str = Field("SUCCESS", max_length=50, description="Status of the action")
    severity: ErrorSeverity = Field(ErrorSeverity.INFO, description="Severity level")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional metadata")
    error_message: Optional[str] = Field(None, max_length=1000, description="Error message if failed")
    execution_time_ms: Optional[int] = Field(None, ge=0, description="Execution time in milliseconds")
    category: Optional[str] = Field(None, max_length=100, description="Category for grouping")
    tags: Optional[List[str]] = Field(None, description="Tags for categorization")
    is_security_related: bool = Field(False, description="Whether this is security-related")
    is_data_change: bool = Field(False, description="Whether this involves data changes")
    is_system_event: bool = Field(False, description="Whether this is a system event")
    notes: Optional[str] = Field(None, max_length=500, description="Additional notes")

    @validator('action_type')
    def validate_action_type(cls, v):
        if not v or not v.strip():
            raise ValueError('Action type cannot be empty')
        return v.strip().upper()

    @validator('action_description')
    def validate_action_description(cls, v):
        if not v or not v.strip():
            raise ValueError('Action description cannot be empty')
        return v.strip()

    @validator('request_ip')
    def validate_request_ip(cls, v):
        if v is not None:
            import ipaddress
            try:
                ipaddress.ip_address(v)
            except ValueError:
                raise ValueError('Invalid IP address format')
        return v

    @validator('tags')
    def validate_tags(cls, v):
        if v is not None:
            # Remove empty tags and duplicates
            clean_tags = list(set(tag.strip() for tag in v if tag.strip()))
            return clean_tags if clean_tags else None
        return v


class ActivityLogCreate(ActivityLogBase):
    """Schema for creating a new activity log entry."""
    
    name: str = Field(..., min_length=1, max_length=100, description="Log entry name")

    @validator('name')
    def validate_name(cls, v):
        if not v or not v.strip():
            raise ValueError('Log entry name cannot be empty')
        return v.strip()


class ActivityLogUpdate(BaseModel):
    """Schema for updating an existing activity log entry."""
    
    status: Optional[str] = Field(None, max_length=50)
    error_message: Optional[str] = Field(None, max_length=1000)
    execution_time_ms: Optional[int] = Field(None, ge=0)
    notes: Optional[str] = Field(None, max_length=500)


class ActivityLogResponse(BaseSchema):
    """Schema for activity log responses."""
    
    id: int
    name: str
    user_id: Optional[int]
    session_id: Optional[str]
    action_type: str
    action_description: str
    target_type: Optional[str]
    target_id: Optional[int]
    target_name: Optional[str]
    request_method: Optional[str]
    request_path: Optional[str]
    request_ip: Optional[str]
    user_agent: Optional[str]
    status: str
    severity: ErrorSeverity
    metadata: Optional[Dict[str, Any]]
    error_message: Optional[str]
    execution_time_ms: Optional[int]
    category: Optional[str]
    tags: Optional[List[str]]
    is_security_related: bool
    is_data_change: bool
    is_system_event: bool
    notes: Optional[str]
    created_at: datetime.datetime
    updated_at: datetime.datetime

    class Config:
        from_attributes = True


class AuditTrailBase(BaseModel):
    """Base schema for AuditTrail with common fields."""
    
    table_name: str = Field(..., min_length=1, max_length=100, description="Name of the table")
    record_id: int = Field(..., description="ID of the record")
    operation: str = Field(..., min_length=1, max_length=20, description="Type of operation")
    user_id: Optional[int] = Field(None, description="ID of the user who made the change")
    activity_log_id: Optional[int] = Field(None, description="ID of related activity log")
    field_name: Optional[str] = Field(None, max_length=100, description="Name of the changed field")
    old_value: Optional[str] = Field(None, max_length=1000, description="Previous value")
    new_value: Optional[str] = Field(None, max_length=1000, description="New value")
    change_reason: Optional[str] = Field(None, max_length=255, description="Reason for the change")
    change_context: Optional[Dict[str, Any]] = Field(None, description="Additional context")
    is_sensitive: bool = Field(False, description="Whether the change involves sensitive data")
    is_system_change: bool = Field(False, description="Whether this is a system change")
    notes: Optional[str] = Field(None, max_length=500, description="Additional notes")

    @validator('table_name')
    def validate_table_name(cls, v):
        if not v or not v.strip():
            raise ValueError('Table name cannot be empty')
        return v.strip()

    @validator('operation')
    def validate_operation(cls, v):
        if not v or not v.strip():
            raise ValueError('Operation cannot be empty')
        valid_operations = ['INSERT', 'UPDATE', 'DELETE', 'SOFT_DELETE', 'RESTORE']
        op = v.strip().upper()
        if op not in valid_operations:
            raise ValueError(f'Operation must be one of: {", ".join(valid_operations)}')
        return op


class AuditTrailCreate(AuditTrailBase):
    """Schema for creating a new audit trail entry."""
    
    name: str = Field(..., min_length=1, max_length=100, description="Audit trail entry name")

    @validator('name')
    def validate_name(cls, v):
        if not v or not v.strip():
            raise ValueError('Audit trail entry name cannot be empty')
        return v.strip()


class AuditTrailUpdate(BaseModel):
    """Schema for updating an existing audit trail entry."""
    
    change_reason: Optional[str] = Field(None, max_length=255)
    change_context: Optional[Dict[str, Any]] = None
    is_sensitive: Optional[bool] = None
    notes: Optional[str] = Field(None, max_length=500)


class AuditTrailResponse(BaseSchema):
    """Schema for audit trail responses."""
    
    id: int
    name: str
    activity_log_id: Optional[int]
    user_id: Optional[int]
    changed_at: datetime.datetime
    table_name: str
    record_id: int
    operation: str
    field_name: Optional[str]
    old_value: Optional[str]
    new_value: Optional[str]
    change_reason: Optional[str]
    change_context: Optional[Dict[str, Any]]
    is_sensitive: bool
    is_system_change: bool
    notes: Optional[str]
    created_at: datetime.datetime
    updated_at: datetime.datetime

    class Config:
        from_attributes = True


class ActivityLogFilter(BaseModel):
    """Schema for filtering activity logs."""
    
    user_id: Optional[int] = None
    action_types: Optional[List[str]] = None
    categories: Optional[List[str]] = None
    severities: Optional[List[ErrorSeverity]] = None
    start_date: Optional[datetime.datetime] = None
    end_date: Optional[datetime.datetime] = None
    is_security_related: Optional[bool] = None
    is_data_change: Optional[bool] = None
    is_system_event: Optional[bool] = None
    status: Optional[str] = None
    target_type: Optional[str] = None
    target_id: Optional[int] = None
    request_ip: Optional[str] = None


class AuditTrailFilter(BaseModel):
    """Schema for filtering audit trails."""
    
    table_name: Optional[str] = None
    record_id: Optional[int] = None
    user_id: Optional[int] = None
    operations: Optional[List[str]] = None
    field_name: Optional[str] = None
    start_date: Optional[datetime.datetime] = None
    end_date: Optional[datetime.datetime] = None
    is_sensitive: Optional[bool] = None
    is_system_change: Optional[bool] = None
    activity_log_id: Optional[int] = None


class AuditSummary(BaseModel):
    """Schema for audit summary statistics."""
    
    total_activities: int
    total_data_changes: int
    security_events: int
    system_events: int
    user_actions: int
    failed_operations: int
    top_action_types: List[Dict[str, Any]]
    top_users: List[Dict[str, Any]]
    activity_timeline: List[Dict[str, Any]]

    class Config:
        from_attributes = True


class RecordHistory(BaseModel):
    """Schema for record change history."""
    
    table_name: str
    record_id: int
    total_changes: int
    first_change: datetime.datetime
    last_change: datetime.datetime
    changes: List[AuditTrailResponse]
    change_summary: Dict[str, int]

    class Config:
        from_attributes = True


class UserActivitySummary(BaseModel):
    """Schema for user activity summary."""
    
    user_id: int
    total_activities: int
    recent_activities: List[ActivityLogResponse]
    top_actions: List[Dict[str, Any]]
    security_events: int
    data_changes: int
    activity_timeline: List[Dict[str, Any]]

    class Config:
        from_attributes = True