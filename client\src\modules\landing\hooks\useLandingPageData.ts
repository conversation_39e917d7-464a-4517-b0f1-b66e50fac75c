/**
 * Hook for managing landing page data
 */

import { useQuery } from '@tanstack/react-query'
import { defaultLandingPageData } from '../utils'
import type { LandingPageData } from '../types'

/**
 * Hook to get landing page data
 * In the future, this could fetch from an API for dynamic content
 */
export function useLandingPageData() {
  return useQuery({
    queryKey: ['landing-page-data'],
    queryFn: async (): Promise<LandingPageData> => {
      // For now, return static data
      // In the future, this could fetch from an API
      return defaultLandingPageData
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  })
}

/**
 * Hook to get specific section data
 */
export function useLandingPageSection<T extends keyof LandingPageData>(section: T) {
  const { data, isLoading, error } = useLandingPageData()

  return {
    data: data?.[section],
    isLoading,
    error,
  }
}
