/**
 * Main Settings Panel Component
 * Ultimate Electrical Designer - Settings & User Preferences
 */

'use client'

import React from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Ta<PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Separator } from '@/components/ui/separator'
import { ScrollArea } from '@/components/ui/scroll-area'
import {
  Search,
  Save,
  RotateCcw,
  Download,
  Upload,
  User,
  Palette,
  Bell,
  Shield,
  Settings as SettingsIcon,
  Calculator,
} from 'lucide-react'
import { useSettings } from '../hooks/useSettings'
import { SETTINGS_CATEGORIES } from '../constants'
import type { SettingsCategory } from '../types'
import { SettingsSearch } from './SettingsSearch'
import { PreferencesForm } from './PreferencesForm'
import { ImportExportDialog } from './ImportExportDialog'
import { SettingsReset } from './SettingsReset'

// Category icons mapping
const CATEGORY_ICONS = {
  account: User,
  appearance: Palette,
  notifications: Bell,
  privacy: Shield,
  advanced: SettingsIcon,
  engineering: Calculator,
} as const

interface SettingsPanelProps {
  className?: string
  autoSave?: boolean
  showSearch?: boolean
  showActions?: boolean
}

/**
 * Main Settings Panel Component
 */
export function SettingsPanel({
  className = '',
  autoSave = false,
  showSearch = true,
  showActions = true,
}: SettingsPanelProps) {
  const settings = useSettings({ autoSave })

  const { ui, hasChanges, changedFields, actions, autoSave: autoSaveState } = settings

  // Handle category change
  const handleCategoryChange = (category: string) => {
    actions.setActiveCategory(category as SettingsCategory)
  }

  // Handle save
  const handleSave = async () => {
    await actions.savePreferences()
  }

  // Handle reset
  const handleReset = () => {
    actions.openResetDialog()
  }

  // Handle export
  const handleExport = () => {
    actions.openExportDialog()
  }

  // Handle import
  const handleImport = () => {
    actions.openImportDialog()
  }

  return (
    <div className={`settings-panel ${className}`}>
      {/* Header */}
      <div className="settings-header mb-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold tracking-tight">Settings</h1>
            <p className="text-muted-foreground">Manage your account settings and preferences</p>
          </div>

          {showActions && (
            <div className="flex items-center gap-2">
              {/* Auto-save indicator */}
              {autoSave && autoSaveState.isAutoSaving && (
                <Badge variant="secondary" className="animate-pulse">
                  Auto-saving...
                </Badge>
              )}

              {/* Unsaved changes indicator */}
              {hasChanges && (
                <Badge variant="outline">
                  {changedFields.length} unsaved change{changedFields.length !== 1 ? 's' : ''}
                </Badge>
              )}

              {/* Action buttons */}
              <Button variant="outline" size="sm" onClick={handleImport} disabled={ui.isSaving}>
                <Upload className="mr-2 h-4 w-4" />
                Import
              </Button>

              <Button variant="outline" size="sm" onClick={handleExport} disabled={ui.isSaving}>
                <Download className="mr-2 h-4 w-4" />
                Export
              </Button>

              <Button variant="outline" size="sm" onClick={handleReset} disabled={ui.isSaving}>
                <RotateCcw className="mr-2 h-4 w-4" />
                Reset
              </Button>

              {!autoSave && (
                <Button onClick={handleSave} disabled={!hasChanges || ui.isSaving} size="sm">
                  <Save className="mr-2 h-4 w-4" />
                  {ui.isSaving ? 'Saving...' : 'Save Changes'}
                </Button>
              )}
            </div>
          )}
        </div>

        {/* Search */}
        {showSearch && (
          <div className="mt-4">
            <SettingsSearch />
          </div>
        )}
      </div>

      {/* Main Content */}
      <div className="settings-content">
        <Tabs value={ui.activeCategory} onValueChange={handleCategoryChange} className="w-full">
          {/* Category Tabs */}
          <TabsList className="mb-6 grid w-full grid-cols-6">
            {SETTINGS_CATEGORIES.map((category) => {
              const Icon = CATEGORY_ICONS[category.id]
              return (
                <TabsTrigger
                  key={category.id}
                  value={category.id}
                  className="flex items-center gap-2"
                >
                  <Icon className="h-4 w-4" />
                  <span className="hidden sm:inline">{category.label}</span>
                </TabsTrigger>
              )
            })}
          </TabsList>

          {/* Category Content */}
          {SETTINGS_CATEGORIES.map((category) => (
            <TabsContent key={category.id} value={category.id} className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    {React.createElement(CATEGORY_ICONS[category.id], { className: 'h-5 w-5' })}
                    {category.label}
                  </CardTitle>
                  <CardDescription>{category.description}</CardDescription>
                </CardHeader>
                <CardContent>
                  <PreferencesForm category={category.id} />
                </CardContent>
              </Card>
            </TabsContent>
          ))}
        </Tabs>
      </div>

      {/* Dialogs */}
      <ImportExportDialog />
      <SettingsReset />

      {/* Error Display */}
      {Object.keys(ui.errors).length > 0 && (
        <Card className="mt-6 border-destructive">
          <CardHeader>
            <CardTitle className="text-destructive">Validation Errors</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {Object.entries(ui.errors).map(([field, error]) => (
                <div key={field} className="text-sm text-destructive">
                  <strong>{field}:</strong> {error}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Loading Overlay */}
      {ui.isLoading && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-background/80 backdrop-blur-sm">
          <Card className="p-6">
            <div className="flex items-center gap-3">
              <div className="h-6 w-6 animate-spin rounded-full border-b-2 border-primary"></div>
              <span>Loading settings...</span>
            </div>
          </Card>
        </div>
      )}
    </div>
  )
}

/**
 * Compact Settings Panel for smaller spaces
 */
export function CompactSettingsPanel({
  className = '',
  autoSave = true,
}: {
  className?: string
  autoSave?: boolean
}) {
  return (
    <SettingsPanel
      className={`compact ${className}`}
      autoSave={autoSave}
      showSearch={false}
      showActions={false}
    />
  )
}

/**
 * Settings Panel with specific category pre-selected
 */
export function CategorySettingsPanel({
  category,
  className = '',
  autoSave = false,
}: {
  category: SettingsCategory
  className?: string
  autoSave?: boolean
}) {
  const settings = useSettings({ autoSave })

  React.useEffect(() => {
    settings.actions.setActiveCategory(category)
  }, [category, settings.actions])

  return (
    <div className={`category-settings-panel ${className}`}>
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            {React.createElement(CATEGORY_ICONS[category], { className: 'h-5 w-5' })}
            {SETTINGS_CATEGORIES.find((cat) => cat.id === category)?.label}
          </CardTitle>
          <CardDescription>
            {SETTINGS_CATEGORIES.find((cat) => cat.id === category)?.description}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <PreferencesForm category={category} />
        </CardContent>
      </Card>
    </div>
  )
}

export default SettingsPanel
