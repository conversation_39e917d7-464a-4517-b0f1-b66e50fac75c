'use client'

/**
 * React Query hooks for authentication
 */

import { apiClient } from '@/lib/api/client'
import { useAuthStore } from '@/stores/authStore'
import type {
  LoginRequest,
  LoginResponse,
  LogoutResponse,
  PasswordChangeRequest,
  PasswordChangeResponse,
  RegisterRequest,
  RegisterResponse,
  UserRead,
} from '@/types/api'
import { MutationKeys, QueryKeys } from '@/types/api'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'

/**
 * Hook for user login
 */
export function useLogin() {
  const queryClient = useQueryClient()
  const { setAuth, clearAuth } = useAuthStore()

  return useMutation({
    mutationKey: MutationKeys.login,
    mutationFn: async (credentials: LoginRequest): Promise<LoginResponse> => {
      const response = await apiClient.login(credentials)
      return response
    },
    onSuccess: (data) => {
      // Set auth token in API client
      apiClient.setAuthToken(data.access_token)

      // Update auth store
      setAuth(data.user, data.access_token)

      // Invalidate and refetch user queries
      queryClient.invalidateQueries({ queryKey: QueryKeys.currentUser })
      queryClient.setQueryData(QueryKeys.currentUser, data.user)
    },
    onError: () => {
      // Clear any existing auth state on login error
      clearAuth()
      apiClient.clearAuthToken()
    },
  })
}

/**
 * Hook for user registration
 */
export function useRegister() {
  const queryClient = useQueryClient()
  const { setAuth } = useAuthStore()

  return useMutation({
    mutationKey: MutationKeys.register,
    mutationFn: async (userData: RegisterRequest): Promise<RegisterResponse> => {
      const response = await apiClient.register(userData)
      return response
    },
    onSuccess: (data) => {
      // Optionally auto-login after successful registration
      // For now, just invalidate queries
      queryClient.invalidateQueries({ queryKey: QueryKeys.users })
    },
  })
}

/**
 * Hook for user logout
 */
export function useLogout() {
  const queryClient = useQueryClient()
  const { clearAuth } = useAuthStore()

  return useMutation({
    mutationKey: MutationKeys.logout,
    mutationFn: async (): Promise<LogoutResponse> => {
      const response = await apiClient.logout()
      return response
    },
    onSuccess: () => {
      // Clear auth state
      clearAuth()
      apiClient.clearAuthToken()

      // Clear all cached data
      queryClient.clear()
    },
    onError: () => {
      // Even if logout fails on server, clear local state
      clearAuth()
      apiClient.clearAuthToken()
      queryClient.clear()
    },
  })
}

/**
 * Hook for password change
 */
export function useChangePassword() {
  return useMutation({
    mutationKey: MutationKeys.changePassword,
    mutationFn: async (data: PasswordChangeRequest): Promise<PasswordChangeResponse> => {
      const response = await apiClient.changePassword(data)
      return response
    },
  })
}

/**
 * Hook for getting current user
 */
export function useCurrentUser() {
  const { isAuthenticated } = useAuthStore()

  return useQuery({
    queryKey: QueryKeys.currentUser,
    queryFn: async (): Promise<UserRead> => {
      const response = await apiClient.getCurrentUser()
      return response
    },
    enabled: isAuthenticated,
    staleTime: 10 * 60 * 1000, // 10 minutes
    gcTime: 30 * 60 * 1000, // 30 minutes
  })
}

/**
 * Hook for updating current user profile
 */
export function useUpdateProfile() {
  const queryClient = useQueryClient()
  const { updateUser } = useAuthStore()

  return useMutation({
    mutationKey: MutationKeys.updateProfile,
    mutationFn: async (data: Partial<UserRead>): Promise<UserRead> => {
      const response = await apiClient.updateCurrentUser(data)
      return response
    },
    onSuccess: (data) => {
      // Update auth store
      updateUser(data)

      // Update cached user data
      queryClient.setQueryData(QueryKeys.currentUser, data)
      queryClient.invalidateQueries({ queryKey: QueryKeys.users })
    },
  })
}
