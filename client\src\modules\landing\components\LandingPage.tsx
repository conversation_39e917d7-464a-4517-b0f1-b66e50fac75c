'use client'

import { Foot<PERSON> } from '@/components/common/Footer'
import { Header } from '@/components/common/Header'
import { cn } from '@/lib/utils'
import dynamic from 'next/dynamic'
import { useEffect } from 'react'
import { useLandingPageData, useLandingPageStore } from '../hooks'
import type { LandingPageProps } from '../types'
import { defaultLandingPageData } from '../utils'
import { HeroSection } from './HeroSection'

// Lazy load components that are below the fold
const FeaturesSection = dynamic(
  () => import('./FeaturesSection').then((mod) => ({ default: mod.FeaturesSection })),
  {
    loading: () => (
      <div className="animate-pulse bg-gradient-to-b from-white to-neutral-50 py-24" />
    ),
  }
)

const TrustIndicators = dynamic(
  () => import('./TrustIndicators').then((mod) => ({ default: mod.TrustIndicators })),
  {
    loading: () => (
      <div className="animate-pulse bg-gradient-to-r from-brand-dark/90 via-brand-secondary/90 to-brand-primary/90 py-16" />
    ),
  }
)

const CTASection = dynamic(
  () => import('./CTASection').then((mod) => ({ default: mod.CTASection })),
  {
    loading: () => (
      <div className="animate-pulse bg-gradient-to-r from-brand-dark via-brand-secondary to-brand-primary py-24" />
    ),
  }
)

/**
 * Main landing page component with all sections
 */
export function LandingPage({ data, className }: LandingPageProps) {
  const { data: fetchedData, isLoading, error } = useLandingPageData()
  const { resetState } = useLandingPageStore()

  // Reset store state on mount
  useEffect(() => {
    resetState()
  }, [resetState])

  // Use provided data, fetched data, or fallback to default
  const landingData = data || fetchedData || defaultLandingPageData

  // Handle loading state
  if (isLoading) {
    return (
      <div className="flex min-h-screen items-center justify-center bg-white">
        <div className="text-center">
          <div className="mx-auto mb-4 h-12 w-12 animate-spin rounded-full border-b-2 border-brand-primary"></div>
          <p className="text-neutral-600">Loading...</p>
        </div>
      </div>
    )
  }

  // Handle error state
  if (error) {
    console.error('Landing page data error:', error)
    // Still render with default data
  }

  // Ensure we have all required data
  const safeHero = landingData.hero || defaultLandingPageData.hero
  const safeFeatures = landingData.features || defaultLandingPageData.features
  const safeTrustIndicators = landingData.trustIndicators || defaultLandingPageData.trustIndicators
  const safeCta = landingData.cta || defaultLandingPageData.cta

  return (
    <div className={cn('min-h-screen bg-white', className)}>
      {/* Structured Data for SEO */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            '@context': 'https://schema.org',
            '@type': 'SoftwareApplication',
            name: 'Ultimate Electrical Designer',
            description: safeHero.description,
            applicationCategory: 'Engineering Software',
            operatingSystem: 'Web Browser',
            offers: {
              '@type': 'Offer',
              price: '0',
              priceCurrency: 'USD',
              availability: 'https://schema.org/InStock',
            },
            aggregateRating: {
              '@type': 'AggregateRating',
              ratingValue: '4.9',
              ratingCount: '1000',
            },
            provider: {
              '@type': 'Organization',
              name: 'Ultimate Electrical Designer',
              url: 'https://ultimate-electrical-designer.com',
            },
          }),
        }}
      />

      {/* Navigation Header */}
      <Header />

      {/* Main Content */}
      <main role="main">
        {/* Hero Section */}
        <HeroSection
          hero={safeHero}
          className="scroll-mt-16"
          aria-label="Hero section with main value proposition"
        />

        {/* Features Section */}
        <FeaturesSection
          features={safeFeatures}
          className="scroll-mt-16"
          aria-label="Product features and capabilities"
        />

        {/* Trust Indicators */}
        <TrustIndicators
          indicators={safeTrustIndicators}
          className="scroll-mt-16"
          aria-label="Trust indicators and certifications"
        />

        {/* Call to Action Section */}
        <CTASection cta={safeCta} className="scroll-mt-16" aria-label="Call to action section" />
      </main>

      {/* Footer */}
      <Footer />
    </div>
  )
}
