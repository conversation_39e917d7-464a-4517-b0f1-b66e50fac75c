# src/core/utils/datetime_utils.py
"""DateTime Utilities.

This module provides standardized date, time, and timezone-aware operations
to ensure consistency across the application.

Key Features:
- UTC timezone-aware datetime handling
- Standard formatting and parsing
- Timezone conversion utilities
- Time difference calculations
"""

import re
from datetime import UTC, datetime, timedelta, timezone
from typing import Optional, Union

from src.config.logging_config import logger

# Standard datetime format patterns
ISO_FORMAT = "%Y-%m-%dT%H:%M:%S.%fZ"
ISO_FORMAT_NO_MICROSECONDS = "%Y-%m-%dT%H:%M:%SZ"
DISPLAY_FORMAT = "%Y-%m-%d %H:%M:%S"
DATE_FORMAT = "%Y-%m-%d"
TIME_FORMAT = "%H:%M:%S"

# Regex patterns for parsing
ISO_DATETIME_PATTERN = re.compile(
    r"^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(?:\.\d+)?(?:Z|[+-]\d{2}:\d{2})?$"
)


def utcnow_aware() -> datetime:
    """Get current UTC datetime with timezone awareness.

    Returns:
        datetime: Current UTC datetime with timezone info

    """
    return datetime.now(UTC)


def utcnow_naive() -> datetime:
    """Get current UTC datetime without timezone info.

    Returns:
        datetime: Current UTC datetime (naive)

    """
    return utcnow_aware().replace(tzinfo=None)


def make_aware(dt: datetime, tz: Optional[timezone] = None) -> datetime:
    """Make a naive datetime timezone-aware.

    Args:
        dt: Naive datetime to make aware
        tz: Timezone to use (defaults to UTC)

    Returns:
        datetime: Timezone-aware datetime

    Raises:
        ValueError: If datetime is already timezone-aware

    """
    if dt.tzinfo is not None:
        raise ValueError("Datetime is already timezone-aware")

    if tz is None:
        tz = UTC

    return dt.replace(tzinfo=tz)


def make_naive(dt: datetime) -> datetime:
    """Convert timezone-aware datetime to naive UTC datetime.

    Args:
        dt: Timezone-aware datetime

    Returns:
        datetime: Naive UTC datetime

    Raises:
        ValueError: If datetime is already naive

    """
    if dt.tzinfo is None:
        raise ValueError("Datetime is already naive")

    # Convert to UTC first, then remove timezone info
    utc_dt = dt.astimezone(UTC)
    return utc_dt.replace(tzinfo=None)


def format_datetime(
    dt: datetime, format_str: str = ISO_FORMAT, ensure_utc: bool = True
) -> str:
    """Format datetime to string with optional UTC conversion.

    Args:
        dt: Datetime to format
        format_str: Format string (defaults to ISO format)
        ensure_utc: Convert to UTC before formatting

    Returns:
        str: Formatted datetime string

    """
    if ensure_utc and dt.tzinfo is not None:
        dt = dt.astimezone(UTC)

    return dt.strftime(format_str)


def parse_datetime(
    datetime_str: str, format_str: Optional[str] = None, make_utc_aware: bool = True
) -> datetime:
    """Parse datetime string with automatic format detection.

    Args:
        datetime_str: String to parse
        format_str: Specific format to use (auto-detect if None)
        make_utc_aware: Make result UTC-aware if naive

    Returns:
        datetime: Parsed datetime

    Raises:
        ValueError: If string cannot be parsed

    """
    datetime_str = datetime_str.strip()

    if format_str:
        # Use specific format
        try:
            dt = datetime.strptime(datetime_str, format_str)
        except ValueError as e:
            raise ValueError(
                f"Failed to parse datetime '{datetime_str}' with format '{format_str}'"
            ) from e
    else:
        # Auto-detect format
        dt = _auto_parse_datetime(datetime_str)

    # Make UTC-aware if requested and datetime is naive
    if make_utc_aware and dt.tzinfo is None:
        dt = make_aware(dt, UTC)

    return dt


def _auto_parse_datetime(datetime_str: str) -> datetime:
    """Attempt to automatically parse datetime string.

    Args:
        datetime_str: String to parse

    Returns:
        datetime: Parsed datetime

    Raises:
        ValueError: If no format matches

    """
    # First, try ISO format with timezone (priority for timezone-aware strings)
    if ISO_DATETIME_PATTERN.match(datetime_str):
        try:
            # Handle ISO format with timezone
            if datetime_str.endswith("Z"):
                dt_str = datetime_str[:-1]
                dt = datetime.fromisoformat(dt_str)
                return dt.replace(tzinfo=UTC)
            return datetime.fromisoformat(datetime_str)
        except ValueError:
            pass

    # Try common naive formats
    formats = [
        ISO_FORMAT,
        "%Y-%m-%dT%H:%M:%S",
        DISPLAY_FORMAT,
        "%Y-%m-%d %H:%M",
        DATE_FORMAT,
        "%m/%d/%Y %H:%M:%S",
        "%m/%d/%Y",
        "%d/%m/%Y %H:%M:%S",
        "%d/%m/%Y",
    ]

    for fmt in formats:
        try:
            return datetime.strptime(datetime_str, fmt)
        except ValueError:
            continue

    raise ValueError(f"Unable to parse datetime string: {datetime_str}")


def convert_timezone(
    dt: datetime, target_tz: timezone, source_tz: Optional[timezone] = None
) -> datetime:
    """Convert datetime between timezones.

    Args:
        dt: Datetime to convert
        target_tz: Target timezone
        source_tz: Source timezone (if dt is naive)

    Returns:
        datetime: Converted datetime

    Raises:
        ValueError: If datetime is naive and no source_tz provided

    """
    if dt.tzinfo is None:
        if source_tz is None:
            raise ValueError("Naive datetime requires source_tz parameter")
        dt = dt.replace(tzinfo=source_tz)

    return dt.astimezone(target_tz)


def calculate_time_difference(
    start_dt: datetime, end_dt: datetime, unit: str = "seconds"
) -> float:
    """Calculate time difference between two datetimes.

    Args:
        start_dt: Start datetime
        end_dt: End datetime
        unit: Unit for result ("seconds", "minutes", "hours", "days")

    Returns:
        float: Time difference in specified unit

    Raises:
        ValueError: If unit is not supported

    """
    diff = end_dt - start_dt
    total_seconds = diff.total_seconds()

    if unit == "seconds":
        return total_seconds
    if unit == "minutes":
        return total_seconds / 60
    if unit == "hours":
        return total_seconds / 3600
    if unit == "days":
        return total_seconds / 86400
    raise ValueError(
        f"Unsupported unit: {unit}. Use 'seconds', 'minutes', 'hours', or 'days'"
    )


def is_business_hours(
    dt: datetime, start_hour: int = 9, end_hour: int = 17, weekdays_only: bool = True
) -> bool:
    """Check if datetime falls within business hours.

    Args:
        dt: Datetime to check
        start_hour: Business day start hour (24-hour format)
        end_hour: Business day end hour (24-hour format)
        weekdays_only: Only consider weekdays as business days

    Returns:
        bool: True if within business hours

    """
    if weekdays_only and dt.weekday() >= 5:  # Saturday = 5, Sunday = 6
        return False

    return start_hour <= dt.hour < end_hour


def add_business_days(dt: datetime, days: int) -> datetime:
    """Add business days to a datetime (skipping weekends).

    Args:
        dt: Starting datetime
        days: Number of business days to add

    Returns:
        datetime: Datetime with business days added

    """
    current = dt
    remaining_days = abs(days)
    direction = 1 if days >= 0 else -1

    while remaining_days > 0:
        current += timedelta(days=direction)
        # Skip weekends
        if current.weekday() < 5:  # Monday = 0, Friday = 4
            remaining_days -= 1

    return current


def get_quarter(dt: datetime) -> int:
    """Get the quarter (1-4) for a given datetime.

    Args:
        dt: Datetime to get quarter for

    Returns:
        int: Quarter number (1-4)

    """
    return (dt.month - 1) // 3 + 1


def start_of_day(dt: datetime) -> datetime:
    """Get start of day (00:00:00) for given datetime.

    Args:
        dt: Input datetime

    Returns:
        datetime: Start of day

    """
    return dt.replace(hour=0, minute=0, second=0, microsecond=0)


def end_of_day(dt: datetime) -> datetime:
    """Get end of day (23:59:59.999999) for given datetime.

    Args:
        dt: Input datetime

    Returns:
        datetime: End of day

    """
    return dt.replace(hour=23, minute=59, second=59, microsecond=999999)
