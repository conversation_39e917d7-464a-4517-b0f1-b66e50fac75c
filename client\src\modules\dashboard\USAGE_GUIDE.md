# Dashboard Module - Usage Guide

## Overview

This guide provides comprehensive instructions for using and customizing the Ultimate Electrical Designer dashboard module. The dashboard is designed for electrical engineers to efficiently manage projects, access calculations, and monitor system metrics.

## Getting Started

### Basic Implementation

The simplest way to use the dashboard is to import and use the main `DashboardOverview` component:

```tsx
import { DashboardOverview } from '@/modules/dashboard'

export default function DashboardPage() {
  return (
    <div className="container mx-auto p-6">
      <DashboardOverview />
    </div>
  )
}
```

### With Authentication Guard

For protected routes, wrap the dashboard with authentication:

```tsx
import { RouteGuard } from '@/components/auth/RouteGuard'
import { DashboardLayout } from '@/components/layout/DashboardLayout'
import { DashboardOverview } from '@/modules/dashboard'

export default function DashboardPage() {
  return (
    <RouteGuard requireAuth={true}>
      <DashboardLayout title="Dashboard">
        <DashboardOverview />
      </DashboardLayout>
    </RouteGuard>
  )
}
```

## Component Usage

### MetricsWidget

Displays key dashboard metrics with visual indicators:

```tsx
import { MetricsWidget } from '@/modules/dashboard'

const metrics = {
  totalProjects: 25,
  activeProjects: 12,
  completedCalculations: 340,
  recentActivity: 15,
  systemUptime: '99.9%',
  lastLogin: new Date().toISOString(),
}

function MyDashboard() {
  return <MetricsWidget metrics={metrics} isLoading={false} className="mb-6" />
}
```

### ProjectsWidget

Manages and displays recent projects:

```tsx
import { ProjectsWidget } from '@/modules/dashboard'

function ProjectsSection() {
  const handleProjectClick = (project) => {
    router.push(`/projects/${project.id}`)
  }

  const handleCreateProject = () => {
    router.push('/projects/new')
  }

  return (
    <ProjectsWidget
      projects={projects}
      isLoading={isLoading}
      onProjectClick={handleProjectClick}
      onCreateProject={handleCreateProject}
      className="lg:col-span-2"
    />
  )
}
```

### QuickActionsWidget

Provides quick access to common tasks:

```tsx
import { QuickActionsWidget } from '@/modules/dashboard'

const quickActions = [
  {
    id: 'heat-tracing',
    title: 'Heat Tracing Design',
    description: 'Design heat tracing systems',
    icon: 'zap',
    color: 'primary',
    href: '/heat-tracing',
    category: 'calculation',
    isEnabled: true,
    requiresAuth: true,
  },
  // ... more actions
]

function ActionsSection() {
  const handleActionClick = (action) => {
    // Handle custom action logic
    console.log('Action clicked:', action)
  }

  return <QuickActionsWidget actions={quickActions} onActionClick={handleActionClick} />
}
```

### RecentCalculationsWidget

Shows recent calculation results:

```tsx
import { RecentCalculationsWidget } from '@/modules/dashboard'

function CalculationsSection() {
  const handleCalculationClick = (calculation) => {
    router.push(`/projects/${calculation.projectId}/calculations/${calculation.id}`)
  }

  const handleViewAll = () => {
    router.push('/calculations')
  }

  return (
    <RecentCalculationsWidget
      calculations={recentCalculations}
      isLoading={isLoading}
      onCalculationClick={handleCalculationClick}
      onViewAll={handleViewAll}
      className="lg:col-span-1"
    />
  )
}
```

## State Management

### Using the Dashboard Store

The dashboard uses Zustand for client-side state management:

```tsx
import { useDashboardStore, useDashboardActions } from '@/modules/dashboard'

function DashboardComponent() {
  // Access state
  const data = useDashboardStore((state) => state.data)
  const isLoading = useDashboardStore((state) => state.isLoading)
  const selectedProject = useDashboardStore((state) => state.selectedProject)

  // Access actions
  const { setSelectedProject, setProjectFilter } = useDashboardActions()

  const handleProjectSelect = (projectId: string) => {
    setSelectedProject(projectId)
  }

  return <div>{/* Component JSX */}</div>
}
```

### Using React Query Hooks

For server state management:

```tsx
import { useDashboardData, useDashboardMetrics } from '@/modules/dashboard'

function DataComponent() {
  const { data: dashboardData, isLoading, error, refetch } = useDashboardData()

  const { data: metrics, isLoading: metricsLoading } = useDashboardMetrics({ timeRange: '7d' })

  if (error) {
    return <div>Error loading dashboard: {error.message}</div>
  }

  return <div>{/* Render dashboard data */}</div>
}
```

## Customization

### Custom Layouts

Create custom dashboard layouts by composing individual widgets:

```tsx
import { MetricsWidget, ProjectsWidget, QuickActionsWidget } from '@/modules/dashboard'

function CustomDashboard() {
  return (
    <div className="space-y-6">
      {/* Top metrics row */}
      <MetricsWidget metrics={metrics} />

      {/* Two-column layout */}
      <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
        <QuickActionsWidget actions={actions} />
        <ProjectsWidget projects={projects} />
      </div>
    </div>
  )
}
```

### Custom Styling

Override default styles with Tailwind CSS:

```tsx
<MetricsWidget
  metrics={metrics}
  className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl shadow-lg"
/>

<ProjectsWidget
  projects={projects}
  className="border-2 border-blue-200 rounded-lg"
/>
```

### Custom Quick Actions

Define custom quick actions for your specific needs:

```tsx
const customActions = [
  {
    id: 'custom-calculation',
    title: 'Custom Calculation',
    description: 'Run custom electrical calculations',
    icon: 'calculator',
    color: 'accent',
    href: '/custom-calculations',
    category: 'calculation',
    isEnabled: true,
    requiresAuth: true,
    requiredRole: 'ENGINEER',
  },
]
```

## Data Integration

### API Integration

Connect the dashboard to your backend APIs:

```tsx
// Custom hook for dashboard data
function useCustomDashboardData() {
  return useQuery({
    queryKey: ['dashboard', 'custom'],
    queryFn: async () => {
      const response = await fetch('/api/dashboard/custom')
      return response.json()
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchInterval: 10 * 60 * 1000, // 10 minutes
  })
}

function CustomDashboard() {
  const { data, isLoading, error } = useCustomDashboardData()

  return <DashboardOverview data={data} isLoading={isLoading} error={error} />
}
```

### Real-time Updates

Implement real-time updates with WebSocket or polling:

```tsx
function RealTimeDashboard() {
  const { data, refetch } = useDashboardData()

  useEffect(() => {
    // Poll for updates every 30 seconds
    const interval = setInterval(() => {
      refetch()
    }, 30000)

    return () => clearInterval(interval)
  }, [refetch])

  return <DashboardOverview data={data} />
}
```

## Performance Optimization

### Lazy Loading

Implement lazy loading for better performance:

```tsx
import dynamic from 'next/dynamic'

const DashboardOverview = dynamic(
  () => import('@/modules/dashboard').then((mod) => ({ default: mod.DashboardOverview })),
  {
    loading: () => <DashboardSkeleton />,
    ssr: false,
  }
)
```

### Memoization

Use React.memo for expensive components:

```tsx
import { memo } from 'react'

const MemoizedProjectsWidget = memo(ProjectsWidget)
const MemoizedMetricsWidget = memo(MetricsWidget)
```

### Data Caching

Configure React Query caching:

```tsx
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000, // 5 minutes
      gcTime: 10 * 60 * 1000, // 10 minutes
      refetchOnWindowFocus: false,
    },
  },
})
```

## Accessibility

### Keyboard Navigation

Ensure proper keyboard navigation:

```tsx
<button
  onKeyDown={(e) => {
    if (e.key === 'Enter' || e.key === ' ') {
      handleAction()
    }
  }}
  aria-label="Create new project"
>
  New Project
</button>
```

### Screen Reader Support

Add proper ARIA labels:

```tsx
<section aria-label="Dashboard metrics">
  <MetricsWidget metrics={metrics} />
</section>

<section aria-label="Recent projects">
  <ProjectsWidget projects={projects} />
</section>
```

## Testing

### Unit Testing

Test individual components:

```tsx
import { render, screen } from '@testing-library/react'
import { MetricsWidget } from '@/modules/dashboard'

test('displays metrics correctly', () => {
  const metrics = {
    totalProjects: 10,
    activeProjects: 5,
    completedCalculations: 25,
    recentActivity: 3,
    systemUptime: '99.9%',
    lastLogin: new Date().toISOString(),
  }

  render(<MetricsWidget metrics={metrics} />)

  expect(screen.getByText('10')).toBeInTheDocument()
  expect(screen.getByText('Total Projects')).toBeInTheDocument()
})
```

### E2E Testing

Test user workflows:

```tsx
test('user can navigate through dashboard', async ({ page }) => {
  await page.goto('/dashboard')

  await expect(page.getByText('Welcome back')).toBeVisible()
  await expect(page.getByText('Quick Actions')).toBeVisible()

  await page.getByText('Heat Tracing Design').click()
  await expect(page).toHaveURL(/\/heat-tracing/)
})
```

## Troubleshooting

### Common Issues

1. **Loading States**: Ensure proper loading state handling
2. **Error Boundaries**: Implement error boundaries for graceful failures
3. **Memory Leaks**: Clean up subscriptions and intervals
4. **Performance**: Monitor component re-renders

### Debug Mode

Enable debug mode for development:

```tsx
const isDevelopment = process.env.NODE_ENV === 'development'

if (isDevelopment) {
  console.log('Dashboard data:', data)
  console.log('Dashboard state:', state)
}
```

## Best Practices

1. **Always handle loading and error states**
2. **Use proper TypeScript types**
3. **Implement proper accessibility features**
4. **Test components thoroughly**
5. **Follow the established design patterns**
6. **Keep components focused and single-purpose**
7. **Use proper semantic HTML**
8. **Implement proper error boundaries**

## Support

For additional help:

- Check the [Architecture Documentation](./ARCHITECTURE.md)
- Review the [README](./README.md)
- Consult the [Development Standards](../../docs/developer-handbooks/030-development-standards.md)
