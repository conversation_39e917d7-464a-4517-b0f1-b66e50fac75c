from unittest.mock import Mock
import pytest

from src.middleware.security_middleware import Security<PERSON>iddleware
from src.middleware.context_middleware import ContextMiddleware
from src.middleware.logging_middleware import LoggingMiddleware
from src.middleware.caching_middleware import CachingMiddleware
from src.middleware.rate_limiting_middleware import RateLimitingMiddleware

@pytest.fixture
def mock_app():
    """Create a mock ASGI app for testing."""
    return Mock()

@pytest.fixture
def mock_request():
    """Create a mock request object with proper attributes."""
    from fastapi import Request
    request = Mock(spec=Request)
    request.method = "GET"
    request.url = Mock()
    request.url.path = "/api/v1/test"
    request.url.query = ""
    request.query_params = {}
    request.headers = {}
    request.client = Mock()
    request.client.host = "127.0.0.1"
    request.state = Mock()
    return request

@pytest.fixture
def mock_response():
    """Create a mock response object."""
    from fastapi import Response
    response = Mock(spec=Response)
    response.status_code = 200
    response.headers = {}
    response.body = b'{"result": "success"}'
    return response

@pytest.fixture
def mock_call_next():
    """Create a mock call_next function that returns a response."""
    async def call_next(request):
        from fastapi import Response
        response = Mock(spec=Response)
        response.status_code = 200
        response.headers = {}
        response.body = b'{"message": "test"}'
        return response
    return call_next

@pytest.fixture
def caching_middleware(mock_app):
    """Create CachingMiddleware instance with default configuration."""
    return CachingMiddleware(
        app=mock_app,
        default_ttl=300,
        enable_caching=True,
        enable_etag=True,
        enable_user_specific_caching=True,
        redis_client=None,
        cache_key_prefix="test_cache:",
        exclude_paths=None,
        max_cache_size=1000,
    )


@pytest.fixture
def caching_middleware_with_redis(mock_app):
    """Create a CachingMiddleware instance with mock Redis client."""
    mock_redis = Mock()
    return CachingMiddleware(
        app=mock_app,
        default_ttl=300,
        enable_caching=True,
        enable_etag=True,
        redis_client=mock_redis,
        max_cache_size=100,
    )


@pytest.fixture
def context_middleware(mock_app):
    """Create ContextMiddleware instance with default configuration."""
    return ContextMiddleware(
        app=mock_app,
        enable_request_id=True,
        enable_user_context=True,
        enable_locale_detection=True,
        enable_timing=True,
        default_locale="en",
    )


@pytest.fixture
def logging_middleware(mock_app):
    """Create LoggingMiddleware instance with default configuration."""
    return LoggingMiddleware(
        app=mock_app,
        enable_request_logging=True,
        enable_response_logging=True,
        enable_error_logging=True,
        enable_performance_logging=True,
        log_request_body=False,
        log_response_body=False,
        max_body_size=1024,
        exclude_paths=None,
        exclude_health_checks=True,
    )


@pytest.fixture
def rate_limiting_middleware(mock_app):
    """Create RateLimitingMiddleware instance with default configuration."""
    return RateLimitingMiddleware(
        app=mock_app,
        default_requests_per_minute=60,
        default_burst_size=10,
        enable_per_ip_limiting=True,
        enable_per_user_limiting=True,
        enable_endpoint_specific_limits=True,
        redis_client=None,
        exclude_paths=None,
    )


@pytest.fixture
def security_middleware(mock_app):
    """Fixture for SecurityMiddleware instance with default configuration."""
    return SecurityMiddleware(
        app=mock_app,
        max_payload_size=1024 * 1024,  # 1MB for testing
        max_json_depth=10,
        rate_limit_requests=5,
        rate_limit_window=10,
        enable_xss_protection=True,
        enable_unicode_validation=True,
        jwt_secret_key="test-secret-key",
        jwt_algorithm="HS256",
        enable_csrf_protection=True,
        use_unified_security=False,  # Disable for isolated testing
    )

@pytest.fixture
def test_app_with_full_stack():
    """Create a test app with full middleware stack for integration testing."""
    from fastapi import FastAPI

    app = FastAPI()

    @app.get("/test")
    async def test_endpoint():
        from datetime import datetime
        return {"message": "test", "timestamp": datetime.utcnow().isoformat()}

    @app.post("/test")
    async def test_post_endpoint():
        from datetime import datetime
        return {"message": "test post", "timestamp": datetime.utcnow().isoformat()}

    @app.get("/health")
    async def health_endpoint():
        return {"status": "ok"}

    @app.post("/data")
    async def data_endpoint(data: dict):
        return {"received": len(str(data))}

    @app.get("/slow")
    async def slow_endpoint():
        import time
        time.sleep(0.1)  # Simulate slow response
        return {"message": "slow"}

    @app.get("/error")
    async def error_endpoint():
        raise ValueError("Test error")

    # Add full middleware stack in correct order
    from src.middleware.context_middleware import ContextMiddleware
    from src.middleware.logging_middleware import LoggingMiddleware
    from src.middleware.caching_middleware import CachingMiddleware
    from src.middleware.rate_limiting_middleware import RateLimitingMiddleware

    # Add middleware in reverse order (last added is executed first)
    # Rate limiting should be before caching to count all requests
    app.add_middleware(
        CachingMiddleware,
        default_ttl=300,
        enable_caching=True,
        enable_etag=True,
        enable_user_specific_caching=True,
        exclude_paths={"/health"}
    )
    app.add_middleware(
        RateLimitingMiddleware,
        default_requests_per_minute=30,
        default_burst_size=10,
        enable_per_ip_limiting=True,
        enable_per_user_limiting=True,
        enable_endpoint_specific_limits=True,
        exclude_paths={"/health"}
    )
    app.add_middleware(
        LoggingMiddleware,
        enable_request_logging=True,
        enable_response_logging=True,
        enable_error_logging=True,
        enable_performance_logging=True,
        exclude_health_checks=True
    )
    app.add_middleware(
        ContextMiddleware,
        enable_request_id=True,
        enable_user_context=True,
        enable_locale_detection=True,
        enable_timing=True,
        default_locale="en"
    )

    return app

@pytest.fixture
def test_app_with_caching():
    """Create a test app with caching middleware for integration testing."""
    from fastapi import FastAPI

    app = FastAPI()

    @app.get("/test")
    async def test_endpoint():
        return {"message": "test", "timestamp": "2024-01-01T00:00:00Z"}

    @app.post("/test")
    async def test_post_endpoint():
        return {"message": "test post", "timestamp": "2024-01-01T00:00:00Z"}

    @app.get("/health")
    async def health_endpoint():
        return {"status": "ok"}

    @app.post("/data")
    async def data_endpoint(data: dict):
        return {"received": len(str(data))}

    # Add caching middleware
    from src.middleware.caching_middleware import CachingMiddleware
    app.add_middleware(CachingMiddleware)

    return app

@pytest.fixture
def test_app_with_logging():
    """Create a test app with logging middleware for integration testing."""
    from fastapi import FastAPI

    app = FastAPI()

    @app.get("/test")
    async def test_endpoint():
        return {"message": "test"}

    @app.post("/test")
    async def test_post_endpoint():
        return {"message": "test post"}

    @app.get("/error")
    async def error_endpoint():
        raise ValueError("Test error")

    @app.post("/error")
    async def error_post_endpoint():
        raise ValueError("Test error")

    # Add logging middleware
    from src.middleware.logging_middleware import LoggingMiddleware
    app.add_middleware(LoggingMiddleware)

    return app