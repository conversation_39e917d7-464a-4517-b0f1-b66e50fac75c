import { expect, test } from '@playwright/test'

test.describe('Landing Page - New Design', () => {
  test('should display the redesigned landing page correctly', async ({ page }) => {
    await page.goto('/')

    // Check page title
    await expect(page).toHaveTitle(/Ultimate Electrical Designer/)

    // Check main heading - updated for new design
    await expect(page.getByText('Ultimate Electrical')).toBeVisible()
    await expect(page.getByText('Designer')).toBeVisible()

    // Check engineering badge
    await expect(page.getByText('Engineering Grade Software')).toBeVisible()

    // Check hero description
    await expect(
      page.getByText(/Professional electrical engineering software for industrial applications/)
    ).toBeVisible()

    // Check navigation
    await expect(page.getByRole('link', { name: /Ultimate Electrical Designer/ })).toBeVisible()

    // Check hero section CTAs
    await expect(page.getByRole('link', { name: /Start Designing/ })).toBeVisible()
    await expect(page.getByRole('link', { name: /View Demo/ })).toBeVisible()

    // Check features section - updated for new design
    await expect(
      page.getByRole('heading', { name: /Engineering-Grade Capabilities/ })
    ).toBeVisible()
    await expect(page.getByText('Professional Features')).toBeVisible()
    await expect(page.getByText('Heat Tracing Design')).toBeVisible()
    await expect(page.getByText('Load Calculations')).toBeVisible()
    await expect(page.getByText('Project Management')).toBeVisible()
    await expect(page.getByText('Cable Sizing')).toBeVisible()
    await expect(page.getByText('Safety Compliance')).toBeVisible()
    await expect(page.getByText('Documentation')).toBeVisible()

    // Check trust indicators
    await expect(page.getByText('Enterprise Security')).toBeVisible()
    await expect(page.getByText('Code Compliant')).toBeVisible()
    await expect(page.getByText('Industry Standard')).toBeVisible()

    // Check CTA section
    await expect(page.getByText(/Ready to Transform Your/)).toBeVisible()
    await expect(page.getByText(/Electrical Design Process/)).toBeVisible()
    await expect(page.getByRole('link', { name: /Get Started Today/ })).toBeVisible()

    // Check footer
    await expect(page.getByText(/© 2024 Ultimate Electrical Designer/)).toBeVisible()
  })

  test('should navigate to login page when clicking login links', async ({ page }) => {
    await page.goto('/')

    // Click login link in navigation or hero section
    await page.getByRole('link', { name: /Start Designing/ }).click()
    await expect(page).toHaveURL('/login')
  })

  test('should navigate to dashboard when clicking dashboard link', async ({ page }) => {
    await page.goto('/')

    // Click dashboard/demo link
    await page.getByRole('link', { name: /View Demo/ }).click()
    await expect(page).toHaveURL('/dashboard')
  })

  test('should display feature cards with hover effects', async ({ page }) => {
    await page.goto('/')

    // Check feature cards
    const featureCard = page.getByText('Heat Tracing Design').locator('..')
    await expect(featureCard).toBeVisible()

    // Hover over feature card
    await featureCard.hover()

    // Check for "Learn more" link
    await expect(featureCard.getByText('Learn more')).toBeVisible()
  })

  test('should display trust indicators with animations', async ({ page }) => {
    await page.goto('/')

    // Check trust indicators section
    await expect(page.getByText('Trusted by Industry Leaders')).toBeVisible()
    await expect(page.getByText('1000+ Engineers')).toBeVisible()
    await expect(page.getByText('Fortune 500 Companies')).toBeVisible()
    await expect(page.getByText('99.9% Uptime')).toBeVisible()

    // Check compliance badges
    await expect(page.getByText('SOC 2 Certified')).toBeVisible()
    await expect(page.getByText('GDPR Compliant')).toBeVisible()
    await expect(page.getByText('ISO 27001')).toBeVisible()
  })

  test('should be responsive on mobile', async ({ page }) => {
    await page.setViewportSize({ width: 375, height: 667 })
    await page.goto('/')

    // Check that content is visible and properly laid out on mobile
    await expect(page.getByText('Ultimate Electrical Designer')).toBeVisible()
    await expect(page.getByText(/Start Designing/)).toBeVisible()

    // Check that features are stacked vertically
    await expect(page.getByText('Heat Tracing Design')).toBeVisible()
    await expect(page.getByText('Load Calculations')).toBeVisible()
  })

  test('should be responsive on tablet', async ({ page }) => {
    await page.setViewportSize({ width: 768, height: 1024 })
    await page.goto('/')

    await expect(page.getByText('Ultimate Electrical Designer')).toBeVisible()
    await expect(page.getByText('Engineering-Grade Capabilities')).toBeVisible()
  })

  test('should have proper accessibility', async ({ page }) => {
    await page.goto('/')

    // Check for proper heading hierarchy
    const h1 = page.getByRole('heading', { level: 1 })
    await expect(h1).toBeVisible()
    await expect(h1).toContainText('Ultimate Electrical')

    // Check for h2 section headings
    const h2Elements = page.getByRole('heading', { level: 2 })
    await expect(h2Elements.first()).toBeVisible()

    // Check for proper semantic structure
    await expect(page.getByRole('main')).toBeVisible()
    await expect(page.getByRole('banner')).toBeVisible()
    await expect(page.getByRole('contentinfo')).toBeVisible()

    // Check for proper link text
    const links = page.getByRole('link')
    const linkCount = await links.count()
    for (let i = 0; i < linkCount; i++) {
      const link = links.nth(i)
      const text = await link.textContent()
      expect(text?.trim()).toBeTruthy()
    }
  })

  test('should include structured data for SEO', async ({ page }) => {
    await page.goto('/')

    // Check for JSON-LD structured data
    const structuredData = page.locator('script[type="application/ld+json"]')
    await expect(structuredData).toBeAttached()

    const content = await structuredData.textContent()
    expect(content).toContain('"@context":"https://schema.org"')
    expect(content).toContain('"@type":"SoftwareApplication"')
    expect(content).toContain('Ultimate Electrical Designer')
  })

  test('should have proper meta tags', async ({ page }) => {
    await page.goto('/')

    // Check meta description
    const metaDescription = page.locator('meta[name="description"]')
    await expect(metaDescription).toHaveAttribute(
      'content',
      /Professional electrical engineering software/
    )

    // Check viewport meta tag
    const viewport = page.locator('meta[name="viewport"]')
    await expect(viewport).toHaveAttribute('content', /width=device-width/)
  })
})
