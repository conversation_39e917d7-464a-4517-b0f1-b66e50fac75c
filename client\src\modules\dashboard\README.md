# Dashboard Module

A comprehensive dashboard module for the Ultimate Electrical Designer application, built following Domain-Driven Design (DDD) principles and engineering-grade standards.

## Overview

The dashboard module provides a modern, professional interface for electrical engineers to:

- View project metrics and system status
- Access quick actions for common tasks
- Manage recent projects and calculations
- Monitor system performance and activity

## Features

### 🎯 Core Functionality

- **Real-time Metrics**: Live dashboard metrics with automatic refresh
- **Project Management**: Quick access to recent projects with status tracking
- **Calculation History**: Recent calculations with results and status
- **Quick Actions**: One-click access to core features
- **Responsive Design**: Mobile-first design that works on all devices
- **Performance Optimized**: Lazy loading and efficient data fetching

### 🏗️ Architecture

- **Domain-Driven Design**: Clean separation of concerns
- **React Query**: Server state management with caching
- **Zustand**: Client state management for UI interactions
- **TypeScript**: Full type safety with strict mode
- **Tailwind CSS**: Utility-first styling with brand consistency

### 🎨 Design System

- **Brand Colors**: Consistent use of engineering-grade color palette
- **Accessibility**: WCAG 2.1 AA compliant with semantic HTML
- **Animations**: Smooth transitions and loading states
- **Icons**: Consistent iconography throughout

## Module Structure

```
src/modules/dashboard/
├── api/                    # API layer
│   ├── dashboardApi.ts    # Dashboard API functions
│   └── index.ts           # API exports
├── components/            # React components
│   ├── DashboardOverview.tsx      # Main dashboard component
│   ├── MetricsWidget.tsx          # Metrics display widget
│   ├── ProjectsWidget.tsx         # Projects management widget
│   ├── RecentCalculationsWidget.tsx # Calculations history widget
│   ├── QuickActionsWidget.tsx     # Quick actions grid
│   └── index.ts                   # Component exports
├── hooks/                 # Custom React hooks
│   ├── useDashboardStore.ts       # Zustand store hook
│   ├── useDashboardData.ts        # React Query hooks
│   └── index.ts                   # Hook exports
├── types.ts              # TypeScript type definitions
├── utils.ts              # Utility functions
├── index.ts              # Module exports
├── README.md             # This file
├── ARCHITECTURE.md       # Architectural decisions
└── USAGE_GUIDE.md        # Usage documentation
```

## Quick Start

### Basic Usage

```tsx
import { DashboardOverview } from '@/modules/dashboard'

function DashboardPage() {
  return (
    <div className="container mx-auto p-6">
      <DashboardOverview />
    </div>
  )
}
```

### With Custom Data

```tsx
import { DashboardOverview } from '@/modules/dashboard'
import type { DashboardData } from '@/modules/dashboard'

function CustomDashboard() {
  const customData: Partial<DashboardData> = {
    metrics: {
      totalProjects: 25,
      activeProjects: 12,
      completedCalculations: 340,
      recentActivity: 15,
      systemUptime: '99.9%',
      lastLogin: new Date().toISOString(),
    },
  }

  return <DashboardOverview data={customData} />
}
```

### Individual Widgets

```tsx
import { MetricsWidget, ProjectsWidget, QuickActionsWidget } from '@/modules/dashboard'

function CustomLayout() {
  return (
    <div className="space-y-6">
      <MetricsWidget metrics={metrics} />
      <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
        <ProjectsWidget projects={projects} onProjectClick={handleProjectClick} />
        <QuickActionsWidget actions={quickActions} onActionClick={handleActionClick} />
      </div>
    </div>
  )
}
```

## State Management

### Zustand Store

The dashboard uses Zustand for client-side state management:

```tsx
import { useDashboardStore } from '@/modules/dashboard'

function DashboardComponent() {
  const { data, isLoading, selectedProject, setSelectedProject } = useDashboardStore()

  // Component logic
}
```

### React Query

Server state is managed with React Query:

```tsx
import { useDashboardData } from '@/modules/dashboard'

function DataComponent() {
  const { data, isLoading, error, refetch } = useDashboardData()

  // Component logic
}
```

## Customization

### Styling

The dashboard uses Tailwind CSS with custom brand colors:

```css
/* Brand colors are defined in globals.css */
--color-brand-primary: 14 78% 54%; /* Engineering Orange */
--color-brand-secondary: 210 100% 56%; /* Professional Blue */
--color-brand-accent: 142 76% 36%; /* Technical Green */
--color-brand-dark: 210 29% 24%; /* Dark Blue-Gray */
```

### Widget Configuration

Widgets can be customized through props:

```tsx
<ProjectsWidget
  projects={projects}
  isLoading={false}
  onProjectClick={(project) => navigate(`/projects/${project.id}`)}
  onCreateProject={() => navigate('/projects/new')}
  className="custom-styling"
/>
```

## Performance

### Optimization Features

- **Lazy Loading**: Components are loaded on demand
- **Memoization**: React.memo for component optimization
- **Efficient Queries**: React Query with proper caching
- **Intersection Observer**: For scroll-based animations
- **Bundle Splitting**: Dynamic imports for code splitting

### Performance Targets

- First Contentful Paint < 1.5s
- Largest Contentful Paint < 2.5s
- Cumulative Layout Shift < 0.1
- First Input Delay < 100ms

## Accessibility

### WCAG 2.1 AA Compliance

- Semantic HTML structure
- ARIA labels and roles
- Keyboard navigation support
- Screen reader compatibility
- Color contrast ratios > 4.5:1
- Focus management

### Example Accessibility Features

```tsx
<section aria-label="Dashboard metrics">
  <MetricsWidget metrics={metrics} />
</section>

<button
  aria-label="Create new project"
  onClick={handleCreateProject}
>
  New Project
</button>
```

## Testing

### Unit Tests

```bash
npm run test:unit -- dashboard
```

### E2E Tests

```bash
npm run test:e2e -- dashboard
```

### Coverage

Target: 100% code coverage for all components and utilities

## Browser Support

- Modern browsers (last 2 versions)
- Progressive enhancement
- Graceful degradation
- Mobile-first responsive design

## Contributing

1. Follow the established DDD structure
2. Maintain TypeScript strict mode compliance
3. Ensure zero ESLint/Prettier errors
4. Write comprehensive tests
5. Update documentation

## Related Documentation

- [Architecture Decisions](./ARCHITECTURE.md)
- [Usage Guide](./USAGE_GUIDE.md)
- [Frontend Specification](../../docs/developer-handbooks/frontend/000-frontend-specification.md)
- [Development Standards](../../docs/developer-handbooks/030-development-standards.md)
