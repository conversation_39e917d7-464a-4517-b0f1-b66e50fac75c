'use client'

import { But<PERSON> } from '@/components/ui/button'
import { useAuth } from '@/hooks/useAuth'
import { cn } from '@/lib/utils'
import Link from 'next/link'
import { useEffect, useRef, useState } from 'react'
import type { CTASectionProps } from '../types'

/**
 * Call-to-Action section with authentication-aware messaging
 */
export function CTASection({ cta, className }: Omit<CTASectionProps, 'isAuthenticated'>) {
  const { isAuthenticated } = useAuth()
  const [isVisible, setIsVisible] = useState(false)
  const sectionRef = useRef<HTMLElement>(null)

  // Intersection observer for animations
  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true)
        }
      },
      { threshold: 0.3 }
    )

    if (sectionRef.current) {
      observer.observe(sectionRef.current)
    }

    return () => observer.disconnect()
  }, [])

  return (
    <section
      ref={sectionRef}
      className={cn(
        'relative overflow-hidden bg-gradient-to-r from-brand-dark via-brand-secondary to-brand-primary py-24',
        className
      )}
    >
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-10" aria-hidden="true">
        <svg className="h-full w-full" fill="none" viewBox="0 0 400 400">
          <defs>
            <pattern id="cta-grid" width="40" height="40" patternUnits="userSpaceOnUse">
              <path d="M 40 0 L 0 0 0 40" fill="none" stroke="currentColor" strokeWidth="1" />
            </pattern>
          </defs>
          <rect width="100%" height="100%" fill="url(#cta-grid)" />
        </svg>
      </div>

      {/* Floating Elements */}
      <div
        className="absolute left-10 top-10 h-24 w-24 animate-pulse rounded-full bg-brand-accent/20 blur-xl"
        aria-hidden="true"
      />
      <div
        className="absolute bottom-10 right-10 h-32 w-32 animate-pulse rounded-full bg-brand-primary/20 blur-xl"
        style={{ animationDelay: '1s' }}
        aria-hidden="true"
      />

      <div className="relative mx-auto max-w-4xl px-4 text-center sm:px-6 lg:px-8">
        {/* Badge */}
        <div
          className={cn(
            'mb-8 inline-flex items-center rounded-full border border-white/20 bg-white/10 px-6 py-3 backdrop-blur-sm transition-all duration-700',
            isVisible ? 'translate-y-0 opacity-100' : 'translate-y-4 opacity-0'
          )}
        >
          <svg
            className="mr-3 h-5 w-5 text-brand-accent"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            aria-hidden="true"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M13 10V3L4 14h7v7l9-11h-7z"
            />
          </svg>
          <span className="text-sm font-semibold uppercase tracking-wide text-white">
            {cta.subtitle}
          </span>
        </div>

        {/* Main Title */}
        <h2
          className={cn(
            'mb-6 text-4xl font-bold text-white transition-all duration-700 lg:text-5xl',
            isVisible ? 'translate-y-0 opacity-100' : 'translate-y-4 opacity-0'
          )}
          style={{ transitionDelay: '0.2s' }}
        >
          {cta.title.split(' ').slice(0, -3).join(' ')}
          <span className="block text-brand-accent">
            {cta.title.split(' ').slice(-3).join(' ')}
          </span>
        </h2>

        {/* Description */}
        <p
          className={cn(
            'mx-auto mb-10 max-w-2xl text-xl leading-relaxed text-white/90 transition-all duration-700',
            isVisible ? 'translate-y-0 opacity-100' : 'translate-y-4 opacity-0'
          )}
          style={{ transitionDelay: '0.4s' }}
        >
          {cta.description}
        </p>

        {/* Action Buttons */}
        <div
          className={cn(
            'mb-12 flex flex-col justify-center gap-4 transition-all duration-700 sm:flex-row sm:gap-6',
            isVisible ? 'translate-y-0 opacity-100' : 'translate-y-4 opacity-0'
          )}
          style={{ transitionDelay: '0.6s' }}
        >
          {isAuthenticated ? (
            <Button
              size="lg"
              className="h-14 bg-white px-8 text-lg font-semibold text-brand-dark shadow-xl transition-all duration-300 hover:scale-105 hover:bg-white/90"
              asChild
            >
              <Link href="/dashboard">
                <svg
                  className="mr-2 h-5 w-5"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  aria-hidden="true"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"
                  />
                </svg>
                Continue to Dashboard
              </Link>
            </Button>
          ) : (
            <>
              <Button
                size="lg"
                className="h-14 bg-white px-8 text-lg font-semibold text-brand-dark shadow-xl transition-all duration-300 hover:scale-105 hover:bg-white/90"
                asChild
              >
                <Link href={cta.primaryAction.href}>
                  <svg
                    className="mr-2 h-5 w-5"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                    aria-hidden="true"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M13 10V3L4 14h7v7l9-11h-7z"
                    />
                  </svg>
                  {cta.primaryAction.label}
                </Link>
              </Button>
              {cta.secondaryAction && (
                <Button
                  size="lg"
                  className="h-14 border-2 border-white/30 px-8 text-lg font-semibold text-white backdrop-blur-sm transition-all duration-300 hover:scale-105 hover:bg-white/10"
                  variant="outline"
                  asChild
                >
                  <Link href={cta.secondaryAction.href}>
                    <svg
                      className="mr-2 h-5 w-5"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                      aria-hidden="true"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"
                      />
                    </svg>
                    {cta.secondaryAction.label}
                  </Link>
                </Button>
              )}
            </>
          )}
        </div>

        {/* Additional Trust Indicators */}
        <div
          className={cn(
            'flex flex-wrap justify-center gap-8 text-white/70 transition-all duration-700',
            isVisible ? 'translate-y-0 opacity-100' : 'translate-y-4 opacity-0'
          )}
          style={{ transitionDelay: '0.8s' }}
        >
          <div className="flex items-center space-x-2">
            <svg
              className="h-5 w-5 text-brand-accent"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              aria-hidden="true"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"
              />
            </svg>
            <span className="text-sm font-medium">1000+ Engineers</span>
          </div>
          <div className="flex items-center space-x-2">
            <svg
              className="h-5 w-5 text-brand-accent"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              aria-hidden="true"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"
              />
            </svg>
            <span className="text-sm font-medium">Fortune 500 Companies</span>
          </div>
          <div className="flex items-center space-x-2">
            <svg
              className="h-5 w-5 text-brand-accent"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              aria-hidden="true"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
            <span className="text-sm font-medium">99.9% Uptime</span>
          </div>
        </div>

        {/* Security Badges */}
        <div
          className={cn(
            'mt-8 flex flex-wrap justify-center gap-6 text-white/60 transition-all duration-700',
            isVisible ? 'translate-y-0 opacity-100' : 'translate-y-4 opacity-0'
          )}
          style={{ transitionDelay: '1s' }}
        >
          <div className="flex items-center space-x-2 text-xs">
            <svg
              className="h-4 w-4"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              aria-hidden="true"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"
              />
            </svg>
            <span>SOC 2 Certified</span>
          </div>
          <div className="flex items-center space-x-2 text-xs">
            <svg
              className="h-4 w-4"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              aria-hidden="true"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"
              />
            </svg>
            <span>GDPR Compliant</span>
          </div>
          <div className="flex items-center space-x-2 text-xs">
            <svg
              className="h-4 w-4"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              aria-hidden="true"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
            <span>ISO 27001</span>
          </div>
        </div>
      </div>
    </section>
  )
}
