/**
 * @jest-environment jsdom
 */

import React from 'react'
import { render, screen, fireEvent } from '@testing-library/react'
import { SystemMetricsWidget } from '../../components/SystemMetricsWidget'
import type { AdminDashboardMetrics } from '../../types'

const mockMetrics: AdminDashboardMetrics = {
  totalUsers: 100,
  activeUsers: 80,
  inactiveUsers: 20,
  adminUsers: 5,
  editorUsers: 30,
  viewerUsers: 65,
  totalProjects: 50,
  activeProjects: 30,
  completedProjects: 15,
  totalCalculations: 200,
  completedCalculations: 180,
  failedCalculations: 20,
  systemUptime: '5 days, 3 hours, 45 minutes',
  serverLoad: 0.65,
  memoryUsage: 75,
  diskUsage: 60,
  databaseConnections: 25,
  apiRequestsToday: 1500,
  errorRate: 2.5,
  lastBackup: '2024-01-01T00:00:00Z',
}

describe('SystemMetricsWidget', () => {
  describe('Loading State', () => {
    it('should show loading skeleton when isLoading is true', () => {
      render(<SystemMetricsWidget metrics={undefined} isLoading={true} />)

      // Check for loading skeleton elements
      const skeletonElements = document.querySelectorAll('.animate-pulse')
      expect(skeletonElements.length).toBeGreaterThan(0)
    })
  })

  describe('No Data State', () => {
    it('should show no data message when metrics is undefined', () => {
      render(<SystemMetricsWidget metrics={undefined} isLoading={false} />)

      expect(screen.getByText('No metrics available')).toBeInTheDocument()
      expect(screen.getByText('System metrics could not be loaded.')).toBeInTheDocument()
    })
  })

  describe('Data Display', () => {
    it('should display system metrics correctly', () => {
      render(<SystemMetricsWidget metrics={mockMetrics} isLoading={false} />)

      // Check header
      expect(screen.getByText('System Metrics')).toBeInTheDocument()

      // Check system uptime
      expect(screen.getByText('System Uptime')).toBeInTheDocument()
      expect(screen.getByText('5 days, 3 hours, 45 minutes')).toBeInTheDocument()

      // Check server load
      expect(screen.getByText('Server Load')).toBeInTheDocument()
      expect(screen.getByText('65.0%')).toBeInTheDocument()

      // Check memory usage
      expect(screen.getByText('Memory Usage')).toBeInTheDocument()
      expect(screen.getByText('75%')).toBeInTheDocument()

      // Check disk usage
      expect(screen.getByText('Disk Usage')).toBeInTheDocument()
      expect(screen.getByText('60%')).toBeInTheDocument()
    })

    it('should display additional metrics correctly', () => {
      render(<SystemMetricsWidget metrics={mockMetrics} isLoading={false} />)

      // Check database connections
      expect(screen.getByText('Database Connections')).toBeInTheDocument()
      expect(screen.getByText('25')).toBeInTheDocument()

      // Check API requests
      expect(screen.getByText('API Requests Today')).toBeInTheDocument()
      expect(screen.getByText('1,500')).toBeInTheDocument()

      // Check error rate
      expect(screen.getByText('Error Rate')).toBeInTheDocument()
      expect(screen.getByText('2.50%')).toBeInTheDocument()

      // Check last backup
      expect(screen.getByText('Last Backup')).toBeInTheDocument()
      expect(screen.getByText('1/1/2024')).toBeInTheDocument()
    })

    it('should show health status correctly', () => {
      render(<SystemMetricsWidget metrics={mockMetrics} isLoading={false} />)

      // Should show warning status due to high memory usage (75%)
      const statusElement = screen.getByText('warning')
      expect(statusElement).toBeInTheDocument()
    })

    it('should show health issues when present', () => {
      const criticalMetrics: AdminDashboardMetrics = {
        ...mockMetrics,
        errorRate: 10, // High error rate
        memoryUsage: 95, // Critical memory usage
        diskUsage: 98, // Critical disk usage
      }

      render(<SystemMetricsWidget metrics={criticalMetrics} isLoading={false} />)

      expect(screen.getByText('System Issues Detected')).toBeInTheDocument()
      expect(screen.getByText('High error rate detected')).toBeInTheDocument()
      expect(screen.getByText('Critical memory usage')).toBeInTheDocument()
      expect(screen.getByText('Critical disk usage')).toBeInTheDocument()
    })
  })

  describe('Refresh Functionality', () => {
    it('should call onRefresh when refresh button is clicked', () => {
      const mockOnRefresh = jest.fn()
      render(
        <SystemMetricsWidget metrics={mockMetrics} isLoading={false} onRefresh={mockOnRefresh} />
      )

      const refreshButton = screen.getByLabelText('Refresh metrics')
      fireEvent.click(refreshButton)

      expect(mockOnRefresh).toHaveBeenCalledTimes(1)
    })

    it('should not show refresh button when onRefresh is not provided', () => {
      render(<SystemMetricsWidget metrics={mockMetrics} isLoading={false} />)

      const refreshButton = screen.queryByLabelText('Refresh metrics')
      expect(refreshButton).not.toBeInTheDocument()
    })
  })

  describe('Health Status Colors', () => {
    it('should show healthy status for good metrics', () => {
      const healthyMetrics: AdminDashboardMetrics = {
        ...mockMetrics,
        errorRate: 1, // Low error rate
        memoryUsage: 50, // Normal memory usage
        diskUsage: 40, // Normal disk usage
        serverLoad: 0.3, // Low server load
      }

      render(<SystemMetricsWidget metrics={healthyMetrics} isLoading={false} />)

      const statusElement = screen.getByText('healthy')
      expect(statusElement).toBeInTheDocument()
      expect(statusElement.closest('span')).toHaveClass('text-green-600', 'bg-green-100')
    })

    it('should show critical status for bad metrics', () => {
      const criticalMetrics: AdminDashboardMetrics = {
        ...mockMetrics,
        errorRate: 15, // Very high error rate
        memoryUsage: 98, // Critical memory usage
        diskUsage: 99, // Critical disk usage
        serverLoad: 0.98, // Very high server load
      }

      render(<SystemMetricsWidget metrics={criticalMetrics} isLoading={false} />)

      const statusElement = screen.getByText('critical')
      expect(statusElement).toBeInTheDocument()
      expect(statusElement.closest('span')).toHaveClass('text-red-600', 'bg-red-100')
    })
  })

  describe('Error Rate Colors', () => {
    it('should show green color for low error rate', () => {
      const lowErrorMetrics = { ...mockMetrics, errorRate: 1 }
      render(<SystemMetricsWidget metrics={lowErrorMetrics} isLoading={false} />)

      const errorRateElement = screen.getByText('1.00%')
      expect(errorRateElement).toHaveClass('text-green-600')
    })

    it('should show yellow color for moderate error rate', () => {
      const moderateErrorMetrics = { ...mockMetrics, errorRate: 3 }
      render(<SystemMetricsWidget metrics={moderateErrorMetrics} isLoading={false} />)

      const errorRateElement = screen.getByText('3.00%')
      expect(errorRateElement).toHaveClass('text-yellow-600')
    })

    it('should show red color for high error rate', () => {
      const highErrorMetrics = { ...mockMetrics, errorRate: 8 }
      render(<SystemMetricsWidget metrics={highErrorMetrics} isLoading={false} />)

      const errorRateElement = screen.getByText('8.00%')
      expect(errorRateElement).toHaveClass('text-red-600')
    })
  })

  describe('Accessibility', () => {
    it('should have proper ARIA labels', () => {
      render(<SystemMetricsWidget metrics={mockMetrics} isLoading={false} />)

      const article = screen.getByRole('article')
      expect(article).toHaveAttribute('aria-labelledby', 'system-metrics-title')

      const title = screen.getByRole('heading', { level: 3 })
      expect(title).toHaveAttribute('id', 'system-metrics-title')
    })

    it('should have proper section labels', () => {
      render(<SystemMetricsWidget metrics={mockMetrics} isLoading={false} />)

      expect(screen.getByLabelText('System Performance Metrics')).toBeInTheDocument()
      expect(screen.getByLabelText('Additional System Information')).toBeInTheDocument()
    })

    it('should have accessible refresh button', () => {
      const mockOnRefresh = jest.fn()
      render(
        <SystemMetricsWidget metrics={mockMetrics} isLoading={false} onRefresh={mockOnRefresh} />
      )

      const refreshButton = screen.getByLabelText('Refresh metrics')
      expect(refreshButton).toBeInTheDocument()
      expect(refreshButton).toHaveAttribute('aria-label', 'Refresh metrics')
    })
  })

  describe('Custom Styling', () => {
    it('should apply custom className', () => {
      const { container } = render(
        <SystemMetricsWidget metrics={mockMetrics} isLoading={false} className="custom-class" />
      )

      expect(container.firstChild).toHaveClass('custom-class')
    })
  })

  describe('Data Formatting', () => {
    it('should format numbers correctly', () => {
      const metricsWithLargeNumbers: AdminDashboardMetrics = {
        ...mockMetrics,
        apiRequestsToday: 1234567,
      }

      render(<SystemMetricsWidget metrics={metricsWithLargeNumbers} isLoading={false} />)

      expect(screen.getByText('1,234,567')).toBeInTheDocument()
    })

    it('should format dates correctly', () => {
      render(<SystemMetricsWidget metrics={mockMetrics} isLoading={false} />)

      // Check that the date is formatted as expected
      expect(screen.getByText('1/1/2024')).toBeInTheDocument()
    })

    it('should format percentages correctly', () => {
      render(<SystemMetricsWidget metrics={mockMetrics} isLoading={false} />)

      expect(screen.getByText('65.0%')).toBeInTheDocument() // Server load
      expect(screen.getByText('75%')).toBeInTheDocument() // Memory usage
      expect(screen.getByText('60%')).toBeInTheDocument() // Disk usage
      expect(screen.getByText('2.50%')).toBeInTheDocument() // Error rate
    })
  })
})
