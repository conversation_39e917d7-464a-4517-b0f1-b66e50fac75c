/**
 * Main admin dashboard overview component
 */

'use client'

import { useAuth } from '@/hooks/useAuth'
import React, { Suspense } from 'react'
import { useAdminDashboardData } from '../hooks'
import { AdminDashboardOverviewProps } from '../types'
import { ComponentLibraryWidget } from './ComponentLibraryWidget'
import { ProjectOversightWidget } from './ProjectOversightWidget'
import { SystemMetricsWidget } from './SystemMetricsWidget'
import { UserManagementWidget } from './UserManagementWidget'

// Lazy load advanced widgets for better performance
const AuditLogsWidget = React.lazy(() =>
  import('./AuditLogsWidget').then((m) => ({ default: m.AuditLogsWidget }))
)
const SecurityMonitoringWidget = React.lazy(() =>
  import('./SecurityMonitoringWidget').then((m) => ({ default: m.SecurityMonitoringWidget }))
)
const SystemConfigurationWidget = React.lazy(() =>
  import('./SystemConfigurationWidget').then((m) => ({ default: m.SystemConfigurationWidget }))
)
const QuickAdminActionsWidget = React.lazy(() =>
  import('./QuickAdminActionsWidget').then((m) => ({ default: m.QuickAdminActionsWidget }))
)

/**
 * Loading skeleton component for widgets
 */
function WidgetSkeleton({ className = '' }: { className?: string }) {
  return (
    <div className={`animate-pulse rounded-lg bg-white p-6 shadow ${className}`}>
      <div className="mb-4 h-6 w-1/3 rounded bg-gray-200"></div>
      <div className="space-y-3">
        <div className="h-4 w-full rounded bg-gray-200"></div>
        <div className="h-4 w-3/4 rounded bg-gray-200"></div>
        <div className="h-4 w-1/2 rounded bg-gray-200"></div>
      </div>
    </div>
  )
}

/**
 * Error boundary component for widgets
 */
function WidgetErrorBoundary({
  children,
  fallback,
}: {
  children: React.ReactNode
  fallback?: React.ReactNode
}) {
  const [hasError, setHasError] = React.useState(false)

  React.useEffect(() => {
    setHasError(false)
  }, [children])

  if (hasError) {
    return (
      <div className="rounded-lg bg-red-50 p-6 shadow">
        <div className="flex">
          <div className="flex-shrink-0">
            <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
              <path
                fillRule="evenodd"
                d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                clipRule="evenodd"
              />
            </svg>
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-red-800">Widget Error</h3>
            <div className="mt-2 text-sm text-red-700">
              {fallback || 'This widget encountered an error and could not be displayed.'}
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <React.ErrorBoundary onError={() => setHasError(true)} fallback={null}>
      {children}
    </React.ErrorBoundary>
  )
}

export function AdminDashboardOverview({
  data,
  isLoading,
  error,
  className = '',
}: AdminDashboardOverviewProps) {
  const { user, isAdmin } = useAuth()
  const {
    data: dashboardData,
    isLoading: isLoadingData,
    error: dataError,
  } = useAdminDashboardData()

  // Use provided data or fetched data
  const finalData = data || dashboardData
  const finalIsLoading = isLoading ?? isLoadingData
  const finalError = error || dataError

  // Access control check
  if (!isAdmin()) {
    return (
      <div className={`py-8 text-center ${className}`}>
        <div className="mx-auto max-w-md">
          <div className="rounded-md border border-red-200 bg-red-50 p-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                  <path
                    fillRule="evenodd"
                    d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                    clipRule="evenodd"
                  />
                </svg>
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-red-800">Access Denied</h3>
                <div className="mt-2 text-sm text-red-700">
                  You don&apos;t have permission to access the admin dashboard.
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  // Error state
  if (finalError) {
    return (
      <div className={`py-8 text-center ${className}`}>
        <div className="mx-auto max-w-md">
          <div className="rounded-md border border-red-200 bg-red-50 p-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                  <path
                    fillRule="evenodd"
                    d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                    clipRule="evenodd"
                  />
                </svg>
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-red-800">Dashboard Error</h3>
                <div className="mt-2 text-sm text-red-700">
                  {finalError.message || 'Failed to load admin dashboard data.'}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <main className={`space-y-8 ${className}`} role="main" aria-label="Admin Dashboard">
      {/* Header */}
      <header className="border-b border-gray-200 pb-5">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Admin Dashboard</h1>
            <p className="mt-2 text-gray-600">
              Welcome back, {user?.name}. Monitor and manage your Ultimate Electrical Designer
              system.
            </p>
          </div>
          <div className="flex items-center space-x-3">
            <div className="flex items-center text-sm text-gray-500">
              <svg className="mr-1.5 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                />
              </svg>
              Last updated: {finalData ? new Date().toLocaleTimeString() : 'Never'}
            </div>
            {finalIsLoading && (
              <div className="flex items-center text-sm text-blue-600">
                <svg className="mr-1.5 h-4 w-4 animate-spin" fill="none" viewBox="0 0 24 24">
                  <circle
                    className="opacity-25"
                    cx="12"
                    cy="12"
                    r="10"
                    stroke="currentColor"
                    strokeWidth="4"
                  ></circle>
                  <path
                    className="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                  ></path>
                </svg>
                Refreshing...
              </div>
            )}
          </div>
        </div>
      </header>

      {/* Core Widgets Grid */}
      <section
        className="grid grid-cols-1 gap-6 lg:grid-cols-2 xl:grid-cols-3"
        aria-label="Core Admin Widgets"
      >
        {/* System Metrics Widget */}
        <WidgetErrorBoundary>
          <SystemMetricsWidget
            metrics={finalData?.metrics}
            isLoading={finalIsLoading}
            className="xl:col-span-1"
          />
        </WidgetErrorBoundary>

        {/* User Management Widget */}
        <WidgetErrorBoundary>
          <UserManagementWidget
            users={finalData?.userSummaries || []}
            isLoading={finalIsLoading}
            className="xl:col-span-1"
          />
        </WidgetErrorBoundary>

        {/* Component Library Widget */}
        <WidgetErrorBoundary>
          <ComponentLibraryWidget
            stats={finalData?.componentLibraryStats}
            isLoading={finalIsLoading}
            className="xl:col-span-1"
          />
        </WidgetErrorBoundary>
      </section>

      {/* Secondary Widgets Grid */}
      <section
        className="grid grid-cols-1 gap-6 lg:grid-cols-2"
        aria-label="Secondary Admin Widgets"
      >
        {/* Project Oversight Widget */}
        <WidgetErrorBoundary>
          <ProjectOversightWidget
            projects={finalData?.projectSummaries || []}
            isLoading={finalIsLoading}
            className="lg:col-span-1"
          />
        </WidgetErrorBoundary>

        {/* Quick Admin Actions Widget */}
        <WidgetErrorBoundary>
          <Suspense fallback={<WidgetSkeleton />}>
            <QuickAdminActionsWidget
              actions={finalData?.quickActions || []}
              className="lg:col-span-1"
            />
          </Suspense>
        </WidgetErrorBoundary>
      </section>

      {/* Advanced Widgets Grid */}
      <section
        className="grid grid-cols-1 gap-6 xl:grid-cols-3"
        aria-label="Advanced Admin Widgets"
      >
        {/* Audit Logs Widget */}
        <WidgetErrorBoundary>
          <Suspense fallback={<WidgetSkeleton />}>
            <AuditLogsWidget
              logs={finalData?.auditLogs || []}
              isLoading={finalIsLoading}
              className="xl:col-span-2"
            />
          </Suspense>
        </WidgetErrorBoundary>

        {/* Security Monitoring Widget */}
        <WidgetErrorBoundary>
          <Suspense fallback={<WidgetSkeleton />}>
            <SecurityMonitoringWidget
              alerts={finalData?.securityAlerts || []}
              isLoading={finalIsLoading}
              className="xl:col-span-1"
            />
          </Suspense>
        </WidgetErrorBoundary>
      </section>

      {/* System Configuration Widget */}
      <section aria-label="System Configuration">
        <WidgetErrorBoundary>
          <Suspense fallback={<WidgetSkeleton />}>
            <SystemConfigurationWidget
              configurations={finalData?.systemConfiguration || []}
              isLoading={finalIsLoading}
            />
          </Suspense>
        </WidgetErrorBoundary>
      </section>
    </main>
  )
}
