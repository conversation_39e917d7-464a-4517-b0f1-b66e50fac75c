'use client'

/**
 * Theme Provider Component for Ultimate Electrical Designer
 * 
 * Provides comprehensive theming support using next-themes with:
 * - TypeScript interfaces matching settings module types
 * - SSR-safe theme handling with hydration protection
 * - Error boundaries and fallback handling
 * - WCAG 2.1 AA accessibility compliance
 * - Integration with existing settings architecture
 */

import type { ThemeProviderProps as NextThemeProviderProps } from 'next-themes'
import { ThemeProvider as NextThemesProvider, useTheme } from 'next-themes'
import * as React from 'react'
import { createContext, useContext, useEffect, useMemo, useState } from 'react'

// Import existing theme types from settings module
import { THEME_CONFIGS } from '../modules/settings/constants'
import type { ThemeConfig } from '../modules/settings/types'

/**
 * Extended theme configuration interface
 * Combines next-themes functionality with UED-specific theme config
 */
export interface UEDThemeConfig extends ThemeConfig {
  /** Whether this theme supports system preference detection */
  supportsSystem?: boolean
  /** Theme-specific CSS class names */
  className?: string
  /** Theme metadata for accessibility */
  metadata?: {
    contrastRatio: number
    colorBlindnessFriendly: boolean
    reducedMotion: boolean
  }
}

/**
 * Theme provider props extending next-themes with UED-specific options
 */
export interface UEDThemeProviderProps extends Omit<NextThemeProviderProps, 'themes'> {
  /** Available theme configurations */
  themes?: string[]
  /** Default theme when system preference is not available */
  defaultTheme?: string
  /** Whether to enable system theme detection */
  enableSystem?: boolean
  /** Storage key for theme persistence */
  storageKey?: string
  /** Whether to disable transitions during theme changes */
  disableTransitionOnChange?: boolean
  /** Custom theme configurations */
  themeConfigs?: Record<string, UEDThemeConfig>
  /** Error boundary fallback component */
  fallback?: React.ComponentType<{ error: Error }>
  /** Children components */
  children: React.ReactNode
}

/**
 * Theme context interface for accessing theme state and actions
 */
export interface UEDThemeContextValue {
  /** Current active theme */
  theme: string | undefined
  /** Set theme programmatically */
  setTheme: (theme: string) => void
  /** Available themes */
  themes: string[]
  /** System theme preference */
  systemTheme: string | undefined
  /** Whether theme has been resolved (SSR safety) */
  resolvedTheme: string | undefined
  /** Theme configuration for current theme */
  themeConfig: UEDThemeConfig | undefined
  /** Whether theme provider is mounted (hydration safety) */
  mounted: boolean
}

/**
 * Theme context for accessing theme state throughout the application
 */
const UEDThemeContext = createContext<UEDThemeContextValue | undefined>(undefined)

/**
 * Error boundary component for theme provider failures
 */
interface ThemeErrorBoundaryProps {
  children: React.ReactNode
  fallback?: React.ComponentType<{ error: Error }>
}

interface ThemeErrorBoundaryState {
  hasError: boolean
  error?: Error
}

class ThemeErrorBoundary extends React.Component<ThemeErrorBoundaryProps, ThemeErrorBoundaryState> {
  constructor(props: ThemeErrorBoundaryProps) {
    super(props)
    this.state = { hasError: false }
  }

  static getDerivedStateFromError(error: Error): ThemeErrorBoundaryState {
    return { hasError: true, error }
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Theme Provider Error:', error, errorInfo)
  }

  render() {
    if (this.state.hasError) {
      const FallbackComponent = this.props.fallback
      if (FallbackComponent && this.state.error) {
        return <FallbackComponent error={this.state.error} />
      }

      // Default fallback with light theme
      return (
        <div className="min-h-screen bg-white text-gray-900" data-theme="light">
          {this.props.children}
        </div>
      )
    }

    return this.props.children
  }
}

/**
 * Default theme error fallback component
 */
const DefaultThemeFallback: React.FC<{ error: Error }> = ({ error }) => (
  <div 
    className="min-h-screen bg-white text-gray-900 flex items-center justify-center" 
    data-theme="light"
    role="alert"
    aria-live="assertive"
  >
    <div className="text-center p-8">
      <h1 className="text-2xl font-bold mb-4">Theme Loading Error</h1>
      <p className="text-gray-600 mb-4">
        There was an error loading the theme system. The application is running in fallback mode.
      </p>
      <details className="text-left text-sm text-gray-500">
        <summary className="cursor-pointer">Error Details</summary>
        <pre className="mt-2 p-2 bg-gray-100 rounded overflow-auto">
          {error.message}
        </pre>
      </details>
    </div>
  </div>
)

/**
 * Main theme provider component
 * Wraps next-themes with UED-specific functionality and error handling
 */
export function UEDThemeProvider({
  children,
  themes = ['light', 'dark', 'system'],
  defaultTheme = 'system',
  enableSystem = true,
  storageKey = 'ued-theme',
  disableTransitionOnChange = false,
  themeConfigs = THEME_CONFIGS,
  fallback = DefaultThemeFallback,
  ...props
}: UEDThemeProviderProps) {
  const [mounted, setMounted] = useState(false)

  // Ensure component is mounted before rendering theme-dependent content
  useEffect(() => {
    setMounted(true)
  }, [])

  return (
    <ThemeErrorBoundary fallback={fallback}>
      <NextThemesProvider
        themes={themes}
        defaultTheme={defaultTheme}
        enableSystem={enableSystem}
        storageKey={storageKey}
        disableTransitionOnChange={disableTransitionOnChange}
        attribute="class"
        {...props}
      >
        <UEDThemeContextProvider themeConfigs={themeConfigs} mounted={mounted}>
          {children}
        </UEDThemeContextProvider>
      </NextThemesProvider>
    </ThemeErrorBoundary>
  )
}

/**
 * Internal context provider that extends next-themes with UED-specific functionality
 */
function UEDThemeContextProvider({
  children,
  themeConfigs,
  mounted,
}: {
  children: React.ReactNode
  themeConfigs: Record<string, UEDThemeConfig>
  mounted: boolean
}) {
  // Import next-themes hook for accessing theme state
  const { theme, setTheme, themes, systemTheme, resolvedTheme } = useTheme()

  // Get current theme configuration
  const themeConfig = useMemo(() => {
    if (!resolvedTheme || !themeConfigs[resolvedTheme]) {
      return themeConfigs.light // Fallback to light theme config
    }
    return themeConfigs[resolvedTheme]
  }, [resolvedTheme, themeConfigs])

  // Apply theme-specific CSS custom properties
  useEffect(() => {
    if (!mounted || !resolvedTheme || !themeConfig) return

    const root = document.documentElement
    const colors = themeConfig.colors

    // Apply theme colors as CSS custom properties
    Object.entries(colors).forEach(([key, value]) => {
      root.style.setProperty(`--color-${key}`, value as string)
    })

    // Add theme class to document element for CSS targeting
    root.setAttribute('data-theme', resolvedTheme)

    // Apply theme-specific metadata for accessibility
    if (themeConfig.metadata) {
      root.setAttribute('data-contrast-ratio', themeConfig.metadata.contrastRatio.toString())
      root.setAttribute('data-color-blind-friendly', themeConfig.metadata.colorBlindnessFriendly.toString())
      root.setAttribute('data-reduced-motion', themeConfig.metadata.reducedMotion.toString())
    }
  }, [mounted, resolvedTheme, themeConfig])

  const contextValue: UEDThemeContextValue = {
    theme,
    setTheme,
    themes,
    systemTheme,
    resolvedTheme,
    themeConfig,
    mounted,
  }

  return (
    <UEDThemeContext.Provider value={contextValue}>
      {children}
    </UEDThemeContext.Provider>
  )
}

/**
 * Hook to access theme context
 * Provides type-safe access to theme state and actions
 */
export function useUEDTheme(): UEDThemeContextValue {
  const context = useContext(UEDThemeContext)
  
  if (context === undefined) {
    throw new Error('useUEDTheme must be used within a UEDThemeProvider')
  }
  
  return context
}

/**
 * Hook for theme-aware styling
 * Returns current theme configuration for dynamic styling
 */
export function useThemeConfig(): UEDThemeConfig | undefined {
  const { themeConfig } = useUEDTheme()
  return themeConfig
}

/**
 * Hook for SSR-safe theme detection
 * Returns undefined during SSR to prevent hydration mismatches
 */
export function useThemeSafe(): string | undefined {
  const { resolvedTheme, mounted } = useUEDTheme()
  return mounted ? resolvedTheme : undefined
}

// Re-export next-themes types for convenience
export type { ThemeProviderProps } from 'next-themes'

// Export theme configurations for external use
export { THEME_CONFIGS } from '../modules/settings/constants'

