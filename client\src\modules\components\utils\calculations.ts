/**
 * Enhanced Calculation Utilities
 * Comprehensive calculation functions for component data
 */

import type { ComponentRead } from '../schemas'

// Price calculation with quantity and discount
export function calculateTotalPrice(
  price: string | number | null | undefined,
  quantity: number | string = 1,
  discount: number | string = 0
): number | null {
  if (price === null || price === undefined) {
    return null
  }

  const numericPrice = typeof price === 'string' ? parseFloat(price) : price
  const qty = typeof quantity === 'string' ? parseFloat(quantity) : quantity
  const disc = typeof discount === 'string' ? parseFloat(discount) : discount

  if (isNaN(numericPrice) || isNaN(qty) || qty <= 0) {
    return null
  }

  // Apply discount if provided
  if (!isNaN(disc) && disc > 0) {
    const discountMultiplier = (100 - disc) / 100
    return numericPrice * qty * discountMultiplier
  }

  return numericPrice * qty
}

// Component-based price calculation
export function calculateComponentTotalPrice(
  component: ComponentRead,
  quantity: number | string = 1,
  discount: number | string = 0
): number | null {
  if (!component.unit_price) {
    return null
  }

  return calculateTotalPrice(component.unit_price, quantity, discount)
}

// Calculate bulk price with volume discounts
export function calculateBulkPrice(
  unitPrice: number,
  quantity: number,
  volumeDiscounts: { quantity: number; discount: number }[] = []
): number {
  if (!volumeDiscounts.length) {
    return unitPrice * quantity
  }

  // Sort discounts by quantity in descending order
  const sortedDiscounts = [...volumeDiscounts].sort((a, b) => b.quantity - a.quantity)

  // Find applicable discount
  const applicableDiscount = sortedDiscounts.find((d) => quantity >= d.quantity)

  if (!applicableDiscount) {
    return unitPrice * quantity
  }

  const discountMultiplier = (100 - applicableDiscount.discount) / 100
  return unitPrice * quantity * discountMultiplier
}

// Calculate shipping weight
export function calculateShippingWeight(
  components: ComponentRead[],
  quantities: number[] | Record<number, number>,
  packagingFactor: number = 1.1
): number {
  let totalWeight = 0

  components.forEach((component, index) => {
    if (!component.weight_kg) return

    const quantity = Array.isArray(quantities)
      ? quantities[index] || 0
      : quantities[component.id] || 0

    totalWeight += component.weight_kg * quantity
  })

  // Apply packaging factor
  return totalWeight * packagingFactor
}

// Calculate shipping dimensions
export function calculateShippingDimensions(
  components: ComponentRead[],
  quantities: number[] | Record<number, number>
): { length: number; width: number; height: number; volume: number } | null {
  // Default result with zero dimensions
  const result = { length: 0, width: 0, height: 0, volume: 0 }

  // Track if we have any valid dimensions
  let hasDimensions = false

  components.forEach((component, index) => {
    if (!component.dimensions) return

    const quantity = Array.isArray(quantities)
      ? quantities[index] || 0
      : quantities[component.id] || 0

    if (quantity <= 0) return

    const { length, width, height } = component.dimensions

    if (length && width && height) {
      hasDimensions = true

      // Simple approximation - take the largest dimension in each direction
      result.length = Math.max(result.length, length)
      result.width = Math.max(result.width, width)
      result.height = Math.max(result.height, height)
    }
  })

  if (!hasDimensions) return null

  // Calculate volume
  result.volume = result.length * result.width * result.height

  return result
}

// Calculate price per unit weight
export function calculatePricePerWeight(
  price: number,
  weight: number,
  unit: 'kg' | 'g' = 'kg'
): number | null {
  if (!weight || weight <= 0) return null

  const weightInKg = unit === 'g' ? weight / 1000 : weight
  return price / weightInKg
}

// Calculate price comparison between components
export function calculatePriceComparison(
  components: ComponentRead[]
): { id: number; price: number; normalizedPrice: number; difference: number }[] {
  if (!components.length) return []

  // Find the component with the lowest price
  const validComponents = components.filter((c) => c.unit_price && c.unit_price > 0)
  if (!validComponents.length) return []

  const lowestPrice = Math.min(...validComponents.map((c) => c.unit_price as number))

  return validComponents.map((component) => {
    const price = component.unit_price as number
    const normalizedPrice = price / lowestPrice
    const difference = ((price - lowestPrice) / lowestPrice) * 100

    return {
      id: component.id,
      price,
      normalizedPrice,
      difference,
    }
  })
}

// Calculate component availability score
export function calculateAvailabilityScore(component: ComponentRead): number {
  if (!component.is_active) return 0

  const stockStatusScores: Record<string, number> = {
    available: 1.0,
    limited: 0.7,
    on_order: 0.5,
    out_of_stock: 0.2,
    discontinued: 0.0,
  }

  const stockScore = stockStatusScores[component.stock_status] || 0.5
  const preferredBonus = component.is_preferred ? 0.2 : 0

  // Cap at 1.0
  return Math.min(stockScore + preferredBonus, 1.0)
}

// Calculate component compatibility score
export function calculateCompatibilityScore(
  component: ComponentRead,
  requiredSpecs: Record<string, any>
): number {
  if (!component.specifications) return 0

  const specs = component.specifications as Record<string, any>
  const requiredKeys = Object.keys(requiredSpecs)

  if (!requiredKeys.length) return 1.0

  let matchCount = 0

  requiredKeys.forEach((key) => {
    if (specs[key] === requiredSpecs[key]) {
      matchCount++
    }
  })

  return matchCount / requiredKeys.length
}

// Calculate component rating based on multiple factors
export function calculateComponentRating(
  component: ComponentRead,
  weights: {
    availability?: number
    price?: number
    specifications?: number
    preferred?: number
  } = {}
): number {
  const { availability = 0.3, price = 0.2, specifications = 0.3, preferred = 0.2 } = weights

  // Availability score (0-1)
  const availabilityScore = calculateAvailabilityScore(component)

  // Price score (0-1, lower is better)
  let priceScore = 0.5 // Default middle score
  if (component.unit_price) {
    // This is a simplified score - in reality would compare to average price
    priceScore =
      component.unit_price > 1000
        ? 0.2
        : component.unit_price > 100
          ? 0.4
          : component.unit_price > 10
            ? 0.6
            : component.unit_price > 1
              ? 0.8
              : 1.0
  }

  // Specifications score (0-1)
  let specificationsScore = 0.5 // Default middle score
  if (component.specifications) {
    const specs = component.specifications as Record<string, any>
    const specCount = Object.keys(specs).length
    specificationsScore =
      specCount > 10 ? 1.0 : specCount > 5 ? 0.8 : specCount > 3 ? 0.6 : specCount > 1 ? 0.4 : 0.2
  }

  // Preferred score (0 or 1)
  const preferredScore = component.is_preferred ? 1.0 : 0.0

  // Calculate weighted average
  const weightSum = availability + price + specifications + preferred
  const weightedScore =
    (availabilityScore * availability +
      priceScore * price +
      specificationsScore * specifications +
      preferredScore * preferred) /
    weightSum

  // Convert to 0-5 scale
  return weightedScore * 5
}

// Calculate volume in cubic units
export function calculateVolume(
  dimensions:
    | { length?: number; width?: number; height?: number; diameter?: number }
    | null
    | undefined
): number | null {
  if (!dimensions) return null

  const { length, width, height, diameter } = dimensions

  // Cylindrical component
  if (diameter) {
    const radius = diameter / 2
    const cylinderHeight = height || length || diameter
    return Math.PI * radius * radius * cylinderHeight
  }

  // Rectangular component
  if (length && width && height) {
    return length * width * height
  }

  return null
}

// Calculate density
export function calculateDensity(
  weight: number | null | undefined,
  dimensions:
    | { length?: number; width?: number; height?: number; diameter?: number }
    | null
    | undefined
): number | null {
  if (!weight || !dimensions) return null

  const volume = calculateVolume(dimensions)
  if (!volume) return null

  return weight / volume
}

// Calculate power consumption
export function calculatePowerConsumption(
  voltage: number | null | undefined,
  current: number | null | undefined
): number | null {
  if (!voltage || !current) return null
  return voltage * current
}

// Calculate energy efficiency
export function calculateEnergyEfficiency(
  outputPower: number | null | undefined,
  inputPower: number | null | undefined
): number | null {
  if (!outputPower || !inputPower || inputPower === 0) return null
  return (outputPower / inputPower) * 100
}

// Calculate heat dissipation
export function calculateHeatDissipation(
  powerLoss: number | null | undefined,
  efficiency: number | null | undefined
): number | null {
  if (powerLoss) return powerLoss
  if (!efficiency) return null

  // If we have efficiency but not power loss, we need input power
  return null // Would need input power to calculate
}

// Calculate component lifespan
export function calculateLifespan(operatingHours: number, failureRate: number): number {
  // Mean Time Between Failures (MTBF) calculation
  return operatingHours / failureRate
}

// Calculate total cost of ownership
export function calculateTCO(
  initialCost: number,
  operatingCostPerYear: number,
  lifespan: number,
  discountRate: number = 0.05
): number {
  let totalCost = initialCost

  for (let year = 1; year <= lifespan; year++) {
    totalCost += operatingCostPerYear / Math.pow(1 + discountRate, year)
  }

  return totalCost
}

// Calculate return on investment
export function calculateROI(
  initialInvestment: number,
  annualSavings: number,
  lifespan: number
): number {
  const totalSavings = annualSavings * lifespan
  const netReturn = totalSavings - initialInvestment
  return (netReturn / initialInvestment) * 100
}

// Calculate payback period
export function calculatePaybackPeriod(initialInvestment: number, annualSavings: number): number {
  if (annualSavings <= 0) return Infinity
  return initialInvestment / annualSavings
}
