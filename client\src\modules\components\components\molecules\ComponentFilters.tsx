/**
 * ComponentFilters Molecule
 * Advanced filtering component with accessibility and real-time updates
 */

import React, { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Checkbox } from '@/components/ui/checkbox'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible'
import { cn } from '@/lib/utils'
import {
  Filter,
  X,
  ChevronDown,
  ChevronUp,
  RotateCcw,
  Settings,
  DollarSign,
  Package,
  Building,
  Calendar,
  Weight,
  Ruler,
} from 'lucide-react'
import { useComponentStoreEnhanced } from '../../hooks/useComponentStoreEnhanced'
import type { ComponentFilter, RangeFilter } from '../../schemas'

export interface ComponentFiltersProps {
  showAdvanced?: boolean
  compact?: boolean
  className?: string
  onFiltersChange?: (filters: ComponentFilter) => void
  onClear?: () => void
  'data-testid'?: string
}

export const ComponentFilters = React.forwardRef<HTMLDivElement, ComponentFiltersProps>(
  (
    {
      showAdvanced = false,
      compact = false,
      className,
      onFiltersChange,
      onClear,
      'data-testid': testId,
      ...props
    },
    ref
  ) => {
    const {
      filterState,
      addFilter,
      removeFilter,
      clearAllFilters,
      toggleAdvancedFilters,
      getActiveFilterCount,
    } = useComponentStoreEnhanced()

    const [isAdvancedOpen, setIsAdvancedOpen] = useState(showAdvanced)
    const activeFilterCount = getActiveFilterCount()

    // Handle filter change
    const handleFilterChange = (key: string, value: any) => {
      if (value === null || value === undefined || value === '') {
        removeFilter(key)
      } else {
        addFilter(key, value)
      }

      if (onFiltersChange) {
        const updatedFilters = { ...filterState.active_filters }
        if (value === null || value === undefined || value === '') {
          delete updatedFilters[key]
        } else {
          updatedFilters[key] = value
        }
        onFiltersChange(updatedFilters as ComponentFilter)
      }
    }

    // Handle range filter change
    const handleRangeFilterChange = (key: string, type: 'min' | 'max', value: string) => {
      const currentRange = (filterState.active_filters[key] as RangeFilter) || {}
      const numericValue = value ? parseFloat(value) : undefined

      const updatedRange = {
        ...currentRange,
        [type]: numericValue,
      }

      // Remove empty range
      if (!updatedRange.min && !updatedRange.max) {
        removeFilter(key)
      } else {
        addFilter(key, updatedRange)
      }

      if (onFiltersChange) {
        const updatedFilters = { ...filterState.active_filters }
        if (!updatedRange.min && !updatedRange.max) {
          delete updatedFilters[key]
        } else {
          updatedFilters[key] = updatedRange
        }
        onFiltersChange(updatedFilters as ComponentFilter)
      }
    }

    // Handle clear all
    const handleClearAll = () => {
      clearAllFilters()
      if (onClear) {
        onClear()
      }
      if (onFiltersChange) {
        onFiltersChange({})
      }
    }

    // Mock data for dropdowns (in real app, these would come from API)
    const manufacturers = ['Siemens', 'ABB', 'Schneider Electric', 'Phoenix Contact', 'Weidmuller']
    const categories = ['RESISTOR', 'CAPACITOR', 'SWITCH', 'CONNECTOR', 'SENSOR', 'RELAY']
    const componentTypes = ['resistor', 'capacitor', 'switch', 'connector', 'sensor', 'relay']
    const stockStatuses = ['available', 'limited', 'out_of_stock', 'discontinued', 'on_order']

    return (
      <div
        ref={ref}
        className={cn('space-y-4', className)}
        data-testid={testId || 'component-filters'}
        {...props}
      >
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Filter className="h-4 w-4 text-gray-600" />
            <h3 className={cn('font-medium', compact ? 'text-sm' : 'text-base')}>Filters</h3>
            {activeFilterCount > 0 && (
              <Badge variant="secondary" className="text-xs">
                {activeFilterCount}
              </Badge>
            )}
          </div>

          <div className="flex items-center gap-2">
            {activeFilterCount > 0 && (
              <Button
                variant="ghost"
                size="sm"
                onClick={handleClearAll}
                className="text-xs"
                data-testid={`${testId || 'component-filters'}-clear-all`}
              >
                <RotateCcw className="mr-1 h-3 w-3" />
                Clear All
              </Button>
            )}

            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsAdvancedOpen(!isAdvancedOpen)}
              className="text-xs"
              data-testid={`${testId || 'component-filters'}-toggle-advanced`}
            >
              <Settings className="mr-1 h-3 w-3" />
              Advanced
              {isAdvancedOpen ? (
                <ChevronUp className="ml-1 h-3 w-3" />
              ) : (
                <ChevronDown className="ml-1 h-3 w-3" />
              )}
            </Button>
          </div>
        </div>

        {/* Basic Filters */}
        <div className="space-y-3">
          {/* Manufacturer */}
          <div className="space-y-2">
            <Label htmlFor="manufacturer-filter" className="text-sm font-medium">
              <Building className="mr-2 inline h-4 w-4" />
              Manufacturer
            </Label>
            <Select
              value={filterState.active_filters.manufacturer || ''}
              onValueChange={(value) => handleFilterChange('manufacturer', value || null)}
            >
              <SelectTrigger id="manufacturer-filter">
                <SelectValue placeholder="All manufacturers" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">All manufacturers</SelectItem>
                {manufacturers.map((manufacturer) => (
                  <SelectItem key={manufacturer} value={manufacturer}>
                    {manufacturer}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Category */}
          <div className="space-y-2">
            <Label htmlFor="category-filter" className="text-sm font-medium">
              <Package className="mr-2 inline h-4 w-4" />
              Category
            </Label>
            <Select
              value={filterState.active_filters.category || ''}
              onValueChange={(value) => handleFilterChange('category', value || null)}
            >
              <SelectTrigger id="category-filter">
                <SelectValue placeholder="All categories" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">All categories</SelectItem>
                {categories.map((category) => (
                  <SelectItem key={category} value={category}>
                    {category}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Stock Status */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">Stock Status</Label>
            <div className="flex flex-wrap gap-2">
              {stockStatuses.map((status) => (
                <div key={status} className="flex items-center space-x-2">
                  <Checkbox
                    id={`stock-${status}`}
                    checked={filterState.active_filters.stock_status === status}
                    onCheckedChange={(checked) =>
                      handleFilterChange('stock_status', checked ? status : null)
                    }
                  />
                  <Label htmlFor={`stock-${status}`} className="text-sm capitalize">
                    {status.replace('_', ' ')}
                  </Label>
                </div>
              ))}
            </div>
          </div>

          {/* Boolean Filters */}
          <div className="space-y-2">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="is-active"
                checked={filterState.active_filters.is_active === true}
                onCheckedChange={(checked) =>
                  handleFilterChange('is_active', checked ? true : null)
                }
              />
              <Label htmlFor="is-active" className="text-sm">
                Active components only
              </Label>
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="is-preferred"
                checked={filterState.active_filters.is_preferred === true}
                onCheckedChange={(checked) =>
                  handleFilterChange('is_preferred', checked ? true : null)
                }
              />
              <Label htmlFor="is-preferred" className="text-sm">
                Preferred components only
              </Label>
            </div>
          </div>
        </div>

        {/* Advanced Filters */}
        <Collapsible open={isAdvancedOpen} onOpenChange={setIsAdvancedOpen}>
          <CollapsibleContent className="space-y-3">
            <Separator />

            {/* Price Range */}
            <div className="space-y-2">
              <Label className="text-sm font-medium">
                <DollarSign className="mr-2 inline h-4 w-4" />
                Price Range (EUR)
              </Label>
              <div className="grid grid-cols-2 gap-2">
                <div>
                  <Label htmlFor="price-min" className="text-xs text-gray-600">
                    Min
                  </Label>
                  <Input
                    id="price-min"
                    type="number"
                    placeholder="0"
                    value={(filterState.active_filters.price_range as RangeFilter)?.min || ''}
                    onChange={(e) => handleRangeFilterChange('price_range', 'min', e.target.value)}
                  />
                </div>
                <div>
                  <Label htmlFor="price-max" className="text-xs text-gray-600">
                    Max
                  </Label>
                  <Input
                    id="price-max"
                    type="number"
                    placeholder="1000"
                    value={(filterState.active_filters.price_range as RangeFilter)?.max || ''}
                    onChange={(e) => handleRangeFilterChange('price_range', 'max', e.target.value)}
                  />
                </div>
              </div>
            </div>

            {/* Weight Range */}
            <div className="space-y-2">
              <Label className="text-sm font-medium">
                <Weight className="mr-2 inline h-4 w-4" />
                Weight Range (kg)
              </Label>
              <div className="grid grid-cols-2 gap-2">
                <div>
                  <Label htmlFor="weight-min" className="text-xs text-gray-600">
                    Min
                  </Label>
                  <Input
                    id="weight-min"
                    type="number"
                    step="0.001"
                    placeholder="0"
                    value={(filterState.active_filters.weight_range as RangeFilter)?.min || ''}
                    onChange={(e) => handleRangeFilterChange('weight_range', 'min', e.target.value)}
                  />
                </div>
                <div>
                  <Label htmlFor="weight-max" className="text-xs text-gray-600">
                    Max
                  </Label>
                  <Input
                    id="weight-max"
                    type="number"
                    step="0.001"
                    placeholder="10"
                    value={(filterState.active_filters.weight_range as RangeFilter)?.max || ''}
                    onChange={(e) => handleRangeFilterChange('weight_range', 'max', e.target.value)}
                  />
                </div>
              </div>
            </div>

            {/* Component Type */}
            <div className="space-y-2">
              <Label htmlFor="component-type-filter" className="text-sm font-medium">
                Component Type
              </Label>
              <Select
                value={filterState.active_filters.component_type || ''}
                onValueChange={(value) => handleFilterChange('component_type', value || null)}
              >
                <SelectTrigger id="component-type-filter">
                  <SelectValue placeholder="All types" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">All types</SelectItem>
                  {componentTypes.map((type) => (
                    <SelectItem key={type} value={type}>
                      {type}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Date Filters */}
            <div className="space-y-2">
              <Label className="text-sm font-medium">
                <Calendar className="mr-2 inline h-4 w-4" />
                Date Added
              </Label>
              <div className="grid grid-cols-2 gap-2">
                <div>
                  <Label htmlFor="date-from" className="text-xs text-gray-600">
                    From
                  </Label>
                  <Input
                    id="date-from"
                    type="date"
                    value={filterState.active_filters.created_after || ''}
                    onChange={(e) => handleFilterChange('created_after', e.target.value || null)}
                  />
                </div>
                <div>
                  <Label htmlFor="date-to" className="text-xs text-gray-600">
                    To
                  </Label>
                  <Input
                    id="date-to"
                    type="date"
                    value={filterState.active_filters.created_before || ''}
                    onChange={(e) => handleFilterChange('created_before', e.target.value || null)}
                  />
                </div>
              </div>
            </div>
          </CollapsibleContent>
        </Collapsible>

        {/* Active Filters Display */}
        {activeFilterCount > 0 && (
          <div className="space-y-2">
            <Label className="text-xs font-medium text-gray-600">Active Filters:</Label>
            <div className="flex flex-wrap gap-1">
              {Object.entries(filterState.active_filters).map(([key, value]) => (
                <Badge key={key} variant="secondary" className="flex items-center gap-1 text-xs">
                  <span className="capitalize">
                    {key.replace('_', ' ')}: {String(value)}
                  </span>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleFilterChange(key, null)}
                    className="h-3 w-3 p-0 hover:bg-gray-200"
                    aria-label={`Remove ${key} filter`}
                  >
                    <X className="h-2 w-2" />
                  </Button>
                </Badge>
              ))}
            </div>
          </div>
        )}
      </div>
    )
  }
)

ComponentFilters.displayName = 'ComponentFilters'
