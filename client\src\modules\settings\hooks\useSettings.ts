/**
 * Main settings hook that combines server state and client state
 * Ultimate Electrical Designer - Settings & User Preferences
 */

import { useCallback, useEffect, useMemo } from 'react'
import {
  useAutoSavePreferences,
  useExportUserPreferences,
  useImportUserPreferences,
  usePreferencesChanges,
  useResetUserPreferences,
  useUpdateUserPreferences,
  useUserPreferences,
} from '../api/settingsQueries'
import { UI_CONSTANTS } from '../constants'
import { settingsStoreSelectors, useSettingsStore } from '../stores/settingsStore'
import type { SettingsCategory, SettingsImport, UserPreferences } from '../types'
import { DEFAULT_USER_PREFERENCES } from '../types'

/**
 * Main settings hook interface
 */
export interface UseSettingsReturn {
  // Server state
  preferences: UserPreferences | undefined
  isLoadingPreferences: boolean
  preferencesError: Error | null

  // Client state
  ui: {
    activeCategory: SettingsCategory
    searchQuery: string
    isLoading: boolean
    isSaving: boolean
    hasUnsavedChanges: boolean
    showResetDialog: boolean
    showImportDialog: boolean
    showExportDialog: boolean
    errors: Record<string, string>
    lastSaved?: string
  }

  // Form state
  formData: Partial<UserPreferences>
  hasChanges: boolean
  changedFields: string[]

  // Actions
  actions: {
    // Navigation
    setActiveCategory: (category: SettingsCategory) => void
    setSearchQuery: (query: string) => void
    clearSearch: () => void

    // Form management
    updateField: <K extends keyof UserPreferences>(field: K, value: UserPreferences[K]) => void
    updateMultipleFields: (updates: Partial<UserPreferences>) => void
    resetForm: () => void
    validateForm: () => { isValid: boolean; errors: Record<string, string> }

    // Server operations
    savePreferences: () => Promise<void>
    resetPreferences: () => Promise<void>
    exportPreferences: () => Promise<void>
    importPreferences: (importData: SettingsImport) => Promise<void>

    // Error handling
    setError: (field: string, error: string) => void
    clearErrors: () => void
    clearFieldError: (field: string) => void

    // Dialog management
    openResetDialog: () => void
    closeResetDialog: () => void
    openImportDialog: () => void
    closeImportDialog: () => void
    openExportDialog: () => void
    closeExportDialog: () => void
    closeAllDialogs: () => void

    // Sync management
    enableSync: () => void
    disableSync: () => void
  }

  // Auto-save
  autoSave: {
    isEnabled: boolean
    isAutoSaving: boolean
    autoSaveError: Error | null
    enableAutoSave: () => void
    disableAutoSave: () => void
  }
}

/**
 * Main settings hook
 */
export function useSettings(options?: {
  autoSave?: boolean
  autoSaveDelay?: number
}): UseSettingsReturn {
  const { autoSave = false, autoSaveDelay = UI_CONSTANTS.AUTO_SAVE_DEBOUNCE_MS } = options || {}

  // Server state
  const {
    data: preferences,
    isLoading: isLoadingPreferences,
    error: preferencesError,
  } = useUserPreferences()

  const updateMutation = useUpdateUserPreferences()
  const resetMutation = useResetUserPreferences()
  const exportMutation = useExportUserPreferences()
  const importMutation = useImportUserPreferences()

  // Client state
  const ui = useSettingsStore(settingsStoreSelectors.ui)
  const formData = useSettingsStore(settingsStoreSelectors.formData)
  const syncEnabled = useSettingsStore(settingsStoreSelectors.syncEnabled)

  // Store actions
  const {
    setActiveCategory,
    setSearchQuery,
    updateFormData,
    resetFormData,
    setLoading,
    setSaving,
    setError,
    clearErrors,
    setHasUnsavedChanges,
    showDialog,
    clearSearch,
    validateFormData,
    enableSync,
    disableSync,
    openDialog,
    closeDialog,
    closeAllDialogs,
  } = useSettingsStore()

  // Changes detection
  const { hasChanges, changes } = usePreferencesChanges(formData)
  const changedFields = useMemo(() => Object.keys(changes), [changes])

  // Auto-save functionality
  const { isAutoSaving, autoSaveError } = useAutoSavePreferences(
    formData,
    autoSave && hasChanges,
    autoSaveDelay
  )

  // Sync form data with server preferences when they load
  useEffect(() => {
    if (preferences && Object.keys(formData).length === 0) {
      updateFormData(preferences)
    }
  }, [preferences, formData, updateFormData])

  // Update unsaved changes state
  useEffect(() => {
    setHasUnsavedChanges(hasChanges)
  }, [hasChanges, setHasUnsavedChanges])

  // Update loading states
  useEffect(() => {
    setLoading(isLoadingPreferences)
  }, [isLoadingPreferences, setLoading])

  useEffect(() => {
    setSaving(updateMutation.isPending || resetMutation.isPending || importMutation.isPending)
  }, [updateMutation.isPending, resetMutation.isPending, importMutation.isPending, setSaving])

  // Form management callbacks
  const updateField = useCallback(
    <K extends keyof UserPreferences>(field: K, value: UserPreferences[K]) => {
      updateFormData({ [field]: value } as Partial<UserPreferences>)
    },
    [updateFormData]
  )

  const updateMultipleFields = useCallback(
    (updates: Partial<UserPreferences>) => {
      updateFormData(updates)
    },
    [updateFormData]
  )

  const resetForm = useCallback(() => {
    resetFormData()
    clearErrors()
  }, [resetFormData, clearErrors])

  const validateForm = useCallback(() => {
    return validateFormData()
  }, [validateFormData])

  // Server operations callbacks
  const savePreferences = useCallback(async () => {
    if (!hasChanges) return

    try {
      await updateMutation.mutateAsync(formData)
      resetFormData()
      clearErrors()
    } catch (error) {
      // Error handling is done in the mutation
    }
  }, [hasChanges, formData, updateMutation, resetFormData, clearErrors])

  const resetPreferences = useCallback(async () => {
    try {
      await resetMutation.mutateAsync()
      resetFormData()
      clearErrors()
      closeDialog('reset')
    } catch (error) {
      // Error handling is done in the mutation
    }
  }, [resetMutation, resetFormData, clearErrors, closeDialog])

  const exportPreferences = useCallback(async () => {
    try {
      await exportMutation.mutateAsync()
      closeDialog('export')
    } catch (error) {
      // Error handling is done in the mutation
    }
  }, [exportMutation, closeDialog])

  const importPreferences = useCallback(
    async (importData: SettingsImport) => {
      try {
        await importMutation.mutateAsync(importData)
        resetFormData()
        clearErrors()
        closeDialog('import')
      } catch (error) {
        // Error handling is done in the mutation
      }
    },
    [importMutation, resetFormData, clearErrors, closeDialog]
  )

  // Error handling callbacks
  const clearFieldError = useCallback(
    (field: string) => {
      const currentErrors = { ...ui.errors }
      delete currentErrors[field]
      clearErrors()
      Object.entries(currentErrors).forEach(([key, value]) => {
        setError(key, value)
      })
    },
    [ui.errors, clearErrors, setError]
  )

  // Dialog management callbacks
  const openResetDialog = useCallback(() => openDialog('reset'), [openDialog])
  const closeResetDialog = useCallback(() => closeDialog('reset'), [closeDialog])
  const openImportDialog = useCallback(() => openDialog('import'), [openDialog])
  const closeImportDialog = useCallback(() => closeDialog('import'), [closeDialog])
  const openExportDialog = useCallback(() => openDialog('export'), [openDialog])
  const closeExportDialog = useCallback(() => closeDialog('export'), [closeDialog])

  // Actions
  const actions = useMemo(
    () => ({
      // Navigation
      setActiveCategory,
      setSearchQuery,
      clearSearch,

      // Form management
      updateField,
      updateMultipleFields,
      resetForm,
      validateForm,

      // Server operations
      savePreferences,
      resetPreferences,
      exportPreferences,
      importPreferences,

      // Error handling
      setError,
      clearErrors,
      clearFieldError,

      // Dialog management
      openResetDialog,
      closeResetDialog,
      openImportDialog,
      closeImportDialog,
      openExportDialog,
      closeExportDialog,
      closeAllDialogs,

      // Sync management
      enableSync,
      disableSync,
    }),
    [
      setActiveCategory,
      setSearchQuery,
      clearSearch,
      updateField,
      updateMultipleFields,
      resetForm,
      validateForm,
      savePreferences,
      resetPreferences,
      exportPreferences,
      importPreferences,
      setError,
      clearErrors,
      clearFieldError,
      openResetDialog,
      closeResetDialog,
      openImportDialog,
      closeImportDialog,
      openExportDialog,
      closeExportDialog,
      closeAllDialogs,
      enableSync,
      disableSync,
    ]
  )

  // Auto-save controls
  const autoSaveControls = useMemo(
    () => ({
      isEnabled: autoSave,
      isAutoSaving,
      autoSaveError,
      enableAutoSave: () => {
        // This would be handled by the parent component
      },
      disableAutoSave: () => {
        // This would be handled by the parent component
      },
    }),
    [autoSave, isAutoSaving, autoSaveError]
  )

  return {
    // Server state
    preferences: preferences || DEFAULT_USER_PREFERENCES,
    isLoadingPreferences,
    preferencesError,

    // Client state
    ui,
    formData,
    hasChanges,
    changedFields,

    // Actions
    actions,

    // Auto-save
    autoSave: autoSaveControls,
  }
}

/**
 * Hook for specific settings category
 */
export function useCategorySettings(category: SettingsCategory) {
  const settings = useSettings()

  useEffect(() => {
    settings.actions.setActiveCategory(category)
  }, [category, settings.actions])

  return {
    ...settings,
    isActiveCategory: settings.ui.activeCategory === category,
  }
}

/**
 * Hook for settings search functionality
 */
export function useSettingsSearch() {
  const searchQuery = useSettingsStore(settingsStoreSelectors.searchQuery)
  const { setSearchQuery, clearSearch } = useSettingsStore()

  const debouncedSearch = useMemo(
    () =>
      debounce((query: string) => {
        setSearchQuery(query)
      }, UI_CONSTANTS.SEARCH_DEBOUNCE_MS),
    [setSearchQuery]
  )

  return {
    searchQuery,
    setSearchQuery: debouncedSearch,
    clearSearch,
  }
}

// Utility function for debouncing
function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout
  return (...args: Parameters<T>) => {
    clearTimeout(timeout)
    timeout = setTimeout(() => func(...args), wait)
  }
}
