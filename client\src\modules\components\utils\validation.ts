/**
 * Enhanced Validation Utilities
 * Comprehensive validation functions with Zod integration
 */

import { z } from 'zod'
import type {
  ComponentCreate,
  ComponentUpdate,
  ComponentRead,
  ComponentFilter,
  ComponentAdvancedSearch,
} from '../schemas'
import {
  ComponentCreateSchema,
  ComponentUpdateSchema,
  ComponentFilterSchema,
  ComponentAdvancedSearchSchema,
  safeValidateComponentCreate,
  safeValidateComponentUpdate,
  safeValidateComponentFilter,
  safeValidateAdvancedSearch,
} from '../schemas'

// Validation result interface
export interface ValidationResult {
  isValid: boolean
  errors: ValidationError[]
  warnings?: ValidationError[]
}

export interface ValidationError {
  field: string
  message: string
  code?: string
  severity?: 'error' | 'warning' | 'info'
}

// Enhanced component validation with business rules
export function validateComponent(
  data: unknown,
  mode: 'create' | 'update' = 'create'
): ValidationResult {
  const schema = mode === 'create' ? ComponentCreateSchema : ComponentUpdateSchema
  const result = schema.safeParse(data)

  if (result.success) {
    // Additional business rule validations
    const businessValidation = validateBusinessRules(result.data, mode)
    return {
      isValid: businessValidation.errors.length === 0,
      errors: businessValidation.errors,
      warnings: businessValidation.warnings,
    }
  }

  // Convert Zod errors to our format
  const errors: ValidationError[] = result.error.errors.map((error) => ({
    field: error.path.join('.'),
    message: error.message,
    code: error.code,
    severity: 'error',
  }))

  return {
    isValid: false,
    errors,
  }
}

// Business rule validation
function validateBusinessRules(
  data: ComponentCreate | ComponentUpdate,
  mode: 'create' | 'update'
): { errors: ValidationError[]; warnings: ValidationError[] } {
  const errors: ValidationError[] = []
  const warnings: ValidationError[] = []

  // Price validation with currency consistency
  if (data.unit_price && data.currency) {
    if (data.unit_price > 10000 && data.currency === 'EUR') {
      warnings.push({
        field: 'unit_price',
        message: 'Price seems unusually high. Please verify.',
        code: 'HIGH_PRICE',
        severity: 'warning',
      })
    }
  }

  // Manufacturer and model number consistency
  if (data.manufacturer && data.model_number) {
    const manufacturer = data.manufacturer.toLowerCase()
    const modelNumber = data.model_number.toLowerCase()

    // Check if model number contains manufacturer name
    if (manufacturer.length > 3 && modelNumber.includes(manufacturer)) {
      warnings.push({
        field: 'model_number',
        message: 'Model number appears to contain manufacturer name. Consider removing redundancy.',
        code: 'REDUNDANT_INFO',
        severity: 'warning',
      })
    }
  }

  // Dimensions validation
  if (data.dimensions) {
    const { length, width, height, diameter } = data.dimensions

    if (diameter && (length || width || height)) {
      errors.push({
        field: 'dimensions',
        message: 'Cannot specify both diameter and length/width/height dimensions.',
        code: 'CONFLICTING_DIMENSIONS',
        severity: 'error',
      })
    }

    // Check for unrealistic dimensions
    const maxDimension = Math.max(length || 0, width || 0, height || 0, diameter || 0)

    if (maxDimension > 10000) {
      // 10 meters in mm
      warnings.push({
        field: 'dimensions',
        message: 'Dimensions seem unusually large. Please verify units.',
        code: 'LARGE_DIMENSIONS',
        severity: 'warning',
      })
    }
  }

  // Weight validation
  if (data.weight_kg && data.weight_kg > 1000) {
    warnings.push({
      field: 'weight_kg',
      message: 'Weight seems unusually high. Please verify.',
      code: 'HIGH_WEIGHT',
      severity: 'warning',
    })
  }

  // Specifications validation
  if (data.specifications) {
    const specs = data.specifications as Record<string, any>

    // Check for empty specifications
    const emptySpecs = Object.entries(specs).filter(
      ([_, value]) => value === '' || value === null || value === undefined
    )

    if (emptySpecs.length > 0) {
      errors.push({
        field: 'specifications',
        message: `Empty specifications found: ${emptySpecs.map(([key]) => key).join(', ')}`,
        code: 'EMPTY_SPECIFICATIONS',
        severity: 'error',
      })
    }

    // Validate electrical specifications
    if (specs.voltage && typeof specs.voltage === 'string') {
      const voltageMatch = specs.voltage.match(/^(\d+(?:\.\d+)?)\s*(V|kV|mV)?$/i)
      if (!voltageMatch) {
        errors.push({
          field: 'specifications.voltage',
          message: 'Voltage must be in format "230V", "1.5kV", etc.',
          code: 'INVALID_VOLTAGE_FORMAT',
          severity: 'error',
        })
      }
    }

    if (specs.current && typeof specs.current === 'string') {
      const currentMatch = specs.current.match(/^(\d+(?:\.\d+)?)\s*(A|mA|kA)?$/i)
      if (!currentMatch) {
        errors.push({
          field: 'specifications.current',
          message: 'Current must be in format "10A", "500mA", etc.',
          code: 'INVALID_CURRENT_FORMAT',
          severity: 'error',
        })
      }
    }

    if (specs.power && typeof specs.power === 'string') {
      const powerMatch = specs.power.match(/^(\d+(?:\.\d+)?)\s*(W|kW|MW|mW)?$/i)
      if (!powerMatch) {
        errors.push({
          field: 'specifications.power',
          message: 'Power must be in format "100W", "1.5kW", etc.',
          code: 'INVALID_POWER_FORMAT',
          severity: 'error',
        })
      }
    }
  }

  return { errors, warnings }
}

// Filter validation
export function validateFilter(data: unknown): ValidationResult {
  const result = safeValidateComponentFilter(data)

  if (result.success) {
    return {
      isValid: true,
      errors: [],
    }
  }

  const errors: ValidationError[] = result.error.errors.map((error) => ({
    field: error.path.join('.'),
    message: error.message,
    code: error.code,
    severity: 'error',
  }))

  return {
    isValid: false,
    errors,
  }
}

// Advanced search validation
export function validateAdvancedSearch(data: unknown): ValidationResult {
  const result = safeValidateAdvancedSearch(data)

  if (result.success) {
    // Additional validation for search parameters
    const searchData = result.data
    const warnings: ValidationError[] = []

    // Check for potentially slow queries
    if (searchData.search?.query && searchData.search.query.length < 3) {
      warnings.push({
        field: 'search.query',
        message: 'Short search queries may return many results and be slow.',
        code: 'SHORT_QUERY',
        severity: 'warning',
      })
    }

    // Check for large page sizes
    if (searchData.size && searchData.size > 50) {
      warnings.push({
        field: 'size',
        message: 'Large page sizes may impact performance.',
        code: 'LARGE_PAGE_SIZE',
        severity: 'warning',
      })
    }

    return {
      isValid: true,
      errors: [],
      warnings,
    }
  }

  const errors: ValidationError[] = result.error.errors.map((error) => ({
    field: error.path.join('.'),
    message: error.message,
    code: error.code,
    severity: 'error',
  }))

  return {
    isValid: false,
    errors,
  }
}

// Form field validation
export function validateField(
  fieldName: string,
  value: any,
  schema: z.ZodSchema
): ValidationResult {
  try {
    schema.parse(value)
    return {
      isValid: true,
      errors: [],
    }
  } catch (error) {
    if (error instanceof z.ZodError) {
      const errors: ValidationError[] = error.errors.map((err) => ({
        field: fieldName,
        message: err.message,
        code: err.code,
        severity: 'error',
      }))

      return {
        isValid: false,
        errors,
      }
    }

    return {
      isValid: false,
      errors: [
        {
          field: fieldName,
          message: 'Validation failed',
          code: 'UNKNOWN_ERROR',
          severity: 'error',
        },
      ],
    }
  }
}

// Bulk validation for multiple components
export function validateBulkComponents(
  components: unknown[],
  mode: 'create' | 'update' = 'create'
): { results: ValidationResult[]; summary: { valid: number; invalid: number; warnings: number } } {
  const results = components.map((component) => validateComponent(component, mode))

  const summary = {
    valid: results.filter((r) => r.isValid).length,
    invalid: results.filter((r) => !r.isValid).length,
    warnings: results.filter((r) => r.warnings && r.warnings.length > 0).length,
  }

  return { results, summary }
}

// Validation error formatting
export function formatValidationErrors(errors: ValidationError[]): string {
  return errors.map((error) => `${error.field}: ${error.message}`).join('; ')
}

// Get validation error by field
export function getFieldError(
  errors: ValidationError[],
  field: string
): ValidationError | undefined {
  return errors.find((error) => error.field === field)
}

// Check if field has error
export function hasFieldError(errors: ValidationError[], field: string): boolean {
  return errors.some((error) => error.field === field)
}

// Get all errors for a field (including nested fields)
export function getFieldErrors(errors: ValidationError[], fieldPrefix: string): ValidationError[] {
  return errors.filter((error) => error.field.startsWith(fieldPrefix))
}

// Validation helpers for common patterns
export const validationHelpers = {
  isValidEmail: (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email)
  },

  isValidUrl: (url: string): boolean => {
    try {
      new URL(url)
      return true
    } catch {
      return false
    }
  },

  isValidPhoneNumber: (phone: string): boolean => {
    const phoneRegex = /^\+?[\d\s\-\(\)]{10,}$/
    return phoneRegex.test(phone)
  },

  isValidPostalCode: (code: string, country: string = 'US'): boolean => {
    const patterns = {
      US: /^\d{5}(-\d{4})?$/,
      CA: /^[A-Z]\d[A-Z] \d[A-Z]\d$/,
      UK: /^[A-Z]{1,2}\d[A-Z\d]? \d[A-Z]{2}$/,
      DE: /^\d{5}$/,
    }

    const pattern = patterns[country as keyof typeof patterns]
    return pattern ? pattern.test(code) : true
  },
}

// Export validation schemas for external use
export {
  ComponentCreateSchema,
  ComponentUpdateSchema,
  ComponentFilterSchema,
  ComponentAdvancedSearchSchema,
}
