/**
 * Search and Filter Zod Validation Schemas
 * Comprehensive validation for component search and filtering functionality
 */

import { z } from 'zod'

// Basic search schema
export const ComponentSearchSchema = z.object({
  query: z
    .string()
    .min(1, 'Search query cannot be empty')
    .max(200, 'Search query must not exceed 200 characters')
    .trim(),

  field: z
    .enum(['name', 'manufacturer', 'model_number', 'description', 'part_number', 'all'])
    .default('all'),

  exact_match: z.boolean().default(false),
  case_sensitive: z.boolean().default(false),
})

// Range filter schema for numeric values
export const RangeFilterSchema = z
  .object({
    min: z.number().optional(),
    max: z.number().optional(),
    unit: z.string().optional(),
  })
  .refine(
    (data) => {
      if (data.min !== undefined && data.max !== undefined) {
        return data.min <= data.max
      }
      return true
    },
    { message: 'Minimum value must be less than or equal to maximum value' }
  )

// Specification filter schema
export const SpecificationFilterSchema = z.object({
  key: z.string().min(1, 'Specification key is required'),
  value: z.union([z.string(), z.number(), z.boolean()]),
  operator: z
    .enum(['equals', 'contains', 'greater_than', 'less_than', 'between'])
    .default('equals'),
  case_sensitive: z.boolean().default(false),
})

// Advanced filter schema
export const AdvancedFilterSchema = z.object({
  field: z.string().min(1, 'Field name is required'),
  operator: z.enum([
    'equals',
    'not_equals',
    'contains',
    'not_contains',
    'starts_with',
    'ends_with',
    'greater_than',
    'greater_than_or_equal',
    'less_than',
    'less_than_or_equal',
    'between',
    'in',
    'not_in',
    'is_null',
    'is_not_null',
  ]),
  value: z.union([
    z.string(),
    z.number(),
    z.boolean(),
    z.array(z.union([z.string(), z.number()])),
    z.null(),
  ]),
  case_sensitive: z.boolean().default(false),
})

// Component filter schema
export const ComponentFilterSchema = z.object({
  // Text filters
  search_term: z.string().max(200).trim().optional(),
  manufacturer: z.string().max(100).trim().optional(),
  category: z.string().max(50).trim().optional(),
  component_type: z.string().max(50).trim().optional(),
  supplier: z.string().max(100).trim().optional(),

  // Boolean filters
  is_active: z.boolean().optional(),
  is_preferred: z.boolean().optional(),

  // Enum filters
  stock_status: z
    .enum(['available', 'limited', 'out_of_stock', 'discontinued', 'on_order'])
    .optional(),

  // Range filters
  price_range: RangeFilterSchema.optional(),
  weight_range: RangeFilterSchema.optional(),

  // Date filters
  created_after: z.string().datetime().optional(),
  created_before: z.string().datetime().optional(),
  updated_after: z.string().datetime().optional(),
  updated_before: z.string().datetime().optional(),

  // Specification filters
  specifications: z.array(SpecificationFilterSchema).optional(),

  // Advanced filters
  advanced_filters: z.array(AdvancedFilterSchema).optional(),

  // Logical operators for combining filters
  filter_logic: z.enum(['AND', 'OR']).default('AND'),
})

// Advanced search schema
export const ComponentAdvancedSearchSchema = z.object({
  // Basic search
  search: ComponentSearchSchema.optional(),

  // Filters
  filters: ComponentFilterSchema.optional(),

  // Sorting
  sort_by: z
    .enum([
      'name',
      'manufacturer',
      'model_number',
      'component_type',
      'category',
      'unit_price',
      'weight_kg',
      'created_at',
      'updated_at',
      'is_preferred',
    ])
    .default('name'),

  sort_order: z.enum(['asc', 'desc']).default('asc'),

  // Pagination
  page: z.number().positive().default(1),
  size: z.number().positive().max(100).default(20),

  // Include options
  include_inactive: z.boolean().default(false),
  include_specifications: z.boolean().default(true),
  include_metadata: z.boolean().default(false),

  // Search options
  fuzzy_search: z.boolean().default(false),
  highlight_matches: z.boolean().default(false),
})

// Search result schema
export const ComponentSearchResultSchema = z.object({
  component: z.object({
    id: z.number().positive(),
    name: z.string(),
    manufacturer: z.string(),
    model_number: z.string(),
    display_name: z.string().optional(),
    component_type: z.string(),
    category: z.string(),
    unit_price: z.number().optional(),
    currency: z.string().default('EUR'),
    is_active: z.boolean(),
    is_preferred: z.boolean(),
    description: z.string().optional(),
    specifications: z.record(z.string(), z.any()).optional(),
  }),

  // Search metadata
  score: z.number().min(0).max(1).optional(),
  highlights: z.record(z.string(), z.array(z.string())).optional(),
  matched_fields: z.array(z.string()).optional(),
})

// Advanced search response schema
export const ComponentAdvancedSearchResponseSchema = z.object({
  results: z.array(ComponentSearchResultSchema),
  total: z.number().nonnegative(),
  page: z.number().positive(),
  size: z.number().positive(),
  pages: z.number().positive(),
  has_next: z.boolean(),
  has_prev: z.boolean(),

  // Search metadata
  query_time_ms: z.number().nonnegative().optional(),
  suggestions: z.array(z.string()).optional(),
  facets: z.record(z.string(), z.record(z.string(), z.number())).optional(),

  // Applied filters summary
  applied_filters: z.object({
    search: ComponentSearchSchema.optional(),
    filters: ComponentFilterSchema.optional(),
    sort_by: z.string(),
    sort_order: z.string(),
  }),
})

// Search suggestions schema
export const SearchSuggestionsSchema = z.object({
  query: z.string(),
  suggestions: z.array(
    z.object({
      text: z.string(),
      type: z.enum(['manufacturer', 'category', 'component_type', 'model', 'general']),
      count: z.number().nonnegative().optional(),
    })
  ),
  max_suggestions: z.number().positive().default(10),
})

// Search history schema
export const SearchHistorySchema = z.object({
  id: z.string(),
  query: z.string(),
  filters: ComponentFilterSchema.optional(),
  timestamp: z.string().datetime(),
  results_count: z.number().nonnegative(),
})

// Saved search schema
export const SavedSearchSchema = z.object({
  id: z.string(),
  name: z.string().min(1, 'Search name is required').max(100),
  description: z.string().max(500).optional(),
  search_params: ComponentAdvancedSearchSchema,
  created_at: z.string().datetime(),
  updated_at: z.string().datetime(),
  is_public: z.boolean().default(false),
  tags: z.array(z.string()).optional(),
})

// Export type definitions
export type ComponentSearch = z.infer<typeof ComponentSearchSchema>
export type RangeFilter = z.infer<typeof RangeFilterSchema>
export type SpecificationFilter = z.infer<typeof SpecificationFilterSchema>
export type AdvancedFilter = z.infer<typeof AdvancedFilterSchema>
export type ComponentFilter = z.infer<typeof ComponentFilterSchema>
export type ComponentAdvancedSearch = z.infer<typeof ComponentAdvancedSearchSchema>
export type ComponentSearchResult = z.infer<typeof ComponentSearchResultSchema>
export type ComponentAdvancedSearchResponse = z.infer<typeof ComponentAdvancedSearchResponseSchema>
export type SearchSuggestions = z.infer<typeof SearchSuggestionsSchema>
export type SearchHistory = z.infer<typeof SearchHistorySchema>
export type SavedSearch = z.infer<typeof SavedSearchSchema>

// Validation helper functions
export const validateComponentSearch = (data: unknown): ComponentSearch => {
  return ComponentSearchSchema.parse(data)
}

export const validateComponentFilter = (data: unknown): ComponentFilter => {
  return ComponentFilterSchema.parse(data)
}

export const validateAdvancedSearch = (data: unknown): ComponentAdvancedSearch => {
  return ComponentAdvancedSearchSchema.parse(data)
}

// Safe validation functions
export const safeValidateComponentSearch = (data: unknown) => {
  return ComponentSearchSchema.safeParse(data)
}

export const safeValidateComponentFilter = (data: unknown) => {
  return ComponentFilterSchema.safeParse(data)
}

export const safeValidateAdvancedSearch = (data: unknown) => {
  return ComponentAdvancedSearchSchema.safeParse(data)
}

// Filter builder helpers
export const createRangeFilter = (min?: number, max?: number, unit?: string): RangeFilter => {
  return RangeFilterSchema.parse({ min, max, unit })
}

export const createSpecificationFilter = (
  key: string,
  value: string | number | boolean,
  operator: 'equals' | 'contains' | 'greater_than' | 'less_than' | 'between' = 'equals'
): SpecificationFilter => {
  return SpecificationFilterSchema.parse({ key, value, operator })
}

export const createAdvancedFilter = (
  field: string,
  operator: AdvancedFilter['operator'],
  value: AdvancedFilter['value']
): AdvancedFilter => {
  return AdvancedFilterSchema.parse({ field, operator, value })
}
