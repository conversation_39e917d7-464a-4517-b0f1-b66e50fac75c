#!/usr/bin/env python3
"""Component Type Database Model.

This module defines the ComponentType model for electrical component type management
in the Ultimate Electrical Designer application. It provides comprehensive data
storage for component types with category relationships and specifications.

Key Features:
- Integration with ComponentCategory for relational data integrity
- Professional electrical design standards compliance
- Soft delete functionality with audit trails
- Comprehensive indexing for performance optimization
- Support for component specifications and metadata
"""

import datetime
import json
from typing import TYPE_CHECKING, Any, Dict, List, Optional, Tuple

if TYPE_CHECKING:
    from src.core.models.general.component import Component
    from src.core.models.general.component_category import ComponentCategory

from sqlalchemy import ForeignKey, Index, String, Text, UniqueConstraint
from sqlalchemy.orm import Mapped, mapped_column, relationship

from src.core.models.base import Base, CommonColumns, SoftDeleteColumns
from src.core.utils.json_validation import FlexibleJSON


class ComponentType(CommonColumns, SoftDeleteColumns, Base):
    """Component type model for electrical component classification.
    
    This model stores comprehensive information about component types
    used for classifying electrical components in professional design workflows.
    It provides detailed type information and maintains relationships with
    component categories for proper organization.
    
    Attributes:
        name: Component type name (unique within category)
        description: Detailed type description
        category_id: Foreign key to ComponentCategory
        is_active: Whether type is active in the system
        specifications_template: JSON template for component specifications
        metadata: Additional metadata for the component type
        category: Related component category
        components: Components of this type

    """

    __tablename__ = "ComponentType"

    # Basic Information
    description: Mapped[Optional[str]] = mapped_column(
        Text, nullable=True,
        comment="Detailed component type description"
    )

    # Category Relationship
    category_id: Mapped[int] = mapped_column(
        ForeignKey("ComponentCategory.id"), nullable=False, index=True,
        comment="Component category ID for organization"
    )

    # Status
    is_active: Mapped[bool] = mapped_column(
        default=True, nullable=False, index=True,
        comment="Whether component type is active in the system"
    )

    # Specifications and Metadata
    specifications_template: Mapped[Optional[str]] = mapped_column(
        FlexibleJSON, nullable=True,
        comment="JSON template for component specifications"
    )

    metadata_json: Mapped[Optional[str]] = mapped_column(
        FlexibleJSON, nullable=True,
        comment="Additional metadata for component type"
    )

    # Relationships
    category: Mapped["ComponentCategory"] = relationship(
        "ComponentCategory",
        back_populates="component_types",
        lazy="select"
    )

    components: Mapped[List["Component"]] = relationship(
        "Component",
        back_populates="component_type_entity",
        cascade="all, delete-orphan",
        lazy="select"
    )

    # Indexes for performance optimization
    __table_args__ = (
        Index("idx_component_type_name_active", "name", "is_active"),
        Index("idx_component_type_category_active", "category_id", "is_active"),
        Index("idx_component_type_name_category", "name", "category_id"),
        UniqueConstraint(
            "name", "category_id", "is_deleted",
            name="uq_component_type_name_category"
        ),
    )

    def __init__(self, **kwargs):
        """Initialize ComponentType with validation.
        
        Args:
            **kwargs: Component type attributes
            
        Raises:
            ValueError: If component type data is invalid

        """
        super().__init__(**kwargs)

    def __repr__(self) -> str:
        """String representation of ComponentType."""
        return f"<ComponentType(id={self.id}, name='{self.name}', category_id={self.category_id}, active={self.is_active})>"

    def __str__(self) -> str:
        """Human-readable string representation."""
        return self.name

    @property
    def full_name(self) -> str:
        """Get the full name including category.
        
        Returns:
            str: Full name with category (e.g., "Power Distribution - Switchboard")

        """
        if self.category:
            return f"{self.category.name} - {self.name}"
        return self.name

    @property
    def category_path(self) -> str:
        """Get the full category path for this component type.
        
        Returns:
            str: Full category path (e.g., "Power Distribution > Switchboards")

        """
        if self.category:
            return self.category.full_path
        return "Unknown Category"

    @property
    def component_count(self) -> int:
        """Get the number of components of this type.
        
        Returns:
            int: Number of active components of this type

        """
        return len([c for c in self.components if c.is_active and not c.is_deleted])

    @property
    def has_specifications_template(self) -> bool:
        """Check if this type has a specifications template.

        Returns:
            bool: True if specifications template is defined

        """
        if not self.specifications_template:
            return False
        try:
            template = json.loads(self.specifications_template) if isinstance(self.specifications_template, str) else self.specifications_template
            return template is not None and len(template) > 0
        except (json.JSONDecodeError, TypeError):
            return False

    def get_specification_fields(self) -> List[str]:
        """Get the list of specification fields from the template.

        Returns:
            List[str]: List of specification field names

        """
        if not self.has_specifications_template or self.specifications_template is None:
            return []

        try:
            template = json.loads(self.specifications_template) if isinstance(self.specifications_template, str) else self.specifications_template
            if not template:
                return []
        except (json.JSONDecodeError, TypeError):
            return []

        fields: List[str] = []
        for section, section_data in template.items():
            if isinstance(section_data, dict):
                fields.extend(section_data.keys())
            else:
                fields.append(section)

        return fields

    def validate_component_specifications(self, specifications: Dict[str, Any]) -> Tuple[bool, List[str]]:
        """Validate component specifications against the template.

        Args:
            specifications: Component specifications to validate

        Returns:
            Tuple[bool, List[str]]: (is_valid, list_of_errors)

        """
        if not self.has_specifications_template or self.specifications_template is None:
            return True, []  # No template means no validation required

        try:
            template = json.loads(self.specifications_template) if isinstance(self.specifications_template, str) else self.specifications_template
            if not template:
                return True, []
        except (json.JSONDecodeError, TypeError):
            return True, []  # Invalid template, skip validation

        errors = []

        # Check required fields (this is a simplified validation)
        for section, section_data in template.items():
            if isinstance(section_data, dict):
                for field, field_config in section_data.items():
                    if isinstance(field_config, dict) and field_config.get('required', False):
                        if section not in specifications or field not in specifications[section]:
                            errors.append(f"Required field missing: {section}.{field}")

        return len(errors) == 0, errors

    def can_delete(self) -> Tuple[bool, Optional[str]]:
        """Check if this component type can be safely deleted.
        
        Returns:
            Tuple[bool, Optional[str]]: (can_delete, reason_if_not)

        """
        # Check for active components
        active_components = [c for c in self.components if c.is_active and not c.is_deleted]
        if active_components:
            return False, f"Component type has {len(active_components)} active components"
        
        return True, None

    def soft_delete(self, deleted_by_user_id: Optional[int] = None) -> bool:
        """Soft delete the component type with dependency checking.
        
        Args:
            deleted_by_user_id: ID of user performing deletion
            
        Returns:
            bool: True if deletion was successful
            
        Raises:
            ValueError: If component type cannot be deleted due to dependencies

        """
        can_delete, reason = self.can_delete()
        if not can_delete:
            raise ValueError(f"Cannot delete component type: {reason}")
        
        self.is_deleted = True
        self.deleted_at = datetime.datetime.utcnow()
        self.deleted_by_user_id = deleted_by_user_id
        self.is_active = False
        
        return True

    def get_related_types(self, limit: int = 10) -> List["ComponentType"]:
        """Get related component types in the same category.
        
        Args:
            limit: Maximum number of related types to return
            
        Returns:
            List[ComponentType]: Related component types

        """
        if not self.category:
            return []
        
        related_types = [
            ct for ct in self.category.component_types 
            if ct.id != self.id and ct.is_active and not ct.is_deleted
        ]
        
        return related_types[:limit]

    def update_specifications_template(self, template: Dict[str, Any]) -> bool:
        """Update the specifications template for this component type.

        Args:
            template: New specifications template

        Returns:
            bool: True if update was successful

        """
        # Basic validation of template structure
        if not isinstance(template, dict):
            raise ValueError("Specifications template must be a dictionary")

        self.specifications_template = json.dumps(template)
        self.updated_at = datetime.datetime.utcnow()

        return True
