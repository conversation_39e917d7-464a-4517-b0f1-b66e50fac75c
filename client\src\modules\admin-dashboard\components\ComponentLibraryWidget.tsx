/**
 * Component library widget for admin dashboard
 */

'use client'

import React from 'react'
import { ComponentLibraryWidgetProps } from '../types'

export function ComponentLibraryWidget({
  stats,
  isLoading,
  onViewDetails,
  onManageComponents,
  className = '',
}: ComponentLibraryWidgetProps) {
  if (isLoading) {
    return (
      <div className={`overflow-hidden rounded-lg bg-white shadow ${className}`}>
        <div className="p-6">
          <div className="animate-pulse">
            <div className="mb-4 h-6 w-1/2 rounded bg-gray-200"></div>
            <div className="space-y-4">
              {Array.from({ length: 3 }).map((_, i) => (
                <div key={i} className="flex items-center justify-between">
                  <div className="h-4 w-1/3 rounded bg-gray-200"></div>
                  <div className="h-4 w-1/4 rounded bg-gray-200"></div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    )
  }

  if (!stats) {
    return (
      <div className={`overflow-hidden rounded-lg bg-white shadow ${className}`}>
        <div className="p-6">
          <div className="text-center">
            <svg
              className="mx-auto h-12 w-12 text-gray-400"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"
              />
            </svg>
            <h3 className="mt-2 text-sm font-medium text-gray-900">No component data available</h3>
            <p className="mt-1 text-sm text-gray-500">
              Component library statistics could not be loaded.
            </p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className={`overflow-hidden rounded-lg bg-white shadow ${className}`}>
      <div className="p-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-medium text-gray-900">Component Library</h3>
          <div className="flex items-center space-x-2">
            {onViewDetails && (
              <button onClick={onViewDetails} className="text-sm text-blue-600 hover:text-blue-500">
                View Details
              </button>
            )}
            {onManageComponents && (
              <button
                onClick={onManageComponents}
                className="inline-flex items-center rounded-md bg-blue-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-blue-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-blue-600"
              >
                <svg className="mr-1 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                  />
                </svg>
                Manage
              </button>
            )}
          </div>
        </div>

        {/* Overview Statistics */}
        <div className="mt-6 grid grid-cols-2 gap-4">
          <div className="rounded-lg bg-blue-50 p-4">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <svg
                  className="h-6 w-6 text-blue-400"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"
                  />
                </svg>
              </div>
              <div className="ml-3 w-0 flex-1">
                <dl>
                  <dt className="truncate text-sm font-medium text-gray-500">Total Components</dt>
                  <dd className="text-lg font-medium text-gray-900">
                    {stats.totalComponents.toLocaleString()}
                  </dd>
                </dl>
              </div>
            </div>
          </div>

          <div className="rounded-lg bg-green-50 p-4">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <svg
                  className="h-6 w-6 text-green-400"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                </svg>
              </div>
              <div className="ml-3 w-0 flex-1">
                <dl>
                  <dt className="truncate text-sm font-medium text-gray-500">Active Components</dt>
                  <dd className="text-lg font-medium text-gray-900">
                    {stats.activeComponents.toLocaleString()}
                  </dd>
                </dl>
              </div>
            </div>
          </div>

          <div className="rounded-lg bg-purple-50 p-4">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <svg
                  className="h-6 w-6 text-purple-400"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"
                  />
                </svg>
              </div>
              <div className="ml-3 w-0 flex-1">
                <dl>
                  <dt className="truncate text-sm font-medium text-gray-500">Categories</dt>
                  <dd className="text-lg font-medium text-gray-900">{stats.categoriesCount}</dd>
                </dl>
              </div>
            </div>
          </div>

          <div className="rounded-lg bg-yellow-50 p-4">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <svg
                  className="h-6 w-6 text-yellow-400"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 4v16m8-8H4"
                  />
                </svg>
              </div>
              <div className="ml-3 w-0 flex-1">
                <dl>
                  <dt className="truncate text-sm font-medium text-gray-500">Recently Added</dt>
                  <dd className="text-lg font-medium text-gray-900">{stats.recentlyAdded}</dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        {/* Components by Category */}
        <div className="mt-6">
          <h4 className="mb-3 text-sm font-medium text-gray-900">Components by Category</h4>
          <div className="space-y-2">
            {stats.componentsByCategory.map((category) => (
              <div key={category.category} className="flex items-center justify-between">
                <span className="text-sm capitalize text-gray-600">
                  {category.category.replace('_', ' ')}
                </span>
                <span className="inline-flex items-center rounded-full bg-gray-100 px-2.5 py-0.5 text-xs font-medium text-gray-800">
                  {category.count}
                </span>
              </div>
            ))}
          </div>
        </div>

        {/* Most Used Components */}
        <div className="mt-6">
          <h4 className="mb-3 text-sm font-medium text-gray-900">Most Used Components</h4>
          <div className="space-y-3">
            {stats.mostUsedComponents.slice(0, 5).map((component) => (
              <div key={component.id} className="flex items-center justify-between">
                <div className="min-w-0 flex-1">
                  <p className="truncate text-sm font-medium text-gray-900">{component.name}</p>
                  <p className="text-xs capitalize text-gray-500">
                    {component.category.replace('_', ' ')}
                  </p>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="text-sm text-gray-600">{component.usageCount} uses</span>
                  <div className="h-2 w-16 rounded-full bg-gray-200">
                    <div
                      className="h-2 rounded-full bg-blue-600"
                      style={{
                        width: `${Math.min(100, (component.usageCount / Math.max(...stats.mostUsedComponents.map((c) => c.usageCount))) * 100)}%`,
                      }}
                    ></div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Quick Actions */}
        <div className="mt-6 flex flex-wrap gap-2">
          <button className="inline-flex items-center rounded-md bg-gray-100 px-3 py-2 text-sm font-medium text-gray-700 hover:bg-gray-200">
            <svg className="mr-1 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 6v6m0 0v6m0-6h6m-6 0H6"
              />
            </svg>
            Add Component
          </button>
          <button className="inline-flex items-center rounded-md bg-gray-100 px-3 py-2 text-sm font-medium text-gray-700 hover:bg-gray-200">
            <svg className="mr-1 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"
              />
            </svg>
            Import Components
          </button>
          <button className="inline-flex items-center rounded-md bg-gray-100 px-3 py-2 text-sm font-medium text-gray-700 hover:bg-gray-200">
            <svg className="mr-1 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 7h6m0 10v-3m-3 3l3-3m-3 3H9m3 0v3"
              />
            </svg>
            Export Library
          </button>
        </div>
      </div>
    </div>
  )
}
