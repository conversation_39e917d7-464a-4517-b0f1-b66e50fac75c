/**
 * @vitest-environment jsdom
 */

import { renderHook, waitFor } from '@testing-library/react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { vi, describe, it, expect, beforeEach } from 'vitest'
import { useLandingPageData, useLandingPageSection } from '../../hooks/useLandingPageData'
import { defaultLandingPageData } from '../../utils'

const createTestQueryClient = () =>
  new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
      },
    },
  })

const createWrapper = () => {
  const queryClient = createTestQueryClient()
  return ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
  )
}

describe('useLandingPageData', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('returns default landing page data', async () => {
    const wrapper = createWrapper()
    const { result } = renderHook(() => useLandingPageData(), { wrapper })

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false)
    })

    expect(result.current.data).toEqual(defaultLandingPageData)
    expect(result.current.error).toBeNull()
  })

  it('has correct query key', async () => {
    const wrapper = createWrapper()
    const { result } = renderHook(() => useLandingPageData(), { wrapper })

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false)
    })

    // The query should be cached with the correct key
    expect(result.current.data).toBeDefined()
  })

  it('uses correct stale time and gc time', async () => {
    const wrapper = createWrapper()
    const { result } = renderHook(() => useLandingPageData(), { wrapper })

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false)
    })

    // Data should be available immediately on second call due to stale time
    const { result: result2 } = renderHook(() => useLandingPageData(), { wrapper })
    expect(result2.current.isLoading).toBe(false)
    expect(result2.current.data).toEqual(defaultLandingPageData)
  })
})

describe('useLandingPageSection', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('returns hero section data', async () => {
    const wrapper = createWrapper()
    const { result } = renderHook(() => useLandingPageSection('hero'), { wrapper })

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false)
    })

    expect(result.current.data).toEqual(defaultLandingPageData.hero)
    expect(result.current.error).toBeNull()
  })

  it('returns features section data', async () => {
    const wrapper = createWrapper()
    const { result } = renderHook(() => useLandingPageSection('features'), { wrapper })

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false)
    })

    expect(result.current.data).toEqual(defaultLandingPageData.features)
    expect(result.current.error).toBeNull()
  })

  it('returns trust indicators section data', async () => {
    const wrapper = createWrapper()
    const { result } = renderHook(() => useLandingPageSection('trustIndicators'), { wrapper })

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false)
    })

    expect(result.current.data).toEqual(defaultLandingPageData.trustIndicators)
    expect(result.current.error).toBeNull()
  })

  it('returns cta section data', async () => {
    const wrapper = createWrapper()
    const { result } = renderHook(() => useLandingPageSection('cta'), { wrapper })

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false)
    })

    expect(result.current.data).toEqual(defaultLandingPageData.cta)
    expect(result.current.error).toBeNull()
  })

  it('handles loading state correctly', () => {
    const wrapper = createWrapper()
    const { result } = renderHook(() => useLandingPageSection('hero'), { wrapper })

    // Initially should be loading
    expect(result.current.isLoading).toBe(true)
    expect(result.current.data).toBeUndefined()
  })

  it('shares data with main hook', async () => {
    const wrapper = createWrapper()

    // First call the main hook
    const { result: mainResult } = renderHook(() => useLandingPageData(), { wrapper })
    await waitFor(() => {
      expect(mainResult.current.isLoading).toBe(false)
    })

    // Then call the section hook - should use cached data
    const { result: sectionResult } = renderHook(() => useLandingPageSection('hero'), { wrapper })

    expect(sectionResult.current.isLoading).toBe(false)
    expect(sectionResult.current.data).toEqual(defaultLandingPageData.hero)
  })
})
