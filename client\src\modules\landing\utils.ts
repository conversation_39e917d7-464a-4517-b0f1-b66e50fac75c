/**
 * Utility functions for the landing page domain
 */

import type { FeatureItem, TrustIndicator, LandingPageData } from './types'

/**
 * Default landing page data
 */
export const defaultLandingPageData: LandingPageData = {
  hero: {
    badge: {
      icon: 'check-circle',
      text: 'Engineering Grade Software',
    },
    title: 'Ultimate Electrical Designer',
    subtitle: 'Professional electrical engineering software for industrial applications',
    description:
      'Design heat tracing systems, perform load calculations, and manage projects with industry-leading precision and compliance.',
    backgroundPattern: true,
    floatingElements: true,
  },
  features: [
    {
      id: 'heat-tracing',
      title: 'Heat Tracing Design',
      description:
        'Advanced heat tracing calculations with support for multiple pipe configurations, environmental conditions, and safety factors.',
      icon: 'zap',
      color: 'primary',
    },
    {
      id: 'load-calculations',
      title: 'Load Calculations',
      description:
        'Comprehensive electrical load analysis with automatic sizing recommendations, safety factors, and code compliance verification.',
      icon: 'calculator',
      color: 'secondary',
    },
    {
      id: 'project-management',
      title: 'Project Management',
      description:
        'Organize and track multiple projects with detailed documentation, progress monitoring, and team collaboration tools.',
      icon: 'folder',
      color: 'accent',
    },
    {
      id: 'cable-sizing',
      title: 'Cable Sizing',
      description:
        'Automated cable sizing calculations based on current capacity, voltage drop, and installation methods with NEC compliance.',
      icon: 'cable',
      color: 'dark',
    },
    {
      id: 'safety-compliance',
      title: 'Safety Compliance',
      description:
        'Built-in safety checks and compliance verification for electrical codes, standards, and regulatory requirements.',
      icon: 'shield-check',
      color: 'accent',
    },
    {
      id: 'documentation',
      title: 'Documentation',
      description:
        'Generate professional reports and documentation with calculations, diagrams, specifications, and compliance certificates.',
      icon: 'file-text',
      color: 'secondary',
    },
  ],
  trustIndicators: [
    {
      id: 'security',
      icon: 'lock',
      label: 'Enterprise Security',
      value: 'SOC 2 Compliant',
    },
    {
      id: 'compliance',
      icon: 'check-circle',
      label: 'Code Compliant',
      value: 'NEC & IEC Standards',
    },
    {
      id: 'industry',
      icon: 'zap',
      label: 'Industry Standard',
      value: 'IEEE Certified',
    },
    {
      id: 'users',
      icon: 'users',
      label: '1000+ Engineers',
      value: 'Trusted Worldwide',
    },
    {
      id: 'companies',
      icon: 'building',
      label: 'Fortune 500 Companies',
      value: 'Enterprise Ready',
    },
    {
      id: 'uptime',
      icon: 'check-circle',
      label: '99.9% Uptime',
      value: 'Reliable Service',
    },
  ],
  cta: {
    title: 'Ready to Transform Your Electrical Design Process?',
    subtitle: 'Start Your Engineering Journey',
    description:
      'Join thousands of professional engineers who trust Ultimate Electrical Designer for their critical industrial projects and compliance requirements.',
    primaryAction: {
      label: 'Get Started Today',
      href: '/login',
    },
    secondaryAction: {
      label: 'Contact Sales',
      href: '/contact',
    },
  },
}

/**
 * Get feature by ID
 */
export function getFeatureById(features: FeatureItem[], id: string): FeatureItem | undefined {
  return features.find((feature) => feature.id === id)
}

/**
 * Get features by color
 */
export function getFeaturesByColor(
  features: FeatureItem[],
  color: FeatureItem['color']
): FeatureItem[] {
  return features.filter((feature) => feature.color === color)
}

/**
 * Get trust indicator by ID
 */
export function getTrustIndicatorById(
  indicators: TrustIndicator[],
  id: string
): TrustIndicator | undefined {
  return indicators.find((indicator) => indicator.id === id)
}

/**
 * Format feature description for accessibility
 */
export function formatFeatureDescription(feature: FeatureItem): string {
  return `${feature.title}: ${feature.description}`
}

/**
 * Generate feature card aria-label
 */
export function generateFeatureAriaLabel(feature: FeatureItem): string {
  return `Learn more about ${feature.title} feature`
}

/**
 * Get icon component name from string
 */
export function getIconComponent(iconName: string): string {
  // Convert kebab-case to PascalCase for icon component names
  return iconName
    .split('-')
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join('')
}

/**
 * Get brand color class for feature
 */
export function getBrandColorClass(
  color: FeatureItem['color'],
  type: 'bg' | 'text' | 'border' = 'bg'
): string {
  const colorMap = {
    primary: 'brand-primary',
    secondary: 'brand-secondary',
    accent: 'brand-accent',
    dark: 'brand-dark',
  }

  return `${type}-${colorMap[color]}`
}
