/**
 * E2E tests for admin dashboard functionality
 */

import { test, expect } from '@playwright/test'

// Test data
const adminUser = {
  email: '<EMAIL>',
  password: 'admin123',
  name: 'Admin User',
}

const regularUser = {
  email: '<EMAIL>',
  password: 'user123',
  name: 'Regular User',
}

test.describe('Admin Dashboard', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the application
    await page.goto('/')
  })

  test.describe('Access Control', () => {
    test('should deny access to non-admin users', async ({ page }) => {
      // Login as regular user
      await page.click('[data-testid="login-button"]')
      await page.fill('[data-testid="email-input"]', regularUser.email)
      await page.fill('[data-testid="password-input"]', regularUser.password)
      await page.click('[data-testid="submit-login"]')

      // Try to access admin dashboard
      await page.goto('/admin')

      // Should see access denied message
      await expect(page.locator('text=Access Denied')).toBeVisible()
      await expect(
        page.locator("text=You don't have permission to access the admin dashboard")
      ).toBeVisible()
    })

    test('should allow access to admin users', async ({ page }) => {
      // Login as admin user
      await page.click('[data-testid="login-button"]')
      await page.fill('[data-testid="email-input"]', adminUser.email)
      await page.fill('[data-testid="password-input"]', adminUser.password)
      await page.click('[data-testid="submit-login"]')

      // Navigate to admin dashboard
      await page.goto('/admin')

      // Should see admin dashboard
      await expect(page.locator('h1:has-text("Admin Dashboard")')).toBeVisible()
      await expect(page.locator(`text=Welcome back, ${adminUser.name}`)).toBeVisible()
    })
  })

  test.describe('Dashboard Overview', () => {
    test.beforeEach(async ({ page }) => {
      // Login as admin
      await page.click('[data-testid="login-button"]')
      await page.fill('[data-testid="email-input"]', adminUser.email)
      await page.fill('[data-testid="password-input"]', adminUser.password)
      await page.click('[data-testid="submit-login"]')
      await page.goto('/admin')
    })

    test('should display all core widgets', async ({ page }) => {
      // Check for core widgets
      await expect(page.locator('text=System Metrics')).toBeVisible()
      await expect(page.locator('text=User Management')).toBeVisible()
      await expect(page.locator('text=Component Library')).toBeVisible()
      await expect(page.locator('text=Project Oversight')).toBeVisible()
    })

    test('should display advanced widgets', async ({ page }) => {
      // Wait for lazy-loaded widgets
      await expect(page.locator('text=Audit Logs')).toBeVisible({ timeout: 10000 })
      await expect(page.locator('text=Security Monitoring')).toBeVisible()
      await expect(page.locator('text=System Configuration')).toBeVisible()
      await expect(page.locator('text=Quick Admin Actions')).toBeVisible()
    })

    test('should show loading states initially', async ({ page }) => {
      // Reload page to see loading states
      await page.reload()

      // Check for loading indicators
      const loadingElements = page.locator('.animate-pulse')
      await expect(loadingElements.first()).toBeVisible()
    })

    test('should update last updated timestamp', async ({ page }) => {
      // Check for last updated timestamp
      await expect(page.locator('text=Last updated:')).toBeVisible()
    })
  })

  test.describe('System Metrics Widget', () => {
    test.beforeEach(async ({ page }) => {
      await page.click('[data-testid="login-button"]')
      await page.fill('[data-testid="email-input"]', adminUser.email)
      await page.fill('[data-testid="password-input"]', adminUser.password)
      await page.click('[data-testid="submit-login"]')
      await page.goto('/admin')
    })

    test('should display system metrics', async ({ page }) => {
      // Check for system metrics
      await expect(page.locator('text=System Uptime')).toBeVisible()
      await expect(page.locator('text=Server Load')).toBeVisible()
      await expect(page.locator('text=Memory Usage')).toBeVisible()
      await expect(page.locator('text=Disk Usage')).toBeVisible()
    })

    test('should show health status indicator', async ({ page }) => {
      // Check for health status
      const healthStatus = page
        .locator('[data-testid="health-status"]')
        .or(
          page
            .locator('text=healthy')
            .or(page.locator('text=warning').or(page.locator('text=critical')))
        )
      await expect(healthStatus).toBeVisible()
    })

    test('should allow manual refresh', async ({ page }) => {
      // Find and click refresh button
      const refreshButton = page.locator('[aria-label="Refresh metrics"]')
      if (await refreshButton.isVisible()) {
        await refreshButton.click()

        // Should show refreshing indicator briefly
        await expect(page.locator('text=Refreshing...')).toBeVisible({ timeout: 1000 })
      }
    })
  })

  test.describe('User Management Widget', () => {
    test.beforeEach(async ({ page }) => {
      await page.click('[data-testid="login-button"]')
      await page.fill('[data-testid="email-input"]', adminUser.email)
      await page.fill('[data-testid="password-input"]', adminUser.password)
      await page.click('[data-testid="submit-login"]')
      await page.goto('/admin')
    })

    test('should display user statistics', async ({ page }) => {
      await expect(page.locator('text=Total Users')).toBeVisible()
      await expect(page.locator('text=Active')).toBeVisible()
      await expect(page.locator('text=Inactive')).toBeVisible()
    })

    test('should display role distribution', async ({ page }) => {
      await expect(page.locator('text=Role Distribution')).toBeVisible()
      await expect(page.locator('text=Administrators')).toBeVisible()
      await expect(page.locator('text=Editors')).toBeVisible()
      await expect(page.locator('text=Viewers')).toBeVisible()
    })

    test('should allow user selection', async ({ page }) => {
      // Look for user list items
      const userItems = page
        .locator('[data-testid="user-item"]')
        .or(page.locator('input[type="checkbox"]').first())

      if (await userItems.first().isVisible()) {
        await userItems.first().click()

        // Should show bulk actions
        await expect(page.locator('text=Bulk Actions')).toBeVisible()
      }
    })

    test('should show add user button', async ({ page }) => {
      const addUserButton = page
        .locator('text=Add User')
        .or(page.locator('[data-testid="add-user-button"]'))

      if (await addUserButton.isVisible()) {
        await expect(addUserButton).toBeVisible()
      }
    })
  })

  test.describe('Security Monitoring Widget', () => {
    test.beforeEach(async ({ page }) => {
      await page.click('[data-testid="login-button"]')
      await page.fill('[data-testid="email-input"]', adminUser.email)
      await page.fill('[data-testid="password-input"]', adminUser.password)
      await page.click('[data-testid="submit-login"]')
      await page.goto('/admin')
    })

    test('should display security status', async ({ page }) => {
      // Wait for security widget to load
      await expect(page.locator('text=Security Monitoring')).toBeVisible({ timeout: 10000 })

      // Check for security status indicators
      const securityStatus = page
        .locator('text=security alerts')
        .or(
          page
            .locator('text=All security alerts resolved')
            .or(page.locator('text=Critical security issues detected'))
        )
      await expect(securityStatus).toBeVisible()
    })

    test('should display severity breakdown', async ({ page }) => {
      await expect(page.locator('text=Security Monitoring')).toBeVisible({ timeout: 10000 })
      await expect(page.locator('text=Severity Breakdown')).toBeVisible()
      await expect(page.locator('text=Critical')).toBeVisible()
      await expect(page.locator('text=High')).toBeVisible()
      await expect(page.locator('text=Medium')).toBeVisible()
      await expect(page.locator('text=Low')).toBeVisible()
    })

    test('should allow filtering alerts', async ({ page }) => {
      await expect(page.locator('text=Security Monitoring')).toBeVisible({ timeout: 10000 })

      // Look for filter tabs
      const filterTabs = page
        .locator('text=Unresolved')
        .or(page.locator('text=Critical').or(page.locator('text=All')))

      if (await filterTabs.first().isVisible()) {
        await filterTabs.first().click()
      }
    })
  })

  test.describe('Responsive Design', () => {
    test.beforeEach(async ({ page }) => {
      await page.click('[data-testid="login-button"]')
      await page.fill('[data-testid="email-input"]', adminUser.email)
      await page.fill('[data-testid="password-input"]', adminUser.password)
      await page.click('[data-testid="submit-login"]')
      await page.goto('/admin')
    })

    test('should work on mobile devices', async ({ page }) => {
      // Set mobile viewport
      await page.setViewportSize({ width: 375, height: 667 })

      // Should still show main dashboard elements
      await expect(page.locator('h1:has-text("Admin Dashboard")')).toBeVisible()
      await expect(page.locator('text=System Metrics')).toBeVisible()
    })

    test('should work on tablet devices', async ({ page }) => {
      // Set tablet viewport
      await page.setViewportSize({ width: 768, height: 1024 })

      // Should show dashboard in tablet layout
      await expect(page.locator('h1:has-text("Admin Dashboard")')).toBeVisible()
      await expect(page.locator('text=User Management')).toBeVisible()
    })

    test('should work on desktop', async ({ page }) => {
      // Set desktop viewport
      await page.setViewportSize({ width: 1920, height: 1080 })

      // Should show full desktop layout
      await expect(page.locator('h1:has-text("Admin Dashboard")')).toBeVisible()
      await expect(page.locator('text=Component Library')).toBeVisible()
    })
  })

  test.describe('Error Handling', () => {
    test('should handle network errors gracefully', async ({ page }) => {
      // Login first
      await page.click('[data-testid="login-button"]')
      await page.fill('[data-testid="email-input"]', adminUser.email)
      await page.fill('[data-testid="password-input"]', adminUser.password)
      await page.click('[data-testid="submit-login"]')

      // Simulate network failure
      await page.route('**/api/v1/admin/**', (route) => route.abort())

      // Navigate to admin dashboard
      await page.goto('/admin')

      // Should show error state or fallback content
      const errorIndicators = page
        .locator('text=Error')
        .or(page.locator('text=Failed to load').or(page.locator('text=Unable to load')))

      // At least one error indicator should be visible
      await expect(errorIndicators.first()).toBeVisible({ timeout: 10000 })
    })

    test('should handle widget errors independently', async ({ page }) => {
      await page.click('[data-testid="login-button"]')
      await page.fill('[data-testid="email-input"]', adminUser.email)
      await page.fill('[data-testid="password-input"]', adminUser.password)
      await page.click('[data-testid="submit-login"]')

      // Simulate error for specific widget
      await page.route('**/api/v1/admin/metrics', (route) => route.abort())

      await page.goto('/admin')

      // Main dashboard should still be visible
      await expect(page.locator('h1:has-text("Admin Dashboard")')).toBeVisible()

      // Other widgets should still work
      await expect(page.locator('text=User Management')).toBeVisible()
    })
  })

  test.describe('Accessibility', () => {
    test.beforeEach(async ({ page }) => {
      await page.click('[data-testid="login-button"]')
      await page.fill('[data-testid="email-input"]', adminUser.email)
      await page.fill('[data-testid="password-input"]', adminUser.password)
      await page.click('[data-testid="submit-login"]')
      await page.goto('/admin')
    })

    test('should have proper heading structure', async ({ page }) => {
      // Check for main heading
      await expect(page.locator('h1:has-text("Admin Dashboard")')).toBeVisible()

      // Check for widget headings
      await expect(page.locator('h3:has-text("System Metrics")')).toBeVisible()
      await expect(page.locator('h3:has-text("User Management")')).toBeVisible()
    })

    test('should be keyboard navigable', async ({ page }) => {
      // Focus on the page
      await page.keyboard.press('Tab')

      // Should be able to navigate through interactive elements
      const focusedElement = page.locator(':focus')
      await expect(focusedElement).toBeVisible()
    })

    test('should have proper ARIA labels', async ({ page }) => {
      // Check for main landmark
      const mainLandmark = page.locator('[role="main"]').or(page.locator('main'))
      await expect(mainLandmark).toBeVisible()
    })
  })
})
