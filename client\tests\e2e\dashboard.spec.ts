import { expect, test } from '@playwright/test'

test.describe('Dashboard Page', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to dashboard page
    await page.goto('/dashboard')
  })

  test('should display dashboard correctly for authenticated users', async ({ page }) => {
    // Check page title
    await expect(page).toHaveTitle(/Ultimate Electrical Designer/)

    // Check welcome message
    await expect(page.getByText(/Welcome back/)).toBeVisible()

    // Check main dashboard sections
    await expect(page.getByText('Total Projects')).toBeVisible()
    await expect(page.getByText('Active Projects')).toBeVisible()
    await expect(page.getByText('Completed Calculations')).toBeVisible()
    await expect(page.getByText('Recent Activity')).toBeVisible()
  })

  test('should display metrics widget with correct data', async ({ page }) => {
    // Check metrics cards are visible
    const metricsCards = page.locator('[role="term"]')
    await expect(metricsCards).toHaveCount(4)

    // Check that metric values are displayed
    await expect(page.locator('[role="definition"]')).toHaveCount(4)

    // Check for proper icons in metrics
    const metricIcons = page.locator('svg').filter({ hasText: '' })
    await expect(metricIcons.first()).toBeVisible()
  })

  test('should display quick actions widget', async ({ page }) => {
    // Check quick actions section
    await expect(page.getByText('Quick Actions')).toBeVisible()
    await expect(page.getByText('Start your electrical design work')).toBeVisible()

    // Check for specific quick actions
    await expect(page.getByText('Heat Tracing Design')).toBeVisible()
    await expect(page.getByText('Load Calculations')).toBeVisible()
    await expect(page.getByText('Cable Sizing')).toBeVisible()
    await expect(page.getByText('New Project')).toBeVisible()
  })

  test('should navigate to heat tracing when quick action is clicked', async ({ page }) => {
    // Click on Heat Tracing quick action
    await page.getByText('Heat Tracing Design').click()

    // Should navigate to heat tracing page
    await expect(page).toHaveURL(/\/heat-tracing/)
  })

  test('should navigate to load calculations when quick action is clicked', async ({ page }) => {
    // Click on Load Calculations quick action
    await page.getByText('Load Calculations').click()

    // Should navigate to load calculations page
    await expect(page).toHaveURL(/\/load-calculations/)
  })

  test('should display projects widget', async ({ page }) => {
    // Check projects section
    await expect(page.getByText('Recent Projects')).toBeVisible()

    // Check for new project button
    await expect(page.getByRole('button', { name: /New Project/ })).toBeVisible()
  })

  test('should display recent calculations widget', async ({ page }) => {
    // Check recent calculations section
    await expect(page.getByText('Recent Calculations')).toBeVisible()
  })

  test('should create new project when button is clicked', async ({ page }) => {
    // Click new project button
    await page.getByRole('button', { name: /New Project/ }).click()

    // Should navigate to new project page
    await expect(page).toHaveURL(/\/projects\/new/)
  })

  test('should display system status', async ({ page }) => {
    // Check system status section
    await expect(page.getByText('System Status')).toBeVisible()
    await expect(page.getByText('All systems operational')).toBeVisible()
    await expect(page.getByText('Online')).toBeVisible()
  })

  test('should be responsive on mobile devices', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 })

    // Check that dashboard is still functional on mobile
    await expect(page.getByText(/Welcome back/)).toBeVisible()
    await expect(page.getByText('Quick Actions')).toBeVisible()

    // Check that metrics are displayed in mobile layout
    await expect(page.getByText('Total Projects')).toBeVisible()
  })

  test('should be responsive on tablet devices', async ({ page }) => {
    // Set tablet viewport
    await page.setViewportSize({ width: 768, height: 1024 })

    // Check that dashboard layout adapts to tablet
    await expect(page.getByText(/Welcome back/)).toBeVisible()
    await expect(page.getByText('Quick Actions')).toBeVisible()

    // Check grid layout on tablet
    const quickActionsGrid = page.locator('.grid').filter({ hasText: 'Heat Tracing Design' })
    await expect(quickActionsGrid).toBeVisible()
  })

  test('should handle loading states gracefully', async ({ page }) => {
    // Intercept API calls to simulate loading
    await page.route('**/api/dashboard/**', async (route) => {
      // Delay response to test loading state
      await new Promise((resolve) => setTimeout(resolve, 1000))
      await route.continue()
    })

    await page.goto('/dashboard')

    // Check for loading indicators (skeleton screens)
    const loadingElements = page.locator('.animate-pulse')
    await expect(loadingElements.first()).toBeVisible()
  })

  test('should have proper accessibility features', async ({ page }) => {
    // Check for proper heading hierarchy
    const h1 = page.getByRole('heading', { level: 1 })
    await expect(h1).toBeVisible()

    // Check for proper landmarks
    const main = page.getByRole('main')
    await expect(main).toBeVisible()

    // Check for proper button accessibility
    const buttons = page.getByRole('button')
    await expect(buttons.first()).toBeVisible()

    // Check for proper link accessibility
    const links = page.getByRole('link')
    await expect(links.first()).toBeVisible()
  })

  test('should support keyboard navigation', async ({ page }) => {
    // Test tab navigation through quick actions
    await page.keyboard.press('Tab')
    await page.keyboard.press('Tab')
    await page.keyboard.press('Tab')

    // Check that focus is visible
    const focusedElement = page.locator(':focus')
    await expect(focusedElement).toBeVisible()

    // Test Enter key activation
    await page.keyboard.press('Enter')

    // Should navigate or trigger action
    await page.waitForTimeout(500)
  })

  test('should display user profile section', async ({ page }) => {
    // Scroll to bottom to see user profile
    await page.evaluate(() => window.scrollTo(0, document.body.scrollHeight))

    // Check for user profile section (from existing UserProfile component)
    await expect(page.locator('text=Profile').or(page.locator('text=Account'))).toBeVisible()
  })

  test('should handle error states gracefully', async ({ page }) => {
    // Intercept API calls to simulate error
    await page.route('**/api/dashboard/**', async (route) => {
      await route.abort('failed')
    })

    await page.goto('/dashboard')

    // Should still display basic layout even with API errors
    await expect(page.getByText(/Welcome back/)).toBeVisible()
  })

  test('should refresh data when refresh is triggered', async ({ page }) => {
    // Wait for initial load
    await page.waitForLoadState('networkidle')

    // Trigger refresh (this would depend on implementation)
    await page.keyboard.press('F5')

    // Check that page reloads properly
    await expect(page.getByText(/Welcome back/)).toBeVisible()
  })

  test('should maintain state across page refreshes', async ({ page }) => {
    // Interact with dashboard (e.g., change filter if implemented)
    await page.waitForLoadState('networkidle')

    // Refresh page
    await page.reload()

    // Check that dashboard loads correctly after refresh
    await expect(page.getByText(/Welcome back/)).toBeVisible()
    await expect(page.getByText('Quick Actions')).toBeVisible()
  })

  test('should display correct time information', async ({ page }) => {
    // Check for last updated time
    await expect(page.getByText(/Last updated:/)).toBeVisible()

    // Check for relative time displays
    await expect(page.locator('text=/ago/')).toBeVisible()
  })

  test('should handle project interactions', async ({ page }) => {
    // Look for project cards (if any are displayed)
    const projectCards = page
      .locator('[data-testid="project-card"]')
      .or(page.locator('text=/Project/').locator('..').locator('..'))

    // If projects are displayed, test interaction
    const projectCount = await projectCards.count()
    if (projectCount > 0) {
      await projectCards.first().click()
      // Should navigate to project details
      await page.waitForTimeout(500)
    }
  })

  test('should display calculation results when available', async ({ page }) => {
    // Look for calculation results in recent calculations widget
    const calculationsSection = page.locator('text=Recent Calculations').locator('..')
    await expect(calculationsSection).toBeVisible()

    // Check for calculation items (if any)
    const calculationItems = calculationsSection
      .locator('[data-testid="calculation-item"]')
      .or(calculationsSection.locator('text=/Result:/'))

    // If calculations are displayed, verify they show results
    const calcCount = await calculationItems.count()
    if (calcCount > 0) {
      await expect(calculationItems.first()).toBeVisible()
    }
  })
})
