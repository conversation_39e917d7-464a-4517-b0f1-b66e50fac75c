'use client'

import { But<PERSON> } from '@/components/ui/button'
import { cn } from '@/lib/utils'
import { memo } from 'react'
import type { ProjectsWidgetProps } from '../types'
import {
  formatProjectStatus,
  getProjectStatusColor,
  formatProjectPriority,
  getProjectPriorityColor,
  formatRelativeTime,
  calculateProjectProgress,
} from '../utils'

/**
 * Dashboard projects widget component
 */
export const ProjectsWidget = memo(function ProjectsWidget({
  projects,
  isLoading = false,
  onProjectClick,
  onCreateProject,
  className,
}: ProjectsWidgetProps) {
  if (isLoading) {
    return (
      <div className={cn('rounded-lg border border-gray-200 bg-white shadow-sm', className)}>
        <div className="border-b border-gray-200 px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="h-6 w-32 animate-pulse rounded bg-gray-200"></div>
            <div className="h-8 w-24 animate-pulse rounded bg-gray-200"></div>
          </div>
        </div>
        <div className="p-6">
          <div className="space-y-4">
            {Array.from({ length: 3 }).map((_, index) => (
              <div key={index} className="animate-pulse">
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="mb-2 h-4 w-3/4 rounded bg-gray-200"></div>
                    <div className="h-3 w-1/2 rounded bg-gray-200"></div>
                  </div>
                  <div className="ml-4">
                    <div className="h-6 w-16 rounded bg-gray-200"></div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className={cn('rounded-lg border border-gray-200 bg-white shadow-sm', className)}>
      {/* Header */}
      <div className="border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-medium text-gray-900">Recent Projects</h3>
          {onCreateProject && (
            <Button
              onClick={onCreateProject}
              size="sm"
              className="bg-brand-primary hover:bg-brand-primary/90"
            >
              <svg className="mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 4v16m8-8H4"
                />
              </svg>
              New Project
            </Button>
          )}
        </div>
      </div>

      {/* Content */}
      <div className="p-6">
        {projects.length === 0 ? (
          <div className="py-8 text-center">
            <svg
              className="mx-auto h-12 w-12 text-gray-400"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"
              />
            </svg>
            <h3 className="mt-2 text-sm font-medium text-gray-900">No projects</h3>
            <p className="mt-1 text-sm text-gray-500">Get started by creating a new project.</p>
            {onCreateProject && (
              <div className="mt-6">
                <Button
                  onClick={onCreateProject}
                  className="bg-brand-primary hover:bg-brand-primary/90"
                >
                  <svg
                    className="mr-2 h-4 w-4"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 4v16m8-8H4"
                    />
                  </svg>
                  New Project
                </Button>
              </div>
            )}
          </div>
        ) : (
          <div className="space-y-4">
            {projects.slice(0, 5).map((project) => (
              <div
                key={project.id}
                className={cn(
                  'rounded-lg border border-gray-200 p-4 transition-colors duration-200 hover:border-gray-300',
                  onProjectClick && 'cursor-pointer hover:bg-gray-50'
                )}
                onClick={() => onProjectClick?.(project)}
              >
                <div className="flex items-start justify-between">
                  <div className="min-w-0 flex-1">
                    <div className="mb-2 flex items-center space-x-2">
                      <h4 className="truncate text-sm font-medium text-gray-900">{project.name}</h4>
                      <span
                        className={cn(
                          'inline-flex items-center rounded-full px-2 py-1 text-xs font-medium',
                          getProjectStatusColor(project.status)
                        )}
                      >
                        {formatProjectStatus(project.status)}
                      </span>
                      <span
                        className={cn(
                          'inline-flex items-center rounded-full px-2 py-1 text-xs font-medium',
                          getProjectPriorityColor(project.priority)
                        )}
                      >
                        {formatProjectPriority(project.priority)}
                      </span>
                    </div>
                    <p className="mb-2 line-clamp-2 text-sm text-gray-500">{project.description}</p>
                    <div className="flex items-center justify-between text-xs text-gray-400">
                      <span>Updated {formatRelativeTime(project.lastModified)}</span>
                      <span>by {project.owner}</span>
                    </div>
                  </div>
                  <div className="ml-4 flex-shrink-0">
                    <div className="text-right">
                      <div className="text-sm font-medium text-gray-900">
                        {calculateProjectProgress(project)}%
                      </div>
                      <div className="mt-1 h-2 w-16 rounded-full bg-gray-200">
                        <div
                          className="h-2 rounded-full bg-brand-primary transition-all duration-300"
                          style={{ width: `${calculateProjectProgress(project)}%` }}
                        ></div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}

            {projects.length > 5 && (
              <div className="border-t border-gray-200 pt-4">
                <Button
                  variant="ghost"
                  className="w-full text-brand-primary hover:bg-brand-primary/10 hover:text-brand-primary/90"
                  onClick={() => {
                    // Navigate to projects page
                    window.location.href = '/projects'
                  }}
                >
                  View all {projects.length} projects
                  <svg
                    className="ml-2 h-4 w-4"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M9 5l7 7-7 7"
                    />
                  </svg>
                </Button>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  )
})

ProjectsWidget.displayName = 'ProjectsWidget'
