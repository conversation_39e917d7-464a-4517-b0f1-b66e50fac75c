/**
 * Settings module main exports
 * Ultimate Electrical Designer - Settings & User Preferences
 *
 * This module provides comprehensive settings and user preferences management
 * following the 14-step client module workflow and engineering-grade standards.
 */

// Types and interfaces
export * from './types'

// Constants and configuration
export * from './constants'

// Utility functions
export * from './utils'

// Validation schemas
export * from './schemas'

// API integration
export * from './api'

// Hooks
export * from './hooks'

// State management
export * from './stores'

// Re-export main hook for convenience
export { useSettings, useCategorySettings, useSettingsSearch } from './hooks/useSettings'

// Re-export main store for convenience
export { useSettingsStore, settingsStoreSelectors } from './stores/settingsStore'

// Re-export main API client for convenience
export { settingsApi } from './api/settingsApi'

// Re-export key types for convenience
export type {
  UserPreferences,
  UserPreferencesUpdate,
  SettingsCategory,
  SettingsExport,
  SettingsImport,
  SettingsStoreState,
  UseSettingsReturn,
} from './types'

// Re-export key constants for convenience
export {
  SETTINGS_CATEGORIES,
  THEME_CONFIGS,
  LANGUAGE_CONFIGS,
  TIMEZONE_CONFIGS,
  DEFAULT_USER_PREFERENCES,
  DEFAULT_ENGINEERING_SETTINGS,
} from './constants'
