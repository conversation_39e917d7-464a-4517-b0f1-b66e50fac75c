/**
 * Registration Form Component
 * Comprehensive registration form with validation, security features, and accessibility
 */

'use client'

import { Button } from '@/components/ui/button'
import { useAuth } from '@/hooks/useAuth'
import { useEffect, useRef, useState } from 'react'
import { ConfirmPasswordInput, EmailInput, PasswordInput, UsernameInput } from './forms/shared'
import {
  focusUtils,
  generateA11yAnnouncement,
  parseAuthError,
  sanitizeRegisterData,
  type FormState,
} from './utils/authHelpers'
import { registerSchema, validateForm, type RegisterFormData } from './utils/validation'

interface RegisterFormProps {
  onSuccess?: () => void
  className?: string
  autoFocus?: boolean
}

export function RegisterForm({ onSuccess, className = '', autoFocus = true }: RegisterFormProps) {
  const { register, isRegisterPending, registerError } = useAuth()
  const formRef = useRef<HTMLFormElement>(null)
  const [formState, setFormState] = useState<FormState>('idle')

  const [formData, setFormData] = useState<RegisterFormData>({
    name: '',
    email: '',
    password: '',
    confirmPassword: '',
  })
  const [errors, setErrors] = useState<Record<string, string>>({})

  // Auto-focus first field on mount
  useEffect(() => {
    if (autoFocus && formRef.current) {
      focusUtils.focusFirstField(formRef.current)
    }
  }, [autoFocus])

  // Handle form state changes for accessibility
  useEffect(() => {
    if (formState !== 'idle') {
      const announcement = generateA11yAnnouncement(formState, 'register', registerError?.message)
      if (announcement) {
        // Announce to screen readers
        const announcer = document.createElement('div')
        announcer.setAttribute('aria-live', 'polite')
        announcer.setAttribute('aria-atomic', 'true')
        announcer.className = 'sr-only'
        announcer.textContent = announcement
        document.body.appendChild(announcer)
        setTimeout(() => document.body.removeChild(announcer), 1000)
      }
    }
  }, [formState, registerError])

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }))

    // Clear field error when user starts typing
    if (errors[name]) {
      setErrors((prev) => ({
        ...prev,
        [name]: '',
      }))
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setFormState('submitting')

    // Validate form data
    const validation = validateForm(registerSchema, formData)
    if (!validation.success) {
      setErrors(validation.errors)
      setFormState('error')

      // Focus first error field
      if (formRef.current) {
        focusUtils.focusFirstError(formRef.current)
      }
      return
    }

    // Clear any existing errors
    setErrors({})

    try {
      // Sanitize and submit form data
      const sanitizedData = sanitizeRegisterData(formData)

      // Convert to API format (remove confirmPassword)
      const { confirmPassword, ...registrationData } = sanitizedData
      await register(registrationData as any)

      setFormState('success')
      onSuccess?.()
    } catch (error) {
      setFormState('error')
      const authError = parseAuthError(error)

      // Set field-specific error if available
      if (authError.field) {
        setErrors({ [authError.field]: authError.message })
      }

      console.error('Registration failed:', error)
    }
  }

  return (
    <form ref={formRef} className={`space-y-6 ${className}`} onSubmit={handleSubmit}>
      {/* Username Field */}
      <UsernameInput
        id="name"
        name="name"
        label="Username"
        placeholder="Enter your username"
        value={formData.name}
        onChange={handleInputChange}
        error={errors.name}
        required
        autoComplete="username"
        helperText="3-50 characters, letters, numbers, underscores, and hyphens only"
      />

      {/* Email Field */}
      <EmailInput
        id="email"
        name="email"
        label="Email Address"
        placeholder="Enter your email address"
        value={formData.email}
        onChange={handleInputChange}
        error={errors.email}
        required
        autoComplete="email"
        helperText="We'll use this to send you important account information"
      />

      {/* Password Field */}
      <PasswordInput
        id="password"
        name="password"
        label="Password"
        placeholder="Create a secure password"
        value={formData.password}
        onChange={handleInputChange}
        error={errors.password}
        required
        autoComplete="new-password"
        showStrength={true}
        strengthVariant="full"
      />

      {/* Confirm Password Field */}
      <ConfirmPasswordInput
        id="confirmPassword"
        name="confirmPassword"
        label="Confirm Password"
        placeholder="Confirm your password"
        value={formData.confirmPassword}
        onChange={handleInputChange}
        error={errors.confirmPassword}
        required
        autoComplete="new-password"
        originalPassword={formData.password}
        showMatchIndicator={true}
      />

      {/* Global Error Display */}
      {registerError && formState === 'error' && (
        <div
          className="rounded-lg border border-error/20 bg-error/5 p-4"
          role="alert"
          aria-live="polite"
        >
          <div className="flex items-start">
            <div className="flex-shrink-0">
              <svg
                className="h-5 w-5 text-error"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                aria-hidden="true"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
                />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-semibold text-error">Registration Failed</h3>
              <div className="mt-1 text-sm text-error/80">
                {parseAuthError(registerError).message}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Success Message */}
      {formState === 'success' && (
        <div
          className="rounded-lg border border-success/20 bg-success/5 p-4"
          role="alert"
          aria-live="polite"
        >
          <div className="flex items-start">
            <div className="flex-shrink-0">
              <svg
                className="h-5 w-5 text-success"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                aria-hidden="true"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-semibold text-success">Account Created Successfully</h3>
              <div className="mt-1 text-sm text-success/80">
                Your account has been created. You can now sign in with your credentials.
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Terms and Privacy */}
      <div className="text-sm text-neutral-600">
        <p>
          By creating an account, you agree to our{' '}
          <a
            href="#"
            className="font-medium text-brand-secondary transition-colors hover:text-brand-secondary/80"
          >
            Terms of Service
          </a>{' '}
          and{' '}
          <a
            href="#"
            className="font-medium text-brand-secondary transition-colors hover:text-brand-secondary/80"
          >
            Privacy Policy
          </a>
          .
        </p>
      </div>

      {/* Submit Button */}
      <div className="space-y-4">
        <Button
          type="submit"
          disabled={isRegisterPending || formState === 'submitting'}
          className="h-12 w-full text-base font-semibold"
          aria-describedby={formState === 'error' ? 'register-error' : undefined}
        >
          {isRegisterPending || formState === 'submitting' ? (
            <>
              <svg
                className="-ml-1 mr-3 h-5 w-5 animate-spin text-white"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                aria-hidden="true"
              >
                <circle
                  className="opacity-25"
                  cx="12"
                  cy="12"
                  r="10"
                  stroke="currentColor"
                  strokeWidth="4"
                ></circle>
                <path
                  className="opacity-75"
                  fill="currentColor"
                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                ></path>
              </svg>
              Creating your account...
            </>
          ) : (
            <>
              <svg
                className="mr-2 h-5 w-5"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                aria-hidden="true"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z"
                />
              </svg>
              Create your account
            </>
          )}
        </Button>

        {/* Security Info */}
        <div className="text-center">
          <p className="flex items-center justify-center space-x-1 text-xs text-neutral-500">
            <svg
              className="h-3 w-3"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              aria-hidden="true"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"
              />
            </svg>
            <span>Your information is protected with enterprise-grade security</span>
          </p>
        </div>
      </div>
    </form>
  )
}
