/**
 * Quick admin actions widget for admin dashboard
 */

'use client'

import React, { useState } from 'react'
import { QuickAdminActionsWidgetProps } from '../types'

export function QuickAdminActionsWidget({
  actions,
  onActionClick,
  className = '',
}: QuickAdminActionsWidgetProps) {
  const [filter, setFilter] = useState<
    'all' | 'user_management' | 'system_maintenance' | 'data_operations' | 'security' | 'monitoring'
  >('all')

  // Filter actions based on selected filter
  const filteredActions =
    filter === 'all' ? actions : actions.filter((action) => action.category === filter)

  // Group actions by category
  const actionsByCategory = filteredActions.reduce(
    (acc, action) => {
      if (!acc[action.category]) {
        acc[action.category] = []
      }
      acc[action.category].push(action)
      return acc
    },
    {} as Record<string, typeof actions>
  )

  const getActionIcon = (iconName: string) => {
    const icons: Record<string, JSX.Element> = {
      users: (
        <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"
          />
        </svg>
      ),
      database: (
        <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4"
          />
        </svg>
      ),
      backup: (
        <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"
          />
        </svg>
      ),
      export: (
        <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
          />
        </svg>
      ),
      security: (
        <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"
          />
        </svg>
      ),
      refresh: (
        <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
          />
        </svg>
      ),
      settings: (
        <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"
          />
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
          />
        </svg>
      ),
      monitor: (
        <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
          />
        </svg>
      ),
    }
    return icons[iconName] || icons.settings
  }

  const getActionColorClasses = (color: string) => {
    const colorMap = {
      primary: 'bg-blue-600 hover:bg-blue-500 text-white',
      secondary: 'bg-gray-600 hover:bg-gray-500 text-white',
      accent: 'bg-purple-600 hover:bg-purple-500 text-white',
      dark: 'bg-gray-900 hover:bg-gray-800 text-white',
      success: 'bg-green-600 hover:bg-green-500 text-white',
      warning: 'bg-yellow-600 hover:bg-yellow-500 text-white',
      danger: 'bg-red-600 hover:bg-red-500 text-white',
    }
    return colorMap[color as keyof typeof colorMap] || colorMap.primary
  }

  const handleActionClick = (action: (typeof actions)[0]) => {
    if (!action.isEnabled) return

    if (action.requiresConfirmation) {
      const confirmed = window.confirm(`Are you sure you want to ${action.title.toLowerCase()}?`)
      if (!confirmed) return
    }

    if (onActionClick) {
      onActionClick(action)
    }
  }

  return (
    <div className={`overflow-hidden rounded-lg bg-white shadow ${className}`}>
      <div className="p-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-medium text-gray-900">Quick Admin Actions</h3>
          <span className="text-sm text-gray-500">
            {actions.filter((a) => a.isEnabled).length} available
          </span>
        </div>

        {/* Filter Tabs */}
        <div className="mt-6">
          <div className="flex space-x-1 rounded-lg bg-gray-100 p-1">
            {[
              { key: 'all', label: 'All' },
              { key: 'user_management', label: 'Users' },
              { key: 'system_maintenance', label: 'System' },
              { key: 'data_operations', label: 'Data' },
              { key: 'security', label: 'Security' },
              { key: 'monitoring', label: 'Monitor' },
            ].map((tab) => (
              <button
                key={tab.key}
                onClick={() => setFilter(tab.key as any)}
                className={`flex-1 rounded-md px-3 py-2 text-sm font-medium transition-colors ${
                  filter === tab.key
                    ? 'bg-white text-gray-900 shadow-sm'
                    : 'text-gray-500 hover:text-gray-700'
                }`}
              >
                {tab.label}
              </button>
            ))}
          </div>
        </div>

        {/* Actions Grid */}
        <div className="mt-6">
          {Object.entries(actionsByCategory).map(([category, categoryActions]) => (
            <div key={category} className="mb-6 last:mb-0">
              <h4 className="mb-3 text-sm font-medium capitalize text-gray-900">
                {category.replace('_', ' ')}
              </h4>

              <div className="grid grid-cols-1 gap-3 sm:grid-cols-2">
                {categoryActions.map((action) => (
                  <button
                    key={action.id}
                    onClick={() => handleActionClick(action)}
                    disabled={!action.isEnabled}
                    className={`relative flex items-center space-x-3 rounded-lg border border-gray-300 bg-white px-4 py-3 text-left shadow-sm transition-all hover:shadow-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ${
                      !action.isEnabled ? 'cursor-not-allowed opacity-50' : 'hover:border-gray-400'
                    }`}
                  >
                    <div
                      className={`flex-shrink-0 rounded-lg p-2 ${getActionColorClasses(action.color)}`}
                    >
                      {getActionIcon(action.icon)}
                    </div>

                    <div className="min-w-0 flex-1">
                      <div className="flex items-center justify-between">
                        <p className="text-sm font-medium text-gray-900">{action.title}</p>
                        {action.requiresConfirmation && (
                          <svg
                            className="h-4 w-4 text-yellow-500"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"
                            />
                          </svg>
                        )}
                      </div>
                      <p className="text-sm text-gray-500">{action.description}</p>
                    </div>
                  </button>
                ))}
              </div>
            </div>
          ))}
        </div>

        {filteredActions.length === 0 && (
          <div className="py-6 text-center">
            <svg
              className="mx-auto h-12 w-12 text-gray-400"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M13 10V3L4 14h7v7l9-11h-7z"
              />
            </svg>
            <h3 className="mt-2 text-sm font-medium text-gray-900">No actions available</h3>
            <p className="mt-1 text-sm text-gray-500">
              {filter === 'all'
                ? 'No admin actions are currently available.'
                : `No ${filter.replace('_', ' ')} actions found.`}
            </p>
          </div>
        )}

        {/* Action Categories Summary */}
        <div className="mt-6 rounded-lg bg-gray-50 p-4">
          <h4 className="mb-2 text-sm font-medium text-gray-900">Actions Summary</h4>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-gray-500">Total Actions:</span>
              <span className="ml-2 font-medium text-gray-900">{actions.length}</span>
            </div>
            <div>
              <span className="text-gray-500">Enabled:</span>
              <span className="ml-2 font-medium text-gray-900">
                {actions.filter((a) => a.isEnabled).length}
              </span>
            </div>
            <div>
              <span className="text-gray-500">Require Confirmation:</span>
              <span className="ml-2 font-medium text-gray-900">
                {actions.filter((a) => a.requiresConfirmation).length}
              </span>
            </div>
            <div>
              <span className="text-gray-500">Categories:</span>
              <span className="ml-2 font-medium text-gray-900">
                {Object.keys(actionsByCategory).length}
              </span>
            </div>
          </div>
        </div>

        {/* Warning for destructive actions */}
        {actions.some((a) => a.requiresConfirmation) && (
          <div className="mt-4 rounded-md bg-yellow-50 p-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                  <path
                    fillRule="evenodd"
                    d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                    clipRule="evenodd"
                  />
                </svg>
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-yellow-800">Caution Required</h3>
                <div className="mt-2 text-sm text-yellow-700">
                  Some actions require confirmation and may have significant system impact.
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
