'use client'

import { cn } from '@/lib/utils'
import { memo, useEffect, useRef } from 'react'
import { useLandingPageStore } from '../hooks'
import type { FeatureItem } from '../types'
import { generateFeatureAriaLabel, getBrandColorClass } from '../utils'

interface FeaturesSectionProps {
  features: FeatureItem[]
  className?: string
}

/**
 * Individual feature card component
 */
const FeatureCard = memo(function FeatureCard({
  feature,
  index,
}: {
  feature: FeatureItem
  index: number
}) {
  const { setHoveredFeature, hoveredFeature } = useLandingPageStore()
  const isHovered = hoveredFeature === feature.id

  const getFeatureIcon = (iconName: string) => {
    const iconMap: Record<string, JSX.Element> = {
      zap: (
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          d="M13 10V3L4 14h7v7l9-11h-7z"
        />
      ),
      calculator: (
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z"
        />
      ),
      folder: (
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
        />
      ),
      cable: (
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"
        />
      ),
      'shield-check': (
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"
        />
      ),
      'file-text': (
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z"
        />
      ),
    }

    return iconMap[iconName] || iconMap['zap']
  }

  return (
    <div
      className={cn(
        'group relative overflow-hidden rounded-2xl border border-neutral-100 bg-white p-8 shadow-lg transition-all duration-300',
        'cursor-pointer hover:-translate-y-1 hover:shadow-xl',
        isHovered && '-translate-y-1 shadow-xl'
      )}
      onMouseEnter={() => setHoveredFeature(feature.id)}
      onMouseLeave={() => setHoveredFeature(null)}
      style={{ animationDelay: `${index * 0.1}s` }}
      role="article"
      aria-label={generateFeatureAriaLabel(feature)}
    >
      {/* Background Gradient */}
      <div
        className={cn(
          'absolute right-0 top-0 h-32 w-32 -translate-y-16 translate-x-16 rounded-full transition-opacity duration-300',
          `bg-gradient-to-br from-${getBrandColorClass(feature.color, 'bg').replace('bg-', '')}/5 to-transparent`
        )}
        aria-hidden="true"
      />

      <div className="relative">
        {/* Icon */}
        <div
          className={cn(
            'mb-6 flex h-14 w-14 items-center justify-center rounded-xl shadow-lg transition-transform duration-300',
            `bg-gradient-to-br from-${getBrandColorClass(feature.color, 'bg').replace('bg-', '')} to-${getBrandColorClass(feature.color, 'bg').replace('bg-', '')}/80`,
            'group-hover:scale-110'
          )}
        >
          <svg
            className="h-7 w-7 text-white"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            aria-hidden="true"
          >
            {getFeatureIcon(feature.icon)}
          </svg>
        </div>

        {/* Content */}
        <h3 className="mb-4 text-xl font-bold text-neutral-900">{feature.title}</h3>
        <p className="mb-6 leading-relaxed text-neutral-600">{feature.description}</p>

        {/* Learn More Link */}
        <div
          className={cn(
            'flex items-center font-medium transition-all duration-200',
            `text-${getBrandColorClass(feature.color, 'text').replace('text-', '')}`,
            'group-hover:translate-x-1'
          )}
        >
          <span className="text-sm">Learn more</span>
          <svg
            className="ml-2 h-4 w-4 transition-transform duration-200 group-hover:translate-x-1"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            aria-hidden="true"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
          </svg>
        </div>
      </div>
    </div>
  )
})

/**
 * Features section with interactive cards and animations
 */
export function FeaturesSection({ features, className }: FeaturesSectionProps) {
  const { setFeaturesVisible } = useLandingPageStore()
  const featuresRef = useRef<HTMLElement>(null)

  // Intersection observer for animations
  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        setFeaturesVisible(entry.isIntersecting)
      },
      {
        threshold: 0.2,
        rootMargin: '0px 0px -10% 0px', // Trigger slightly before element is fully visible
      }
    )

    if (featuresRef.current) {
      observer.observe(featuresRef.current)
    }

    return () => observer.disconnect()
  }, [setFeaturesVisible])

  return (
    <section
      ref={featuresRef}
      className={cn('bg-gradient-to-b from-white to-neutral-50 py-24', className)}
    >
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="mb-20 text-center">
          <div className="animate-fadeIn mb-6 inline-flex items-center rounded-full bg-brand-primary/10 px-4 py-2">
            <span className="text-sm font-semibold uppercase tracking-wide text-brand-primary">
              Professional Features
            </span>
          </div>
          <h2 className="animate-slideIn mb-6 text-4xl font-bold text-neutral-900 lg:text-5xl">
            Engineering-Grade Capabilities
          </h2>
          <p
            className="animate-slideIn mx-auto max-w-3xl text-xl leading-relaxed text-neutral-600"
            style={{ animationDelay: '0.2s' }}
          >
            Built for professional electrical engineers with industry-standard calculations,
            compliance verification, and comprehensive project management workflows.
          </p>
        </div>

        {/* Features Grid */}
        <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3">
          {features.map((feature, index) => (
            <div
              key={feature.id}
              className="animate-slideIn"
              style={{ animationDelay: `${0.4 + index * 0.1}s` }}
            >
              <FeatureCard feature={feature} index={index} />
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}
