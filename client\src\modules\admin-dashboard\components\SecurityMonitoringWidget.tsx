/**
 * Security monitoring widget for admin dashboard
 */

'use client'

import React, { useState } from 'react'
import { SecurityMonitoringWidgetProps } from '../types'
import { getSecurityAlertColor, formatSecurityAlertType, formatRelativeTime } from '../utils'

export function SecurityMonitoringWidget({
  alerts,
  isLoading,
  onAlertClick,
  onResolveAlert,
  onViewAll,
  className = '',
}: SecurityMonitoringWidgetProps) {
  const [filter, setFilter] = useState<
    'all' | 'unresolved' | 'critical' | 'high' | 'medium' | 'low'
  >('unresolved')

  // Filter alerts based on selected filter
  const filteredAlerts =
    filter === 'all'
      ? alerts
      : filter === 'unresolved'
        ? alerts.filter((alert) => !alert.resolved)
        : alerts.filter((alert) => alert.severity === filter)

  // Sort alerts by severity and timestamp
  const sortedAlerts = [...filteredAlerts].sort((a, b) => {
    const severityOrder = { critical: 4, high: 3, medium: 2, low: 1 }
    const aSeverity = severityOrder[a.severity]
    const bSeverity = severityOrder[b.severity]

    if (aSeverity !== bSeverity) {
      return bSeverity - aSeverity
    }

    return new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
  })

  // Calculate statistics
  const stats = {
    total: alerts.length,
    unresolved: alerts.filter((alert) => !alert.resolved).length,
    critical: alerts.filter((alert) => alert.severity === 'critical').length,
    high: alerts.filter((alert) => alert.severity === 'high').length,
    medium: alerts.filter((alert) => alert.severity === 'medium').length,
    low: alerts.filter((alert) => alert.severity === 'low').length,
  }

  if (isLoading) {
    return (
      <div className={`overflow-hidden rounded-lg bg-white shadow ${className}`}>
        <div className="p-6">
          <div className="animate-pulse">
            <div className="mb-4 h-6 w-1/2 rounded bg-gray-200"></div>
            <div className="space-y-3">
              {Array.from({ length: 5 }).map((_, i) => (
                <div key={i} className="flex items-center space-x-3">
                  <div className="h-4 w-1/4 rounded bg-gray-200"></div>
                  <div className="h-4 w-1/3 rounded bg-gray-200"></div>
                  <div className="h-4 w-1/5 rounded bg-gray-200"></div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className={`overflow-hidden rounded-lg bg-white shadow ${className}`}>
      <div className="p-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-medium text-gray-900">Security Monitoring</h3>
          {onViewAll && (
            <button onClick={onViewAll} className="text-sm text-blue-600 hover:text-blue-500">
              View All Alerts
            </button>
          )}
        </div>

        {/* Security Status Indicator */}
        <div className="mt-4">
          {stats.critical > 0 ? (
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                  <path
                    fillRule="evenodd"
                    d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                    clipRule="evenodd"
                  />
                </svg>
              </div>
              <span className="ml-2 text-sm font-medium text-red-800">
                Critical security issues detected
              </span>
            </div>
          ) : stats.high > 0 ? (
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                  <path
                    fillRule="evenodd"
                    d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                    clipRule="evenodd"
                  />
                </svg>
              </div>
              <span className="ml-2 text-sm font-medium text-yellow-800">
                High priority security alerts
              </span>
            </div>
          ) : stats.unresolved > 0 ? (
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                  <path
                    fillRule="evenodd"
                    d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                    clipRule="evenodd"
                  />
                </svg>
              </div>
              <span className="ml-2 text-sm font-medium text-blue-800">
                Security monitoring active
              </span>
            </div>
          ) : (
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                  <path
                    fillRule="evenodd"
                    d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                    clipRule="evenodd"
                  />
                </svg>
              </div>
              <span className="ml-2 text-sm font-medium text-green-800">
                All security alerts resolved
              </span>
            </div>
          )}
        </div>

        {/* Statistics */}
        <div className="mt-6 grid grid-cols-2 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-red-600">{stats.unresolved}</div>
            <div className="text-sm text-gray-500">Unresolved</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-gray-900">{stats.total}</div>
            <div className="text-sm text-gray-500">Total Alerts</div>
          </div>
        </div>

        {/* Severity Breakdown */}
        <div className="mt-6">
          <h4 className="mb-3 text-sm font-medium text-gray-900">Severity Breakdown</h4>
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Critical</span>
              <span className="inline-flex items-center rounded-full bg-red-100 px-2.5 py-0.5 text-xs font-medium text-red-800">
                {stats.critical}
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">High</span>
              <span className="inline-flex items-center rounded-full bg-orange-100 px-2.5 py-0.5 text-xs font-medium text-orange-800">
                {stats.high}
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Medium</span>
              <span className="inline-flex items-center rounded-full bg-yellow-100 px-2.5 py-0.5 text-xs font-medium text-yellow-800">
                {stats.medium}
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Low</span>
              <span className="inline-flex items-center rounded-full bg-blue-100 px-2.5 py-0.5 text-xs font-medium text-blue-800">
                {stats.low}
              </span>
            </div>
          </div>
        </div>

        {/* Filter Tabs */}
        <div className="mt-6">
          <div className="flex space-x-1 rounded-lg bg-gray-100 p-1">
            {[
              { key: 'unresolved', label: 'Unresolved' },
              { key: 'critical', label: 'Critical' },
              { key: 'high', label: 'High' },
              { key: 'all', label: 'All' },
            ].map((tab) => (
              <button
                key={tab.key}
                onClick={() => setFilter(tab.key as any)}
                className={`flex-1 rounded-md px-3 py-2 text-sm font-medium transition-colors ${
                  filter === tab.key
                    ? 'bg-white text-gray-900 shadow-sm'
                    : 'text-gray-500 hover:text-gray-700'
                }`}
              >
                {tab.label}
              </button>
            ))}
          </div>
        </div>

        {/* Alerts List */}
        <div className="mt-6">
          <div className="max-h-64 space-y-3 overflow-y-auto">
            {sortedAlerts.slice(0, 8).map((alert) => (
              <div
                key={alert.id}
                className={`rounded-lg border p-3 transition-colors ${getSecurityAlertColor(alert.severity)} ${
                  alert.resolved ? 'opacity-60' : ''
                }`}
              >
                <div className="flex items-start justify-between">
                  <div className="min-w-0 flex-1">
                    <div className="flex items-center space-x-2">
                      <span
                        className={`inline-flex items-center rounded-full px-2 py-1 text-xs font-medium ${getSecurityAlertColor(alert.severity)}`}
                      >
                        {alert.severity.toUpperCase()}
                      </span>
                      <span className="text-xs text-gray-500">
                        {formatSecurityAlertType(alert.type)}
                      </span>
                      {alert.resolved && (
                        <span className="inline-flex items-center rounded-full bg-green-100 px-2 py-1 text-xs font-medium text-green-800">
                          Resolved
                        </span>
                      )}
                    </div>

                    <h4 className="mt-1 text-sm font-medium text-gray-900">{alert.title}</h4>
                    <p className="mt-1 text-sm text-gray-600">{alert.description}</p>

                    <div className="mt-2 flex items-center justify-between text-xs text-gray-500">
                      <span>{formatRelativeTime(alert.timestamp)}</span>
                      {alert.userName && <span>User: {alert.userName}</span>}
                      {alert.ipAddress && <span>IP: {alert.ipAddress}</span>}
                    </div>

                    {alert.resolved && alert.resolvedBy && (
                      <p className="mt-1 text-xs text-green-600">
                        Resolved by {alert.resolvedBy} on{' '}
                        {new Date(alert.resolvedAt!).toLocaleDateString()}
                      </p>
                    )}
                  </div>

                  <div className="ml-4 flex items-center space-x-2">
                    {!alert.resolved && onResolveAlert && (
                      <button
                        onClick={() => onResolveAlert(alert.id)}
                        className="rounded-md bg-green-600 px-2 py-1 text-xs font-medium text-white hover:bg-green-500"
                      >
                        Resolve
                      </button>
                    )}
                    {onAlertClick && (
                      <button
                        onClick={() => onAlertClick(alert)}
                        className="rounded-md p-1 text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500"
                        aria-label="View alert details"
                      >
                        <svg
                          className="h-4 w-4"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M9 5l7 7-7 7"
                          />
                        </svg>
                      </button>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>

          {sortedAlerts.length > 8 && (
            <div className="mt-3 text-center">
              <button onClick={onViewAll} className="text-sm text-blue-600 hover:text-blue-500">
                View all {sortedAlerts.length} alerts
              </button>
            </div>
          )}

          {sortedAlerts.length === 0 && (
            <div className="py-6 text-center">
              <svg
                className="mx-auto h-12 w-12 text-gray-400"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"
                />
              </svg>
              <h3 className="mt-2 text-sm font-medium text-gray-900">No security alerts</h3>
              <p className="mt-1 text-sm text-gray-500">
                {filter === 'all'
                  ? 'No security alerts have been detected.'
                  : `No ${filter} security alerts found.`}
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
