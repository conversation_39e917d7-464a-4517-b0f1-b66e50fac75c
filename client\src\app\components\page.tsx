'use client'

/**
 * Component Catalog Page
 * Main page for component management with search, filtering, and CRUD operations
 */

import { Button } from '@/components/ui/button'
import {
  BulkOperations,
  ComponentFiltersWidget as ComponentFilters,
  ComponentList,
  ComponentSearchWidget as ComponentSearch,
  ComponentStatsWidget as ComponentStats,
} from '@/modules/components'
import { useComponents } from '@/modules/components/api/componentQueries'
import {
  useComponentFilters,
  useComponentSelection,
  useComponentStore,
} from '@/modules/components/hooks/useComponentStore'
import type { ComponentRead } from '@/modules/components/types'
import { Download, Plus, Settings } from 'lucide-react'
import { useState } from 'react'

export default function ComponentCatalogPage() {
  const [showStats, setShowStats] = useState(false)
  const [showFilters, setShowFilters] = useState(true)

  // Store state
  const { listState, updateFilters, setListState, selectComponent, clearSelection } =
    useComponentStore()
  const filters = useComponentFilters()
  const selectedIds = useComponentSelection()

  // Fetch components
  const {
    data: componentsData,
    isLoading,
    error,
    refetch,
  } = useComponents({
    page: listState.page,
    size: listState.pageSize,
    search_term: filters.search_term || undefined,
    component_category_id: filters.component_category_id || undefined,
    component_type_id: filters.component_type_id || undefined,
    manufacturer: filters.manufacturer || undefined,
    is_preferred: filters.is_preferred ?? undefined,
    is_active: filters.is_active ?? undefined,
  })

  // Handle search
  const handleSearch = (query: string) => {
    updateFilters({ search_term: query })
  }

  // Handle filter changes
  const handleFiltersChange = (newFilters: any) => {
    updateFilters(newFilters)
  }

  // Handle view mode change
  const handleViewModeChange = (mode: 'grid' | 'list' | 'table' | 'cards') => {
    setListState({ viewMode: mode })
  }

  // Handle pagination
  const handlePageChange = (page: number) => {
    setListState({ page })
  }

  const handlePageSizeChange = (size: number) => {
    setListState({ pageSize: size, page: 1 })
  }

  // Handle component actions
  const handleComponentView = (component: ComponentRead) => {
    // Navigate to component details
    window.location.href = `/components/${component.id}`
  }

  const handleComponentEdit = (component: ComponentRead) => {
    // Navigate to edit form
    window.location.href = `/components/${component.id}/edit`
  }

  const handleComponentDelete = (component: ComponentRead) => {
    // Show delete confirmation
    if (confirm(`Are you sure you want to delete ${component.name}?`)) {
      // Handle delete logic here
      console.log('Deleting component:', component.id)
    }
  }

  const handleTogglePreferred = (component: ComponentRead) => {
    // Handle toggle preferred logic here
    console.log('Toggling preferred for component:', component.id)
  }

  const handleSelectionChange = (selectedIds: number[]) => {
    // Update selection in store
    selectedIds.forEach((id) => selectComponent(id))
  }

  const handleCreateNew = () => {
    window.location.href = '/components/new'
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="border-b border-gray-200 bg-white">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
          <div className="flex h-16 items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Component Catalog</h1>
              <p className="text-sm text-gray-600">
                Manage electrical components and specifications
              </p>
            </div>

            <div className="flex items-center gap-3">
              <Button variant="outline" size="sm" onClick={() => setShowStats(!showStats)}>
                <Settings className="mr-2 h-4 w-4" />
                {showStats ? 'Hide Stats' : 'Show Stats'}
              </Button>

              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  // Handle export
                  console.log('Exporting components')
                }}
              >
                <Download className="mr-2 h-4 w-4" />
                Export
              </Button>

              <Button onClick={handleCreateNew} data-testid="add-component-btn">
                <Plus className="mr-2 h-4 w-4" />
                Add Component
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Stats Dashboard */}
      {showStats && (
        <div className="border-b border-gray-200 bg-white">
          <div className="mx-auto max-w-7xl px-4 py-6 sm:px-6 lg:px-8">
            <ComponentStats />
          </div>
        </div>
      )}

      {/* Main Content */}
      <div className="mx-auto max-w-7xl px-4 py-6 sm:px-6 lg:px-8">
        <div className="flex gap-6">
          {/* Sidebar with Filters */}
          {showFilters && (
            <div className="w-80 flex-shrink-0">
              <div className="sticky top-6 space-y-6">
                <ComponentFilters
                  filters={filters}
                  onFiltersChange={handleFiltersChange}
                  onClear={() =>
                    updateFilters({
                      search_term: '',
                      component_category_id: null,
                      component_type_id: null,
                      manufacturer: '',
                      is_preferred: null,
                      is_active: null,
                      min_price: null,
                      max_price: null,
                      stock_status: '',
                    })
                  }
                />

                {selectedIds.length > 0 && (
                  <BulkOperations
                    selectedComponentIds={selectedIds}
                    onSelectionClear={clearSelection}
                    onOperationComplete={() => {
                      refetch()
                      clearSelection()
                    }}
                  />
                )}
              </div>
            </div>
          )}

          {/* Main Content Area */}
          <div className="min-w-0 flex-1">
            <div className="space-y-6">
              {/* Search Bar */}
              <div className="rounded-lg border border-gray-200 bg-white p-4">
                <ComponentSearch
                  value={filters.search_term || ''}
                  onSearch={handleSearch}
                  onChange={(value) => updateFilters({ search_term: value })}
                  onClear={() => updateFilters({ search_term: '' })}
                  className="w-full"
                />
              </div>

              {/* Component List */}
              <div className="rounded-lg border border-gray-200 bg-white p-6">
                <ComponentList
                  components={componentsData?.items || []}
                  loading={isLoading}
                  error={error?.message || null}
                  viewMode={listState.viewMode}
                  onComponentView={handleComponentView}
                  onComponentEdit={handleComponentEdit}
                  onComponentDelete={handleComponentDelete}
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Toggle Filters Button (Mobile) */}
      <div className="fixed bottom-4 left-4 lg:hidden">
        <Button
          variant="outline"
          size="sm"
          onClick={() => setShowFilters(!showFilters)}
          className="bg-white shadow-lg"
        >
          <Settings className="mr-2 h-4 w-4" />
          {showFilters ? 'Hide' : 'Show'} Filters
        </Button>
      </div>
    </div>
  )
}
