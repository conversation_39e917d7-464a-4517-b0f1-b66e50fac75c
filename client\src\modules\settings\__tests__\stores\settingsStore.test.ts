/**
 * Unit tests for Settings Store
 * Ultimate Electrical Designer - Settings & User Preferences
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { renderHook, act } from '@testing-library/react'
import { useSettingsStore } from '../../stores/settingsStore'
import type { SettingsCategory, UserPreferences } from '../../types'

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
}

Object.defineProperty(window, 'localStorage', {
  value: localStorageMock,
})

// Mock BroadcastChannel
class MockBroadcastChannel {
  name: string
  onmessage: ((event: MessageEvent) => void) | null = null

  constructor(name: string) {
    this.name = name
  }

  postMessage(data: any) {
    // Mock implementation
  }

  close() {
    // Mock implementation
  }

  addEventListener(type: string, listener: (event: MessageEvent) => void) {
    if (type === 'message') {
      this.onmessage = listener
    }
  }

  removeEventListener(type: string, listener: (event: MessageEvent) => void) {
    if (type === 'message') {
      this.onmessage = null
    }
  }
}

Object.defineProperty(window, 'BroadcastChannel', {
  value: MockBroadcastChannel,
})

describe('Settings Store', () => {
  beforeEach(() => {
    // Reset store state before each test
    useSettingsStore.getState().reset()
    vi.clearAllMocks()
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  describe('Initial State', () => {
    it('should have correct initial state', () => {
      const { result } = renderHook(() => useSettingsStore())
      const state = result.current

      expect(state.ui.activeCategory).toBe('account')
      expect(state.ui.searchQuery).toBe('')
      expect(state.ui.isLoading).toBe(false)
      expect(state.ui.isSaving).toBe(false)
      expect(state.ui.hasUnsavedChanges).toBe(false)
      expect(state.ui.errors).toEqual({})
      expect(state.formData).toEqual({})
      expect(state.syncEnabled).toBe(true)
    })
  })

  describe('UI Actions', () => {
    it('should set active category', () => {
      const { result } = renderHook(() => useSettingsStore())

      act(() => {
        result.current.setActiveCategory('appearance')
      })

      expect(result.current.ui.activeCategory).toBe('appearance')
    })

    it('should set search query', () => {
      const { result } = renderHook(() => useSettingsStore())

      act(() => {
        result.current.setSearchQuery('test query')
      })

      expect(result.current.ui.searchQuery).toBe('test query')
    })

    it('should set loading state', () => {
      const { result } = renderHook(() => useSettingsStore())

      act(() => {
        result.current.setLoading(true)
      })

      expect(result.current.ui.isLoading).toBe(true)
    })

    it('should set saving state', () => {
      const { result } = renderHook(() => useSettingsStore())

      act(() => {
        result.current.setSaving(true)
      })

      expect(result.current.ui.isSaving).toBe(true)
    })

    it('should set error', () => {
      const { result } = renderHook(() => useSettingsStore())

      act(() => {
        result.current.setError('theme', 'Invalid theme')
      })

      expect(result.current.ui.errors.theme).toBe('Invalid theme')
    })

    it('should clear errors', () => {
      const { result } = renderHook(() => useSettingsStore())

      act(() => {
        result.current.setError('theme', 'Invalid theme')
        result.current.setError('language', 'Invalid language')
      })

      expect(Object.keys(result.current.ui.errors)).toHaveLength(2)

      act(() => {
        result.current.clearErrors()
      })

      expect(result.current.ui.errors).toEqual({})
    })

    it('should show and hide dialogs', () => {
      const { result } = renderHook(() => useSettingsStore())

      act(() => {
        result.current.showDialog('reset', true)
      })

      expect(result.current.ui.showResetDialog).toBe(true)

      act(() => {
        result.current.showDialog('reset', false)
      })

      expect(result.current.ui.showResetDialog).toBe(false)
    })
  })

  describe('Form Data Management', () => {
    it('should update form data', () => {
      const { result } = renderHook(() => useSettingsStore())

      const updateData: Partial<UserPreferences> = {
        theme: 'dark',
        language: 'es',
      }

      act(() => {
        result.current.updateFormData(updateData)
      })

      expect(result.current.formData).toEqual(updateData)
      expect(result.current.ui.hasUnsavedChanges).toBe(true)
    })

    it('should reset form data', () => {
      const { result } = renderHook(() => useSettingsStore())

      act(() => {
        result.current.updateFormData({ theme: 'dark' })
      })

      expect(result.current.formData).toEqual({ theme: 'dark' })

      act(() => {
        result.current.resetFormData()
      })

      expect(result.current.formData).toEqual({})
      expect(result.current.ui.hasUnsavedChanges).toBe(false)
    })

    it('should update multiple fields', () => {
      const { result } = renderHook(() => useSettingsStore())

      const updates: Partial<UserPreferences> = {
        theme: 'dark',
        language: 'es',
        auto_save_interval: 600,
      }

      act(() => {
        result.current.updateMultipleFields(updates)
      })

      expect(result.current.formData).toEqual(updates)
    })
  })

  describe('Validation', () => {
    it('should validate form data correctly', () => {
      const { result } = renderHook(() => useSettingsStore())

      act(() => {
        result.current.updateFormData({
          auto_save_interval: 10, // Invalid: too low
          calculation_precision: -1, // Invalid: negative
        })
      })

      let validation: { isValid: boolean; errors: Record<string, string> }
      act(() => {
        validation = result.current.validateFormData()
      })

      expect(validation!.isValid).toBe(false)
      expect(validation!.errors).toHaveProperty('auto_save_interval')
      expect(validation!.errors).toHaveProperty('calculation_precision')
    })

    it('should validate valid form data', () => {
      const { result } = renderHook(() => useSettingsStore())

      act(() => {
        result.current.updateFormData({
          auto_save_interval: 300,
          calculation_precision: 2,
        })
      })

      let validation: { isValid: boolean; errors: Record<string, string> }
      act(() => {
        validation = result.current.validateFormData()
      })

      expect(validation!.isValid).toBe(true)
      expect(validation!.errors).toEqual({})
    })
  })

  describe('Utility Methods', () => {
    it('should get changed fields', () => {
      const { result } = renderHook(() => useSettingsStore())

      act(() => {
        result.current.updateFormData({
          theme: 'dark',
          language: 'es',
        })
      })

      const changedFields = result.current.getChangedFields()
      expect(changedFields).toEqual(['theme', 'language'])
    })

    it('should check field errors', () => {
      const { result } = renderHook(() => useSettingsStore())

      act(() => {
        result.current.setError('theme', 'Invalid theme')
      })

      expect(result.current.hasFieldError('theme')).toBe(true)
      expect(result.current.hasFieldError('language')).toBe(false)
      expect(result.current.getFieldError('theme')).toBe('Invalid theme')
      expect(result.current.getFieldError('language')).toBeUndefined()
    })

    it('should reset store completely', () => {
      const { result } = renderHook(() => useSettingsStore())

      act(() => {
        result.current.updateFormData({ theme: 'dark' })
        result.current.setActiveCategory('appearance')
        result.current.setSearchQuery('test')
        result.current.setError('theme', 'error')
      })

      act(() => {
        result.current.reset()
      })

      expect(result.current.formData).toEqual({})
      expect(result.current.ui.activeCategory).toBe('account')
      expect(result.current.ui.searchQuery).toBe('')
      expect(result.current.ui.errors).toEqual({})
    })
  })

  describe('Sync Management', () => {
    it('should enable and disable sync', () => {
      const { result } = renderHook(() => useSettingsStore())

      act(() => {
        result.current.disableSync()
      })

      expect(result.current.syncEnabled).toBe(false)

      act(() => {
        result.current.enableSync()
      })

      expect(result.current.syncEnabled).toBe(true)
    })

    it('should broadcast changes when sync is enabled', () => {
      const { result } = renderHook(() => useSettingsStore())
      const broadcastSpy = vi.spyOn(result.current, 'broadcastChange')

      act(() => {
        result.current.updateFormData({ theme: 'dark' })
      })

      expect(broadcastSpy).toHaveBeenCalledWith({ theme: 'dark' })
    })
  })

  describe('Dialog Management', () => {
    it('should open and close dialogs', () => {
      const { result } = renderHook(() => useSettingsStore())

      act(() => {
        result.current.openDialog('reset')
      })

      expect(result.current.ui.showResetDialog).toBe(true)

      act(() => {
        result.current.closeDialog('reset')
      })

      expect(result.current.ui.showResetDialog).toBe(false)
    })

    it('should close all dialogs', () => {
      const { result } = renderHook(() => useSettingsStore())

      act(() => {
        result.current.openDialog('reset')
        result.current.openDialog('import')
        result.current.openDialog('export')
      })

      expect(result.current.ui.showResetDialog).toBe(true)
      expect(result.current.ui.showImportDialog).toBe(true)
      expect(result.current.ui.showExportDialog).toBe(true)

      act(() => {
        result.current.closeAllDialogs()
      })

      expect(result.current.ui.showResetDialog).toBe(false)
      expect(result.current.ui.showImportDialog).toBe(false)
      expect(result.current.ui.showExportDialog).toBe(false)
    })
  })

  describe('Persistence', () => {
    it('should persist state to localStorage', () => {
      const { result } = renderHook(() => useSettingsStore())

      act(() => {
        result.current.setActiveCategory('appearance')
        result.current.updateFormData({ theme: 'dark' })
      })

      // The store should call localStorage.setItem
      expect(localStorageMock.setItem).toHaveBeenCalled()
    })
  })
})
