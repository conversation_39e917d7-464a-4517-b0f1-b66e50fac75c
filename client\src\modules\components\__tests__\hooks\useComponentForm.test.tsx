/**
 * useComponentForm Hook Tests
 * Tests the form management hook for component creation and editing
 */

import { act, renderHook } from '@testing-library/react'
import { mockComponent, mockComponentCreate, mockMutationIdle } from '@/test/utils'
import { beforeEach, describe, expect, it, vi } from 'vitest'
import { useComponentForm } from '../../hooks/useComponentForm'

// Import the functions to be mocked
import { useCreateComponent, useUpdateComponent } from '../../api/componentMutations'
import { validateComponent } from '../../utils'

// Mock the mutation hooks
vi.mock('../../api/componentMutations', () => ({
  useCreateComponent: vi.fn(),
  useUpdateComponent: vi.fn(),
}))

// Mock the validation utility
vi.mock('../../utils', () => ({
  validateComponent: vi.fn(),
}))

describe('useComponentForm', () => {
  const mockCreateMutation = mockMutationIdle()
  const mockUpdateMutation = mockMutationIdle()
  const mockOnSuccess = vi.fn()
  const mockOnError = vi.fn()

  beforeEach(() => {
    vi.clearAllMocks()

    // Mock the mutation hooks
    vi.mocked(useCreateComponent).mockReturnValue(mockCreateMutation as any)
    vi.mocked(useUpdateComponent).mockReturnValue(mockUpdateMutation as any)

    // Mock the validation utility
    vi.mocked(validateComponent).mockReturnValue({
      isValid: true,
      errors: [],
    })
  })

  describe('Initialization', () => {
    it('initializes with empty form for creation', () => {
      const { result } = renderHook(() =>
        useComponentForm({
          onSuccess: mockOnSuccess,
          onError: mockOnError,
        })
      )

      expect(result.current.data).toEqual({})
      expect(result.current.errors).toEqual([])
      expect(result.current.isDirty).toBe(false)
      expect(result.current.isSubmitting).toBe(false)
      expect(result.current.isValid).toBe(true)
      expect(result.current.isEditing).toBe(false)
    })

    it('initializes with component data for editing', () => {
      const { result } = renderHook(() =>
        useComponentForm({
          component: mockComponent,
          onSuccess: mockOnSuccess,
          onError: mockOnError,
        })
      )

      expect(result.current.data).toEqual(mockComponent)
      expect(result.current.isEditing).toBe(true)
    })
  })

  describe('Field Updates', () => {
    it('updates individual fields', () => {
      const { result } = renderHook(() =>
        useComponentForm({
          onSuccess: mockOnSuccess,
          onError: mockOnError,
        })
      )

      act(() => {
        result.current.updateField('name', 'Test Component')
      })

      expect(result.current.data.name).toBe('Test Component')
      expect(result.current.isDirty).toBe(true)
    })

    it('updates multiple fields at once', () => {
      const { result } = renderHook(() =>
        useComponentForm({
          onSuccess: mockOnSuccess,
          onError: mockOnError,
        })
      )

      act(() => {
        result.current.updateFields({
          name: 'Test Component',
          manufacturer: 'Test Manufacturer',
          part_number: 'TEST-001',
        })
      })

      expect(result.current.data.name).toBe('Test Component')
      expect(result.current.data.manufacturer).toBe('Test Manufacturer')
      expect(result.current.data.part_number).toBe('TEST-001')
      expect(result.current.isDirty).toBe(true)
    })

    it('validates fields on update', () => {
      vi.mocked(validateComponent).mockReturnValue({
        isValid: false,
        errors: [{ field: 'name', message: 'Name is required' }],
      })

      const { result } = renderHook(() =>
        useComponentForm({
          onSuccess: mockOnSuccess,
          onError: mockOnError,
        })
      )

      act(() => {
        result.current.updateField('name', '')
      })

      expect(result.current.isValid).toBe(false)
      expect(result.current.errors).toEqual([{ field: 'name', message: 'Name is required' }])
    })

    it('validates specific field', () => {
      vi.mocked(validateComponent).mockReturnValue({
        isValid: false,
        errors: [{ field: 'price', message: 'Price must be positive' }],
      })

      const { result } = renderHook(() =>
        useComponentForm({
          onSuccess: mockOnSuccess,
          onError: mockOnError,
        })
      )

      act(() => {
        result.current.validateField('price')
      })

      expect(result.current.errors).toEqual([{ field: 'price', message: 'Price must be positive' }])
    })
  })

  describe('Error Management', () => {
    it('gets field error', () => {
      vi.mocked(validateComponent).mockReturnValue({
        isValid: false,
        errors: [
          { field: 'name', message: 'Name is required' },
          { field: 'price', message: 'Price must be positive' },
        ],
      })

      const { result } = renderHook(() =>
        useComponentForm({
          onSuccess: mockOnSuccess,
          onError: mockOnError,
        })
      )

      act(() => {
        result.current.updateField('name', '')
      })

      expect(result.current.getFieldError('name')).toBe('Name is required')
      expect(result.current.getFieldError('price')).toBe('Price must be positive')
      expect(result.current.getFieldError('manufacturer')).toBeUndefined()
    })

    it('checks if field has error', () => {
      vi.mocked(validateComponent).mockReturnValue({
        isValid: false,
        errors: [{ field: 'name', message: 'Name is required' }],
      })

      const { result } = renderHook(() =>
        useComponentForm({
          onSuccess: mockOnSuccess,
          onError: mockOnError,
        })
      )

      act(() => {
        result.current.updateField('name', '')
      })

      expect(result.current.hasFieldError('name')).toBe(true)
      expect(result.current.hasFieldError('manufacturer')).toBe(false)
    })

    it('sets field error manually', () => {
      const { result } = renderHook(() =>
        useComponentForm({
          onSuccess: mockOnSuccess,
          onError: mockOnError,
        })
      )

      act(() => {
        result.current.setFieldError('custom_field', 'Custom error message')
      })

      expect(result.current.getFieldError('custom_field')).toBe('Custom error message')
      expect(result.current.hasFieldError('custom_field')).toBe(true)
    })

    it('clears field error', () => {
      const { result } = renderHook(() =>
        useComponentForm({
          onSuccess: mockOnSuccess,
          onError: mockOnError,
        })
      )

      act(() => {
        result.current.setFieldError('name', 'Error message')
      })

      expect(result.current.hasFieldError('name')).toBe(true)

      act(() => {
        result.current.clearFieldError('name')
      })

      expect(result.current.hasFieldError('name')).toBe(false)
    })
  })

  describe('Form Submission', () => {
    it('submits form for creation', async () => {
      const mockMutation = {
        ...mockMutationIdle(),
        mutateAsync: vi.fn().mockResolvedValue(mockComponent),
      }
      vi.mocked(useCreateComponent).mockReturnValue(mockMutation as any)

      const { result } = renderHook(() =>
        useComponentForm({
          onSuccess: mockOnSuccess,
          onError: mockOnError,
        })
      )

      act(() => {
        result.current.updateFields(mockComponentCreate)
      })

      await act(async () => {
        await result.current.submit()
      })

      expect(mockMutation.mutateAsync).toHaveBeenCalledWith(mockComponentCreate)
    })

    it('submits form for update', async () => {
      const mockMutation = {
        ...mockMutationIdle(),
        mutateAsync: vi.fn().mockResolvedValue(mockComponent),
      }
      vi.mocked(useUpdateComponent).mockReturnValue(mockMutation as any)

      const { result } = renderHook(() =>
        useComponentForm({
          component: mockComponent,
          onSuccess: mockOnSuccess,
          onError: mockOnError,
        })
      )

      act(() => {
        result.current.updateField('name', 'Updated Name')
      })

      await act(async () => {
        await result.current.submit()
      })

      expect(mockMutation.mutateAsync).toHaveBeenCalledWith({
        id: mockComponent.id,
        component: expect.objectContaining({
          name: 'Updated Name',
        }),
      })
    })

    it('prevents submission when form is invalid', async () => {
      vi.mocked(validateComponent).mockReturnValue({
        isValid: false,
        errors: [{ field: 'name', message: 'Name is required' }],
      })

      const { result } = renderHook(() =>
        useComponentForm({
          onSuccess: mockOnSuccess,
          onError: mockOnError,
        })
      )

      await act(async () => {
        await result.current.submit()
      })

      expect(mockCreateMutation.mutateAsync).not.toHaveBeenCalled()
    })

    it('prevents submission when already submitting', async () => {
      const { result } = renderHook(() =>
        useComponentForm({
          onSuccess: mockOnSuccess,
          onError: mockOnError,
        })
      )

      // Simulate submitting state
      act(() => {
        result.current.updateFields(mockComponentCreate)
      })

      // Start first submission
      await act(async () => {
        result.current.submit() // Don't await this one
      })

      // Try to submit again while first is in progress
      await act(async () => {
        await result.current.submit()
      })

      // Should only be called once
      expect(mockCreateMutation.mutateAsync).toHaveBeenCalledTimes(1)
    })

    it('handles submission errors', async () => {
      const error = new Error('Submission failed')
      const mockMutation = {
        ...mockMutationIdle(),
        mutateAsync: vi.fn().mockRejectedValue(error),
      }
      vi.mocked(useCreateComponent).mockReturnValue(mockMutation as any)

      const { result } = renderHook(() =>
        useComponentForm({
          onSuccess: mockOnSuccess,
          onError: mockOnError,
        })
      )

      act(() => {
        result.current.updateFields(mockComponentCreate)
      })

      await act(async () => {
        try {
          await result.current.submit()
        } catch (err) {
          // Expected to throw, error should be handled by the hook
        }
      })

      expect(mockOnError).toHaveBeenCalledWith(error)
    })
  })

  describe('Form Reset', () => {
    it('resets form to initial state for creation', () => {
      const { result } = renderHook(() =>
        useComponentForm({
          onSuccess: mockOnSuccess,
          onError: mockOnError,
        })
      )

      act(() => {
        result.current.updateFields(mockComponentCreate)
      })

      expect(result.current.isDirty).toBe(true)

      act(() => {
        result.current.reset()
      })

      expect(result.current.data).toEqual({})
      expect(result.current.isDirty).toBe(false)
      expect(result.current.errors).toEqual([])
    })

    it('resets form to component data for editing', () => {
      const { result } = renderHook(() =>
        useComponentForm({
          component: mockComponent,
          onSuccess: mockOnSuccess,
          onError: mockOnError,
        })
      )

      act(() => {
        result.current.updateField('name', 'Modified Name')
      })

      expect(result.current.data.name).toBe('Modified Name')
      expect(result.current.isDirty).toBe(true)

      act(() => {
        result.current.reset()
      })

      expect(result.current.data.name).toBe(mockComponent.name)
      expect(result.current.isDirty).toBe(false)
    })
  })

  describe('Mutation State Integration', () => {
    it('exposes creation mutation state', () => {
      const mockMutation = {
        ...mockMutationIdle(),
        isPending: true,
        error: new Error('Create error'),
      }
      vi.mocked(useCreateComponent).mockReturnValue(mockMutation as any)

      const { result } = renderHook(() =>
        useComponentForm({
          onSuccess: mockOnSuccess,
          onError: mockOnError,
        })
      )

      expect(result.current.isCreating).toBe(true)
      expect(result.current.createError).toEqual(new Error('Create error'))
    })

    it('exposes update mutation state', () => {
      const mockMutation = {
        ...mockMutationIdle(),
        isPending: true,
        error: new Error('Update error'),
      }
      vi.mocked(useUpdateComponent).mockReturnValue(mockMutation as any)

      const { result } = renderHook(() =>
        useComponentForm({
          component: mockComponent,
          onSuccess: mockOnSuccess,
          onError: mockOnError,
        })
      )

      expect(result.current.isUpdating).toBe(true)
      expect(result.current.updateError).toEqual(new Error('Update error'))
    })
  })

  describe('Edge Cases', () => {
    it('handles missing handlers gracefully', () => {
      const { result } = renderHook(() => useComponentForm({}))

      expect(result.current.data).toEqual({})
      expect(result.current.isEditing).toBe(false)
    })

    it('handles component prop changes', () => {
      const { result, rerender } = renderHook(({ component }) => useComponentForm({ component }), {
        initialProps: { component: undefined as any },
      })

      expect(result.current.isEditing).toBe(false)

      rerender({ component: mockComponent })

      expect(result.current.isEditing).toBe(true)
      expect(result.current.data).toEqual(mockComponent)
    })

    it('handles deeply nested field updates', () => {
      const { result } = renderHook(() =>
        useComponentForm({
          onSuccess: mockOnSuccess,
          onError: mockOnError,
        })
      )

      act(() => {
        result.current.updateField('specifications.resistance', '1000')
      })

      expect(result.current.data.specifications?.resistance).toBe('1000')
    })
  })
})
