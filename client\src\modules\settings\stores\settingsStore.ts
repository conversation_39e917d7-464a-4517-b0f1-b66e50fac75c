/**
 * Zustand store for Settings module client state
 * Ultimate Electrical Designer - Settings & User Preferences
 */

import { create } from 'zustand'
import { createJSONStorage, devtools, persist } from 'zustand/middleware'
import type {
  SettingsStoreState,
  SettingsCategory,
  UserPreferences,
  SettingsUIState,
} from '../types'
import { STORAGE_KEYS, SYNC_CHANNELS, UI_CONSTANTS } from '../constants'

// Default UI state
const defaultUIState: SettingsUIState = {
  activeCategory: 'account',
  searchQuery: '',
  isLoading: false,
  isSaving: false,
  hasUnsavedChanges: false,
  showResetDialog: false,
  showImportDialog: false,
  showExportDialog: false,
  errors: {},
  lastSaved: undefined,
}

// Default form data
const defaultFormData: Partial<UserPreferences> = {}

/**
 * Settings store interface
 */
interface SettingsStore extends SettingsStoreState {
  // Additional utility methods
  reset: () => void
  clearFormData: () => void
  updateMultipleFields: (updates: Partial<UserPreferences>) => void
  validateFormData: () => { isValid: boolean; errors: Record<string, string> }
  getChangedFields: () => string[]
  hasFieldError: (field: string) => boolean
  getFieldError: (field: string) => string | undefined

  // Cross-tab sync methods
  enableSync: () => void
  disableSync: () => void
  broadcastChange: (data: Partial<UserPreferences>) => void

  // Search and filtering
  getFilteredCategories: (query: string) => SettingsCategory[]
  clearSearch: () => void

  // Dialog management
  openDialog: (dialog: 'reset' | 'import' | 'export') => void
  closeDialog: (dialog: 'reset' | 'import' | 'export') => void
  closeAllDialogs: () => void
}

/**
 * Create the settings store
 */
export const useSettingsStore = create<SettingsStore>()(
  devtools(
    persist(
      (set, get) => ({
        // State
        ui: defaultUIState,
        formData: defaultFormData,
        syncEnabled: true,
        lastSyncTime: undefined,

        // UI Actions
        setActiveCategory: (category: SettingsCategory) => {
          set(
            (state) => ({
              ui: { ...state.ui, activeCategory: category },
            }),
            false,
            'setActiveCategory'
          )
        },

        setSearchQuery: (query: string) => {
          set(
            (state) => ({
              ui: { ...state.ui, searchQuery: query },
            }),
            false,
            'setSearchQuery'
          )
        },

        updateFormData: (data: Partial<UserPreferences>) => {
          set(
            (state) => {
              const newFormData = { ...state.formData, ...data }
              const hasChanges = Object.keys(newFormData).length > 0

              return {
                formData: newFormData,
                ui: {
                  ...state.ui,
                  hasUnsavedChanges: hasChanges,
                  lastSaved: hasChanges ? state.ui.lastSaved : new Date().toISOString(),
                },
              }
            },
            false,
            'updateFormData'
          )

          // Broadcast change if sync is enabled
          if (get().syncEnabled) {
            get().broadcastChange(data)
          }
        },

        resetFormData: () => {
          set(
            (state) => ({
              formData: defaultFormData,
              ui: { ...state.ui, hasUnsavedChanges: false },
            }),
            false,
            'resetFormData'
          )
        },

        setLoading: (loading: boolean) => {
          set(
            (state) => ({
              ui: { ...state.ui, isLoading: loading },
            }),
            false,
            'setLoading'
          )
        },

        setSaving: (saving: boolean) => {
          set(
            (state) => ({
              ui: { ...state.ui, isSaving: saving },
            }),
            false,
            'setSaving'
          )
        },

        setError: (field: string, error: string) => {
          set(
            (state) => ({
              ui: {
                ...state.ui,
                errors: { ...state.ui.errors, [field]: error },
              },
            }),
            false,
            'setError'
          )
        },

        clearErrors: () => {
          set(
            (state) => ({
              ui: { ...state.ui, errors: {} },
            }),
            false,
            'clearErrors'
          )
        },

        setHasUnsavedChanges: (hasChanges: boolean) => {
          set(
            (state) => ({
              ui: { ...state.ui, hasUnsavedChanges: hasChanges },
            }),
            false,
            'setHasUnsavedChanges'
          )
        },

        showDialog: (dialog: 'reset' | 'import' | 'export', show: boolean) => {
          set(
            (state) => ({
              ui: {
                ...state.ui,
                [`show${dialog.charAt(0).toUpperCase() + dialog.slice(1)}Dialog`]: show,
              },
            }),
            false,
            'showDialog'
          )
        },

        // Utility methods
        reset: () => {
          set(
            {
              ui: defaultUIState,
              formData: defaultFormData,
              syncEnabled: true,
              lastSyncTime: undefined,
            },
            false,
            'reset'
          )
        },

        clearFormData: () => {
          set(
            (state) => ({
              formData: defaultFormData,
              ui: { ...state.ui, hasUnsavedChanges: false },
            }),
            false,
            'clearFormData'
          )
        },

        updateMultipleFields: (updates: Partial<UserPreferences>) => {
          get().updateFormData(updates)
        },

        validateFormData: () => {
          const { formData } = get()
          const errors: Record<string, string> = {}
          let isValid = true

          // Validation logic
          if (formData.auto_save_interval !== undefined) {
            if (formData.auto_save_interval < 30) {
              errors.auto_save_interval = 'Auto save interval must be at least 30 seconds'
              isValid = false
            }
            if (formData.auto_save_interval > 3600) {
              errors.auto_save_interval = 'Auto save interval cannot exceed 1 hour'
              isValid = false
            }
          }

          if (formData.calculation_precision !== undefined) {
            if (formData.calculation_precision < 0 || formData.calculation_precision > 6) {
              errors.calculation_precision = 'Calculation precision must be between 0 and 6'
              isValid = false
            }
          }

          // Update errors in state
          set(
            (state) => ({
              ui: { ...state.ui, errors },
            }),
            false,
            'validateFormData'
          )

          return { isValid, errors }
        },

        getChangedFields: () => {
          return Object.keys(get().formData)
        },

        hasFieldError: (field: string) => {
          return field in get().ui.errors
        },

        getFieldError: (field: string) => {
          return get().ui.errors[field]
        },

        // Cross-tab sync methods
        enableSync: () => {
          set({ syncEnabled: true }, false, 'enableSync')
        },

        disableSync: () => {
          set({ syncEnabled: false }, false, 'disableSync')
        },

        broadcastChange: (data: Partial<UserPreferences>) => {
          if (typeof window !== 'undefined' && 'BroadcastChannel' in window) {
            try {
              const channel = new BroadcastChannel(SYNC_CHANNELS.SETTINGS)
              channel.postMessage({
                type: 'SETTINGS_UPDATE',
                data,
                timestamp: Date.now(),
              })
              channel.close()
            } catch (error) {
              console.warn('Failed to broadcast settings change:', error)
            }
          }
        },

        // Search and filtering
        getFilteredCategories: (query: string) => {
          // This would filter categories based on search query
          // Implementation depends on SETTINGS_CATEGORIES structure
          return []
        },

        clearSearch: () => {
          get().setSearchQuery('')
        },

        // Dialog management
        openDialog: (dialog: 'reset' | 'import' | 'export') => {
          get().showDialog(dialog, true)
        },

        closeDialog: (dialog: 'reset' | 'import' | 'export') => {
          get().showDialog(dialog, false)
        },

        closeAllDialogs: () => {
          set(
            (state) => ({
              ui: {
                ...state.ui,
                showResetDialog: false,
                showImportDialog: false,
                showExportDialog: false,
              },
            }),
            false,
            'closeAllDialogs'
          )
        },
      }),
      {
        name: STORAGE_KEYS.SETTINGS_STORE,
        storage: createJSONStorage(() => localStorage),
        // Only persist UI preferences and form backup
        partialize: (state) => ({
          ui: {
            activeCategory: state.ui.activeCategory,
            searchQuery: state.ui.searchQuery,
          },
          formData: state.formData,
          syncEnabled: state.syncEnabled,
        }),
        // Rehydrate with proper defaults
        onRehydrateStorage: () => (state) => {
          if (state) {
            // Ensure UI state has all required properties
            state.ui = { ...defaultUIState, ...state.ui }
          }
        },
      }
    ),
    { name: 'settings-store' }
  )
)

// Cross-tab synchronization setup
if (typeof window !== 'undefined' && 'BroadcastChannel' in window) {
  try {
    const syncChannel = new BroadcastChannel(SYNC_CHANNELS.SETTINGS)

    syncChannel.addEventListener('message', (event) => {
      const { type, data, timestamp } = event.data

      if (type === 'SETTINGS_UPDATE') {
        const store = useSettingsStore.getState()

        // Only apply if sync is enabled and the change is newer
        if (
          store.syncEnabled &&
          (!store.lastSyncTime || timestamp > new Date(store.lastSyncTime).getTime())
        ) {
          store.updateFormData(data)
          useSettingsStore.setState({
            lastSyncTime: new Date(timestamp).toISOString(),
          })
        }
      }
    })

    // Cleanup on page unload
    window.addEventListener('beforeunload', () => {
      syncChannel.close()
    })
  } catch (error) {
    console.warn('Failed to setup cross-tab sync:', error)
  }
}

// Export store selectors for performance optimization
export const settingsStoreSelectors = {
  ui: (state: SettingsStore) => state.ui,
  formData: (state: SettingsStore) => state.formData,
  activeCategory: (state: SettingsStore) => state.ui.activeCategory,
  searchQuery: (state: SettingsStore) => state.ui.searchQuery,
  isLoading: (state: SettingsStore) => state.ui.isLoading,
  isSaving: (state: SettingsStore) => state.ui.isSaving,
  hasUnsavedChanges: (state: SettingsStore) => state.ui.hasUnsavedChanges,
  errors: (state: SettingsStore) => state.ui.errors,
  syncEnabled: (state: SettingsStore) => state.syncEnabled,
}
