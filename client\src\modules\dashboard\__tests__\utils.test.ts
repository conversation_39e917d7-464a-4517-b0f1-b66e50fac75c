/**
 * Unit tests for dashboard utilities
 */

import { describe, it, expect } from 'vitest'
import {
  formatProjectStatus,
  getProjectStatusColor,
  formatCalculationType,
  getCalculationTypeColor,
  formatProjectPriority,
  getProjectPriorityColor,
  formatRelativeTime,
  calculateProjectProgress,
  getBrandColorClass,
  filterProjectsByStatus,
  filterCalculationsByType,
  searchProjects,
  sortProjects,
  generateMetricsSummary,
} from '../utils'
import type { ProjectSummary, RecentCalculation, DashboardData } from '../types'

describe('Dashboard Utils', () => {
  describe('formatProjectStatus', () => {
    it('should format project status correctly', () => {
      expect(formatProjectStatus('active')).toBe('Active')
      expect(formatProjectStatus('completed')).toBe('Completed')
      expect(formatProjectStatus('on_hold')).toBe('On Hold')
      expect(formatProjectStatus('draft')).toBe('Draft')
    })
  })

  describe('getProjectStatusColor', () => {
    it('should return correct color classes for project status', () => {
      expect(getProjectStatusColor('active')).toBe('text-green-600 bg-green-100')
      expect(getProjectStatusColor('completed')).toBe('text-blue-600 bg-blue-100')
      expect(getProjectStatusColor('on_hold')).toBe('text-yellow-600 bg-yellow-100')
      expect(getProjectStatusColor('draft')).toBe('text-gray-600 bg-gray-100')
    })
  })

  describe('formatCalculationType', () => {
    it('should format calculation type correctly', () => {
      expect(formatCalculationType('heat_tracing')).toBe('Heat Tracing')
      expect(formatCalculationType('load_calculation')).toBe('Load Calculation')
      expect(formatCalculationType('cable_sizing')).toBe('Cable Sizing')
    })
  })

  describe('getCalculationTypeColor', () => {
    it('should return correct color classes for calculation type', () => {
      expect(getCalculationTypeColor('heat_tracing')).toBe('text-orange-600 bg-orange-100')
      expect(getCalculationTypeColor('load_calculation')).toBe('text-blue-600 bg-blue-100')
      expect(getCalculationTypeColor('cable_sizing')).toBe('text-purple-600 bg-purple-100')
    })
  })

  describe('formatProjectPriority', () => {
    it('should format project priority correctly', () => {
      expect(formatProjectPriority('low')).toBe('Low')
      expect(formatProjectPriority('medium')).toBe('Medium')
      expect(formatProjectPriority('high')).toBe('High')
      expect(formatProjectPriority('critical')).toBe('Critical')
    })
  })

  describe('getProjectPriorityColor', () => {
    it('should return correct color classes for project priority', () => {
      expect(getProjectPriorityColor('low')).toBe('text-gray-600 bg-gray-100')
      expect(getProjectPriorityColor('medium')).toBe('text-blue-600 bg-blue-100')
      expect(getProjectPriorityColor('high')).toBe('text-orange-600 bg-orange-100')
      expect(getProjectPriorityColor('critical')).toBe('text-red-600 bg-red-100')
    })
  })

  describe('formatRelativeTime', () => {
    it('should format relative time correctly', () => {
      const now = new Date()
      const oneMinuteAgo = new Date(now.getTime() - 60 * 1000)
      const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000)
      const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000)

      expect(formatRelativeTime(oneMinuteAgo.toISOString())).toBe('1 minute ago')
      expect(formatRelativeTime(oneHourAgo.toISOString())).toBe('1 hour ago')
      expect(formatRelativeTime(oneDayAgo.toISOString())).toBe('1 day ago')
    })

    it('should return "Just now" for very recent times', () => {
      const now = new Date()
      const thirtySecondsAgo = new Date(now.getTime() - 30 * 1000)

      expect(formatRelativeTime(thirtySecondsAgo.toISOString())).toBe('Just now')
    })
  })

  describe('calculateProjectProgress', () => {
    it('should return project progress within valid range', () => {
      const project: ProjectSummary = {
        id: '1',
        name: 'Test Project',
        description: 'Test Description',
        status: 'active',
        progress: 75,
        lastModified: new Date().toISOString(),
        createdAt: new Date().toISOString(),
        owner: 'Test User',
        type: 'heat_tracing',
        priority: 'medium',
      }

      expect(calculateProjectProgress(project)).toBe(75)

      // Test edge cases
      project.progress = -10
      expect(calculateProjectProgress(project)).toBe(0)

      project.progress = 150
      expect(calculateProjectProgress(project)).toBe(100)
    })
  })

  describe('getBrandColorClass', () => {
    it('should return correct brand color classes', () => {
      expect(getBrandColorClass('primary')).toBe('bg-brand-primary')
      expect(getBrandColorClass('secondary', 'text')).toBe('text-brand-secondary')
      expect(getBrandColorClass('accent', 'border')).toBe('border-brand-accent')
      expect(getBrandColorClass('dark')).toBe('bg-brand-dark')
    })
  })

  describe('filterProjectsByStatus', () => {
    const projects: ProjectSummary[] = [
      {
        id: '1',
        name: 'Active Project',
        description: 'Description',
        status: 'active',
        progress: 50,
        lastModified: new Date().toISOString(),
        createdAt: new Date().toISOString(),
        owner: 'User 1',
        type: 'heat_tracing',
        priority: 'medium',
      },
      {
        id: '2',
        name: 'Completed Project',
        description: 'Description',
        status: 'completed',
        progress: 100,
        lastModified: new Date().toISOString(),
        createdAt: new Date().toISOString(),
        owner: 'User 2',
        type: 'load_calculation',
        priority: 'high',
      },
    ]

    it('should filter projects by status', () => {
      expect(filterProjectsByStatus(projects, 'active')).toHaveLength(1)
      expect(filterProjectsByStatus(projects, 'completed')).toHaveLength(1)
      expect(filterProjectsByStatus(projects, 'on_hold')).toHaveLength(0)
    })

    it('should return all projects when status is "all"', () => {
      expect(filterProjectsByStatus(projects, 'all')).toHaveLength(2)
    })
  })

  describe('searchProjects', () => {
    const projects: ProjectSummary[] = [
      {
        id: '1',
        name: 'Heat Tracing Project',
        description: 'Industrial heat tracing system',
        status: 'active',
        progress: 50,
        lastModified: new Date().toISOString(),
        createdAt: new Date().toISOString(),
        owner: 'User 1',
        type: 'heat_tracing',
        priority: 'medium',
      },
      {
        id: '2',
        name: 'Load Calculation',
        description: 'Power distribution analysis',
        status: 'completed',
        progress: 100,
        lastModified: new Date().toISOString(),
        createdAt: new Date().toISOString(),
        owner: 'User 2',
        type: 'load_calculation',
        priority: 'high',
      },
    ]

    it('should search projects by name', () => {
      expect(searchProjects(projects, 'heat')).toHaveLength(1)
      expect(searchProjects(projects, 'load')).toHaveLength(1)
    })

    it('should search projects by description', () => {
      expect(searchProjects(projects, 'industrial')).toHaveLength(1)
      expect(searchProjects(projects, 'power')).toHaveLength(1)
    })

    it('should return all projects for empty query', () => {
      expect(searchProjects(projects, '')).toHaveLength(2)
      expect(searchProjects(projects, '   ')).toHaveLength(2)
    })

    it('should be case insensitive', () => {
      expect(searchProjects(projects, 'HEAT')).toHaveLength(1)
      expect(searchProjects(projects, 'Load')).toHaveLength(1)
    })
  })

  describe('generateMetricsSummary', () => {
    it('should generate correct metrics summary', () => {
      const data: DashboardData = {
        metrics: {
          totalProjects: 10,
          activeProjects: 5,
          completedCalculations: 25,
          recentActivity: 3,
          systemUptime: '99.9%',
          lastLogin: new Date().toISOString(),
        },
        projects: [],
        recentCalculations: [{ id: '1' } as RecentCalculation, { id: '2' } as RecentCalculation],
        quickActions: [],
        widgets: [],
        recentActivity: [],
        preferences: {
          layout: 'grid',
          theme: 'light',
          widgetOrder: [],
          hiddenWidgets: [],
          refreshInterval: 300000,
          showWelcomeMessage: true,
          defaultProjectView: 'active',
          calculationHistoryLimit: 10,
        },
      }

      const summary = generateMetricsSummary(data)
      expect(summary).toBe('10 projects, 25 calculations, 2 recent activities')
    })
  })
})
