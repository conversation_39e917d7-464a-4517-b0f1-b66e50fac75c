/**
 * Authentication E2E Tests
 * End-to-end tests for login and registration flows
 */

import { test, expect } from '@playwright/test'

test.describe('Authentication Flow', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the application
    await page.goto('/')
  })

  test.describe('Login Page', () => {
    test('should display login form', async ({ page }) => {
      await page.goto('/login')

      // Check page title and heading
      await expect(page).toHaveTitle(/Ultimate Electrical Designer/i)
      await expect(page.getByRole('heading', { name: /sign in to your account/i })).toBeVisible()

      // Check form fields
      await expect(page.getByLabel(/username or email/i)).toBeVisible()
      await expect(page.getByLabel(/password/i)).toBeVisible()
      await expect(page.getByRole('button', { name: /sign in/i })).toBeVisible()

      // Check additional elements
      await expect(page.getByText(/remember me/i)).toBeVisible()
      await expect(page.getByText(/forgot your password/i)).toBeVisible()
      await expect(page.getByText(/enterprise-grade encryption/i)).toBeVisible()
    })

    test('should validate required fields', async ({ page }) => {
      await page.goto('/login')

      // Submit empty form
      await page.getByRole('button', { name: /sign in/i }).click()

      // Check validation errors
      await expect(page.getByText(/username or email is required/i)).toBeVisible()
      await expect(page.getByText(/password is required/i)).toBeVisible()
    })

    test('should validate password length', async ({ page }) => {
      await page.goto('/login')

      // Fill form with short password
      await page.getByLabel(/username or email/i).fill('<EMAIL>')
      await page.getByLabel(/password/i).fill('123')
      await page.getByRole('button', { name: /sign in/i }).click()

      // Check validation error
      await expect(page.getByText(/password must be at least 8 characters/i)).toBeVisible()
    })

    test('should show password visibility toggle', async ({ page }) => {
      await page.goto('/login')

      const passwordInput = page.getByLabel(/password/i)
      const toggleButton = page.getByRole('button', { name: /show password|hide password/i })

      // Password should be hidden by default
      await expect(passwordInput).toHaveAttribute('type', 'password')

      // Click toggle to show password
      await toggleButton.click()
      await expect(passwordInput).toHaveAttribute('type', 'text')

      // Click toggle to hide password again
      await toggleButton.click()
      await expect(passwordInput).toHaveAttribute('type', 'password')
    })

    test('should clear field errors when typing', async ({ page }) => {
      await page.goto('/login')

      // Submit empty form to show errors
      await page.getByRole('button', { name: /sign in/i }).click()
      await expect(page.getByText(/username or email is required/i)).toBeVisible()

      // Start typing in username field
      await page.getByLabel(/username or email/i).fill('test')

      // Error should be cleared
      await expect(page.getByText(/username or email is required/i)).not.toBeVisible()
    })

    test('should navigate to register page', async ({ page }) => {
      await page.goto('/login')

      // Click register link
      await page.getByText(/create an account/i).click()

      // Should navigate to register page
      await expect(page).toHaveURL(/\/register/)
      await expect(page.getByRole('heading', { name: /create your account/i })).toBeVisible()
    })

    test('should be responsive on mobile', async ({ page }) => {
      // Set mobile viewport
      await page.setViewportSize({ width: 375, height: 667 })
      await page.goto('/login')

      // Check mobile layout
      await expect(page.getByRole('heading', { name: /sign in to your account/i })).toBeVisible()
      await expect(page.getByLabel(/username or email/i)).toBeVisible()
      await expect(page.getByLabel(/password/i)).toBeVisible()

      // Form should be usable on mobile
      await page.getByLabel(/username or email/i).fill('<EMAIL>')
      await page.getByLabel(/password/i).fill('password123')
      await expect(page.getByRole('button', { name: /sign in/i })).toBeVisible()
    })
  })

  test.describe('Register Page', () => {
    test('should display registration form', async ({ page }) => {
      await page.goto('/register')

      // Check page title and heading
      await expect(page).toHaveTitle(/Ultimate Electrical Designer/i)
      await expect(page.getByRole('heading', { name: /create your account/i })).toBeVisible()

      // Check form fields
      await expect(page.getByLabel(/username/i)).toBeVisible()
      await expect(page.getByLabel(/email address/i)).toBeVisible()
      await expect(page.getByLabel(/^password$/i)).toBeVisible()
      await expect(page.getByLabel(/confirm password/i)).toBeVisible()
      await expect(page.getByRole('button', { name: /create your account/i })).toBeVisible()

      // Check additional elements
      await expect(page.getByText(/terms of service/i)).toBeVisible()
      await expect(page.getByText(/privacy policy/i)).toBeVisible()
      await expect(page.getByText(/enterprise-grade security/i)).toBeVisible()
    })

    test('should validate required fields', async ({ page }) => {
      await page.goto('/register')

      // Submit empty form
      await page.getByRole('button', { name: /create your account/i }).click()

      // Check validation errors
      await expect(page.getByText(/username must be at least 3 characters/i)).toBeVisible()
      await expect(page.getByText(/please enter a valid email address/i)).toBeVisible()
      await expect(page.getByText(/password must be at least 8 characters/i)).toBeVisible()
    })

    test('should validate username format', async ({ page }) => {
      await page.goto('/register')

      // Test short username
      await page.getByLabel(/username/i).fill('ab')
      await page.getByRole('button', { name: /create your account/i }).click()
      await expect(page.getByText(/username must be at least 3 characters/i)).toBeVisible()

      // Test invalid characters
      await page.getByLabel(/username/i).fill('user@name')
      await page.getByRole('button', { name: /create your account/i }).click()
      await expect(
        page.getByText(/username can only contain letters, numbers, underscores, and hyphens/i)
      ).toBeVisible()
    })

    test('should validate email format', async ({ page }) => {
      await page.goto('/register')

      await page.getByLabel(/email address/i).fill('invalid-email')
      await page.getByRole('button', { name: /create your account/i }).click()

      await expect(page.getByText(/please enter a valid email address/i)).toBeVisible()
    })

    test('should show password strength indicator', async ({ page }) => {
      await page.goto('/register')

      const passwordInput = page.getByLabel(/^password$/i)

      // Type weak password
      await passwordInput.fill('password')
      await expect(page.getByText(/password strength/i)).toBeVisible()
      await expect(page.getByText(/weak/i)).toBeVisible()

      // Type strong password
      await passwordInput.fill('TestPassword123!')
      await expect(page.getByText(/strong/i)).toBeVisible()

      // Check requirements checklist
      await expect(page.getByText(/at least 8 characters/i)).toBeVisible()
      await expect(page.getByText(/one uppercase letter/i)).toBeVisible()
      await expect(page.getByText(/one lowercase letter/i)).toBeVisible()
      await expect(page.getByText(/one number/i)).toBeVisible()
      await expect(page.getByText(/one special character/i)).toBeVisible()
    })

    test('should validate password confirmation', async ({ page }) => {
      await page.goto('/register')

      await page.getByLabel(/^password$/i).fill('TestPassword123!')
      await page.getByLabel(/confirm password/i).fill('DifferentPassword123!')
      await page.getByRole('button', { name: /create your account/i }).click()

      await expect(page.getByText(/passwords do not match/i)).toBeVisible()
    })

    test('should show password match indicator', async ({ page }) => {
      await page.goto('/register')

      const passwordInput = page.getByLabel(/^password$/i)
      const confirmPasswordInput = page.getByLabel(/confirm password/i)

      // Type matching passwords
      await passwordInput.fill('TestPassword123!')
      await confirmPasswordInput.fill('TestPassword123!')

      // Should show match indicator
      await expect(page.getByText(/passwords match/i)).toBeVisible()
    })

    test('should navigate to login page', async ({ page }) => {
      await page.goto('/register')

      // Click login link
      await page.getByText(/sign in here/i).click()

      // Should navigate to login page
      await expect(page).toHaveURL(/\/login/)
      await expect(page.getByRole('heading', { name: /sign in to your account/i })).toBeVisible()
    })

    test('should be responsive on mobile', async ({ page }) => {
      // Set mobile viewport
      await page.setViewportSize({ width: 375, height: 667 })
      await page.goto('/register')

      // Check mobile layout
      await expect(page.getByRole('heading', { name: /create your account/i })).toBeVisible()
      await expect(page.getByLabel(/username/i)).toBeVisible()
      await expect(page.getByLabel(/email address/i)).toBeVisible()

      // Form should be usable on mobile
      await page.getByLabel(/username/i).fill('testuser')
      await page.getByLabel(/email address/i).fill('<EMAIL>')
      await expect(page.getByRole('button', { name: /create your account/i })).toBeVisible()
    })
  })

  test.describe('Accessibility', () => {
    test('login page should be accessible', async ({ page }) => {
      await page.goto('/login')

      // Check for proper heading structure
      const headings = await page.locator('h1, h2, h3, h4, h5, h6').all()
      expect(headings.length).toBeGreaterThan(0)

      // Check for form labels
      await expect(page.getByLabel(/username or email/i)).toBeVisible()
      await expect(page.getByLabel(/password/i)).toBeVisible()

      // Check for ARIA attributes
      const submitButton = page.getByRole('button', { name: /sign in/i })
      await expect(submitButton).toBeVisible()

      // Check keyboard navigation
      await page.keyboard.press('Tab')
      await expect(page.getByLabel(/username or email/i)).toBeFocused()
    })

    test('register page should be accessible', async ({ page }) => {
      await page.goto('/register')

      // Check for proper heading structure
      const headings = await page.locator('h1, h2, h3, h4, h5, h6').all()
      expect(headings.length).toBeGreaterThan(0)

      // Check for form labels
      await expect(page.getByLabel(/username/i)).toBeVisible()
      await expect(page.getByLabel(/email address/i)).toBeVisible()
      await expect(page.getByLabel(/^password$/i)).toBeVisible()
      await expect(page.getByLabel(/confirm password/i)).toBeVisible()

      // Check keyboard navigation
      await page.keyboard.press('Tab')
      await expect(page.getByLabel(/username/i)).toBeFocused()
    })
  })

  test.describe('Performance', () => {
    test('login page should load quickly', async ({ page }) => {
      const startTime = Date.now()
      await page.goto('/login')
      await page.waitForLoadState('networkidle')
      const loadTime = Date.now() - startTime

      // Page should load within 3 seconds
      expect(loadTime).toBeLessThan(3000)

      // Check that critical elements are visible
      await expect(page.getByRole('heading', { name: /sign in to your account/i })).toBeVisible()
      await expect(page.getByLabel(/username or email/i)).toBeVisible()
    })

    test('register page should load quickly', async ({ page }) => {
      const startTime = Date.now()
      await page.goto('/register')
      await page.waitForLoadState('networkidle')
      const loadTime = Date.now() - startTime

      // Page should load within 3 seconds
      expect(loadTime).toBeLessThan(3000)

      // Check that critical elements are visible
      await expect(page.getByRole('heading', { name: /create your account/i })).toBeVisible()
      await expect(page.getByLabel(/username/i)).toBeVisible()
    })
  })
})
